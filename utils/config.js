export const builtInModelGroups = [
  {
    name: "OpenAI Models",
    models: [
      // GPT-5 Family (Latest & Best - Released Aug 2025)
      { value: "gpt-5", label: "GPT-5 (Latest & Best for Coding/Agentic)" },
      { value: "gpt-5-mini", label: "GPT-5 Mini (Fast & Efficient)" },
      { value: "gpt-5-nano", label: "GPT-5 Nano (Fastest & Cheapest)" },
      { value: "gpt-5-chat-latest", label: "GPT-5 Chat (Non-reasoning)" },

      // GPT-4o Family (Current Generation)
      { value: "gpt-4o", label: "GPT-4o (Multimodal)" },
      { value: "gpt-4o-mini", label: "GPT-4o Mini (Fast)" },

      // Reasoning Models (o1 Series)
      { value: "o1", label: "o1 (Advanced Reasoning)" },
      { value: "o1-mini", label: "o1 Mini (Fast Reasoning)" },

      // GPT-4 Family (Still Supported)
      { value: "gpt-4-turbo", label: "GPT-4 Turbo" },
      { value: "gpt-4", label: "GPT-4" },

      // GPT-3.5 Family (Legacy)
      { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo (Legacy)" },
    ]
  },
  {
    name: "Gemini Models",
    models: [
      // Gemini 2.5 Family (Latest & Best)
      { value: "gemini-2.5-pro", label: "Gemini 2.5 Pro (Latest & Best)" },
      { value: "gemini-2.5-flash", label: "Gemini 2.5 Flash (Recommended)" },
      { value: "gemini-2.5-flash-lite", label: "Gemini 2.5 Flash Lite (Fast)" },

      // Gemini 2.0 Family (Current)
      { value: "gemini-2.0-flash", label: "Gemini 2.0 Flash" },
      { value: "gemini-2.0-flash-lite", label: "Gemini 2.0 Flash Lite" },

      // Gemini 1.5 Family (Deprecated but still supported)
      { value: "gemini-1.5-flash", label: "Gemini 1.5 Flash (Deprecated)" },
      { value: "gemini-1.5-flash-8b", label: "Gemini 1.5 Flash 8B (Deprecated)" },
      { value: "gemini-1.5-pro", label: "Gemini 1.5 Pro (Deprecated)" },
    ]
  }
];


const defaultConfig = {
  openaiKey: '',
  geminiKey: '',
  aiModel: 'gpt-5-mini', // Latest GPT-5 model with excellent performance and efficiency
  silenceTimerDuration: 1.2,
  responseLength: 'medium',
  gptSystemPrompt: `You are an AI interview assistant. Your role is to:
- Highlight key points in responses
- Suggest related technical concepts to explore
- Maintain professional tone`,
  azureToken: '',
  azureRegion: 'eastus',
  azureLanguage: 'en-US',
  customModels: [], // Array for user-added models { value: 'model-id', label: 'Display Name', type: 'openai' | 'gemini' }
  systemAutoMode: true,
  isManualMode: false,
  // Advanced settings for reasoning models
  reasoningEffort: 'medium', // low, medium, high (for o1 and future reasoning models)
  verbosity: 1, // 0-2 for response detail level (for advanced models)
  thinkingBudget: null, // null = default, 0 = disabled, number = custom budget for Gemini 2.5 thinking
};

export function getConfig() {
  if (typeof window !== 'undefined') {
    const storedConfig = localStorage.getItem('interviewCopilotConfig');
    let parsed = storedConfig ? JSON.parse(storedConfig) : {};
    
    // Migrate old config format for aiModel if gptModel exists
    if (parsed.gptModel && !parsed.aiModel) {
      parsed.aiModel = parsed.gptModel;
      delete parsed.gptModel;
    }
    // Ensure customModels is an array
    if (!Array.isArray(parsed.customModels)) {
        parsed.customModels = [];
    }

    return { ...defaultConfig, ...parsed };
  }
  return defaultConfig;
}

export function setConfig(config) {
  if (typeof window !== 'undefined') {
    // Ensure customModels is an array before saving
    const configToSave = {
        ...config,
        customModels: Array.isArray(config.customModels) ? config.customModels : []
    };
    localStorage.setItem('interviewCopilotConfig', JSON.stringify(configToSave));
  }
}
