export const builtInModelGroups = [
  {
    name: "OpenAI Models",
    models: [
      // Latest GPT-4 Family (Most Current)
      { value: "gpt-4o", label: "GPT-4o (Latest & Best)" },
      { value: "gpt-4o-mini", label: "GPT-4o Mini (Fast & Efficient)" },
      { value: "gpt-4-turbo", label: "GPT-4 Turbo" },
      { value: "gpt-4", label: "GPT-4" },

      // GPT-3.5 Family (Legacy but still supported)
      { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo (Legacy)" },
    ]
  },
  {
    name: "Gemini Models",
    models: [
      // Gemini 2.0 Family (Latest)
      { value: "gemini-2.0-flash-exp", label: "Gemini 2.0 Flash (Experimental)" },

      // Gemini 1.5 Family (Stable & Recommended)
      { value: "gemini-1.5-flash", label: "Gemini 1.5 Flash (Recommended)" },
      { value: "gemini-1.5-flash-8b", label: "Gemini 1.5 Flash 8B (Fast)" },
      { value: "gemini-1.5-pro", label: "Gemini 1.5 Pro (Best Quality)" },

      // Legacy models (still supported)
      { value: "gemini-pro", label: "Gemini Pro (Legacy)" },
    ]
  }
];


const defaultConfig = {
  openaiKey: '',
  geminiKey: '',
  aiModel: 'gpt-4o-mini', // Better default than gpt-3.5-turbo
  silenceTimerDuration: 1.2,
  responseLength: 'medium',
  gptSystemPrompt: `You are an AI interview assistant. Your role is to:
- Highlight key points in responses
- Suggest related technical concepts to explore
- Maintain professional tone`,
  azureToken: '',
  azureRegion: 'eastus',
  azureLanguage: 'en-US',
  customModels: [], // Array for user-added models { value: 'model-id', label: 'Display Name', type: 'openai' | 'gemini' }
  systemAutoMode: true,
  isManualMode: false,
  // Future-ready settings for advanced models
  reasoningEffort: 'medium', // low, medium, high (for future advanced models)
  verbosity: 1, // 0-2 for response detail level (for future advanced models)
};

export function getConfig() {
  if (typeof window !== 'undefined') {
    const storedConfig = localStorage.getItem('interviewCopilotConfig');
    let parsed = storedConfig ? JSON.parse(storedConfig) : {};
    
    // Migrate old config format for aiModel if gptModel exists
    if (parsed.gptModel && !parsed.aiModel) {
      parsed.aiModel = parsed.gptModel;
      delete parsed.gptModel;
    }
    // Ensure customModels is an array
    if (!Array.isArray(parsed.customModels)) {
        parsed.customModels = [];
    }

    return { ...defaultConfig, ...parsed };
  }
  return defaultConfig;
}

export function setConfig(config) {
  if (typeof window !== 'undefined') {
    // Ensure customModels is an array before saving
    const configToSave = {
        ...config,
        customModels: Array.isArray(config.customModels) ? config.customModels : []
    };
    localStorage.setItem('interviewCopilotConfig', JSON.stringify(configToSave));
  }
}
