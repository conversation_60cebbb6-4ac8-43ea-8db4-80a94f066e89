// Dynamic imports for client-side only
let createClient = null;
let SpeechSDK = null;

// Initialize client-side dependencies
if (typeof window !== 'undefined') {
  import('@deepgram/sdk').then(module => {
    createClient = module.createClient;
  });
  import('microsoft-cognitiveservices-speech-sdk').then(module => {
    SpeechSDK = module;
  });
}

/**
 * Abstract base class for speech services
 */
class SpeechService {
  constructor(config, callbacks) {
    this.config = config;
    this.callbacks = callbacks;
    this.isActive = false;
    this.recognizer = null;
  }

  async start(mediaStream, source) {
    throw new Error('start method must be implemented');
  }

  async stop() {
    throw new Error('stop method must be implemented');
  }

  cleanup() {
    // Default cleanup implementation
    if (this.recognizer) {
      this.recognizer = null;
    }
    this.isActive = false;
  }
}

/**
 * Deepgram Speech Service Implementation
 */
export class DeepgramSpeechService extends SpeechService {
  constructor(config, callbacks) {
    super(config, callbacks);
    this.deepgram = null;
    this.connection = null;
    this.mediaRecorder = null;
    this.source = null;
  }

  async start(mediaStream, source) {
    try {
      if (!this.config.deepgramKey) {
        throw new Error('Deepgram API key is required');
      }

      if (!createClient) {
        const { createClient: dgCreateClient } = await import('@deepgram/sdk');
        createClient = dgCreateClient;
      }

      this.source = source;
      this.deepgram = createClient(this.config.deepgramKey);

      // Create WebSocket connection for real-time transcription
      this.connection = this.deepgram.listen.live({
        model: this.config.deepgramModel || 'nova-2',
        language: this.config.deepgramLanguage || 'en-US',
        smart_format: true,
        interim_results: true,
        endpointing: 300, // 300ms of silence before finalizing
        utterance_end_ms: 1000,
        vad_events: true,
      });

      // Set up event handlers
      this.connection.on('open', () => {
        console.log(`Deepgram connection opened for ${source}`);
        this.isActive = true;
        this.callbacks.onStart?.(source);
      });

      this.connection.on('Results', (data) => {
        const transcript = data.channel?.alternatives?.[0]?.transcript;
        if (transcript) {
          if (data.is_final) {
            // Final result
            this.callbacks.onFinalResult?.(transcript, source);
          } else {
            // Interim result
            this.callbacks.onInterimResult?.(transcript, source);
          }
        }
      });

      this.connection.on('error', (error) => {
        console.error(`Deepgram error for ${source}:`, error);
        this.callbacks.onError?.(error, source);
        this.stop();
      });

      this.connection.on('close', () => {
        console.log(`Deepgram connection closed for ${source}`);
        this.isActive = false;
        this.callbacks.onStop?.(source);
      });

      // Set up MediaRecorder to send audio data to Deepgram
      this.mediaRecorder = new MediaRecorder(mediaStream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0 && this.connection && this.isActive) {
          this.connection.send(event.data);
        }
      };

      this.mediaRecorder.onerror = (error) => {
        console.error(`MediaRecorder error for ${source}:`, error);
        this.callbacks.onError?.(error, source);
      };

      // Start recording and sending data
      this.mediaRecorder.start(100); // Send data every 100ms
      
      return this;
    } catch (error) {
      console.error(`Failed to start Deepgram for ${source}:`, error);
      this.callbacks.onError?.(error, source);
      throw error;
    }
  }

  async stop() {
    try {
      this.isActive = false;

      if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
        this.mediaRecorder.stop();
      }

      if (this.connection) {
        this.connection.finish();
        this.connection = null;
      }

      this.cleanup();
    } catch (error) {
      console.error('Error stopping Deepgram:', error);
      throw error;
    }
  }

  cleanup() {
    super.cleanup();
    this.mediaRecorder = null;
    this.connection = null;
    this.deepgram = null;
    this.source = null;
  }
}

/**
 * Azure Speech Service Implementation (Legacy)
 */
export class AzureSpeechService extends SpeechService {
  constructor(config, callbacks) {
    super(config, callbacks);
    this.audioConfig = null;
    this.speechConfig = null;
  }

  async start(mediaStream, source) {
    try {
      if (!this.config.azureToken || !this.config.azureRegion) {
        throw new Error('Azure Speech credentials are required');
      }

      if (!SpeechSDK) {
        SpeechSDK = await import('microsoft-cognitiveservices-speech-sdk');
      }

      // Create audio config from media stream
      this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);
      
      // Create speech config
      this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
        this.config.azureToken, 
        this.config.azureRegion
      );
      this.speechConfig.speechRecognitionLanguage = this.config.azureLanguage || 'en-US';

      // Create recognizer
      this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);

      // Set up event handlers
      this.recognizer.recognizing = (s, e) => {
        if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {
          this.callbacks.onInterimResult?.(e.result.text, source);
        }
      };

      this.recognizer.recognized = (s, e) => {
        if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {
          this.callbacks.onFinalResult?.(e.result.text, source);
        }
      };

      this.recognizer.canceled = (s, e) => {
        console.log(`Azure recognition canceled for ${source}: ${e.reason}`);
        if (e.reason === SpeechSDK.CancellationReason.Error) {
          const error = new Error(`Azure Speech error: ${e.errorDetails}`);
          this.callbacks.onError?.(error, source);
        }
        this.stop();
      };

      this.recognizer.sessionStopped = (s, e) => {
        console.log(`Azure session stopped for ${source}`);
        this.callbacks.onStop?.(source);
        this.stop();
      };

      // Start continuous recognition
      await this.recognizer.startContinuousRecognitionAsync();
      this.isActive = true;
      this.callbacks.onStart?.(source);

      return this;
    } catch (error) {
      console.error(`Failed to start Azure Speech for ${source}:`, error);
      this.callbacks.onError?.(error, source);
      throw error;
    }
  }

  async stop() {
    try {
      this.isActive = false;

      if (this.recognizer && typeof this.recognizer.stopContinuousRecognitionAsync === 'function') {
        await this.recognizer.stopContinuousRecognitionAsync();
      }

      if (this.audioConfig && typeof this.audioConfig.close === 'function') {
        this.audioConfig.close();
      }

      this.cleanup();
    } catch (error) {
      console.error('Error stopping Azure Speech:', error);
      throw error;
    }
  }

  cleanup() {
    super.cleanup();
    this.audioConfig = null;
    this.speechConfig = null;
  }
}

/**
 * Factory function to create speech service instances
 */
export function createSpeechService(config, callbacks) {
  const serviceType = config.speechService || 'deepgram';
  
  switch (serviceType) {
    case 'deepgram':
      return new DeepgramSpeechService(config, callbacks);
    case 'azure':
      return new AzureSpeechService(config, callbacks);
    default:
      throw new Error(`Unknown speech service: ${serviceType}`);
  }
}
