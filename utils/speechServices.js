// Dynamic imports for client-side only
let createClient = null;
let SpeechSDK = null;

// Initialize client-side dependencies
if (typeof window !== 'undefined') {
  import('@deepgram/sdk').then(module => {
    createClient = module.createClient;
  });
  import('microsoft-cognitiveservices-speech-sdk').then(module => {
    SpeechSDK = module;
  });
}

/**
 * Abstract base class for speech services
 */
class SpeechService {
  constructor(config, callbacks) {
    this.config = config;
    this.callbacks = callbacks;
    this.isActive = false;
    this.recognizer = null;
  }

  async start(mediaStream, source) {
    throw new Error('start method must be implemented');
  }

  async stop() {
    throw new Error('stop method must be implemented');
  }

  cleanup() {
    // Default cleanup implementation
    if (this.recognizer) {
      this.recognizer = null;
    }
    this.isActive = false;
  }
}

/**
 * Deepgram Speech Service Implementation
 */
export class DeepgramSpeechService extends SpeechService {
  constructor(config, callbacks) {
    super(config, callbacks);
    this.deepgram = null;
    this.connection = null;
    this.mediaRecorder = null;
    this.source = null;
    this.connectionTimeout = null;
    this.transcriptionTimeout = null;
    this.dataInterval = null;
    this.hasReceivedResults = false;
  }

  async start(mediaStream, source) {
    try {
      if (!this.config.deepgramKey) {
        throw new Error('Deepgram API key is required');
      }

      if (!createClient) {
        const { createClient: dgCreateClient } = await import('@deepgram/sdk');
        createClient = dgCreateClient;
      }

      this.source = source;
      this.deepgram = createClient(this.config.deepgramKey);

      // Create WebSocket connection for real-time transcription
      // Use more compatible settings for better transcription results
      this.connection = this.deepgram.listen.live({
        model: this.config.deepgramModel || 'nova-2',
        language: this.config.deepgramLanguage || 'en-US',
        smart_format: true,
        interim_results: true,
        endpointing: 300, // 300ms of silence before finalizing
        utterance_end_ms: 1000,
        vad_events: true,
        encoding: 'webm',
        sample_rate: 48000, // Common sample rate for WebRTC
        channels: 1, // Mono audio
      });

      // Set up event handlers
      this.connection.on('open', () => {
        console.log(`Deepgram connection opened for ${source}`);
        this.isActive = true;

        // Clear any connection timeout
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }

        this.callbacks.onStart?.(source);
      });

      this.connection.on('Results', (data) => {
        console.log(`Deepgram results for ${source}:`, data);
        this.hasReceivedResults = true; // Mark that we've received results

        const transcript = data.channel?.alternatives?.[0]?.transcript;
        if (transcript && transcript.trim()) {
          if (data.is_final) {
            console.log(`Final transcript for ${source}: "${transcript}"`);
            this.callbacks.onFinalResult?.(transcript, source);
          } else {
            console.log(`Interim transcript for ${source}: "${transcript}"`);
            this.callbacks.onInterimResult?.(transcript, source);
          }
        } else {
          // Log when we get results but no transcript
          console.log(`Deepgram results received but no transcript for ${source}:`, {
            hasChannel: !!data.channel,
            hasAlternatives: !!data.channel?.alternatives,
            alternativesLength: data.channel?.alternatives?.length,
            firstAlternative: data.channel?.alternatives?.[0],
            transcript: transcript
          });
        }
      });

      this.connection.on('Metadata', (data) => {
        console.log(`Deepgram metadata for ${source}:`, data);
      });

      this.connection.on('error', (error) => {
        console.error(`Deepgram error for ${source}:`, error);
        this.callbacks.onError?.(error, source);
        this.stop();
      });

      this.connection.on('close', (closeEvent) => {
        console.log(`Deepgram connection closed for ${source}:`, closeEvent);
        this.isActive = false;

        // If connection closes immediately after opening, it might be a configuration issue
        if (closeEvent && closeEvent.code && closeEvent.code !== 1000) {
          console.error(`Deepgram connection closed with error code ${closeEvent.code}: ${closeEvent.reason}`);
          this.callbacks.onError?.(new Error(`Connection closed: ${closeEvent.reason || 'Unknown error'}`), source);
        } else {
          this.callbacks.onStop?.(source);
        }
      });

      this.connection.on('warning', (warning) => {
        console.warn(`Deepgram warning for ${source}:`, warning);
      });

      // Set up MediaRecorder to send audio data to Deepgram
      // Try different mimeTypes for better compatibility
      let mimeType = 'audio/webm;codecs=opus';
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        mimeType = 'audio/webm';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = 'audio/mp4';
          if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = ''; // Use default
          }
        }
      }

      console.log(`Using MediaRecorder mimeType: ${mimeType || 'default'} for ${source}`);

      this.mediaRecorder = new MediaRecorder(mediaStream, mimeType ? { mimeType } : {});

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0 && this.connection && this.isActive) {
          console.log(`Sending audio data to Deepgram: ${event.data.size} bytes for ${source}`);
          this.connection.send(event.data);
        } else if (event.data.size === 0) {
          console.warn(`Empty audio data received for ${source}`);
        }
      };

      this.mediaRecorder.onerror = (error) => {
        console.error(`MediaRecorder error for ${source}:`, error);
        this.callbacks.onError?.(error, source);
      };

      this.mediaRecorder.onstart = () => {
        console.log(`MediaRecorder started for ${source}`);
      };

      this.mediaRecorder.onstop = () => {
        console.log(`MediaRecorder stopped for ${source}`);
        // Clear the data interval when recorder stops
        if (this.dataInterval) {
          clearInterval(this.dataInterval);
          this.dataInterval = null;
        }
        // Don't automatically close connection when MediaRecorder stops
        // The connection should stay open for the duration of the session
      };

      // Start recording and sending data
      // Use continuous recording instead of timed chunks to prevent auto-stopping
      this.mediaRecorder.start(); // Continuous recording

      // Set up interval to request data periodically
      this.dataInterval = setInterval(() => {
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
          this.mediaRecorder.requestData();
        }
      }, 250); // Request data every 250ms

      // Set up connection timeout to detect failed connections
      this.connectionTimeout = setTimeout(() => {
        if (!this.isActive) {
          console.error(`Deepgram connection timeout for ${source}`);
          this.callbacks.onError?.(new Error('Connection timeout - please check your API key and network connection'), source);
          this.stop();
        }
      }, 10000); // 10 second timeout

      // Set up a timeout to check if we're receiving any transcription results
      this.transcriptionTimeout = setTimeout(() => {
        if (this.isActive && !this.hasReceivedResults) {
          console.warn(`No transcription results received from Deepgram for ${source} after 15 seconds. This might indicate an API key issue or audio format problem.`);
          // Don't stop the connection, just warn the user
          this.callbacks.onError?.(new Error('No speech recognition results - please check your Deepgram API key and try speaking clearly'), source);
        }
      }, 15000); // 15 second timeout for transcription results

      return this;
    } catch (error) {
      console.error(`Failed to start Deepgram for ${source}:`, error);
      this.callbacks.onError?.(error, source);
      throw error;
    }
  }

  async stop() {
    try {
      this.isActive = false;

      // Clear data interval
      if (this.dataInterval) {
        clearInterval(this.dataInterval);
        this.dataInterval = null;
      }

      if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
        this.mediaRecorder.stop();
      }

      if (this.connection) {
        this.connection.finish();
        this.connection = null;
      }

      this.cleanup();
    } catch (error) {
      console.error('Error stopping Deepgram:', error);
      throw error;
    }
  }

  cleanup() {
    super.cleanup();
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
    if (this.transcriptionTimeout) {
      clearTimeout(this.transcriptionTimeout);
      this.transcriptionTimeout = null;
    }
    if (this.dataInterval) {
      clearInterval(this.dataInterval);
      this.dataInterval = null;
    }
    this.mediaRecorder = null;
    this.connection = null;
    this.deepgram = null;
    this.source = null;
    this.hasReceivedResults = false;
  }
}

/**
 * Azure Speech Service Implementation (Legacy)
 */
export class AzureSpeechService extends SpeechService {
  constructor(config, callbacks) {
    super(config, callbacks);
    this.audioConfig = null;
    this.speechConfig = null;
  }

  async start(mediaStream, source) {
    try {
      if (!this.config.azureToken || !this.config.azureRegion) {
        throw new Error('Azure Speech credentials are required');
      }

      if (!SpeechSDK) {
        SpeechSDK = await import('microsoft-cognitiveservices-speech-sdk');
      }

      // Create audio config from media stream
      this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);
      
      // Create speech config
      this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(
        this.config.azureToken, 
        this.config.azureRegion
      );
      this.speechConfig.speechRecognitionLanguage = this.config.azureLanguage || 'en-US';

      // Create recognizer
      this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);

      // Set up event handlers
      this.recognizer.recognizing = (s, e) => {
        if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {
          this.callbacks.onInterimResult?.(e.result.text, source);
        }
      };

      this.recognizer.recognized = (s, e) => {
        if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {
          this.callbacks.onFinalResult?.(e.result.text, source);
        }
      };

      this.recognizer.canceled = (s, e) => {
        console.log(`Azure recognition canceled for ${source}: ${e.reason}`);
        if (e.reason === SpeechSDK.CancellationReason.Error) {
          const error = new Error(`Azure Speech error: ${e.errorDetails}`);
          this.callbacks.onError?.(error, source);
        }
        this.stop();
      };

      this.recognizer.sessionStopped = (s, e) => {
        console.log(`Azure session stopped for ${source}`);
        this.callbacks.onStop?.(source);
        this.stop();
      };

      // Start continuous recognition
      await this.recognizer.startContinuousRecognitionAsync();
      this.isActive = true;
      this.callbacks.onStart?.(source);

      return this;
    } catch (error) {
      console.error(`Failed to start Azure Speech for ${source}:`, error);
      this.callbacks.onError?.(error, source);
      throw error;
    }
  }

  async stop() {
    try {
      this.isActive = false;

      if (this.recognizer && typeof this.recognizer.stopContinuousRecognitionAsync === 'function') {
        await this.recognizer.stopContinuousRecognitionAsync();
      }

      if (this.audioConfig && typeof this.audioConfig.close === 'function') {
        this.audioConfig.close();
      }

      this.cleanup();
    } catch (error) {
      console.error('Error stopping Azure Speech:', error);
      throw error;
    }
  }

  cleanup() {
    super.cleanup();
    this.audioConfig = null;
    this.speechConfig = null;
  }
}

/**
 * Factory function to create speech service instances
 */
export function createSpeechService(config, callbacks) {
  const serviceType = config.speechService || 'deepgram';
  
  switch (serviceType) {
    case 'deepgram':
      return new DeepgramSpeechService(config, callbacks);
    case 'azure':
      return new AzureSpeechService(config, callbacks);
    default:
      throw new Error(`Unknown speech service: ${serviceType}`);
  }
}
