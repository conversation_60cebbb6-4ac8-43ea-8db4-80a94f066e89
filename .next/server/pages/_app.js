(()=>{var a={};a.id=636,a.ids=[636],a.modules={5672:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{A:()=>h});var e=c(69198),f=c(10610),g=a([e,f]);[e,f]=g.then?(await g)():g;let h=(0,e.configureStore)({reducer:f.A});d()}catch(a){d(a)}})},7546:a=>{"use strict";a.exports=require("@mui/utils/deepmerge")},8732:a=>{"use strict";a.exports=require("react/jsx-runtime")},10610:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{A:()=>j});var e=c(39756),f=c(64626),g=c(26935),h=c(62390),i=a([e,f,g,h]);[e,f,g,h]=i.then?(await i)():i;let j=(0,e.combineReducers)({transcription:f.Ay,aiResponse:g.Ay,history:h.A});d()}catch(a){d(a)}})},14062:a=>{"use strict";a.exports=import("react-redux")},16581:a=>{"use strict";a.exports=require("@mui/system/spacing")},16670:a=>{"use strict";a.exports=require("@mui/system/styleFunctionSx")},18215:a=>{"use strict";a.exports=require("@mui/system/createStyled")},20985:a=>{"use strict";a.exports=require("@mui/utils/formatMuiErrorMessage")},24509:a=>{"use strict";a.exports=require("@mui/system/InitColorSchemeScript")},26935:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{Ay:()=>j,UM:()=>h});var e=c(69198),f=a([e]);e=(f.then?(await f)():f)[0];let g=(0,e.createSlice)({name:"aiResponse",initialState:"",reducers:{setAIResponse:(a,b)=>b.payload,appendAIResponse:(a,b)=>a+b.payload}}),{setAIResponse:h,appendAIResponse:i}=g.actions,j=g.reducer;d()}catch(a){d(a)}})},29825:a=>{"use strict";a.exports=require("prop-types")},37616:a=>{"use strict";a.exports=require("@mui/system/DefaultPropsProvider")},39756:a=>{"use strict";a.exports=import("redux")},41756:a=>{"use strict";a.exports=require("@mui/system/createBreakpoints")},49964:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(37940);let e=(0,c(67798).createTheme)({palette:{primary:{main:"#4F46E5",light:"#7C3AED",dark:"#3730A3",contrastText:"#ffffff"},secondary:{main:"#6366F1",light:"#8B5CF6",dark:"#4338CA",contrastText:"#ffffff"},error:{main:d.red.A400},background:{default:d.grey[100],paper:"#ffffff"},text:{primary:d.grey[900],secondary:d.grey[700]}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h4:{fontWeight:700,color:"#333333",marginBottom:"0.75em"},h5:{fontWeight:600,color:"#444444",marginBottom:"0.5em"},h6:{fontWeight:600,color:"#555555",marginBottom:"0.5em"},button:{textTransform:"none",fontWeight:500,letterSpacing:"0.5px"},body1:{lineHeight:1.6},caption:{color:d.grey[600]}},shape:{borderRadius:8},components:{MuiPaper:{styleOverrides:{root:{boxShadow:"0px 5px 15px rgba(0,0,0,0.08)"}}},MuiButton:{styleOverrides:{root:{padding:"10px 20px",boxShadow:"none","&:hover":{boxShadow:"0px 2px 8px rgba(0,0,0,0.1)"}},containedPrimary:{"&:hover":{backgroundColor:"#3730A3"}},containedSecondary:{"&:hover":{backgroundColor:"#4338CA"}}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{"& fieldset":{},"&:hover fieldset":{borderColor:"#4F46E5"}},"& .MuiInputLabel-root.Mui-focused":{color:"#4F46E5"}}}},MuiIconButton:{styleOverrides:{root:{"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.06)"}}}},MuiChip:{styleOverrides:{root:{fontWeight:500}}},MuiAppBar:{styleOverrides:{root:{boxShadow:"0px 2px 4px -1px rgba(0,0,0,0.06), 0px 4px 5px 0px rgba(0,0,0,0.04), 0px 1px 10px 0px rgba(0,0,0,0.03)"}}},MuiList:{styleOverrides:{root:{"& .MuiListItem-root":{borderRadius:8}}}},MuiCard:{styleOverrides:{root:{}}}}})},52768:()=>{},57522:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{default:()=>l});var e=c(8732),f=c(14062);c(52768);var g=c(5672),h=c(67798),i=c(37566),j=c(49964),k=a([f,g]);function l({Component:a,pageProps:b}){return(0,e.jsx)(f.Provider,{store:g.A,children:(0,e.jsxs)(h.ThemeProvider,{theme:j.A,children:[(0,e.jsx)(i.default,{}),(0,e.jsx)(a,{...b})]})})}[f,g]=k.then?(await k)():k,d()}catch(a){d(a)}})},59989:a=>{"use strict";a.exports=require("@mui/system/createTheme")},61119:a=>{"use strict";a.exports=require("@mui/utils/generateUtilityClass")},62390:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{A:()=>i,P:()=>h});var e=c(69198),f=a([e]);e=(f.then?(await f)():f)[0];let g=(0,e.createSlice)({name:"history",initialState:[],reducers:{addToHistory:(a,b)=>{a.push(b.payload)}}}),{addToHistory:h}=g.actions,i=g.reducer;d()}catch(a){d(a)}})},64626:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{Ay:()=>j,gs:()=>i,nx:()=>h});var e=c(69198),f=a([e]);e=(f.then?(await f)():f)[0];let g=(0,e.createSlice)({name:"transcription",initialState:"",reducers:{setTranscription:(a,b)=>b.payload,clearTranscription:()=>""}}),{setTranscription:h,clearTranscription:i}=g.actions,j=g.reducer;d()}catch(a){d(a)}})},66099:a=>{"use strict";a.exports=require("@mui/system")},69198:a=>{"use strict";a.exports=import("@reduxjs/toolkit")},77769:a=>{"use strict";a.exports=require("@mui/system/cssVars")},82015:a=>{"use strict";a.exports=require("react")},84781:a=>{"use strict";a.exports=require("@mui/system/colorManipulator")},88698:a=>{"use strict";a.exports=require("@mui/system/useThemeProps")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[672],()=>b(b.s=57522));module.exports=c})();