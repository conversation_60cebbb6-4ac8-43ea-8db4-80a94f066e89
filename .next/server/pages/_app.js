/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ \"react-redux\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../store */ \"(pages-dir-node)/./store.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material/styles */ \"(pages-dir-node)/./node_modules/@mui/material/node/styles/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @mui/material/CssBaseline */ \"(pages-dir-node)/./node_modules/@mui/material/node/CssBaseline/index.js\");\n/* harmony import */ var _theme__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../theme */ \"(pages-dir-node)/./theme.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_redux__WEBPACK_IMPORTED_MODULE_1__, _store__WEBPACK_IMPORTED_MODULE_3__]);\n([react_redux__WEBPACK_IMPORTED_MODULE_1__, _store__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n // Ensure this is imported before theme for overrides to work correctly\n\n\n\n // Import the custom theme\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_1__.Provider, {\n        store: _store__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_styles__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n            theme: _theme__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material_CssBaseline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/_app.js\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/_app.js\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/repos/mycopilot/pages/_app.js\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/repos/mycopilot/pages/_app.js\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVDO0FBQ1QsQ0FBQyx1RUFBdUU7QUFDekU7QUFDcUM7QUFDZDtBQUN2QixDQUFDLDBCQUEwQjtBQUV6QyxTQUFTTSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFO0lBQ2xELHFCQUNFLDhEQUFDUixpREFBUUE7UUFBQ0MsT0FBT0EsOENBQUtBO2tCQUNwQiw0RUFBQ0MsK0RBQWFBO1lBQUNHLE9BQU9BLDhDQUFLQTs7OEJBRXpCLDhEQUFDRCxpRUFBV0E7Ozs7OzhCQUNaLDhEQUFDRztvQkFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7OztBQUloQyIsInNvdXJjZXMiOlsiL1VzZXJzL3N1cmVuZHJhZ2FubmUvcmVwb3MvbXljb3BpbG90L3BhZ2VzL19hcHAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJvdmlkZXIgfSBmcm9tICdyZWFjdC1yZWR1eCc7XG5pbXBvcnQgXCJAL3N0eWxlcy9nbG9iYWxzLmNzc1wiOyAvLyBFbnN1cmUgdGhpcyBpcyBpbXBvcnRlZCBiZWZvcmUgdGhlbWUgZm9yIG92ZXJyaWRlcyB0byB3b3JrIGNvcnJlY3RseVxuaW1wb3J0IHN0b3JlIGZyb20gJy4uL3N0b3JlJztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIsIGNyZWF0ZVRoZW1lIH0gZnJvbSAnQG11aS9tYXRlcmlhbC9zdHlsZXMnO1xuaW1wb3J0IENzc0Jhc2VsaW5lIGZyb20gJ0BtdWkvbWF0ZXJpYWwvQ3NzQmFzZWxpbmUnO1xuaW1wb3J0IHRoZW1lIGZyb20gJy4uL3RoZW1lJzsgLy8gSW1wb3J0IHRoZSBjdXN0b20gdGhlbWVcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gKFxuICAgIDxQcm92aWRlciBzdG9yZT17c3RvcmV9PlxuICAgICAgPFRoZW1lUHJvdmlkZXIgdGhlbWU9e3RoZW1lfT5cbiAgICAgICAgey8qIENzc0Jhc2VsaW5lIGtpY2tzdGFydCBhbiBlbGVnYW50LCBjb25zaXN0ZW50LCBhbmQgc2ltcGxlIGJhc2VsaW5lIHRvIGJ1aWxkIHVwb24uICovfVxuICAgICAgICA8Q3NzQmFzZWxpbmUgLz5cbiAgICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgICAgPC9UaGVtZVByb3ZpZGVyPlxuICAgIDwvUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUHJvdmlkZXIiLCJzdG9yZSIsIlRoZW1lUHJvdmlkZXIiLCJjcmVhdGVUaGVtZSIsIkNzc0Jhc2VsaW5lIiwidGhlbWUiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./reducers.js":
/*!*********************!*\
  !*** ./reducers.js ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux */ \"redux\");\n/* harmony import */ var _redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./redux/transcriptionSlice */ \"(pages-dir-node)/./redux/transcriptionSlice.js\");\n/* harmony import */ var _redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./redux/aiResponseSlice */ \"(pages-dir-node)/./redux/aiResponseSlice.js\");\n/* harmony import */ var _redux_historySlice__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./redux/historySlice */ \"(pages-dir-node)/./redux/historySlice.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([redux__WEBPACK_IMPORTED_MODULE_0__, _redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_1__, _redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_2__, _redux_historySlice__WEBPACK_IMPORTED_MODULE_3__]);\n([redux__WEBPACK_IMPORTED_MODULE_0__, _redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_1__, _redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_2__, _redux_historySlice__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst rootReducer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)({\n    transcription: _redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    aiResponse: _redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    history: _redux_historySlice__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (rootReducer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3JlZHVjZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXdDO0FBQ3NCO0FBQ047QUFDTjtBQUVsRCxNQUFNSSxjQUFjSixzREFBZUEsQ0FBQztJQUNsQ0ssZUFBZUosaUVBQW9CQTtJQUNuQ0ssWUFBWUosOERBQWlCQTtJQUM3QkssU0FBU0osMkRBQWNBO0FBQ3pCO0FBRUEsaUVBQWVDLFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zdXJlbmRyYWdhbm5lL3JlcG9zL215Y29waWxvdC9yZWR1Y2Vycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb21iaW5lUmVkdWNlcnMgfSBmcm9tICdyZWR1eCc7XG5pbXBvcnQgdHJhbnNjcmlwdGlvblJlZHVjZXIgZnJvbSAnLi9yZWR1eC90cmFuc2NyaXB0aW9uU2xpY2UnO1xuaW1wb3J0IGFpUmVzcG9uc2VSZWR1Y2VyIGZyb20gJy4vcmVkdXgvYWlSZXNwb25zZVNsaWNlJztcbmltcG9ydCBoaXN0b3J5UmVkdWNlciBmcm9tICcuL3JlZHV4L2hpc3RvcnlTbGljZSc7XG5cbmNvbnN0IHJvb3RSZWR1Y2VyID0gY29tYmluZVJlZHVjZXJzKHtcbiAgdHJhbnNjcmlwdGlvbjogdHJhbnNjcmlwdGlvblJlZHVjZXIsXG4gIGFpUmVzcG9uc2U6IGFpUmVzcG9uc2VSZWR1Y2VyLFxuICBoaXN0b3J5OiBoaXN0b3J5UmVkdWNlcixcbn0pO1xuXG5leHBvcnQgZGVmYXVsdCByb290UmVkdWNlcjtcbiJdLCJuYW1lcyI6WyJjb21iaW5lUmVkdWNlcnMiLCJ0cmFuc2NyaXB0aW9uUmVkdWNlciIsImFpUmVzcG9uc2VSZWR1Y2VyIiwiaGlzdG9yeVJlZHVjZXIiLCJyb290UmVkdWNlciIsInRyYW5zY3JpcHRpb24iLCJhaVJlc3BvbnNlIiwiaGlzdG9yeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./reducers.js\n");

/***/ }),

/***/ "(pages-dir-node)/./redux/aiResponseSlice.js":
/*!**********************************!*\
  !*** ./redux/aiResponseSlice.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiResponseSlice: () => (/* binding */ aiResponseSlice),\n/* harmony export */   appendAIResponse: () => (/* binding */ appendAIResponse),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   setAIResponse: () => (/* binding */ setAIResponse)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"@reduxjs/toolkit\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__]);\n_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst aiResponseSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: 'aiResponse',\n    initialState: '',\n    reducers: {\n        setAIResponse: (state, action)=>action.payload,\n        appendAIResponse: (state, action)=>state + action.payload\n    }\n});\nconst { setAIResponse, appendAIResponse } = aiResponseSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (aiResponseSlice.reducer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3JlZHV4L2FpUmVzcG9uc2VTbGljZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUErQztBQUV4QyxNQUFNQyxrQkFBa0JELDZEQUFXQSxDQUFDO0lBQ3pDRSxNQUFNO0lBQ05DLGNBQWM7SUFDZEMsVUFBVTtRQUNSQyxlQUFlLENBQUNDLE9BQU9DLFNBQVdBLE9BQU9DLE9BQU87UUFDaERDLGtCQUFrQixDQUFDSCxPQUFPQyxTQUFXRCxRQUFRQyxPQUFPQyxPQUFPO0lBQzdEO0FBQ0YsR0FBRztBQUVJLE1BQU0sRUFBRUgsYUFBYSxFQUFFSSxnQkFBZ0IsRUFBRSxHQUFHUixnQkFBZ0JTLE9BQU8sQ0FBQztBQUMzRSxpRUFBZVQsZ0JBQWdCVSxPQUFPLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zdXJlbmRyYWdhbm5lL3JlcG9zL215Y29waWxvdC9yZWR1eC9haVJlc3BvbnNlU2xpY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU2xpY2UgfSBmcm9tICdAcmVkdXhqcy90b29sa2l0JztcblxuZXhwb3J0IGNvbnN0IGFpUmVzcG9uc2VTbGljZSA9IGNyZWF0ZVNsaWNlKHtcbiAgbmFtZTogJ2FpUmVzcG9uc2UnLFxuICBpbml0aWFsU3RhdGU6ICcnLFxuICByZWR1Y2Vyczoge1xuICAgIHNldEFJUmVzcG9uc2U6IChzdGF0ZSwgYWN0aW9uKSA9PiBhY3Rpb24ucGF5bG9hZCxcbiAgICBhcHBlbmRBSVJlc3BvbnNlOiAoc3RhdGUsIGFjdGlvbikgPT4gc3RhdGUgKyBhY3Rpb24ucGF5bG9hZCxcbiAgfSxcbn0pO1xuXG5leHBvcnQgY29uc3QgeyBzZXRBSVJlc3BvbnNlLCBhcHBlbmRBSVJlc3BvbnNlIH0gPSBhaVJlc3BvbnNlU2xpY2UuYWN0aW9ucztcbmV4cG9ydCBkZWZhdWx0IGFpUmVzcG9uc2VTbGljZS5yZWR1Y2VyOyJdLCJuYW1lcyI6WyJjcmVhdGVTbGljZSIsImFpUmVzcG9uc2VTbGljZSIsIm5hbWUiLCJpbml0aWFsU3RhdGUiLCJyZWR1Y2VycyIsInNldEFJUmVzcG9uc2UiLCJzdGF0ZSIsImFjdGlvbiIsInBheWxvYWQiLCJhcHBlbmRBSVJlc3BvbnNlIiwiYWN0aW9ucyIsInJlZHVjZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./redux/aiResponseSlice.js\n");

/***/ }),

/***/ "(pages-dir-node)/./redux/historySlice.js":
/*!*******************************!*\
  !*** ./redux/historySlice.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToHistory: () => (/* binding */ addToHistory),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"@reduxjs/toolkit\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__]);\n_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst historySlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: 'history',\n    initialState: [],\n    reducers: {\n        addToHistory: (state, action)=>{\n            state.push(action.payload);\n        }\n    }\n});\nconst { addToHistory } = historySlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (historySlice.reducer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3JlZHV4L2hpc3RvcnlTbGljZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBK0M7QUFFL0MsTUFBTUMsZUFBZUQsNkRBQVdBLENBQUM7SUFDL0JFLE1BQU07SUFDTkMsY0FBYyxFQUFFO0lBQ2hCQyxVQUFVO1FBQ1JDLGNBQWMsQ0FBQ0MsT0FBT0M7WUFDcEJELE1BQU1FLElBQUksQ0FBQ0QsT0FBT0UsT0FBTztRQUMzQjtJQUNGO0FBQ0Y7QUFFTyxNQUFNLEVBQUVKLFlBQVksRUFBRSxHQUFHSixhQUFhUyxPQUFPLENBQUM7QUFDckQsaUVBQWVULGFBQWFVLE9BQU8sRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL3N1cmVuZHJhZ2FubmUvcmVwb3MvbXljb3BpbG90L3JlZHV4L2hpc3RvcnlTbGljZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVTbGljZSB9IGZyb20gJ0ByZWR1eGpzL3Rvb2xraXQnO1xuXG5jb25zdCBoaXN0b3J5U2xpY2UgPSBjcmVhdGVTbGljZSh7XG4gIG5hbWU6ICdoaXN0b3J5JyxcbiAgaW5pdGlhbFN0YXRlOiBbXSxcbiAgcmVkdWNlcnM6IHtcbiAgICBhZGRUb0hpc3Rvcnk6IChzdGF0ZSwgYWN0aW9uKSA9PiB7XG4gICAgICBzdGF0ZS5wdXNoKGFjdGlvbi5wYXlsb2FkKTtcbiAgICB9LFxuICB9LFxufSk7XG5cbmV4cG9ydCBjb25zdCB7IGFkZFRvSGlzdG9yeSB9ID0gaGlzdG9yeVNsaWNlLmFjdGlvbnM7XG5leHBvcnQgZGVmYXVsdCBoaXN0b3J5U2xpY2UucmVkdWNlcjtcbiJdLCJuYW1lcyI6WyJjcmVhdGVTbGljZSIsImhpc3RvcnlTbGljZSIsIm5hbWUiLCJpbml0aWFsU3RhdGUiLCJyZWR1Y2VycyIsImFkZFRvSGlzdG9yeSIsInN0YXRlIiwiYWN0aW9uIiwicHVzaCIsInBheWxvYWQiLCJhY3Rpb25zIiwicmVkdWNlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./redux/historySlice.js\n");

/***/ }),

/***/ "(pages-dir-node)/./redux/transcriptionSlice.js":
/*!*************************************!*\
  !*** ./redux/transcriptionSlice.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearTranscription: () => (/* binding */ clearTranscription),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   setTranscription: () => (/* binding */ setTranscription)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"@reduxjs/toolkit\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__]);\n_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst transcriptionSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: 'transcription',\n    initialState: '',\n    reducers: {\n        setTranscription: (state, action)=>action.payload,\n        clearTranscription: ()=>''\n    }\n});\nconst { setTranscription, clearTranscription } = transcriptionSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (transcriptionSlice.reducer);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3JlZHV4L3RyYW5zY3JpcHRpb25TbGljZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQStDO0FBRS9DLE1BQU1DLHFCQUFxQkQsNkRBQVdBLENBQUM7SUFDckNFLE1BQU07SUFDTkMsY0FBYztJQUNkQyxVQUFVO1FBQ1JDLGtCQUFrQixDQUFDQyxPQUFPQyxTQUFXQSxPQUFPQyxPQUFPO1FBQ25EQyxvQkFBb0IsSUFBTTtJQUM1QjtBQUNGO0FBRU8sTUFBTSxFQUFFSixnQkFBZ0IsRUFBRUksa0JBQWtCLEVBQUUsR0FBR1IsbUJBQW1CUyxPQUFPLENBQUM7QUFDbkYsaUVBQWVULG1CQUFtQlUsT0FBTyxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvc3VyZW5kcmFnYW5uZS9yZXBvcy9teWNvcGlsb3QvcmVkdXgvdHJhbnNjcmlwdGlvblNsaWNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVNsaWNlIH0gZnJvbSAnQHJlZHV4anMvdG9vbGtpdCc7XG5cbmNvbnN0IHRyYW5zY3JpcHRpb25TbGljZSA9IGNyZWF0ZVNsaWNlKHtcbiAgbmFtZTogJ3RyYW5zY3JpcHRpb24nLFxuICBpbml0aWFsU3RhdGU6ICcnLFxuICByZWR1Y2Vyczoge1xuICAgIHNldFRyYW5zY3JpcHRpb246IChzdGF0ZSwgYWN0aW9uKSA9PiBhY3Rpb24ucGF5bG9hZCxcbiAgICBjbGVhclRyYW5zY3JpcHRpb246ICgpID0+ICcnXG4gIH0sXG59KTtcblxuZXhwb3J0IGNvbnN0IHsgc2V0VHJhbnNjcmlwdGlvbiwgY2xlYXJUcmFuc2NyaXB0aW9uIH0gPSB0cmFuc2NyaXB0aW9uU2xpY2UuYWN0aW9ucztcbmV4cG9ydCBkZWZhdWx0IHRyYW5zY3JpcHRpb25TbGljZS5yZWR1Y2VyO1xuIl0sIm5hbWVzIjpbImNyZWF0ZVNsaWNlIiwidHJhbnNjcmlwdGlvblNsaWNlIiwibmFtZSIsImluaXRpYWxTdGF0ZSIsInJlZHVjZXJzIiwic2V0VHJhbnNjcmlwdGlvbiIsInN0YXRlIiwiYWN0aW9uIiwicGF5bG9hZCIsImNsZWFyVHJhbnNjcmlwdGlvbiIsImFjdGlvbnMiLCJyZWR1Y2VyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./redux/transcriptionSlice.js\n");

/***/ }),

/***/ "(pages-dir-node)/./store.js":
/*!******************!*\
  !*** ./store.js ***!
  \******************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"@reduxjs/toolkit\");\n/* harmony import */ var _reducers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reducers */ \"(pages-dir-node)/./reducers.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__, _reducers__WEBPACK_IMPORTED_MODULE_1__]);\n([_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__, _reducers__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.configureStore)({\n    reducer: _reducers__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (store);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3N0b3JlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFrRDtBQUNiO0FBRXJDLE1BQU1FLFFBQVFGLGdFQUFjQSxDQUFDO0lBQzNCRyxTQUFTRixpREFBV0E7QUFDdEI7QUFFQSxpRUFBZUMsS0FBS0EsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL3N1cmVuZHJhZ2FubmUvcmVwb3MvbXljb3BpbG90L3N0b3JlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbmZpZ3VyZVN0b3JlIH0gZnJvbSAnQHJlZHV4anMvdG9vbGtpdCc7XG5pbXBvcnQgcm9vdFJlZHVjZXIgZnJvbSAnLi9yZWR1Y2Vycyc7XG5cbmNvbnN0IHN0b3JlID0gY29uZmlndXJlU3RvcmUoe1xuICByZWR1Y2VyOiByb290UmVkdWNlcixcbn0pO1xuXG5leHBvcnQgZGVmYXVsdCBzdG9yZTtcbiJdLCJuYW1lcyI6WyJjb25maWd1cmVTdG9yZSIsInJvb3RSZWR1Y2VyIiwic3RvcmUiLCJyZWR1Y2VyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./store.js\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./theme.js":
/*!******************!*\
  !*** ./theme.js ***!
  \******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _mui_material_colors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @mui/material/colors */ \"(pages-dir-node)/./node_modules/@mui/material/node/colors/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/material/styles */ \"(pages-dir-node)/./node_modules/@mui/material/node/styles/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__);\n\n\n// Create a theme instance.\nconst theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_0__.createTheme)({\n    palette: {\n        primary: {\n            main: '#4F46E5',\n            light: '#7C3AED',\n            dark: '#3730A3',\n            contrastText: '#ffffff'\n        },\n        secondary: {\n            main: '#6366F1',\n            light: '#8B5CF6',\n            dark: '#4338CA',\n            contrastText: '#ffffff'\n        },\n        error: {\n            main: _mui_material_colors__WEBPACK_IMPORTED_MODULE_1__.red.A400\n        },\n        background: {\n            default: _mui_material_colors__WEBPACK_IMPORTED_MODULE_1__.grey[100],\n            paper: '#ffffff'\n        },\n        text: {\n            primary: _mui_material_colors__WEBPACK_IMPORTED_MODULE_1__.grey[900],\n            secondary: _mui_material_colors__WEBPACK_IMPORTED_MODULE_1__.grey[700]\n        }\n    },\n    typography: {\n        fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n        h4: {\n            fontWeight: 700,\n            color: '#333333',\n            marginBottom: '0.75em'\n        },\n        h5: {\n            fontWeight: 600,\n            color: '#444444',\n            marginBottom: '0.5em'\n        },\n        h6: {\n            fontWeight: 600,\n            color: '#555555',\n            marginBottom: '0.5em'\n        },\n        button: {\n            textTransform: 'none',\n            fontWeight: 500,\n            letterSpacing: '0.5px'\n        },\n        body1: {\n            lineHeight: 1.6\n        },\n        caption: {\n            color: _mui_material_colors__WEBPACK_IMPORTED_MODULE_1__.grey[600]\n        }\n    },\n    shape: {\n        borderRadius: 8\n    },\n    components: {\n        MuiPaper: {\n            styleOverrides: {\n                root: {\n                    // borderRadius: 12, // Slightly more rounded corners for paper\n                    boxShadow: '0px 5px 15px rgba(0,0,0,0.08)'\n                }\n            }\n        },\n        MuiButton: {\n            styleOverrides: {\n                root: {\n                    // borderRadius: 8, // Consistent rounded corners for buttons\n                    padding: '10px 20px',\n                    boxShadow: 'none',\n                    '&:hover': {\n                        boxShadow: '0px 2px 8px rgba(0,0,0,0.1)'\n                    }\n                },\n                containedPrimary: {\n                    '&:hover': {\n                        backgroundColor: '#3730A3'\n                    }\n                },\n                containedSecondary: {\n                    '&:hover': {\n                        backgroundColor: '#4338CA'\n                    }\n                }\n            }\n        },\n        MuiTextField: {\n            styleOverrides: {\n                root: {\n                    '& .MuiOutlinedInput-root': {\n                        // borderRadius: 8, // Rounded corners for text fields\n                        '& fieldset': {\n                        },\n                        '&:hover fieldset': {\n                            borderColor: '#4F46E5'\n                        }\n                    },\n                    '& .MuiInputLabel-root.Mui-focused': {\n                        color: '#4F46E5'\n                    }\n                }\n            }\n        },\n        MuiIconButton: {\n            styleOverrides: {\n                root: {\n                    '&:hover': {\n                        backgroundColor: 'rgba(0, 0, 0, 0.06)' // Standard hover for icon buttons\n                    }\n                }\n            }\n        },\n        MuiChip: {\n            styleOverrides: {\n                root: {\n                    // borderRadius: 16, // More rounded chips\n                    fontWeight: 500\n                }\n            }\n        },\n        MuiAppBar: {\n            styleOverrides: {\n                root: {\n                    boxShadow: '0px 2px 4px -1px rgba(0,0,0,0.06), 0px 4px 5px 0px rgba(0,0,0,0.04), 0px 1px 10px 0px rgba(0,0,0,0.03)'\n                }\n            }\n        },\n        MuiList: {\n            styleOverrides: {\n                root: {\n                    '& .MuiListItem-root': {\n                        borderRadius: 8\n                    }\n                }\n            }\n        },\n        MuiCard: {\n            styleOverrides: {\n                root: {\n                }\n            }\n        }\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (theme);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3RoZW1lLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUQ7QUFDRTtBQUVuRCwyQkFBMkI7QUFDM0IsTUFBTUcsUUFBUUQsaUVBQVdBLENBQUM7SUFDeEJFLFNBQVM7UUFDUEMsU0FBUztZQUNQQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxjQUFjO1FBQ2hCO1FBQ0FDLFdBQVc7WUFDVEosTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsY0FBYztRQUNoQjtRQUNBRSxPQUFPO1lBQ0xMLE1BQU1MLHFEQUFHQSxDQUFDVyxJQUFJO1FBQ2hCO1FBQ0FDLFlBQVk7WUFDVkMsU0FBU2Qsc0RBQUksQ0FBQyxJQUFJO1lBQ2xCZSxPQUFPO1FBQ1Q7UUFDQUMsTUFBTTtZQUNKWCxTQUFTTCxzREFBSSxDQUFDLElBQUk7WUFDbEJVLFdBQVdWLHNEQUFJLENBQUMsSUFBSTtRQUN0QjtJQUNGO0lBQ0FpQixZQUFZO1FBQ1ZDLFlBQVk7UUFDWkMsSUFBSTtZQUNGQyxZQUFZO1lBQ1pDLE9BQU87WUFDUEMsY0FBYztRQUNoQjtRQUNBQyxJQUFJO1lBQ0ZILFlBQVk7WUFDWkMsT0FBTztZQUNQQyxjQUFjO1FBQ2hCO1FBQ0FFLElBQUk7WUFDRkosWUFBWTtZQUNaQyxPQUFPO1lBQ1BDLGNBQWM7UUFDaEI7UUFDQUcsUUFBUTtZQUNOQyxlQUFlO1lBQ2ZOLFlBQVk7WUFDWk8sZUFBZTtRQUNqQjtRQUNBQyxPQUFPO1lBQ0xDLFlBQVk7UUFDZDtRQUNBQyxTQUFTO1lBQ1BULE9BQU9yQixzREFBSSxDQUFDLElBQUk7UUFDbEI7SUFDRjtJQUNBK0IsT0FBTztRQUNMQyxjQUFjO0lBQ2hCO0lBQ0FDLFlBQVk7UUFDVkMsVUFBVTtZQUNSQyxnQkFBZ0I7Z0JBQ2RDLE1BQU07b0JBQ0osK0RBQStEO29CQUMvREMsV0FBVztnQkFDYjtZQUNGO1FBQ0Y7UUFDQUMsV0FBVztZQUNUSCxnQkFBZ0I7Z0JBQ2RDLE1BQU07b0JBQ0osNkRBQTZEO29CQUM3REcsU0FBUztvQkFDVEYsV0FBVztvQkFDWCxXQUFXO3dCQUNUQSxXQUFXO29CQUNiO2dCQUNGO2dCQUNBRyxrQkFBa0I7b0JBQ2hCLFdBQVc7d0JBQ1RDLGlCQUFpQjtvQkFDbkI7Z0JBQ0Y7Z0JBQ0FDLG9CQUFvQjtvQkFDbEIsV0FBVzt3QkFDVEQsaUJBQWlCO29CQUNuQjtnQkFDRjtZQUNGO1FBQ0Y7UUFDQUUsY0FBYztZQUNaUixnQkFBZ0I7Z0JBQ2RDLE1BQU07b0JBQ0osNEJBQTRCO3dCQUMxQixzREFBc0Q7d0JBQ3RELGNBQWM7d0JBRWQ7d0JBQ0Esb0JBQW9COzRCQUNsQlEsYUFBYTt3QkFDZjtvQkFJRjtvQkFDQSxxQ0FBcUM7d0JBQ25DdkIsT0FBTztvQkFDVDtnQkFDRjtZQUNGO1FBQ0Y7UUFDQXdCLGVBQWU7WUFDYlYsZ0JBQWdCO2dCQUNkQyxNQUFNO29CQUNKLFdBQVc7d0JBQ1RLLGlCQUFpQixzQkFBc0Isa0NBQWtDO29CQUMzRTtnQkFDRjtZQUNGO1FBQ0Y7UUFDQUssU0FBUztZQUNQWCxnQkFBZ0I7Z0JBQ2RDLE1BQU07b0JBQ0osMENBQTBDO29CQUMxQ2hCLFlBQVk7Z0JBQ2Q7WUFDRjtRQUNGO1FBQ0EyQixXQUFXO1lBQ1BaLGdCQUFnQjtnQkFDWkMsTUFBTTtvQkFDRkMsV0FBVztnQkFDZjtZQUNKO1FBQ0o7UUFDQVcsU0FBUztZQUNMYixnQkFBZ0I7Z0JBQ1pDLE1BQU07b0JBQ0YsdUJBQXVCO3dCQUNuQkosY0FBYztvQkFDbEI7Z0JBQ0o7WUFDSjtRQUNKO1FBQ0FpQixTQUFTO1lBQ0xkLGdCQUFnQjtnQkFDWkMsTUFBTTtnQkFHTjtZQUNKO1FBQ0o7SUFDRjtBQUNGO0FBRUEsaUVBQWVqQyxLQUFLQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvc3VyZW5kcmFnYW5uZS9yZXBvcy9teWNvcGlsb3QvdGhlbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ3JleSwgcmVkIH0gZnJvbSAnQG11aS9tYXRlcmlhbC9jb2xvcnMnO1xuaW1wb3J0IHsgY3JlYXRlVGhlbWUgfSBmcm9tICdAbXVpL21hdGVyaWFsL3N0eWxlcyc7XG5cbi8vIENyZWF0ZSBhIHRoZW1lIGluc3RhbmNlLlxuY29uc3QgdGhlbWUgPSBjcmVhdGVUaGVtZSh7XG4gIHBhbGV0dGU6IHtcbiAgICBwcmltYXJ5OiB7XG4gICAgICBtYWluOiAnIzRGNDZFNScsIC8vIEluZGlnbyBwcmltYXJ5IGNvbG9yXG4gICAgICBsaWdodDogJyM3QzNBRUQnLCAvLyBMaWdodGVyIGluZGlnb1xuICAgICAgZGFyazogJyMzNzMwQTMnLCAvLyBEYXJrZXIgaW5kaWdvXG4gICAgICBjb250cmFzdFRleHQ6ICcjZmZmZmZmJyxcbiAgICB9LFxuICAgIHNlY29uZGFyeToge1xuICAgICAgbWFpbjogJyM2MzY2RjEnLCAvLyBJbmRpZ28gc2Vjb25kYXJ5IGNvbG9yIChzbGlnaHRseSBsaWdodGVyIHRoYW4gcHJpbWFyeSlcbiAgICAgIGxpZ2h0OiAnIzhCNUNGNicsIC8vIExpZ2h0IHB1cnBsZS1pbmRpZ29cbiAgICAgIGRhcms6ICcjNDMzOENBJywgLy8gRGFyayBpbmRpZ29cbiAgICAgIGNvbnRyYXN0VGV4dDogJyNmZmZmZmYnLFxuICAgIH0sXG4gICAgZXJyb3I6IHtcbiAgICAgIG1haW46IHJlZC5BNDAwLFxuICAgIH0sXG4gICAgYmFja2dyb3VuZDoge1xuICAgICAgZGVmYXVsdDogZ3JleVsxMDBdLCAvLyBBIHZlcnkgbGlnaHQgZ3JleSBmb3IgdGhlIGFwcCBiYWNrZ3JvdW5kXG4gICAgICBwYXBlcjogJyNmZmZmZmYnLCAgIC8vIFdoaXRlIGZvciBwYXBlciBlbGVtZW50c1xuICAgIH0sXG4gICAgdGV4dDoge1xuICAgICAgcHJpbWFyeTogZ3JleVs5MDBdLCAvLyBEYXJrIGdyZXkgZm9yIHByaW1hcnkgdGV4dFxuICAgICAgc2Vjb25kYXJ5OiBncmV5WzcwMF0sIC8vIE1lZGl1bSBncmV5IGZvciBzZWNvbmRhcnkgdGV4dFxuICAgIH1cbiAgfSxcbiAgdHlwb2dyYXBoeToge1xuICAgIGZvbnRGYW1pbHk6ICdcIlJvYm90b1wiLCBcIkhlbHZldGljYVwiLCBcIkFyaWFsXCIsIHNhbnMtc2VyaWYnLFxuICAgIGg0OiB7XG4gICAgICBmb250V2VpZ2h0OiA3MDAsXG4gICAgICBjb2xvcjogJyMzMzMzMzMnLCAvLyBEYXJrZXIgY29sb3IgZm9yIG1haW4gaGVhZGluZ3NcbiAgICAgIG1hcmdpbkJvdHRvbTogJzAuNzVlbScsXG4gICAgfSxcbiAgICBoNToge1xuICAgICAgZm9udFdlaWdodDogNjAwLFxuICAgICAgY29sb3I6ICcjNDQ0NDQ0JyxcbiAgICAgIG1hcmdpbkJvdHRvbTogJzAuNWVtJyxcbiAgICB9LFxuICAgIGg2OiB7XG4gICAgICBmb250V2VpZ2h0OiA2MDAsXG4gICAgICBjb2xvcjogJyM1NTU1NTUnLCAvLyBTbGlnaHRseSBsaWdodGVyIGZvciBzdWJoZWFkaW5nc1xuICAgICAgbWFyZ2luQm90dG9tOiAnMC41ZW0nLFxuICAgIH0sXG4gICAgYnV0dG9uOiB7XG4gICAgICB0ZXh0VHJhbnNmb3JtOiAnbm9uZScsIC8vIEtlZXAgYnV0dG9uIHRleHQgY2FzZSBhcyBpc1xuICAgICAgZm9udFdlaWdodDogNTAwLFxuICAgICAgbGV0dGVyU3BhY2luZzogJzAuNXB4JyxcbiAgICB9LFxuICAgIGJvZHkxOiB7XG4gICAgICBsaW5lSGVpZ2h0OiAxLjYsXG4gICAgfSxcbiAgICBjYXB0aW9uOiB7XG4gICAgICBjb2xvcjogZ3JleVs2MDBdLFxuICAgIH1cbiAgfSxcbiAgc2hhcGU6IHtcbiAgICBib3JkZXJSYWRpdXM6IDgsIC8vIERlZmF1bHQgYm9yZGVyIHJhZGl1cyBmb3IgY29tcG9uZW50c1xuICB9LFxuICBjb21wb25lbnRzOiB7XG4gICAgTXVpUGFwZXI6IHtcbiAgICAgIHN0eWxlT3ZlcnJpZGVzOiB7XG4gICAgICAgIHJvb3Q6IHtcbiAgICAgICAgICAvLyBib3JkZXJSYWRpdXM6IDEyLCAvLyBTbGlnaHRseSBtb3JlIHJvdW5kZWQgY29ybmVycyBmb3IgcGFwZXJcbiAgICAgICAgICBib3hTaGFkb3c6ICcwcHggNXB4IDE1cHggcmdiYSgwLDAsMCwwLjA4KScsIC8vIFNvZnRlciwgbW9yZSBtb2Rlcm4gc2hhZG93XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9LFxuICAgIE11aUJ1dHRvbjoge1xuICAgICAgc3R5bGVPdmVycmlkZXM6IHtcbiAgICAgICAgcm9vdDoge1xuICAgICAgICAgIC8vIGJvcmRlclJhZGl1czogOCwgLy8gQ29uc2lzdGVudCByb3VuZGVkIGNvcm5lcnMgZm9yIGJ1dHRvbnNcbiAgICAgICAgICBwYWRkaW5nOiAnMTBweCAyMHB4JywgLy8gTW9yZSBnZW5lcm91cyBwYWRkaW5nXG4gICAgICAgICAgYm94U2hhZG93OiAnbm9uZScsIC8vIFJlbW92ZSBkZWZhdWx0IGJ1dHRvbiBzaGFkb3cgZm9yIGEgZmxhdHRlciBsb29rLCBjYW4gYWRkIG9uIGhvdmVyXG4gICAgICAgICAgJyY6aG92ZXInOiB7XG4gICAgICAgICAgICBib3hTaGFkb3c6ICcwcHggMnB4IDhweCByZ2JhKDAsMCwwLDAuMSknLCAvLyBTdWJ0bGUgc2hhZG93IG9uIGhvdmVyXG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBjb250YWluZWRQcmltYXJ5OiB7XG4gICAgICAgICAgJyY6aG92ZXInOiB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMzczMEEzJywgLy8gRGFya2VuIHByaW1hcnkgb24gaG92ZXJcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIGNvbnRhaW5lZFNlY29uZGFyeToge1xuICAgICAgICAgICcmOmhvdmVyJzoge1xuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnIzQzMzhDQScsIC8vIERhcmtlbiBzZWNvbmRhcnkgb24gaG92ZXJcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICB9XG4gICAgfSxcbiAgICBNdWlUZXh0RmllbGQ6IHtcbiAgICAgIHN0eWxlT3ZlcnJpZGVzOiB7XG4gICAgICAgIHJvb3Q6IHtcbiAgICAgICAgICAnJiAuTXVpT3V0bGluZWRJbnB1dC1yb290Jzoge1xuICAgICAgICAgICAgLy8gYm9yZGVyUmFkaXVzOiA4LCAvLyBSb3VuZGVkIGNvcm5lcnMgZm9yIHRleHQgZmllbGRzXG4gICAgICAgICAgICAnJiBmaWVsZHNldCc6IHtcbiAgICAgICAgICAgICAgLy8gYm9yZGVyQ29sb3I6ICdyZ2JhKDAsIDAsIDAsIDAuMjMpJyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAnJjpob3ZlciBmaWVsZHNldCc6IHtcbiAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjNEY0NkU1JywgLy8gUHJpbWFyeSBjb2xvciBib3JkZXIgb24gaG92ZXJcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAvLyAnJi5NdWktZm9jdXNlZCBmaWVsZHNldCc6IHtcbiAgICAgICAgICAgIC8vICAgYm9yZGVyQ29sb3I6ICcjNEY0NkU1JywgLy8gUHJpbWFyeSBjb2xvciBib3JkZXIgd2hlbiBmb2N1c2VkXG4gICAgICAgICAgICAvLyB9LFxuICAgICAgICAgIH0sXG4gICAgICAgICAgJyYgLk11aUlucHV0TGFiZWwtcm9vdC5NdWktZm9jdXNlZCc6IHtcbiAgICAgICAgICAgIGNvbG9yOiAnIzRGNDZFNScsIC8vIFByaW1hcnkgY29sb3IgZm9yIGxhYmVsIHdoZW4gZm9jdXNlZFxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0sXG4gICAgTXVpSWNvbkJ1dHRvbjoge1xuICAgICAgc3R5bGVPdmVycmlkZXM6IHtcbiAgICAgICAgcm9vdDoge1xuICAgICAgICAgICcmOmhvdmVyJzoge1xuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgwLCAwLCAwLCAwLjA2KScgLy8gU3RhbmRhcmQgaG92ZXIgZm9yIGljb24gYnV0dG9uc1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0sXG4gICAgTXVpQ2hpcDoge1xuICAgICAgc3R5bGVPdmVycmlkZXM6IHtcbiAgICAgICAgcm9vdDoge1xuICAgICAgICAgIC8vIGJvcmRlclJhZGl1czogMTYsIC8vIE1vcmUgcm91bmRlZCBjaGlwc1xuICAgICAgICAgIGZvbnRXZWlnaHQ6IDUwMCxcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0sXG4gICAgTXVpQXBwQmFyOiB7XG4gICAgICAgIHN0eWxlT3ZlcnJpZGVzOiB7XG4gICAgICAgICAgICByb290OiB7XG4gICAgICAgICAgICAgICAgYm94U2hhZG93OiAnMHB4IDJweCA0cHggLTFweCByZ2JhKDAsMCwwLDAuMDYpLCAwcHggNHB4IDVweCAwcHggcmdiYSgwLDAsMCwwLjA0KSwgMHB4IDFweCAxMHB4IDBweCByZ2JhKDAsMCwwLDAuMDMpJywgLy8gU29mdGVyIGFwcCBiYXIgc2hhZG93XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9LFxuICAgIE11aUxpc3Q6IHtcbiAgICAgICAgc3R5bGVPdmVycmlkZXM6IHtcbiAgICAgICAgICAgIHJvb3Q6IHtcbiAgICAgICAgICAgICAgICAnJiAuTXVpTGlzdEl0ZW0tcm9vdCc6IHtcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiA4LCAvLyBSb3VuZGVkIGxpc3QgaXRlbXMgaWYgdGhleSBhcmUgaW50ZXJhY3RpdmVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9LFxuICAgIE11aUNhcmQ6IHtcbiAgICAgICAgc3R5bGVPdmVycmlkZXM6IHtcbiAgICAgICAgICAgIHJvb3Q6IHtcbiAgICAgICAgICAgICAgICAvLyBib3JkZXJSYWRpdXM6IDEyLFxuICAgICAgICAgICAgICAgIC8vIGJveFNoYWRvdzogJzBweCA1cHggMTVweCByZ2JhKDAsMCwwLDAuMDgpJyxcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgfVxufSk7XG5cbmV4cG9ydCBkZWZhdWx0IHRoZW1lOyJdLCJuYW1lcyI6WyJncmV5IiwicmVkIiwiY3JlYXRlVGhlbWUiLCJ0aGVtZSIsInBhbGV0dGUiLCJwcmltYXJ5IiwibWFpbiIsImxpZ2h0IiwiZGFyayIsImNvbnRyYXN0VGV4dCIsInNlY29uZGFyeSIsImVycm9yIiwiQTQwMCIsImJhY2tncm91bmQiLCJkZWZhdWx0IiwicGFwZXIiLCJ0ZXh0IiwidHlwb2dyYXBoeSIsImZvbnRGYW1pbHkiLCJoNCIsImZvbnRXZWlnaHQiLCJjb2xvciIsIm1hcmdpbkJvdHRvbSIsImg1IiwiaDYiLCJidXR0b24iLCJ0ZXh0VHJhbnNmb3JtIiwibGV0dGVyU3BhY2luZyIsImJvZHkxIiwibGluZUhlaWdodCIsImNhcHRpb24iLCJzaGFwZSIsImJvcmRlclJhZGl1cyIsImNvbXBvbmVudHMiLCJNdWlQYXBlciIsInN0eWxlT3ZlcnJpZGVzIiwicm9vdCIsImJveFNoYWRvdyIsIk11aUJ1dHRvbiIsInBhZGRpbmciLCJjb250YWluZWRQcmltYXJ5IiwiYmFja2dyb3VuZENvbG9yIiwiY29udGFpbmVkU2Vjb25kYXJ5IiwiTXVpVGV4dEZpZWxkIiwiYm9yZGVyQ29sb3IiLCJNdWlJY29uQnV0dG9uIiwiTXVpQ2hpcCIsIk11aUFwcEJhciIsIk11aUxpc3QiLCJNdWlDYXJkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./theme.js\n");

/***/ }),

/***/ "@mui/system":
/*!******************************!*\
  !*** external "@mui/system" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system");

/***/ }),

/***/ "@mui/system/DefaultPropsProvider":
/*!***************************************************!*\
  !*** external "@mui/system/DefaultPropsProvider" ***!
  \***************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system/DefaultPropsProvider");

/***/ }),

/***/ "@mui/system/InitColorSchemeScript":
/*!****************************************************!*\
  !*** external "@mui/system/InitColorSchemeScript" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system/InitColorSchemeScript");

/***/ }),

/***/ "@mui/system/colorManipulator":
/*!***********************************************!*\
  !*** external "@mui/system/colorManipulator" ***!
  \***********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system/colorManipulator");

/***/ }),

/***/ "@mui/system/createBreakpoints":
/*!************************************************!*\
  !*** external "@mui/system/createBreakpoints" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system/createBreakpoints");

/***/ }),

/***/ "@mui/system/createStyled":
/*!*******************************************!*\
  !*** external "@mui/system/createStyled" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system/createStyled");

/***/ }),

/***/ "@mui/system/createTheme":
/*!******************************************!*\
  !*** external "@mui/system/createTheme" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system/createTheme");

/***/ }),

/***/ "@mui/system/cssVars":
/*!**************************************!*\
  !*** external "@mui/system/cssVars" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system/cssVars");

/***/ }),

/***/ "@mui/system/spacing":
/*!**************************************!*\
  !*** external "@mui/system/spacing" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system/spacing");

/***/ }),

/***/ "@mui/system/styleFunctionSx":
/*!**********************************************!*\
  !*** external "@mui/system/styleFunctionSx" ***!
  \**********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system/styleFunctionSx");

/***/ }),

/***/ "@mui/system/useThemeProps":
/*!********************************************!*\
  !*** external "@mui/system/useThemeProps" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/system/useThemeProps");

/***/ }),

/***/ "@mui/utils/deepmerge":
/*!***************************************!*\
  !*** external "@mui/utils/deepmerge" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/utils/deepmerge");

/***/ }),

/***/ "@mui/utils/formatMuiErrorMessage":
/*!***************************************************!*\
  !*** external "@mui/utils/formatMuiErrorMessage" ***!
  \***************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/utils/formatMuiErrorMessage");

/***/ }),

/***/ "@mui/utils/generateUtilityClass":
/*!**************************************************!*\
  !*** external "@mui/utils/generateUtilityClass" ***!
  \**************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/utils/generateUtilityClass");

/***/ }),

/***/ "@reduxjs/toolkit":
/*!***********************************!*\
  !*** external "@reduxjs/toolkit" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = import("@reduxjs/toolkit");;

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-redux":
/*!******************************!*\
  !*** external "react-redux" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-redux");;

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "redux":
/*!************************!*\
  !*** external "redux" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = import("redux");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@mui","vendor-chunks/@babel"], () => (__webpack_exec__("(pages-dir-node)/./pages/_app.js")));
module.exports = __webpack_exports__;

})();