(()=>{var a={};a.id=731,a.ids=[220,636,731],a.modules={5672:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{A:()=>h});var e=c(69198),f=c(10610),g=a([e,f]);[e,f]=g.then?(await g)():g;let h=(0,e.configureStore)({reducer:f.A});d()}catch(a){d(a)}})},7546:a=>{"use strict";a.exports=require("@mui/utils/deepmerge")},8732:a=>{"use strict";a.exports=require("react/jsx-runtime")},10610:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{A:()=>j});var e=c(39756),f=c(64626),g=c(26935),h=c(62390),i=a([e,f,g,h]);[e,f,g,h]=i.then?(await i)():i;let j=(0,e.combineReducers)({transcription:f.Ay,aiResponse:g.Ay,history:h.A});d()}catch(a){d(a)}})},14062:a=>{"use strict";a.exports=import("react-redux")},15952:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/remove-trailing-slash")},16581:a=>{"use strict";a.exports=require("@mui/system/spacing")},16670:a=>{"use strict";a.exports=require("@mui/system/styleFunctionSx")},18215:a=>{"use strict";a.exports=require("@mui/system/createStyled")},20985:a=>{"use strict";a.exports=require("@mui/utils/formatMuiErrorMessage")},21145:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return p},defaultHead:function(){return l}});let d=c(87020),e=c(3147),f=c(8732),g=e._(c(82015)),h=d._(c(88160)),i=c(57043),j=c(1523),k=c(40609);function l(a){void 0===a&&(a=!1);let b=[(0,f.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,f.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function m(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===g.default.Fragment?a.concat(g.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}c(61025);let n=["name","httpEquiv","charSet","itemProp"];function o(a,b){let{inAmpMode:c}=b;return a.reduce(m,[]).reverse().concat(l(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=n.length;a<b;a++){let b=n[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let c=a.key||b;return g.default.cloneElement(a,{key:c})})}let p=function(a){let{children:b}=a,c=(0,g.useContext)(i.AmpStateContext),d=(0,g.useContext)(j.HeadManagerContext);return(0,f.jsx)(h.default,{reduceComponentsToState:o,headManager:d,inAmpMode:(0,k.isInAmpMode)(c),children:b})};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},23103:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/add-path-prefix")},24509:a=>{"use strict";a.exports=require("@mui/system/InitColorSchemeScript")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},26935:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{Ay:()=>j,UM:()=>h});var e=c(69198),f=a([e]);e=(f.then?(await f)():f)[0];let g=(0,e.createSlice)({name:"aiResponse",initialState:"",reducers:{setAIResponse:(a,b)=>b.payload,appendAIResponse:(a,b)=>a+b.payload}}),{setAIResponse:h,appendAIResponse:i}=g.actions,j=g.reducer;d()}catch(a){d(a)}})},29825:a=>{"use strict";a.exports=require("prop-types")},33873:a=>{"use strict";a.exports=require("path")},37616:a=>{"use strict";a.exports=require("@mui/system/DefaultPropsProvider")},39756:a=>{"use strict";a.exports=import("redux")},40361:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},40609:(a,b)=>{"use strict";function c(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isInAmpMode",{enumerable:!0,get:function(){return c}})},41322:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/format-url")},41756:a=>{"use strict";a.exports=require("@mui/system/createBreakpoints")},49964:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(37940);let e=(0,c(67798).createTheme)({palette:{primary:{main:"#4F46E5",light:"#7C3AED",dark:"#3730A3",contrastText:"#ffffff"},secondary:{main:"#6366F1",light:"#8B5CF6",dark:"#4338CA",contrastText:"#ffffff"},error:{main:d.red.A400},background:{default:d.grey[100],paper:"#ffffff"},text:{primary:d.grey[900],secondary:d.grey[700]}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h4:{fontWeight:700,color:"#333333",marginBottom:"0.75em"},h5:{fontWeight:600,color:"#444444",marginBottom:"0.5em"},h6:{fontWeight:600,color:"#555555",marginBottom:"0.5em"},button:{textTransform:"none",fontWeight:500,letterSpacing:"0.5px"},body1:{lineHeight:1.6},caption:{color:d.grey[600]}},shape:{borderRadius:8},components:{MuiPaper:{styleOverrides:{root:{boxShadow:"0px 5px 15px rgba(0,0,0,0.08)"}}},MuiButton:{styleOverrides:{root:{padding:"10px 20px",boxShadow:"none","&:hover":{boxShadow:"0px 2px 8px rgba(0,0,0,0.1)"}},containedPrimary:{"&:hover":{backgroundColor:"#3730A3"}},containedSecondary:{"&:hover":{backgroundColor:"#4338CA"}}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{"& fieldset":{},"&:hover fieldset":{borderColor:"#4F46E5"}},"& .MuiInputLabel-root.Mui-focused":{color:"#4F46E5"}}}},MuiIconButton:{styleOverrides:{root:{"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.06)"}}}},MuiChip:{styleOverrides:{root:{fontWeight:500}}},MuiAppBar:{styleOverrides:{root:{boxShadow:"0px 2px 4px -1px rgba(0,0,0,0.06), 0px 4px 5px 0px rgba(0,0,0,0.04), 0px 1px 10px 0px rgba(0,0,0,0.03)"}}},MuiList:{styleOverrides:{root:{"& .MuiListItem-root":{borderRadius:8}}}},MuiCard:{styleOverrides:{root:{}}}}})},52768:()=>{},57043:(a,b,c)=>{"use strict";a.exports=c(63885).vendored.contexts.AmpContext},57063:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{config:()=>L,default:()=>H,getServerSideProps:()=>K,getStaticPaths:()=>J,getStaticProps:()=>I,handler:()=>G,reportWebVitals:()=>M,routeModule:()=>S,unstable_getServerProps:()=>Q,unstable_getServerSideProps:()=>R,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>O,unstable_getStaticProps:()=>N});var e=c(63885),f=c(80237),g=c(70772),h=c(12410),i=c(41322),j=c(35124),k=c(88647),l=c(83709),m=c(57909),n=c(55122),o=c(81413),p=c(89674),q=c(57522),r=c(85379),s=c.n(r),t=c(56755),u=c(80156),v=c(30012),w=c(32072),x=c(18164),y=c(64971),z=c(78737),A=c(86439),B=c(25735),C=c(26713),D=c(23103),E=c(15952),F=a([q]);q=(F.then?(await F)():F)[0];let H=(0,o.M)(r,"default"),I=(0,o.M)(r,"getStaticProps"),J=(0,o.M)(r,"getStaticPaths"),K=(0,o.M)(r,"getServerSideProps"),L=(0,o.M)(r,"config"),M=(0,o.M)(r,"reportWebVitals"),N=(0,o.M)(r,"unstable_getStaticProps"),O=(0,o.M)(r,"unstable_getStaticPaths"),P=(0,o.M)(r,"unstable_getStaticParams"),Q=(0,o.M)(r,"unstable_getServerProps"),R=(0,o.M)(r,"unstable_getServerSideProps"),S=new e.PagesRouteModule({definition:{kind:f.RouteKind.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},distDir:".next",projectDir:"",components:{App:q.default,Document:p.default},userland:r});async function G(a,b,c){var d,e;let o="/_error";"/index"===o&&(o="/");let p="false",q=await S.prepare(a,b,{srcPage:o,multiZoneDraftMode:p});if(!q){b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve());return}let{buildId:F,query:G,params:H,parsedUrl:M,originalQuery:N,originalPathname:O,buildManifest:P,nextFontManifest:Q,serverFilesManifest:R,reactLoadableManifest:T,prerenderManifest:U,isDraftMode:V,isOnDemandRevalidate:W,revalidateOnlyGenerated:X,locale:Y,locales:Z,defaultLocale:$,routerServerContext:_,nextConfig:aa,resolvedPathname:ab}=q,ac=null==R||null==(e=R.config)||null==(d=e.experimental)?void 0:d.isExperimentalCompile,ad=!!K,ae=!!I,af=!!J,ag=!!(s()||r).getInitialProps,ah=G.amp&&L.amp,ai=null,aj=!1,ak=q.isNextDataRequest&&(ae||ad),al="/404"===o,am="/500"===o,an="/_error"===o;if(S.isDev||V||!ae||(ai=`${Y?`/${Y}`:""}${("/"===o||"/"===ab)&&Y?"":ab}${ah?".amp":""}`,(al||am||an)&&(ai=`${Y?`/${Y}`:""}${o}${ah?".amp":""}`),ai="/index"===ai?"/":ai),af&&!V){let a=(0,E.removeTrailingSlash)(Y?(0,D.addPathPrefix)(ab,`/${Y}`):ab),b=!!U.routes[a]||U.notFoundRoutes.includes(a),c=U.dynamicRoutes[o];if(c){if(!1===c.fallback&&!b)throw new A.NoFallbackError;"string"!=typeof c.fallback||b||ak||(aj=!0)}}(aj&&(0,C.isBot)(a.headers["user-agent"]||"")||(0,j.getRequestMeta)(a,"minimalMode"))&&(aj=!1);let ao=(0,h.getTracer)(),ap=ao.getActiveScopeSpan();try{let d=a.method||"GET",e=(0,i.formatUrl)({pathname:aa.trailingSlash?M.pathname:(0,E.removeTrailingSlash)(M.pathname||"/"),query:ae?{}:N}),q=(null==_?void 0:_.publicRuntimeConfig)||aa.publicRuntimeConfig,s=async h=>{var s,A;let C,D=async({previousCacheEntry:s})=>{var t;let u=async()=>{try{var c,f,t;return await S.render(a,b,{query:ae&&!ac?{...H,...ah?{amp:G.amp}:{}}:{...G,...H},params:H,page:o,renderContext:{isDraftMode:V,isFallback:aj,developmentNotFoundSourcePage:(0,j.getRequestMeta)(a,"developmentNotFoundSourcePage")},sharedContext:{buildId:F,customServer:!!(null==_?void 0:_.isCustomServer)||void 0,deploymentId:!1},renderOpts:{params:H,routeModule:S,page:o,pageConfig:L||{},Component:(0,k.T)(r),ComponentMod:r,getStaticProps:I,getStaticPaths:J,getServerSideProps:K,supportsDynamicResponse:!ae,buildManifest:P,nextFontManifest:Q,reactLoadableManifest:T,assetPrefix:aa.assetPrefix,strictNextHead:aa.experimental.strictNextHead??!0,previewProps:U.preview,images:aa.images,nextConfigOutput:aa.output,optimizeCss:!!aa.experimental.optimizeCss,nextScriptWorkers:!!aa.experimental.nextScriptWorkers,domainLocales:null==(c=aa.i18n)?void 0:c.domains,crossOrigin:aa.crossOrigin,multiZoneDraftMode:p,basePath:aa.basePath,canonicalBase:aa.amp.canonicalBase||"",ampOptimizerConfig:null==(f=aa.experimental.amp)?void 0:f.optimizer,disableOptimizedLoading:aa.experimental.disableOptimizedLoading,largePageDataBytes:aa.experimental.largePageDataBytes,runtimeConfig:Object.keys(q).length>0?q:void 0,isExperimentalCompile:ac,experimental:{clientTraceMetadata:aa.experimental.clientTraceMetadata||[]},locale:Y,locales:Z,defaultLocale:$,setIsrStatus:null==_?void 0:_.setIsrStatus,isNextDataRequest:ak&&(ad||ae),resolvedUrl:e,resolvedAsPath:ad||ag?(0,i.formatUrl)({pathname:ak?(0,m.normalizeDataPath)(O):O,query:N}):e,isOnDemandRevalidate:W,ErrorDebug:(0,j.getRequestMeta)(a,"PagesErrorDebug"),err:(0,j.getRequestMeta)(a,"invokeError"),dev:S.isDev,distDir:`${S.projectDir}/${S.distDir}`,ampSkipValidation:null==(t=aa.experimental.amp)?void 0:t.skipValidation,ampValidator:(0,j.getRequestMeta)(a,"ampValidator")}}).then(a=>{let{metadata:b}=a,c=b.cacheControl;return"isNotFound"in b&&b.isNotFound?{value:null,cacheControl:c}:b.isRedirect?{value:{kind:n.CachedRouteKind.REDIRECT,props:b.pageData??b.flightData},cacheControl:c}:{value:{kind:n.CachedRouteKind.PAGES,html:a,pageData:a.metadata.pageData,headers:a.metadata.headers,status:a.metadata.statusCode},cacheControl:c}}).finally(()=>{if(!h)return;h.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let c=ao.getRootSpanAttributes();if(!c)return;if(c.get("next.span_type")!==g.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${c.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=c.get("next.route");if(e){let a=`${d} ${e}`;h.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),h.updateName(a)}else h.updateName(`${d} ${a.url}`)})}catch(b){throw(null==s?void 0:s.isStale)&&await S.onRequestError(a,b,{routerKind:"Pages Router",routePath:o,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:W})},_),b}};if(s&&(aj=!1),aj){let b=await S.getResponseCache(a).get(S.isDev?null:Y?`/${Y}${o}`:o,async({previousCacheEntry:a=null})=>S.isDev?u():(0,z.toResponseCacheEntry)(a),{routeKind:f.RouteKind.PAGES,isFallback:!0,isRoutePPREnabled:!1,isOnDemandRevalidate:!1,incrementalCache:await S.getIncrementalCache(a,aa,U),waitUntil:c.waitUntil});if(b)return delete b.cacheControl,b.isMiss=!0,b}return!(0,j.getRequestMeta)(a,"minimalMode")&&W&&X&&!s?(b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null):aj&&(null==s||null==(t=s.value)?void 0:t.kind)===n.CachedRouteKind.PAGES?{value:{kind:n.CachedRouteKind.PAGES,html:new y.default(Buffer.from(s.value.html),{contentType:"text/html;utf-8",metadata:{statusCode:s.value.status,headers:s.value.headers}}),pageData:{},status:s.value.status,headers:s.value.headers},cacheControl:{revalidate:0,expire:void 0}}:u()},E=await S.handleResponse({cacheKey:ai,req:a,nextConfig:aa,routeKind:f.RouteKind.PAGES,isOnDemandRevalidate:W,revalidateOnlyGenerated:X,waitUntil:c.waitUntil,responseGenerator:D,prerenderManifest:U});if(!aj||(null==E?void 0:E.isMiss)||(aj=!1),E){if(ae&&!(0,j.getRequestMeta)(a,"minimalMode")&&b.setHeader("x-nextjs-cache",W?"REVALIDATED":E.isMiss?"MISS":E.isStale?"STALE":"HIT"),!ae||aj)b.getHeader("Cache-Control")||(C={revalidate:0,expire:void 0});else if(al){let b=(0,j.getRequestMeta)(a,"notFoundRevalidate");C={revalidate:void 0===b?0:b,expire:void 0}}else if(am)C={revalidate:0,expire:void 0};else if(E.cacheControl)if("number"==typeof E.cacheControl.revalidate){if(E.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${E.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});C={revalidate:E.cacheControl.revalidate,expire:(null==(s=E.cacheControl)?void 0:s.expire)??aa.expireTime}}else C={revalidate:w.CACHE_ONE_YEAR,expire:void 0};if(C&&!b.getHeader("Cache-Control")&&b.setHeader("Cache-Control",(0,t.getCacheControlHeader)(C)),!E.value)return((0,j.addRequestMeta)(a,"notFoundRevalidate",null==(A=E.cacheControl)?void 0:A.revalidate),b.statusCode=404,ak)?void b.end('{"notFound":true}'):void((null==_?void 0:_.render404)?await _.render404(a,b,M,!1):b.end("This page could not be found"));if(E.value.kind===n.CachedRouteKind.REDIRECT)if(!ak)return await (a=>{let c={destination:a.pageProps.__N_REDIRECT,statusCode:a.pageProps.__N_REDIRECT_STATUS,basePath:a.pageProps.__N_REDIRECT_BASE_PATH},d=(0,v.getRedirectStatus)(c),{basePath:e}=aa;e&&!1!==c.basePath&&c.destination.startsWith("/")&&(c.destination=`${e}${c.destination}`),c.destination.startsWith("/")&&(c.destination=(0,u.normalizeRepeatedSlashes)(c.destination)),b.statusCode=d,b.setHeader("Location",c.destination),d===B.RedirectStatusCode.PermanentRedirect&&b.setHeader("Refresh",`0;url=${c.destination}`),b.end(c.destination)})(E.value.props),null;else{b.setHeader("content-type","application/json"),b.end(JSON.stringify(E.value.props));return}if(E.value.kind!==n.CachedRouteKind.PAGES)throw Object.defineProperty(Error("Invariant: received non-pages cache entry in pages handler"),"__NEXT_ERROR_CODE",{value:"E695",enumerable:!1,configurable:!0});if(S.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),V&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),(0,j.getRequestMeta)(a,"customErrorRender")||an&&(0,j.getRequestMeta)(a,"minimalMode")&&500===b.statusCode)return null;await (0,x.sendRenderResult)({req:a,res:b,result:!ak||an||am?E.value.html:new y.default(Buffer.from(JSON.stringify(E.value.pageData)),{contentType:"application/json",metadata:E.value.html.metadata}),generateEtags:aa.generateEtags,poweredByHeader:aa.poweredByHeader,cacheControl:S.isDev?void 0:C,type:ak?"json":"html"})}};ap?await s():await ao.withPropagatedContext(a.headers,()=>ao.trace(g.BaseServerSpan.handleRequest,{spanName:`${d} ${a.url}`,kind:h.SpanKind.SERVER,attributes:{"http.method":d,"http.target":a.url}},s))}catch(b){throw b instanceof A.NoFallbackError||await S.onRequestError(a,b,{routerKind:"Pages Router",routePath:o,routeType:"render",revalidateReason:(0,l.c)({isRevalidate:ae,isOnDemandRevalidate:W})},_),b}}d()}catch(a){d(a)}})},57522:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{default:()=>l});var e=c(8732),f=c(14062);c(52768);var g=c(5672),h=c(67798),i=c(37566),j=c(49964),k=a([f,g]);function l({Component:a,pageProps:b}){return(0,e.jsx)(f.Provider,{store:g.A,children:(0,e.jsxs)(h.ThemeProvider,{theme:j.A,children:[(0,e.jsx)(i.default,{}),(0,e.jsx)(a,{...b})]})})}[f,g]=k.then?(await k)():k,d()}catch(a){d(a)}})},57909:a=>{"use strict";a.exports=require("next/dist/shared/lib/page-path/normalize-data-path")},59989:a=>{"use strict";a.exports=require("@mui/system/createTheme")},61119:a=>{"use strict";a.exports=require("@mui/utils/generateUtilityClass")},62390:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{A:()=>i,P:()=>h});var e=c(69198),f=a([e]);e=(f.then?(await f)():f)[0];let g=(0,e.createSlice)({name:"history",initialState:[],reducers:{addToHistory:(a,b)=>{a.push(b.payload)}}}),{addToHistory:h}=g.actions,i=g.reducer;d()}catch(a){d(a)}})},64626:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{Ay:()=>j,gs:()=>i,nx:()=>h});var e=c(69198),f=a([e]);e=(f.then?(await f)():f)[0];let g=(0,e.createSlice)({name:"transcription",initialState:"",reducers:{setTranscription:(a,b)=>b.payload,clearTranscription:()=>""}}),{setTranscription:h,clearTranscription:i}=g.actions,j=g.reducer;d()}catch(a){d(a)}})},66099:a=>{"use strict";a.exports=require("@mui/system")},69198:a=>{"use strict";a.exports=import("@reduxjs/toolkit")},77769:a=>{"use strict";a.exports=require("@mui/system/cssVars")},80156:a=>{"use strict";a.exports=require("next/dist/shared/lib/utils")},82015:a=>{"use strict";a.exports=require("react")},84781:a=>{"use strict";a.exports=require("@mui/system/colorManipulator")},85379:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return k}});let d=c(87020),e=c(8732),f=d._(c(82015)),g=d._(c(21145)),h={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function i(a){let b,{req:d,res:e,err:f}=a,g=e&&e.statusCode?e.statusCode:f?f.statusCode:404;if(d){let{getRequestMeta:a}=c(35124),e=a(d,"initURL");e&&(b=new URL(e).hostname)}return{statusCode:g,hostname:b}}let j={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class k extends f.default.Component{render(){let{statusCode:a,withDarkMode:b=!0}=this.props,c=this.props.title||h[a]||"An unexpected error has occurred";return(0,e.jsxs)("div",{style:j.error,children:[(0,e.jsx)(g.default,{children:(0,e.jsx)("title",{children:a?a+": "+c:"Application error: a client-side exception has occurred"})}),(0,e.jsxs)("div",{style:j.desc,children:[(0,e.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(b?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),a?(0,e.jsx)("h1",{className:"next-error-h1",style:j.h1,children:a}):null,(0,e.jsx)("div",{style:j.wrap,children:(0,e.jsxs)("h2",{style:j.h2,children:[this.props.title||a?c:(0,e.jsxs)(e.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,e.jsxs)(e.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}k.displayName="ErrorPage",k.getInitialProps=i,k.origGetInitialProps=i,("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88160:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return f}});let d=c(82015),e=()=>{};function f(a){var b;let{headManager:c,reduceComponentsToState:f}=a;function g(){if(c&&c.mountedInstances){let b=d.Children.toArray(Array.from(c.mountedInstances).filter(Boolean));c.updateHead(f(b,a))}}return null==c||null==(b=c.mountedInstances)||b.add(a.children),g(),e(()=>{var b;return null==c||null==(b=c.mountedInstances)||b.add(a.children),()=>{var b;null==c||null==(b=c.mountedInstances)||b.delete(a.children)}}),e(()=>(c&&(c._pendingUpdate=g),()=>{c&&(c._pendingUpdate=g)})),null}},88698:a=>{"use strict";a.exports=require("@mui/system/useThemeProps")},89674:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(8732),e=c(82341);function f(){return(0,d.jsxs)(e.Html,{lang:"en",children:[(0,d.jsx)(e.Head,{}),(0,d.jsxs)("body",{children:[(0,d.jsx)(e.Main,{}),(0,d.jsx)(e.NextScript,{})]})]})}}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[341,672,412],()=>b(b.s=57063));module.exports=c})();