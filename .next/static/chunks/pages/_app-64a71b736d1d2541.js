(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{126:(e,t,r)=>{"use strict";r.d(t,{X4:()=>f,e$:()=>p,tL:()=>g,eM:()=>u,YL:()=>s,a:()=>m,Cg:()=>d,Me:()=>a,Nd:()=>h,Y9:()=>b,j4:()=>y});var n=r(69135);function o(e,t=0,r=1){return function(e,t=Number.MIN_SAFE_INTEGER,r=Number.MAX_SAFE_INTEGER){return Math.max(t,Math.min(e,r))}(e,t,r)}function i(e){let t;if(e.type)return e;if("#"===e.charAt(0))return i(function(e){e=e.slice(1);let t=RegExp(`.{1,${e.length>=6?2:1}}`,"g"),r=e.match(t);return r&&1===r[0].length&&(r=r.map(e=>e+e)),r?`rgb${4===r.length?"a":""}(${r.map((e,t)=>t<3?parseInt(e,16):Math.round(parseInt(e,16)/255*1e3)/1e3).join(", ")})`:""}(e));let r=e.indexOf("("),o=e.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(o))throw Error((0,n.A)(9,e));let a=e.substring(r+1,e.length-1);if("color"===o){if(t=(a=a.split(" ")).shift(),4===a.length&&"/"===a[3].charAt(0)&&(a[3]=a[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(t))throw Error((0,n.A)(10,t))}else a=a.split(",");return{type:o,values:a=a.map(e=>parseFloat(e)),colorSpace:t}}let a=(e,t)=>{try{return(e=>{let t=i(e);return t.values.slice(0,3).map((e,r)=>t.type.includes("hsl")&&0!==r?`${e}%`:e).join(" ")})(e)}catch(t){return e}};function l(e){let{type:t,colorSpace:r}=e,{values:n}=e;return t.includes("rgb")?n=n.map((e,t)=>t<3?parseInt(e,10):e):t.includes("hsl")&&(n[1]=`${n[1]}%`,n[2]=`${n[2]}%`),n=t.includes("color")?`${r} ${n.join(" ")}`:`${n.join(", ")}`,`${t}(${n})`}function s(e){let{values:t}=e=i(e),r=t[0],n=t[1]/100,o=t[2]/100,a=n*Math.min(o,1-o),s=(e,t=(e+r/30)%12)=>o-a*Math.max(Math.min(t-3,9-t,1),-1),c="rgb",u=[Math.round(255*s(0)),Math.round(255*s(8)),Math.round(255*s(4))];return"hsla"===e.type&&(c+="a",u.push(t[3])),l({type:c,values:u})}function c(e){let t="hsl"===(e=i(e)).type||"hsla"===e.type?i(s(e)).values:e.values;return Number((.2126*(t=t.map(t=>("color"!==e.type&&(t/=255),t<=.03928?t/12.92:((t+.055)/1.055)**2.4)))[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function u(e,t){let r=c(e),n=c(t);return(Math.max(r,n)+.05)/(Math.min(r,n)+.05)}function f(e,t){return e=i(e),t=o(t),("rgb"===e.type||"hsl"===e.type)&&(e.type+="a"),"color"===e.type?e.values[3]=`/${t}`:e.values[3]=t,l(e)}function d(e,t,r){try{return f(e,t)}catch(t){return e}}function p(e,t){if(e=i(e),t=o(t),e.type.includes("hsl"))e.values[2]*=1-t;else if(e.type.includes("rgb")||e.type.includes("color"))for(let r=0;r<3;r+=1)e.values[r]*=1-t;return l(e)}function h(e,t,r){try{return p(e,t)}catch(t){return e}}function m(e,t){if(e=i(e),t=o(t),e.type.includes("hsl"))e.values[2]+=(100-e.values[2])*t;else if(e.type.includes("rgb"))for(let r=0;r<3;r+=1)e.values[r]+=(255-e.values[r])*t;else if(e.type.includes("color"))for(let r=0;r<3;r+=1)e.values[r]+=(1-e.values[r])*t;return l(e)}function y(e,t,r){try{return m(e,t)}catch(t){return e}}function g(e,t=.15){return c(e)>.5?p(e,t):m(e,t)}function b(e,t,r){try{return g(e,t)}catch(t){return e}}},581:(e,t,r)=>{"use strict";r.d(t,{A:()=>function e(t,r){let n={...r};for(let o in t)if(Object.prototype.hasOwnProperty.call(t,o))if("components"===o||"slots"===o)n[o]={...t[o],...n[o]};else if("componentsProps"===o||"slotProps"===o){let i=t[o],a=r[o];if(a)if(i)for(let t in n[o]={...a},i)Object.prototype.hasOwnProperty.call(i,t)&&(n[o][t]=e(i[t],a[t]));else n[o]=a;else n[o]=i||{}}else void 0===n[o]&&(n[o]=t[o]);return n}})},743:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"}},903:(e,t,r)=>{"use strict";function n(e,t){if(!e.containerQueries)return t;let r=Object.keys(t).filter(e=>e.startsWith("@container")).sort((e,t)=>{let r=/min-width:\s*([0-9.]+)/;return(e.match(r)?.[1]||0)-(t.match(r)?.[1]||0)});return r.length?r.reduce((e,r)=>{let n=t[r];return delete e[r],e[r]=n,e},{...t}):t}function o(e,t){return"@"===t||t.startsWith("@")&&(e.some(e=>t.startsWith(`@${e}`))||!!t.match(/^@\d/))}function i(e,t){let r=t.match(/^@([^/]+)?\/?(.+)?$/);if(!r)return null;let[,n,o]=r,i=Number.isNaN(+n)?n||0:+n;return e.containerQueries(o).up(i)}function a(e){let t=(e,t)=>e.replace("@media",t?`@container ${t}`:"@container");function r(r,n){r.up=(...r)=>t(e.breakpoints.up(...r),n),r.down=(...r)=>t(e.breakpoints.down(...r),n),r.between=(...r)=>t(e.breakpoints.between(...r),n),r.only=(...r)=>t(e.breakpoints.only(...r),n),r.not=(...r)=>{let o=t(e.breakpoints.not(...r),n);return o.includes("not all and")?o.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):o}}let n={},o=e=>(r(n,e),n);return r(o),{...e,containerQueries:o}}r.d(t,{Ay:()=>a,CT:()=>i,_S:()=>n,ob:()=>o})},3637:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n="$$material"},3904:(e,t,r)=>{"use strict";r.d(t,{A:()=>H});var n=function(){function e(e){var t=this;this._insertTag=function(e){var r;r=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,r),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(((t=document.createElement("style")).setAttribute("data-emotion",this.key),void 0!==this.nonce&&t.setAttribute("nonce",this.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t));var t,r=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(r);try{n.insertRule(e,n.cssRules.length)}catch(e){}}else r.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach(function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),this.tags=[],this.ctr=0},e}(),o=Math.abs,i=String.fromCharCode,a=Object.assign;function l(e,t,r){return e.replace(t,r)}function s(e,t){return e.indexOf(t)}function c(e,t){return 0|e.charCodeAt(t)}function u(e,t,r){return e.slice(t,r)}function f(e){return e.length}function d(e,t){return t.push(e),e}var p=1,h=1,m=0,y=0,g=0,b="";function v(e,t,r,n,o,i,a){return{value:e,root:t,parent:r,type:n,props:o,children:i,line:p,column:h,length:a,return:""}}function S(e,t){return a(v("",null,null,"",null,null,0),e,{length:-e.length},t)}function A(){return g=y<m?c(b,y++):0,h++,10===g&&(h=1,p++),g}function w(){return c(b,y)}function x(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function k(e){return p=h=1,m=f(b=e),y=0,[]}function C(e){var t,r;return(t=y-1,r=function e(t){for(;A();)switch(g){case t:return y;case 34:case 39:34!==t&&39!==t&&e(g);break;case 40:41===t&&e(t);break;case 92:A()}return y}(91===e?e+2:40===e?e+1:e),u(b,t,r)).trim()}var _="-ms-",$="-moz-",O="-webkit-",j="comm",E="rule",T="decl",M="@keyframes";function P(e,t){for(var r="",n=e.length,o=0;o<n;o++)r+=t(e[o],o,e,t)||"";return r}function N(e,t,r,n){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case T:return e.return=e.return||e.value;case j:return"";case M:return e.return=e.value+"{"+P(e.children,n)+"}";case E:e.value=e.props.join(",")}return f(r=P(e.children,n))?e.return=e.value+"{"+r+"}":""}function B(e,t,r,n,i,a,s,c,f,d,p){for(var h=i-1,m=0===i?a:[""],y=m.length,g=0,b=0,S=0;g<n;++g)for(var A=0,w=u(e,h+1,h=o(b=s[g])),x=e;A<y;++A)(x=(b>0?m[A]+" "+w:l(w,/&\f/g,m[A])).trim())&&(f[S++]=x);return v(e,t,r,0===i?E:c,f,d,p)}function I(e,t,r,n){return v(e,t,r,T,u(e,0,n),u(e,n+1,-1),n)}var R=function(e,t,r){for(var n=0,o=0;n=o,o=w(),38===n&&12===o&&(t[r]=1),!x(o);)A();return u(b,e,y)},L=function(e,t){var r=-1,n=44;do switch(x(n)){case 0:38===n&&12===w()&&(t[r]=1),e[r]+=R(y-1,t,r);break;case 2:e[r]+=C(n);break;case 4:if(44===n){e[++r]=58===w()?"&\f":"",t[r]=e[r].length;break}default:e[r]+=i(n)}while(n=A());return e},F=function(e,t){var r;return r=L(k(e),t),b="",r},D=new WeakMap,W=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,r=e.parent,n=e.column===r.column&&e.line===r.line;"rule"!==r.type;)if(!(r=r.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||D.get(r))&&!n){D.set(e,!0);for(var o=[],i=F(t,o),a=r.props,l=0,s=0;l<i.length;l++)for(var c=0;c<a.length;c++,s++)e.props[s]=o[l]?i[l].replace(/&\f/g,a[c]):a[c]+" "+i[l]}}},z=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}},K=[function(e,t,r,n){if(e.length>-1&&!e.return)switch(e.type){case T:e.return=function e(t,r){switch(45^c(t,0)?(((r<<2^c(t,0))<<2^c(t,1))<<2^c(t,2))<<2^c(t,3):0){case 5103:return O+"print-"+t+t;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return O+t+t;case 5349:case 4246:case 4810:case 6968:case 2756:return O+t+$+t+_+t+t;case 6828:case 4268:return O+t+_+t+t;case 6165:return O+t+_+"flex-"+t+t;case 5187:return O+t+l(t,/(\w+).+(:[^]+)/,O+"box-$1$2"+_+"flex-$1$2")+t;case 5443:return O+t+_+"flex-item-"+l(t,/flex-|-self/,"")+t;case 4675:return O+t+_+"flex-line-pack"+l(t,/align-content|flex-|-self/,"")+t;case 5548:return O+t+_+l(t,"shrink","negative")+t;case 5292:return O+t+_+l(t,"basis","preferred-size")+t;case 6060:return O+"box-"+l(t,"-grow","")+O+t+_+l(t,"grow","positive")+t;case 4554:return O+l(t,/([^-])(transform)/g,"$1"+O+"$2")+t;case 6187:return l(l(l(t,/(zoom-|grab)/,O+"$1"),/(image-set)/,O+"$1"),t,"")+t;case 5495:case 3959:return l(t,/(image-set\([^]*)/,O+"$1$`$1");case 4968:return l(l(t,/(.+:)(flex-)?(.*)/,O+"box-pack:$3"+_+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+O+t+t;case 4095:case 3583:case 4068:case 2532:return l(t,/(.+)-inline(.+)/,O+"$1$2")+t;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(f(t)-1-r>6)switch(c(t,r+1)){case 109:if(45!==c(t,r+4))break;case 102:return l(t,/(.+:)(.+)-([^]+)/,"$1"+O+"$2-$3$1"+$+(108==c(t,r+3)?"$3":"$2-$3"))+t;case 115:return~s(t,"stretch")?e(l(t,"stretch","fill-available"),r)+t:t}break;case 4949:if(115!==c(t,r+1))break;case 6444:switch(c(t,f(t)-3-(~s(t,"!important")&&10))){case 107:return l(t,":",":"+O)+t;case 101:return l(t,/(.+:)([^;!]+)(;|!.+)?/,"$1"+O+(45===c(t,14)?"inline-":"")+"box$3$1"+O+"$2$3$1"+_+"$2box$3")+t}break;case 5936:switch(c(t,r+11)){case 114:return O+t+_+l(t,/[svh]\w+-[tblr]{2}/,"tb")+t;case 108:return O+t+_+l(t,/[svh]\w+-[tblr]{2}/,"tb-rl")+t;case 45:return O+t+_+l(t,/[svh]\w+-[tblr]{2}/,"lr")+t}return O+t+_+t+t}return t}(e.value,e.length);break;case M:return P([S(e,{value:l(e.value,"@","@"+O)})],n);case E:if(e.length){var o,i;return o=e.props,i=function(t){var r;switch(r=t,(r=/(::plac\w+|:read-\w+)/.exec(r))?r[0]:r){case":read-only":case":read-write":return P([S(e,{props:[l(t,/:(read-\w+)/,":"+$+"$1")]})],n);case"::placeholder":return P([S(e,{props:[l(t,/:(plac\w+)/,":"+O+"input-$1")]}),S(e,{props:[l(t,/:(plac\w+)/,":"+$+"$1")]}),S(e,{props:[l(t,/:(plac\w+)/,_+"input-$1")]})],n)}return""},o.map(i).join("")}}}],H=function(e){var t,r,o,a,m,S=e.key;if("css"===S){var _=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(_,function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))})}var $=e.stylisPlugins||K,O={},E=[];a=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+S+' "]'),function(e){for(var t=e.getAttribute("data-emotion").split(" "),r=1;r<t.length;r++)O[t[r]]=!0;E.push(e)});var T=(r=(t=[W,z].concat($,[N,(o=function(e){m.insert(e)},function(e){!e.root&&(e=e.return)&&o(e)})])).length,function(e,n,o,i){for(var a="",l=0;l<r;l++)a+=t[l](e,n,o,i)||"";return a}),M=function(e){var t,r;return P((r=function e(t,r,n,o,a,m,S,k,_){for(var $,O=0,E=0,T=S,M=0,P=0,N=0,R=1,L=1,F=1,D=0,W="",z=a,K=m,H=o,V=W;L;)switch(N=D,D=A()){case 40:if(108!=N&&58==c(V,T-1)){-1!=s(V+=l(C(D),"&","&\f"),"&\f")&&(F=-1);break}case 34:case 39:case 91:V+=C(D);break;case 9:case 10:case 13:case 32:V+=function(e){for(;g=w();)if(g<33)A();else break;return x(e)>2||x(g)>3?"":" "}(N);break;case 92:V+=function(e,t){for(var r;--t&&A()&&!(g<48)&&!(g>102)&&(!(g>57)||!(g<65))&&(!(g>70)||!(g<97)););return r=y+(t<6&&32==w()&&32==A()),u(b,e,r)}(y-1,7);continue;case 47:switch(w()){case 42:case 47:d(($=function(e,t){for(;A();)if(e+g===57)break;else if(e+g===84&&47===w())break;return"/*"+u(b,t,y-1)+"*"+i(47===e?e:A())}(A(),y),v($,r,n,j,i(g),u($,2,-2),0)),_);break;default:V+="/"}break;case 123*R:k[O++]=f(V)*F;case 125*R:case 59:case 0:switch(D){case 0:case 125:L=0;case 59+E:-1==F&&(V=l(V,/\f/g,"")),P>0&&f(V)-T&&d(P>32?I(V+";",o,n,T-1):I(l(V," ","")+";",o,n,T-2),_);break;case 59:V+=";";default:if(d(H=B(V,r,n,O,E,a,k,W,z=[],K=[],T),m),123===D)if(0===E)e(V,r,H,H,z,m,T,k,K);else switch(99===M&&110===c(V,3)?100:M){case 100:case 108:case 109:case 115:e(t,H,H,o&&d(B(t,H,H,0,0,a,k,W,a,z=[],T),K),a,K,T,k,o?z:K);break;default:e(V,H,H,H,[""],K,0,k,K)}}O=E=P=0,R=F=1,W=V="",T=S;break;case 58:T=1+f(V),P=N;default:if(R<1){if(123==D)--R;else if(125==D&&0==R++&&125==(g=y>0?c(b,--y):0,h--,10===g&&(h=1,p--),g))continue}switch(V+=i(D),D*R){case 38:F=E>0?1:(V+="\f",-1);break;case 44:k[O++]=(f(V)-1)*F,F=1;break;case 64:45===w()&&(V+=C(A())),M=w(),E=T=f(W=V+=function(e){for(;!x(w());)A();return u(b,e,y)}(y)),D++;break;case 45:45===N&&2==f(V)&&(R=0)}}return m}("",null,null,null,[""],t=k(t=e),0,[0],t),b="",r),T)},R={key:S,sheet:new n({key:S,container:a,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:O,registered:{},insert:function(e,t,r,n){m=r,M(e?e+"{"+t.styles+"}":t.styles),n&&(R.inserted[t.name]=!0)}};return R.sheet.hydrate(E),R}},10339:(e,t,r)=>{"use strict";r.d(t,{U1:()=>J,Z0:()=>en});var n,o=r(83129);function i(e){return({dispatch:t,getState:r})=>n=>o=>"function"==typeof o?o(t,r,e):n(o)}var a=i(),l=Symbol.for("immer-nothing"),s=Symbol.for("immer-draftable"),c=Symbol.for("immer-state");function u(e){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var f=Object.getPrototypeOf;function d(e){return!!e&&!!e[c]}function p(e){return!!e&&(m(e)||Array.isArray(e)||!!e[s]||!!e.constructor?.[s]||S(e)||A(e))}var h=Object.prototype.constructor.toString();function m(e){if(!e||"object"!=typeof e)return!1;let t=f(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===h}function y(e,t){0===g(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function g(e){let t=e[c];return t?t.type_:Array.isArray(e)?1:S(e)?2:3*!!A(e)}function b(e,t){return 2===g(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function v(e,t,r){let n=g(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function S(e){return e instanceof Map}function A(e){return e instanceof Set}function w(e){return e.copy_||e.base_}function x(e,t){if(S(e))return new Map(e);if(A(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=m(e);if(!0!==t&&("class_only"!==t||r)){let t=f(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[c];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let o=r[n],i=t[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(f(e),t)}}function k(e,t=!1){return _(e)||d(e)||!p(e)||(g(e)>1&&(e.set=e.add=e.clear=e.delete=C),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>k(t,!0))),e}function C(){u(2)}function _(e){return Object.isFrozen(e)}var $={};function O(e){let t=$[e];return t||u(0,e),t}function j(e,t){t&&(O("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function E(e){T(e),e.drafts_.forEach(P),e.drafts_=null}function T(e){e===n&&(n=e.parent_)}function M(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function P(e){let t=e[c];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function N(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[c].modified_&&(E(t),u(4)),p(e)&&(e=B(t,e),t.parent_||R(t,e)),t.patches_&&O("Patches").generateReplacementPatches_(r[c].base_,e,t.patches_,t.inversePatches_)):e=B(t,r,[]),E(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==l?e:void 0}function B(e,t,r){if(_(t))return t;let n=t[c];if(!n)return y(t,(o,i)=>I(e,n,t,o,i,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return R(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,o=t,i=!1;3===n.type_&&(o=new Set(t),t.clear(),i=!0),y(o,(o,a)=>I(e,n,t,o,a,r,i)),R(e,t,!1),r&&e.patches_&&O("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function I(e,t,r,n,o,i,a){if(d(o)){let a=B(e,o,i&&t&&3!==t.type_&&!b(t.assigned_,n)?i.concat(n):void 0);if(v(r,n,a),!d(a))return;e.canAutoFreeze_=!1}else a&&r.add(o);if(p(o)&&!_(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;B(e,o),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&R(e,o)}}function R(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&k(t,r)}var L={get(e,t){if(t===c)return e;let r=w(e);if(!b(r,t)){var n=e,o=r,i=t;let a=W(o,i);return a?"value"in a?a.value:a.get?.call(n.draft_):void 0}let a=r[t];return e.finalized_||!p(a)?a:a===D(e.base_,t)?(K(e),e.copy_[t]=H(a,e)):a},has:(e,t)=>t in w(e),ownKeys:e=>Reflect.ownKeys(w(e)),set(e,t,r){let n=W(w(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=D(w(e),t),o=n?.[c];if(o&&o.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||b(e.base_,t)))return!0;K(e),z(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==D(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,K(e),z(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=w(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){u(11)},getPrototypeOf:e=>f(e.base_),setPrototypeOf(){u(12)}},F={};function D(e,t){let r=e[c];return(r?w(r):e)[t]}function W(e,t){if(!(t in e))return;let r=f(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=f(r)}}function z(e){!e.modified_&&(e.modified_=!0,e.parent_&&z(e.parent_))}function K(e){e.copy_||(e.copy_=x(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function H(e,t){let r=S(e)?O("MapSet").proxyMap_(e,t):A(e)?O("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),o={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},i=o,a=L;r&&(i=[o],a=F);let{revoke:l,proxy:s}=Proxy.revocable(i,a);return o.draft_=s,o.revoke_=l,s}(e,t);return(t?t.scope_:n).drafts_.push(r),r}y(L,(e,t)=>{F[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),F.deleteProperty=function(e,t){return F.set.call(this,e,t,void 0)},F.set=function(e,t,r){return L.set.call(this,e[0],t,r,e[0])};var V=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...o){return n.produce(e,e=>t.call(this,e,...o))}}if("function"!=typeof t&&u(6),void 0!==r&&"function"!=typeof r&&u(7),p(e)){let o=M(this),i=H(e,void 0),a=!0;try{n=t(i),a=!1}finally{a?E(o):T(o)}return j(o,r),N(n,o)}if(e&&"object"==typeof e)u(1,e);else{if(void 0===(n=t(e))&&(n=e),n===l&&(n=void 0),this.autoFreeze_&&k(n,!0),r){let t=[],o=[];O("Patches").generateReplacementPatches_(e,n,t,o),r(t,o)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){var t;p(e)||u(8),d(e)&&(d(t=e)||u(10,t),e=function e(t){let r;if(!p(t)||_(t))return t;let n=t[c];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=x(t,n.scope_.immer_.useStrictShallowCopy_)}else r=x(t,!0);return y(r,(t,n)=>{v(r,t,e(n))}),n&&(n.finalized_=!1),r}(t));let r=M(this),n=H(e,void 0);return n[c].isManual_=!0,T(r),n}finishDraft(e,t){let r=e&&e[c];r&&r.isManual_||u(9);let{scope_:n}=r;return j(n,t),N(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=O("Patches").applyPatches_;return d(e)?n(e,t):this.produce(e,e=>n(e,t))}},G=V.produce;V.produceWithPatches.bind(V),V.setAutoFreeze.bind(V),V.setUseStrictShallowCopy.bind(V),V.applyPatches.bind(V),V.createDraft.bind(V),V.finishDraft.bind(V),r(65364);var X="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?o.Zz:o.Zz.apply(null,arguments)};function U(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(eu(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,o.ve)(t)&&t.type===e,r}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var Y=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function q(e){return p(e)?G(e,()=>{}):e}function Q(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var Z=e=>t=>{setTimeout(t,e)};function J(e){let t,r,n,l=function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{},l=new Y;return t&&("boolean"==typeof t?l.push(a):l.push(i(t.extraArgument))),l},{reducer:s,middleware:c,devTools:u=!0,duplicateMiddlewareCheck:f=!0,preloadedState:d,enhancers:p}=e||{};if("function"==typeof s)t=s;else if((0,o.Qd)(s))t=(0,o.HY)(s);else throw Error(eu(1));r="function"==typeof c?c(l):l();let h=o.Zz;u&&(h=X({trace:!1,..."object"==typeof u&&u}));let m=(n=(0,o.Tw)(...r),function(e){let{autoBatch:t=!0}=e??{},r=new Y(n);return t&&r.push(((e={type:"raf"})=>t=>(...r)=>{let n=t(...r),o=!0,i=!1,a=!1,l=new Set,s="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Z(10):"callback"===e.type?e.queueNotification:Z(e.timeout),c=()=>{a=!1,i&&(i=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>o&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(i=!(o=!e?.meta?.RTK_autoBatch))&&!a&&(a=!0,s(c)),n.dispatch(e)}finally{o=!0}}})})("object"==typeof t?t:void 0)),r}),y=h(..."function"==typeof p?p(m):m());return(0,o.y$)(t,d,y)}function ee(e){let t,r={},n=[],o={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(eu(28));if(n in r)throw Error(eu(29));return r[n]=t,o},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),o),addDefaultCase:e=>(t=e,o)};return e(o),[r,n,t]}var et=Symbol.for("rtk-slice-createasyncthunk"),er=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(er||{}),en=function({creators:e}={}){let t=e?.asyncThunk?.[et];return function(e){let r,{name:n,reducerPath:o=n}=e;if(!n)throw Error(eu(11));let i=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},a=Object.keys(i),l={},s={},c={},u=[],f={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(eu(12));if(r in s)throw Error(eu(13));return s[r]=t,f},addMatcher:(e,t)=>(u.push({matcher:e,reducer:t}),f),exposeAction:(e,t)=>(c[e]=t,f),exposeCaseReducer:(e,t)=>(l[e]=t,f)};function h(){let[t={},r=[],n]="function"==typeof e.extraReducers?ee(e.extraReducers):[e.extraReducers],o={...t,...s};return function(e,t){let r,[n,o,i]=ee(t);if("function"==typeof e)r=()=>q(e());else{let t=q(e);r=()=>t}function a(e=r(),t){let l=[n[t.type],...o.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===l.filter(e=>!!e).length&&(l=[i]),l.reduce((e,r)=>{if(r)if(d(e)){let n=r(e,t);return void 0===n?e:n}else{if(p(e))return G(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return a.getInitialState=r,a}(e.initialState,e=>{for(let t in o)e.addCase(t,o[t]);for(let t of u)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}a.forEach(r=>{let o=i[r],a={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===o._reducerDefinitionType?function({type:e,reducerName:t},r,n,o){if(!o)throw Error(eu(18));let{payloadCreator:i,fulfilled:a,pending:l,rejected:s,settled:c,options:u}=r,f=o(e,i,u);n.exposeAction(t,f),a&&n.addCase(f.fulfilled,a),l&&n.addCase(f.pending,l),s&&n.addCase(f.rejected,s),c&&n.addMatcher(f.settled,c),n.exposeCaseReducer(t,{fulfilled:a||eo,pending:l||eo,rejected:s||eo,settled:c||eo})}(a,o,f,t):function({type:e,reducerName:t,createNotation:r},n,o){let i,a;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(eu(17));i=n.reducer,a=n.prepare}else i=n;o.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,a?U(e,a):U(e))}(a,o,f)});let m=e=>e,y=new Map,g=new WeakMap;function b(e,t){return r||(r=h()),r(e,t)}function v(){return r||(r=h()),r.getInitialState()}function S(t,r=!1){function n(e){let o=e[t];return void 0===o&&r&&(o=Q(g,n,v)),o}function o(t=m){let n=Q(y,r,()=>new WeakMap);return Q(n,t,()=>{let n={};for(let[o,i]of Object.entries(e.selectors??{}))n[o]=function(e,t,r,n){function o(i,...a){let l=t(i);return void 0===l&&n&&(l=r()),e(l,...a)}return o.unwrapped=e,o}(i,t,()=>Q(g,t,v),r);return n})}return{reducerPath:t,getSelectors:o,get selectors(){return o(n)},selectSlice:n}}let A={name:n,reducer:b,actions:c,caseReducers:l,getInitialState:v,...S(o),injectInto(e,{reducerPath:t,...r}={}){let n=t??o;return e.inject({reducerPath:n,reducer:b},r),{...A,...S(n,!0)}}};return A}}();function eo(){}var{assign:ei}=Object,ea="listenerMiddleware",el=ei(e=>{let{type:t,predicate:r,effect:n}=(e=>{let{type:t,actionCreator:r,matcher:n,predicate:o,effect:i}=e;if(t)o=U(t).match;else if(r)t=r.type,o=r.match;else if(n)o=n;else if(o);else throw Error(eu(21));if("function"!=typeof i)throw TypeError(eu(32));return{predicate:o,type:t,effect:i}})(e);return{id:((e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t})(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(eu(22))}}},{withTypes:()=>el}),es=ei(U(`${ea}/add`),{withTypes:()=>es}),ec=ei(U(`${ea}/remove`),{withTypes:()=>ec});function eu(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original")},12535:(e,t,r)=>{"use strict";r.d(t,{A:()=>function e(t,r,a={clone:!0}){let l=a.clone?{...t}:t;return i(t)&&i(r)&&Object.keys(r).forEach(s=>{n.isValidElement(r[s])||(0,o.Hy)(r[s])?l[s]=r[s]:i(r[s])&&Object.prototype.hasOwnProperty.call(t,s)&&i(t[s])?l[s]=e(t[s],r[s],a):a.clone?l[s]=i(r[s])?function e(t){if(n.isValidElement(t)||(0,o.Hy)(t)||!i(t))return t;let r={};return Object.keys(t).forEach(n=>{r[n]=e(t[n])}),r}(r[s]):r[s]:l[s]=r[s]}),l},Q:()=>i});var n=r(14232),o=r(37639);function i(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}},12961:(e,t,r)=>{"use strict";r.d(t,{LX:()=>h,MA:()=>p,_W:()=>m,Lc:()=>g,Ms:()=>b});var n=r(72487),o=r(50327),i=r(45519);let a={m:"margin",p:"padding"},l={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},s={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},c=function(e){let t={};return r=>(void 0===t[r]&&(t[r]=e(r)),t[r])}(e=>{if(e.length>2)if(!s[e])return[e];else e=s[e];let[t,r]=e.split(""),n=a[t],o=l[r]||"";return Array.isArray(o)?o.map(e=>n+e):[n+o]}),u=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],f=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"],d=[...u,...f];function p(e,t,r,n){let i=(0,o.Yn)(e,t,!0)??r;return"number"==typeof i||"string"==typeof i?e=>"string"==typeof e?e:"string"==typeof i?`calc(${e} * ${i})`:i*e:Array.isArray(i)?e=>{if("string"==typeof e)return e;let t=i[Math.abs(e)];return e>=0?t:"number"==typeof t?-t:`-${t}`}:"function"==typeof i?i:()=>void 0}function h(e){return p(e,"spacing",8,"spacing")}function m(e,t){return"string"==typeof t||null==t?t:e(t)}function y(e,t){let r=h(e.theme);return Object.keys(e).map(o=>(function(e,t,r,o){var i;if(!t.includes(r))return null;let a=(i=c(r),e=>i.reduce((t,r)=>(t[r]=m(o,e),t),{})),l=e[r];return(0,n.NI)(e,l,a)})(e,t,o,r)).reduce(i.A,{})}function g(e){return y(e,u)}function b(e){return y(e,f)}function v(e){return y(e,d)}g.propTypes={},g.filterProps=u,b.propTypes={},b.filterProps=f,v.propTypes={},v.filterProps=d},21398:(e,t,r)=>{"use strict";r.d(t,{E:()=>m,T:()=>u,c:()=>p,h:()=>f,w:()=>c});var n=r(14232),o=r(3904),i=r(74849),a=r(67955),l=r(78455),s=n.createContext("undefined"!=typeof HTMLElement?(0,o.A)({key:"css"}):null);s.Provider;var c=function(e){return(0,n.forwardRef)(function(t,r){return e(t,(0,n.useContext)(s),r)})},u=n.createContext({}),f={}.hasOwnProperty,d="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",p=function(e,t){var r={};for(var n in t)f.call(t,n)&&(r[n]=t[n]);return r[d]=e,r},h=function(e){var t=e.cache,r=e.serialized,n=e.isStringTag;return(0,i.SF)(t,r,n),(0,l.s)(function(){return(0,i.sk)(t,r,n)}),null},m=c(function(e,t,r){var o=e.css;"string"==typeof o&&void 0!==t.registered[o]&&(o=t.registered[o]);var l=e[d],s=[o],c="";"string"==typeof e.className?c=(0,i.Rk)(t.registered,s,e.className):null!=e.className&&(c=e.className+" ");var p=(0,a.J)(s,void 0,n.useContext(u));c+=t.key+"-"+p.name;var m={};for(var y in e)f.call(e,y)&&"css"!==y&&y!==d&&(m[y]=e[y]);return m.className=c,r&&(m.ref=r),n.createElement(n.Fragment,null,n.createElement(h,{cache:t,serialized:p,isStringTag:"string"==typeof l}),n.createElement(l,m))})},23520:(e,t,r)=>{"use strict";var n=r(68128),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},l={};function s(e){return n.isMemo(e)?a:l[e.$$typeof]||o}l[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},l[n.Memo]=a;var c=Object.defineProperty,u=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,r,n){if("string"!=typeof r){if(h){var o=p(r);o&&o!==h&&e(t,o,n)}var a=u(r);f&&(a=a.concat(f(r)));for(var l=s(t),m=s(r),y=0;y<a.length;++y){var g=a[y];if(!i[g]&&!(n&&n[g])&&!(m&&m[g])&&!(l&&l[g])){var b=d(r,g);try{c(t,g,b)}catch(e){}}}}return t}},24802:(e,t,r)=>{"use strict";var n=r(14232),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useSyncExternalStore,a=n.useRef,l=n.useEffect,s=n.useMemo,c=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,u){var f=a(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=i(e,(f=s(function(){function e(e){if(!l){if(l=!0,i=e,e=n(e),void 0!==u&&d.hasValue){var t=d.value;if(u(t,e))return a=t}return a=e}if(t=a,o(i,e))return t;var r=n(e);return void 0!==u&&u(t,r)?(i=e,t):(i=e,a=r)}var i,a,l=!1,s=void 0===r?null:r;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,r,n,u]))[0],f[1]);return l(function(){d.hasValue=!0,d.value=p},[p]),c(p),p}},29044:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,o=r?Symbol.for("react.portal"):60106,i=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,s=r?Symbol.for("react.provider"):60109,c=r?Symbol.for("react.context"):60110,u=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,h=r?Symbol.for("react.suspense_list"):60120,m=r?Symbol.for("react.memo"):60115,y=r?Symbol.for("react.lazy"):60116,g=r?Symbol.for("react.block"):60121,b=r?Symbol.for("react.fundamental"):60117,v=r?Symbol.for("react.responder"):60118,S=r?Symbol.for("react.scope"):60119;function A(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case u:case f:case i:case l:case a:case p:return e;default:switch(e=e&&e.$$typeof){case c:case d:case y:case m:case s:return e;default:return t}}case o:return t}}}function w(e){return A(e)===f}t.AsyncMode=u,t.ConcurrentMode=f,t.ContextConsumer=c,t.ContextProvider=s,t.Element=n,t.ForwardRef=d,t.Fragment=i,t.Lazy=y,t.Memo=m,t.Portal=o,t.Profiler=l,t.StrictMode=a,t.Suspense=p,t.isAsyncMode=function(e){return w(e)||A(e)===u},t.isConcurrentMode=w,t.isContextConsumer=function(e){return A(e)===c},t.isContextProvider=function(e){return A(e)===s},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return A(e)===d},t.isFragment=function(e){return A(e)===i},t.isLazy=function(e){return A(e)===y},t.isMemo=function(e){return A(e)===m},t.isPortal=function(e){return A(e)===o},t.isProfiler=function(e){return A(e)===l},t.isStrictMode=function(e){return A(e)===a},t.isSuspense=function(e){return A(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===f||e===l||e===a||e===p||e===h||"object"==typeof e&&null!==e&&(e.$$typeof===y||e.$$typeof===m||e.$$typeof===s||e.$$typeof===c||e.$$typeof===d||e.$$typeof===b||e.$$typeof===v||e.$$typeof===S||e.$$typeof===g)},t.typeOf=A},31061:(e,t,r)=>{"use strict";r.d(t,{A:()=>z});var n=r(12961),o=r(50327),i=r(45519);let a=function(...e){let t=e.reduce((e,t)=>(t.filterProps.forEach(r=>{e[r]=t}),e),{}),r=e=>Object.keys(e).reduce((r,n)=>t[n]?(0,i.A)(r,t[n](e)):r,{});return r.propTypes={},r.filterProps=e.reduce((e,t)=>e.concat(t.filterProps),[]),r};var l=r(72487);function s(e){return"number"!=typeof e?e:`${e}px solid`}function c(e,t){return(0,o.Ay)({prop:e,themeKey:"borders",transform:t})}let u=c("border",s),f=c("borderTop",s),d=c("borderRight",s),p=c("borderBottom",s),h=c("borderLeft",s),m=c("borderColor"),y=c("borderTopColor"),g=c("borderRightColor"),b=c("borderBottomColor"),v=c("borderLeftColor"),S=c("outline",s),A=c("outlineColor"),w=e=>{if(void 0!==e.borderRadius&&null!==e.borderRadius){let t=(0,n.MA)(e.theme,"shape.borderRadius",4,"borderRadius");return(0,l.NI)(e,e.borderRadius,e=>({borderRadius:(0,n._W)(t,e)}))}return null};w.propTypes={},w.filterProps=["borderRadius"],a(u,f,d,p,h,m,y,g,b,v,w,S,A);let x=e=>{if(void 0!==e.gap&&null!==e.gap){let t=(0,n.MA)(e.theme,"spacing",8,"gap");return(0,l.NI)(e,e.gap,e=>({gap:(0,n._W)(t,e)}))}return null};x.propTypes={},x.filterProps=["gap"];let k=e=>{if(void 0!==e.columnGap&&null!==e.columnGap){let t=(0,n.MA)(e.theme,"spacing",8,"columnGap");return(0,l.NI)(e,e.columnGap,e=>({columnGap:(0,n._W)(t,e)}))}return null};k.propTypes={},k.filterProps=["columnGap"];let C=e=>{if(void 0!==e.rowGap&&null!==e.rowGap){let t=(0,n.MA)(e.theme,"spacing",8,"rowGap");return(0,l.NI)(e,e.rowGap,e=>({rowGap:(0,n._W)(t,e)}))}return null};C.propTypes={},C.filterProps=["rowGap"];let _=(0,o.Ay)({prop:"gridColumn"}),$=(0,o.Ay)({prop:"gridRow"}),O=(0,o.Ay)({prop:"gridAutoFlow"}),j=(0,o.Ay)({prop:"gridAutoColumns"}),E=(0,o.Ay)({prop:"gridAutoRows"}),T=(0,o.Ay)({prop:"gridTemplateColumns"}),M=(0,o.Ay)({prop:"gridTemplateRows"});function P(e,t){return"grey"===t?t:e}a(x,k,C,_,$,O,j,E,T,M,(0,o.Ay)({prop:"gridTemplateAreas"}),(0,o.Ay)({prop:"gridArea"}));let N=(0,o.Ay)({prop:"color",themeKey:"palette",transform:P});function B(e){return e<=1&&0!==e?`${100*e}%`:e}a(N,(0,o.Ay)({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:P}),(0,o.Ay)({prop:"backgroundColor",themeKey:"palette",transform:P}));let I=(0,o.Ay)({prop:"width",transform:B}),R=e=>void 0!==e.maxWidth&&null!==e.maxWidth?(0,l.NI)(e,e.maxWidth,t=>{let r=e.theme?.breakpoints?.values?.[t]||l.zu[t];return r?e.theme?.breakpoints?.unit!=="px"?{maxWidth:`${r}${e.theme.breakpoints.unit}`}:{maxWidth:r}:{maxWidth:B(t)}}):null;R.filterProps=["maxWidth"];let L=(0,o.Ay)({prop:"minWidth",transform:B}),F=(0,o.Ay)({prop:"height",transform:B}),D=(0,o.Ay)({prop:"maxHeight",transform:B}),W=(0,o.Ay)({prop:"minHeight",transform:B});(0,o.Ay)({prop:"size",cssProperty:"width",transform:B}),(0,o.Ay)({prop:"size",cssProperty:"height",transform:B}),a(I,R,L,F,D,W,(0,o.Ay)({prop:"boxSizing"}));let z={border:{themeKey:"borders",transform:s},borderTop:{themeKey:"borders",transform:s},borderRight:{themeKey:"borders",transform:s},borderBottom:{themeKey:"borders",transform:s},borderLeft:{themeKey:"borders",transform:s},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:s},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:w},color:{themeKey:"palette",transform:P},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:P},backgroundColor:{themeKey:"palette",transform:P},p:{style:n.Ms},pt:{style:n.Ms},pr:{style:n.Ms},pb:{style:n.Ms},pl:{style:n.Ms},px:{style:n.Ms},py:{style:n.Ms},padding:{style:n.Ms},paddingTop:{style:n.Ms},paddingRight:{style:n.Ms},paddingBottom:{style:n.Ms},paddingLeft:{style:n.Ms},paddingX:{style:n.Ms},paddingY:{style:n.Ms},paddingInline:{style:n.Ms},paddingInlineStart:{style:n.Ms},paddingInlineEnd:{style:n.Ms},paddingBlock:{style:n.Ms},paddingBlockStart:{style:n.Ms},paddingBlockEnd:{style:n.Ms},m:{style:n.Lc},mt:{style:n.Lc},mr:{style:n.Lc},mb:{style:n.Lc},ml:{style:n.Lc},mx:{style:n.Lc},my:{style:n.Lc},margin:{style:n.Lc},marginTop:{style:n.Lc},marginRight:{style:n.Lc},marginBottom:{style:n.Lc},marginLeft:{style:n.Lc},marginX:{style:n.Lc},marginY:{style:n.Lc},marginInline:{style:n.Lc},marginInlineStart:{style:n.Lc},marginInlineEnd:{style:n.Lc},marginBlock:{style:n.Lc},marginBlockStart:{style:n.Lc},marginBlockEnd:{style:n.Lc},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:x},rowGap:{style:C},columnGap:{style:k},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:B},maxWidth:{style:R},minWidth:{transform:B},height:{transform:B},maxHeight:{transform:B},minHeight:{transform:B},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}}},31777:(e,t,r)=>{"use strict";r.d(t,{Kq:()=>d,d4:()=>v,wA:()=>g});var n=r(14232),o=r(87282),i={notify(){},get:()=>[]},a="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,l="undefined"!=typeof navigator&&"ReactNative"===navigator.product,s=a||l?n.useLayoutEffect:n.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var c=Symbol.for("react-redux-context"),u="undefined"!=typeof globalThis?globalThis:{},f=function(){if(!n.createContext)return{};let e=u[c]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),d=function(e){let{children:t,context:r,serverState:o,store:a}=e,l=n.useMemo(()=>{let e=function(e,t){let r,n=i,o=0,a=!1;function l(){u.onStateChange&&u.onStateChange()}function s(){if(o++,!r){let t,o;r=e.subscribe(l),t=null,o=null,n={clear(){t=null,o=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=o={callback:e,next:null,prev:o};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:o=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function c(){o--,r&&0===o&&(r(),r=void 0,n.clear(),n=i)}let u={addNestedSub:function(e){s();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:l,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,s())},tryUnsubscribe:function(){a&&(a=!1,c())},getListeners:()=>n};return u}(a);return{store:a,subscription:e,getServerState:o?()=>o:void 0}},[a,o]),c=n.useMemo(()=>a.getState(),[a]);return s(()=>{let{subscription:e}=l;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),c!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[l,c]),n.createElement((r||f).Provider,{value:l},t)};function p(e=f){return function(){return n.useContext(e)}}var h=p();function m(e=f){let t=e===f?h:p(e),r=()=>{let{store:e}=t();return e};return Object.assign(r,{withTypes:()=>r}),r}var y=m(),g=function(e=f){let t=e===f?y:m(e),r=()=>t().dispatch;return Object.assign(r,{withTypes:()=>r}),r}(),b=(e,t)=>e===t,v=function(e=f){let t=e===f?h:p(e),r=(e,r={})=>{let{equalityFn:i=b}="function"==typeof r?{equalityFn:r}:r,{store:a,subscription:l,getServerState:s}=t();n.useRef(!0);let c=n.useCallback({[e.name]:t=>e(t)}[e.name],[e]),u=(0,o.useSyncExternalStoreWithSelector)(l.addNestedSub,a.getState,s||a.getState,c,i);return n.useDebugValue(u),u};return Object.assign(r,{withTypes:()=>r}),r}()},36048:()=>{},37094:(e,t,r)=>{"use strict";r.d(t,{A:()=>i}),r(14232);var n=r(38993),o=r(37876);function i(e){let{styles:t,defaultTheme:r={}}=e,i="function"==typeof t?e=>t(null==e||0===Object.keys(e).length?r:e):t;return(0,o.jsx)(n.mL,{styles:i})}},37551:(e,t,r)=>{"use strict";r.d(t,{Dp:()=>f,Dg:()=>d}),r(14232);var n=r(90809),o=r(37094),i=r(64289),a=r(37876);let l=function({styles:e,themeId:t,defaultTheme:r={}}){let n=(0,i.A)(r),l="function"==typeof e?e(t&&n[t]||n):e;return(0,a.jsx)(o.A,{styles:l})};var s=r(56892),c=r(3637);let u=function(e){return(0,a.jsx)(l,{...e,defaultTheme:s.A,themeId:c.A})};function f(e){return function(t){return(0,a.jsx)(u,{styles:"function"==typeof e?r=>e({theme:r,...t}):e})}}function d(){return n.A}},37639:(e,t)=>{"use strict";var r=Symbol.for("react.transitional.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler");Symbol.for("react.provider");var l=Symbol.for("react.consumer"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),h=Symbol.for("react.view_transition"),m=Symbol.for("react.client.reference");t.Hy=function(e){return"string"==typeof e||"function"==typeof e||e===o||e===a||e===i||e===u||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===p||e.$$typeof===d||e.$$typeof===s||e.$$typeof===l||e.$$typeof===c||e.$$typeof===m||void 0!==e.getModuleId)||!1}},38993:(e,t,r)=>{"use strict";r.d(t,{AH:()=>u,i7:()=>f,mL:()=>c});var n=r(21398),o=r(14232),i=r(74849),a=r(78455),l=r(67955);r(3904),r(23520);var s=function(e,t){var r=arguments;if(null==t||!n.h.call(t,"css"))return o.createElement.apply(void 0,r);var i=r.length,a=Array(i);a[0]=n.E,a[1]=(0,n.c)(e,t);for(var l=2;l<i;l++)a[l]=r[l];return o.createElement.apply(null,a)};!function(e){var t;t||(t=e.JSX||(e.JSX={}))}(s||(s={}));var c=(0,n.w)(function(e,t){var r=e.styles,s=(0,l.J)([r],void 0,o.useContext(n.T)),c=o.useRef();return(0,a.i)(function(){var e=t.key+"-global",r=new t.sheet.constructor({key:e,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),n=!1,o=document.querySelector('style[data-emotion="'+e+" "+s.name+'"]');return t.sheet.tags.length&&(r.before=t.sheet.tags[0]),null!==o&&(n=!0,o.setAttribute("data-emotion",e),r.hydrate([o])),c.current=[r,n],function(){r.flush()}},[t]),(0,a.i)(function(){var e=c.current,r=e[0];if(e[1]){e[1]=!1;return}if(void 0!==s.next&&(0,i.sk)(t,s.next,!0),r.tags.length){var n=r.tags[r.tags.length-1].nextElementSibling;r.before=n,r.flush()}t.insert("",s,r,!1)},[t,s.name]),null});function u(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.J)(t)}function f(){var e=u.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}},40345:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a,gs:()=>i,nx:()=>o});let n=(0,r(10339).Z0)({name:"transcription",initialState:"",reducers:{setTranscription:(e,t)=>t.payload,clearTranscription:()=>""}}),{setTranscription:o,clearTranscription:i}=n.actions,a=n.reducer},41945:(e,t,r)=>{"use strict";function n(e){var t=Object.create(null);return function(r){return void 0===t[r]&&(t[r]=e(r)),t[r]}}r.d(t,{A:()=>n})},45519:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12535);let o=function(e,t){return t?(0,n.A)(e,t,{clone:!1}):e}},47402:(e,t,r)=>{"use strict";function n(e){return Math.round(10*(e<1?5.11916*e**2:4.5*Math.log(e+1)+2))/1e3}r.d(t,{A:()=>n})},48877:(e,t,r)=>{"use strict";r.d(t,{A:()=>s,b:()=>l});var n=r(14232),o=r(581),i=r(37876);let a=n.createContext(void 0);function l({props:e,name:t}){let{theme:r,name:i,props:l}={props:e,name:t,theme:{components:n.useContext(a)}};if(!r||!r.components||!r.components[i])return l;let s=r.components[i];return s.defaultProps?(0,o.A)(s.defaultProps,l):s.styleOverrides||s.variants?l:(0,o.A)(s,l)}let s=function({value:e,children:t}){return(0,i.jsx)(a.Provider,{value:e,children:t})}},50161:(e,t,r)=>{"use strict";r.d(t,{A:()=>N});var n=r(14232);let o=n.createContext(null);function i(){return n.useContext(o)}let a="function"==typeof Symbol&&Symbol.for?Symbol.for("mui.nested"):"__THEME_NESTED__";var l=r(37876);let s=function(e){let{children:t,theme:r}=e,s=i(),c=n.useMemo(()=>{var e,t;let n=null===s?{...r}:(e=s,"function"==typeof(t=r)?t(e):{...e,...t});return null!=n&&(n[a]=null!==s),n},[r,s]);return(0,l.jsx)(o.Provider,{value:c,children:t})};var c=r(21398),u=r(76975),f=r(62844),d=r(48877);let p={};function h(e,t,r,o=!1){return n.useMemo(()=>{let n=e&&t[e]||t;if("function"==typeof r){let i=r(n),a=e?{...t,[e]:i}:i;return o?()=>a:a}return e?{...t,[e]:r}:{...t,...r}},[e,t,r,o])}let m=function(e){let{children:t,theme:r,themeId:n}=e,o=(0,u.A)(p),a=i()||p,m=h(n,o,r),y=h(n,a,r,!0),g="rtl"===(n?m[n]:m).direction;return(0,l.jsx)(s,{theme:y,children:(0,l.jsx)(c.T.Provider,{value:m,children:(0,l.jsx)(f.A,{value:g,children:(0,l.jsx)(d.A,{value:n?m[n].components:m.components,children:t})})})})};var y=r(3637);function g(e){let{theme:t,...r}=e,n=y.A in t?t[y.A]:void 0;return(0,l.jsx)(m,{...r,themeId:n?y.A:void 0,theme:n||t})}var b=r(74615),v=r(37094),S=r(99659);let A="mode",w="color-scheme";function x(){}let k=({key:e,storageWindow:t})=>(t||"undefined"==typeof window||(t=window),{get(r){let n;if("undefined"!=typeof window){if(!t)return r;try{n=t.localStorage.getItem(e)}catch{}return n||r}},set:r=>{if(t)try{t.localStorage.setItem(e,r)}catch{}},subscribe:r=>{if(!t)return x;let n=t=>{let n=t.newValue;t.key===e&&r(n)};return t.addEventListener("storage",n),()=>{t.removeEventListener("storage",n)}}});function C(){}function _(e){if("undefined"!=typeof window&&"function"==typeof window.matchMedia&&"system"===e)return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function $(e,t){return"light"===e.mode||"system"===e.mode&&"light"===e.systemMode?t("light"):"dark"===e.mode||"system"===e.mode&&"dark"===e.systemMode?t("dark"):void 0}var O=r(68988),j=r(88707);let E={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:T,useColorScheme:M,getInitColorSchemeScript:P}=function(e){let{themeId:t,theme:r={},modeStorageKey:o=A,colorSchemeStorageKey:a=w,disableTransitionOnChange:s=!1,defaultColorScheme:c,resolveTheme:u}=e,f={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},d=n.createContext(void 0),p={},h={},y="string"==typeof c?c:c.light,g="string"==typeof c?c:c.dark;return{CssVarsProvider:function(e){let{children:f,theme:y,modeStorageKey:g=o,colorSchemeStorageKey:b=a,disableTransitionOnChange:x=s,storageManager:O,storageWindow:j="undefined"==typeof window?void 0:window,documentNode:E="undefined"==typeof document?void 0:document,colorSchemeNode:T="undefined"==typeof document?void 0:document.documentElement,disableNestedContext:M=!1,disableStyleSheetGeneration:P=!1,defaultMode:N="system",noSsr:B}=e,I=n.useRef(!1),R=i(),L=n.useContext(d),F=!!L&&!M,D=n.useMemo(()=>y||("function"==typeof r?r():r),[y]),W=D[t],z=W||D,{colorSchemes:K=p,components:H=h,cssVarPrefix:V}=z,G=Object.keys(K).filter(e=>!!K[e]).join(","),X=n.useMemo(()=>G.split(","),[G]),U="string"==typeof c?c:c.light,Y="string"==typeof c?c:c.dark,q=K[U]&&K[Y]?N:K[z.defaultColorScheme]?.palette?.mode||z.palette?.mode,{mode:Q,setMode:Z,systemMode:J,lightColorScheme:ee,darkColorScheme:et,colorScheme:er,setColorScheme:en}=function(e){let{defaultMode:t="light",defaultLightColorScheme:r,defaultDarkColorScheme:o,supportedColorSchemes:i=[],modeStorageKey:a=A,colorSchemeStorageKey:l=w,storageWindow:s="undefined"==typeof window?void 0:window,storageManager:c=k,noSsr:u=!1}=e,f=i.join(","),d=i.length>1,p=n.useMemo(()=>c?.({key:a,storageWindow:s}),[c,a,s]),h=n.useMemo(()=>c?.({key:`${l}-light`,storageWindow:s}),[c,l,s]),m=n.useMemo(()=>c?.({key:`${l}-dark`,storageWindow:s}),[c,l,s]),[y,g]=n.useState(()=>{let e=p?.get(t)||t,n=h?.get(r)||r,i=m?.get(o)||o;return{mode:e,systemMode:_(e),lightColorScheme:n,darkColorScheme:i}}),[b,v]=n.useState(u||!d);n.useEffect(()=>{v(!0)},[]);let S=$(y,e=>"light"===e?y.lightColorScheme:"dark"===e?y.darkColorScheme:void 0),x=n.useCallback(e=>{g(r=>{if(e===r.mode)return r;let n=e??t;return p?.set(n),{...r,mode:n,systemMode:_(n)}})},[p,t]),O=n.useCallback(e=>{e?"string"==typeof e?e&&!f.includes(e)?console.error(`\`${e}\` does not exist in \`theme.colorSchemes\`.`):g(t=>{let r={...t};return $(t,t=>{"light"===t&&(h?.set(e),r.lightColorScheme=e),"dark"===t&&(m?.set(e),r.darkColorScheme=e)}),r}):g(t=>{let n={...t},i=null===e.light?r:e.light,a=null===e.dark?o:e.dark;return i&&(f.includes(i)?(n.lightColorScheme=i,h?.set(i)):console.error(`\`${i}\` does not exist in \`theme.colorSchemes\`.`)),a&&(f.includes(a)?(n.darkColorScheme=a,m?.set(a)):console.error(`\`${a}\` does not exist in \`theme.colorSchemes\`.`)),n}):g(e=>(h?.set(r),m?.set(o),{...e,lightColorScheme:r,darkColorScheme:o}))},[f,h,m,r,o]),j=n.useCallback(e=>{"system"===y.mode&&g(t=>{let r=e?.matches?"dark":"light";return t.systemMode===r?t:{...t,systemMode:r}})},[y.mode]),E=n.useRef(j);return E.current=j,n.useEffect(()=>{if("function"!=typeof window.matchMedia||!d)return;let e=(...e)=>E.current(...e),t=window.matchMedia("(prefers-color-scheme: dark)");return t.addListener(e),e(t),()=>{t.removeListener(e)}},[d]),n.useEffect(()=>{if(d){let e=p?.subscribe(e=>{(!e||["light","dark","system"].includes(e))&&x(e||t)})||C,r=h?.subscribe(e=>{(!e||f.match(e))&&O({light:e})})||C,n=m?.subscribe(e=>{(!e||f.match(e))&&O({dark:e})})||C;return()=>{e(),r(),n()}}},[O,x,f,t,s,d,p,h,m]),{...y,mode:b?y.mode:void 0,systemMode:b?y.systemMode:void 0,colorScheme:b?S:void 0,setMode:x,setColorScheme:O}}({supportedColorSchemes:X,defaultLightColorScheme:U,defaultDarkColorScheme:Y,modeStorageKey:g,colorSchemeStorageKey:b,defaultMode:q,storageManager:O,storageWindow:j,noSsr:B}),eo=Q,ei=er;F&&(eo=L.mode,ei=L.colorScheme);let ea=n.useMemo(()=>{let e=ei||z.defaultColorScheme,t=z.generateThemeVars?.()||z.vars,r={...z,components:H,colorSchemes:K,cssVarPrefix:V,vars:t};if("function"==typeof r.generateSpacing&&(r.spacing=r.generateSpacing()),e){let t=K[e];t&&"object"==typeof t&&Object.keys(t).forEach(e=>{t[e]&&"object"==typeof t[e]?r[e]={...r[e],...t[e]}:r[e]=t[e]})}return u?u(r):r},[z,ei,H,K,V]),el=z.colorSchemeSelector;(0,S.A)(()=>{if(ei&&T&&el&&"media"!==el){let e=el;if("class"===el&&(e=".%s"),"data"===el&&(e="[data-%s]"),el?.startsWith("data-")&&!el.includes("%s")&&(e=`[${el}="%s"]`),e.startsWith("."))T.classList.remove(...X.map(t=>e.substring(1).replace("%s",t))),T.classList.add(e.substring(1).replace("%s",ei));else{let t=e.replace("%s",ei).match(/\[([^\]]+)\]/);if(t){let[e,r]=t[1].split("=");r||X.forEach(t=>{T.removeAttribute(e.replace(ei,t))}),T.setAttribute(e,r?r.replace(/"|'/g,""):"")}else T.setAttribute(e,ei)}}},[ei,el,T,X]),n.useEffect(()=>{let e;if(x&&I.current&&E){let t=E.createElement("style");t.appendChild(E.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),E.head.appendChild(t),window.getComputedStyle(E.body),e=setTimeout(()=>{E.head.removeChild(t)},1)}return()=>{clearTimeout(e)}},[ei,x,E]),n.useEffect(()=>(I.current=!0,()=>{I.current=!1}),[]);let es=n.useMemo(()=>({allColorSchemes:X,colorScheme:ei,darkColorScheme:et,lightColorScheme:ee,mode:eo,setColorScheme:en,setMode:Z,systemMode:J}),[X,ei,et,ee,eo,en,Z,J,ea.colorSchemeSelector]),ec=!0;(P||!1===z.cssVariables||F&&R?.cssVarPrefix===V)&&(ec=!1);let eu=(0,l.jsxs)(n.Fragment,{children:[(0,l.jsx)(m,{themeId:W?t:void 0,theme:ea,children:f}),ec&&(0,l.jsx)(v.A,{styles:ea.generateStyleSheets?.()||[]})]});return F?eu:(0,l.jsx)(d.Provider,{value:es,children:eu})},useColorScheme:()=>n.useContext(d)||f,getInitColorSchemeScript:e=>(function(e){let{defaultMode:t="system",defaultLightColorScheme:r="light",defaultDarkColorScheme:n="dark",modeStorageKey:o=A,colorSchemeStorageKey:i=w,attribute:a="data-color-scheme",colorSchemeNode:s="document.documentElement",nonce:c}=e||{},u="",f=a;if("class"===a&&(f=".%s"),"data"===a&&(f="[data-%s]"),f.startsWith(".")){let e=f.substring(1);u+=`${s}.classList.remove('${e}'.replace('%s', light), '${e}'.replace('%s', dark));
      ${s}.classList.add('${e}'.replace('%s', colorScheme));`}let d=f.match(/\[([^\]]+)\]/);if(d){let[e,t]=d[1].split("=");t||(u+=`${s}.removeAttribute('${e}'.replace('%s', light));
      ${s}.removeAttribute('${e}'.replace('%s', dark));`),u+=`
      ${s}.setAttribute('${e}'.replace('%s', colorScheme), ${t?`${t}.replace('%s', colorScheme)`:'""'});`}else u+=`${s}.setAttribute('${f}', colorScheme);`;return(0,l.jsx)("script",{suppressHydrationWarning:!0,nonce:"undefined"==typeof window?c:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${o}') || '${t}';
  const dark = localStorage.getItem('${i}-dark') || '${n}';
  const light = localStorage.getItem('${i}-light') || '${r}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${u}
  }
} catch(e){}})();`}},"mui-color-scheme-init")})({colorSchemeStorageKey:a,defaultLightColorScheme:y,defaultDarkColorScheme:g,modeStorageKey:o,...e})}}({themeId:y.A,theme:()=>(0,O.A)({cssVariables:!0}),colorSchemeStorageKey:E.colorSchemeStorageKey,modeStorageKey:E.modeStorageKey,defaultColorScheme:{light:E.defaultLightColorScheme,dark:E.defaultDarkColorScheme},resolveTheme:e=>{let t={...e,typography:(0,j.A)(e.palette,e.typography)};return t.unstable_sx=function(e){return(0,b.A)({sx:e,theme:this})},t}});function N(e){let{theme:t,...r}=e,o=n.useMemo(()=>{if("function"==typeof t)return t;let e=y.A in t?t[y.A]:t;return"colorSchemes"in e?null:"vars"in e?t:{...t,vars:null}},[t]);return o?(0,l.jsx)(g,{theme:o,...r}):(0,l.jsx)(T,{theme:t,...r})}},50327:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>l,BO:()=>a,Yn:()=>i});var n=r(93725),o=r(72487);function i(e,t,r=!0){if(!t||"string"!=typeof t)return null;if(e&&e.vars&&r){let r=`vars.${t}`.split(".").reduce((e,t)=>e&&e[t]?e[t]:null,e);if(null!=r)return r}return t.split(".").reduce((e,t)=>e&&null!=e[t]?e[t]:null,e)}function a(e,t,r,n=r){let o;return o="function"==typeof e?e(r):Array.isArray(e)?e[r]||n:i(e,r)||n,t&&(o=t(o,n,e)),o}let l=function(e){let{prop:t,cssProperty:r=e.prop,themeKey:l,transform:s}=e,c=e=>{if(null==e[t])return null;let c=e[t],u=i(e.theme,l)||{};return(0,o.NI)(e,c,e=>{let o=a(u,s,e);return(e===o&&"string"==typeof e&&(o=a(u,s,`${t}${"default"===e?"":(0,n.A)(e)}`,e)),!1===r)?o:{[r]:o}})};return c.propTypes={},c.filterProps=[t],c}},52196:(e,t,r)=>{"use strict";r.d(t,{b:()=>o}),r(14232);var n=r(48877);function o(e){return(0,n.b)(e)}r(37876)},53111:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12961);function o(e=8,t=(0,n.LX)({spacing:e})){if(e.mui)return e;let r=(...e)=>(0===e.length?[1]:e).map(e=>{let r=t(e);return"number"==typeof r?`${r}px`:r}).join(" ");return r.mui=!0,r}},56556:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return r(74363)}])},56892:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(68988).A)()},62844:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,I:()=>a});var n=r(14232),o=r(37876);let i=n.createContext(),a=()=>n.useContext(i)??!1,l=function({value:e,...t}){return(0,o.jsx)(i.Provider,{value:e??!0,...t})}},64289:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(89856),o=r(76975);let i=(0,n.A)(),a=function(e=i){return(0,o.A)(e)}},67955:(e,t,r)=>{"use strict";r.d(t,{J:()=>h});var n,o={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},i=r(41945),a=/[A-Z]|^ms/g,l=/_EMO_([^_]+?)_([^]*?)_EMO_/g,s=function(e){return 45===e.charCodeAt(1)},c=function(e){return null!=e&&"boolean"!=typeof e},u=(0,i.A)(function(e){return s(e)?e:e.replace(a,"-$&").toLowerCase()}),f=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(l,function(e,t,r){return n={name:t,styles:r,next:n},t})}return 1===o[e]||s(e)||"number"!=typeof t||0===t?t:t+"px"};function d(e,t,r){if(null==r)return"";if(void 0!==r.__emotion_styles)return r;switch(typeof r){case"boolean":return"";case"object":if(1===r.anim)return n={name:r.name,styles:r.styles,next:n},r.name;if(void 0!==r.styles){var o=r.next;if(void 0!==o)for(;void 0!==o;)n={name:o.name,styles:o.styles,next:n},o=o.next;return r.styles+";"}return function(e,t,r){var n="";if(Array.isArray(r))for(var o=0;o<r.length;o++)n+=d(e,t,r[o])+";";else for(var i in r){var a=r[i];if("object"!=typeof a)null!=t&&void 0!==t[a]?n+=i+"{"+t[a]+"}":c(a)&&(n+=u(i)+":"+f(i,a)+";");else if(Array.isArray(a)&&"string"==typeof a[0]&&(null==t||void 0===t[a[0]]))for(var l=0;l<a.length;l++)c(a[l])&&(n+=u(i)+":"+f(i,a[l])+";");else{var s=d(e,t,a);switch(i){case"animation":case"animationName":n+=u(i)+":"+s+";";break;default:n+=i+"{"+s+"}"}}}return n}(e,t,r);case"function":if(void 0!==e){var i=n,a=r(e);return n=i,d(e,t,a)}}if(null==t)return r;var l=t[r];return void 0!==l?l:r}var p=/label:\s*([^\s;{]+)\s*(;|$)/g;function h(e,t,r){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var o,i=!0,a="";n=void 0;var l=e[0];null==l||void 0===l.raw?(i=!1,a+=d(r,t,l)):a+=l[0];for(var s=1;s<e.length;s++)a+=d(r,t,e[s]),i&&(a+=l[s]);p.lastIndex=0;for(var c="";null!==(o=p.exec(a));)c+="-"+o[1];return{name:function(e){for(var t,r=0,n=0,o=e.length;o>=4;++n,o-=4)t=(65535&(t=255&e.charCodeAt(n)|(255&e.charCodeAt(++n))<<8|(255&e.charCodeAt(++n))<<16|(255&e.charCodeAt(++n))<<24))*0x5bd1e995+((t>>>16)*59797<<16),t^=t>>>24,r=(65535&t)*0x5bd1e995+((t>>>16)*59797<<16)^(65535&r)*0x5bd1e995+((r>>>16)*59797<<16);switch(o){case 3:r^=(255&e.charCodeAt(n+2))<<16;case 2:r^=(255&e.charCodeAt(n+1))<<8;case 1:r^=255&e.charCodeAt(n),r=(65535&r)*0x5bd1e995+((r>>>16)*59797<<16)}return r^=r>>>13,(((r=(65535&r)*0x5bd1e995+((r>>>16)*59797<<16))^r>>>15)>>>0).toString(36)}(a)+c,styles:a,next:n}}},68050:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>f});var n=r(14232),o=r(37551),i=r(52196),a=r(37876);let l="function"==typeof(0,o.Dp)({}),s=function(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o={};n&&e.colorSchemes&&"function"==typeof e.getColorSchemeSelector&&Object.entries(e.colorSchemes).forEach(t=>{var r,n;let[i,a]=t,l=e.getColorSchemeSelector(i);l.startsWith("@")?o[l]={":root":{colorScheme:null==(r=a.palette)?void 0:r.mode}}:o[l.replace(/\s*&/,"")]={colorScheme:null==(n=a.palette)?void 0:n.mode}});let i={html:{WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...n&&!e.vars&&{colorScheme:e.palette.mode}},"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:e.typography.fontWeightBold},body:{margin:0,...{color:(e.vars||e).palette.text.primary,...e.typography.body1,backgroundColor:(e.vars||e).palette.background.default,"@media print":{backgroundColor:(e.vars||e).palette.common.white}},"&::backdrop":{backgroundColor:(e.vars||e).palette.background.default}},...o},a=null==(r=e.components)||null==(t=r.MuiCssBaseline)?void 0:t.styleOverrides;return a&&(i=[i,a]),i},c="mui-ecs",u=(0,o.Dp)(l?e=>{let{theme:t,enableColorScheme:r}=e;return s(t,r)}:e=>{let{theme:t}=e;return(e=>{let t=s(e,!1),r=Array.isArray(t)?t[0]:t;return!e.vars&&r&&(r.html[":root:has(".concat(c,")")]={colorScheme:e.palette.mode}),e.colorSchemes&&Object.entries(e.colorSchemes).forEach(t=>{var n,o;let[i,a]=t,l=e.getColorSchemeSelector(i);l.startsWith("@")?r[l]={[":root:not(:has(.".concat(c,"))")]:{colorScheme:null==(n=a.palette)?void 0:n.mode}}:r[l.replace(/\s*&/,"")]={["&:not(:has(.".concat(c,"))")]:{colorScheme:null==(o=a.palette)?void 0:o.mode}}}),t})(t)}),f=function(e){let{children:t,enableColorScheme:r=!1}=(0,i.b)({props:e,name:"MuiCssBaseline"});return(0,a.jsxs)(n.Fragment,{children:[l&&(0,a.jsx)(u,{enableColorScheme:r}),!l&&!r&&(0,a.jsx)("span",{className:c,style:{display:"none"}}),t]})}},68128:(e,t,r)=>{"use strict";e.exports=r(29044)},68988:(e,t,r)=>{"use strict";r.d(t,{A:()=>q});var n=r(69135),o=r(12535),i=r(126);let a={black:"#000",white:"#fff"};var l=r(743);let s={50:"#f3e5f5",100:"#e1bee7",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",600:"#8e24aa",700:"#7b1fa2",800:"#6a1b9a",900:"#4a148c",A100:"#ea80fc",A200:"#e040fb",A400:"#d500f9",A700:"#aa00ff"};var c=r(98291);let u={50:"#fff3e0",100:"#ffe0b2",200:"#ffcc80",300:"#ffb74d",400:"#ffa726",500:"#ff9800",600:"#fb8c00",700:"#f57c00",800:"#ef6c00",900:"#e65100",A100:"#ffd180",A200:"#ffab40",A400:"#ff9100",A700:"#ff6d00"},f={50:"#e3f2fd",100:"#bbdefb",200:"#90caf9",300:"#64b5f6",400:"#42a5f5",500:"#2196f3",600:"#1e88e5",700:"#1976d2",800:"#1565c0",900:"#0d47a1",A100:"#82b1ff",A200:"#448aff",A400:"#2979ff",A700:"#2962ff"},d={50:"#e1f5fe",100:"#b3e5fc",200:"#81d4fa",300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",600:"#039be5",700:"#0288d1",800:"#0277bd",900:"#01579b",A100:"#80d8ff",A200:"#40c4ff",A400:"#00b0ff",A700:"#0091ea"},p={50:"#e8f5e9",100:"#c8e6c9",200:"#a5d6a7",300:"#81c784",400:"#66bb6a",500:"#4caf50",600:"#43a047",700:"#388e3c",800:"#2e7d32",900:"#1b5e20",A100:"#b9f6ca",A200:"#69f0ae",A400:"#00e676",A700:"#00c853"};function h(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:a.white,default:a.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}let m=h();function y(){return{text:{primary:a.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:a.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}let g=y();function b(e,t,r,n){let o=n.light||n,a=n.dark||1.5*n;e[t]||(e.hasOwnProperty(r)?e[t]=e[r]:"light"===t?e.light=(0,i.a)(e.main,o):"dark"===t&&(e.dark=(0,i.e$)(e.main,a)))}function v(e){let t,{mode:r="light",contrastThreshold:v=3,tonalOffset:S=.2,...A}=e,w=e.primary||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:f[200],light:f[50],dark:f[400]}:{main:f[700],light:f[400],dark:f[800]}}(r),x=e.secondary||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:s[200],light:s[50],dark:s[400]}:{main:s[500],light:s[300],dark:s[700]}}(r),k=e.error||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:c.A[500],light:c.A[300],dark:c.A[700]}:{main:c.A[700],light:c.A[400],dark:c.A[800]}}(r),C=e.info||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:d[400],light:d[300],dark:d[700]}:{main:d[700],light:d[500],dark:d[900]}}(r),_=e.success||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:p[400],light:p[300],dark:p[700]}:{main:p[800],light:p[500],dark:p[900]}}(r),$=e.warning||function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light";return"dark"===e?{main:u[400],light:u[300],dark:u[700]}:{main:"#ed6c02",light:u[500],dark:u[900]}}(r);function O(e){return(0,i.eM)(e,g.text.primary)>=v?g.text.primary:m.text.primary}let j=e=>{let{color:t,name:r,mainShade:o=500,lightShade:i=300,darkShade:a=700}=e;if(!(t={...t}).main&&t[o]&&(t.main=t[o]),!t.hasOwnProperty("main"))throw Error((0,n.A)(11,r?" (".concat(r,")"):"",o));if("string"!=typeof t.main)throw Error((0,n.A)(12,r?" (".concat(r,")"):"",JSON.stringify(t.main)));return b(t,"light",i,S),b(t,"dark",a,S),t.contrastText||(t.contrastText=O(t.main)),t};return"light"===r?t=h():"dark"===r&&(t=y()),(0,o.A)({common:{...a},mode:r,primary:j({color:w,name:"primary"}),secondary:j({color:x,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:j({color:k,name:"error"}),warning:j({color:$,name:"warning"}),info:j({color:C,name:"info"}),success:j({color:_,name:"success"}),grey:l.A,contrastThreshold:v,getContrastText:O,augmentColor:j,tonalOffset:S,...t},A)}var S=r(53111),A=r(12961);let w=(e,t,r,n=[])=>{let o=e;t.forEach((e,i)=>{i===t.length-1?Array.isArray(o)?o[Number(e)]=r:o&&"object"==typeof o&&(o[e]=r):o&&"object"==typeof o&&(o[e]||(o[e]=n.includes(e)?[]:{}),o=o[e])})};function x(e,t){var r,n;let{prefix:o,shouldSkipGeneratingVar:i}=t||{},a={},l={},s={};return r=(e,t,r)=>{if(("string"==typeof t||"number"==typeof t)&&(!i||!i(e,t))){var n,c;let i=`--${o?`${o}-`:""}${e.join("-")}`,u=(n=e,"number"==typeof(c=t)?["lineHeight","fontWeight","opacity","zIndex"].some(e=>n.includes(e))||n[n.length-1].toLowerCase().includes("opacity")?c:`${c}px`:c);Object.assign(a,{[i]:u}),w(l,e,`var(${i})`,r),w(s,e,`var(${i}, ${u})`,r)}},n=e=>"vars"===e[0],function e(t,o=[],i=[]){Object.entries(t).forEach(([t,a])=>{n&&(!n||n([...o,t]))||null==a||("object"==typeof a&&Object.keys(a).length>0?e(a,[...o,t],Array.isArray(a)?[...i,t]:i):r([...o,t],a,i))})}(e),{css:a,vars:l,varsWithDefaults:s}}let k=function(e,t={}){let{getSelector:r=function(t,r){let n=i;if("class"===i&&(n=".%s"),"data"===i&&(n="[data-%s]"),i?.startsWith("data-")&&!i.includes("%s")&&(n=`[${i}="%s"]`),t){if("media"===n){if(e.defaultColorScheme===t)return":root";let n=a[t]?.palette?.mode||t;return{[`@media (prefers-color-scheme: ${n})`]:{":root":r}}}if(n)return e.defaultColorScheme===t?`:root, ${n.replace("%s",String(t))}`:n.replace("%s",String(t))}return":root"},disableCssColorScheme:n,colorSchemeSelector:i}=t,{colorSchemes:a={},components:l,defaultColorScheme:s="light",...c}=e,{vars:u,css:f,varsWithDefaults:d}=x(c,t),p=d,h={},{[s]:m,...y}=a;if(Object.entries(y||{}).forEach(([e,r])=>{let{vars:n,css:i,varsWithDefaults:a}=x(r,t);p=(0,o.A)(p,a),h[e]={css:i,vars:n}}),m){let{css:e,vars:r,varsWithDefaults:n}=x(m,t);p=(0,o.A)(p,n),h[s]={css:e,vars:r}}return{vars:p,generateThemeVars:()=>{let e={...u};return Object.entries(h).forEach(([,{vars:t}])=>{e=(0,o.A)(e,t)}),e},generateStyleSheets:()=>{let t=[],o=e.defaultColorScheme||"light";function i(e,r){Object.keys(r).length&&t.push("string"==typeof e?{[e]:{...r}}:e)}i(r(void 0,{...f}),f);let{[o]:l,...s}=h;if(l){let{css:e}=l,t=a[o]?.palette?.mode,s=!n&&t?{colorScheme:t,...e}:{...e};i(r(o,{...s}),s)}return Object.entries(s).forEach(([e,{css:t}])=>{let o=a[e]?.palette?.mode,l=!n&&o?{colorScheme:o,...t}:{...t};i(r(e,{...l}),l)}),t}}};var C=r(31061),_=r(74615),$=r(89856),O=r(88707);function j(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return["".concat(t[0],"px ").concat(t[1],"px ").concat(t[2],"px ").concat(t[3],"px rgba(0,0,0,").concat(.2,")"),"".concat(t[4],"px ").concat(t[5],"px ").concat(t[6],"px ").concat(t[7],"px rgba(0,0,0,").concat(.14,")"),"".concat(t[8],"px ").concat(t[9],"px ").concat(t[10],"px ").concat(t[11],"px rgba(0,0,0,").concat(.12,")")].join(",")}let E=["none",j(0,2,1,-1,0,1,1,0,0,1,3,0),j(0,3,1,-2,0,2,2,0,0,1,5,0),j(0,3,3,-2,0,3,4,0,0,1,8,0),j(0,2,4,-1,0,4,5,0,0,1,10,0),j(0,3,5,-1,0,5,8,0,0,1,14,0),j(0,3,5,-1,0,6,10,0,0,1,18,0),j(0,4,5,-2,0,7,10,1,0,2,16,1),j(0,5,5,-3,0,8,10,1,0,3,14,2),j(0,5,6,-3,0,9,12,1,0,3,16,2),j(0,6,6,-3,0,10,14,1,0,4,18,3),j(0,6,7,-4,0,11,15,1,0,4,20,3),j(0,7,8,-4,0,12,17,2,0,5,22,4),j(0,7,8,-4,0,13,19,2,0,5,24,4),j(0,7,9,-4,0,14,21,2,0,5,26,4),j(0,8,9,-5,0,15,22,2,0,6,28,5),j(0,8,10,-5,0,16,24,2,0,6,30,5),j(0,8,11,-5,0,17,26,2,0,6,32,5),j(0,9,11,-5,0,18,28,2,0,7,34,6),j(0,9,12,-6,0,19,29,2,0,7,36,6),j(0,10,13,-6,0,20,31,3,0,8,38,7),j(0,10,13,-6,0,21,33,3,0,8,40,7),j(0,10,14,-6,0,22,35,3,0,8,42,7),j(0,11,14,-7,0,23,36,3,0,9,44,8),j(0,11,15,-7,0,24,38,3,0,9,46,8)],T={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},M={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function P(e){return"".concat(Math.round(e),"ms")}function N(e){if(!e)return 0;let t=e/36;return Math.min(Math.round((4+15*t**.25+t/5)*10),3e3)}let B={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function I(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={...e};return!function e(t){let r=Object.entries(t);for(let n=0;n<r.length;n++){let[i,a]=r[n];!((0,o.Q)(a)||void 0===a||"string"==typeof a||"boolean"==typeof a||"number"==typeof a||Array.isArray(a))||i.startsWith("unstable_")?delete t[i]:(0,o.Q)(a)&&(t[i]={...a},e(t[i]))}}(t),"import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';\n\nconst theme = ".concat(JSON.stringify(t,null,2),";\n\ntheme.breakpoints = createBreakpoints(theme.breakpoints || {});\ntheme.transitions = createTransitions(theme.transitions || {});\n\nexport default theme;")}let R=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};for(var t,r=arguments.length,i=Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];let{breakpoints:l,mixins:s={},spacing:c,palette:u={},transitions:f={},typography:d={},shape:p,...h}=e;if(e.vars&&void 0===e.generateThemeVars)throw Error((0,n.A)(20));let m=v(u),y=(0,$.A)(e),g=(0,o.A)(y,{mixins:(t=y.breakpoints,{toolbar:{minHeight:56,[t.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[t.up("sm")]:{minHeight:64}},...s}),palette:m,shadows:E.slice(),typography:(0,O.A)(m,d),transitions:function(e){let t={...T,...e.easing},r={...M,...e.duration};return{getAutoHeightDuration:N,create:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["all"],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{duration:o=r.standard,easing:i=t.easeInOut,delay:a=0,...l}=n;return(Array.isArray(e)?e:[e]).map(e=>"".concat(e," ").concat("string"==typeof o?o:P(o)," ").concat(i," ").concat("string"==typeof a?a:P(a))).join(",")},...e,easing:t,duration:r}}(f),zIndex:{...B}});return g=(0,o.A)(g,h),(g=i.reduce((e,t)=>(0,o.A)(e,t),g)).unstable_sxConfig={...C.A,...null==h?void 0:h.unstable_sxConfig},g.unstable_sx=function(e){return(0,_.A)({sx:e,theme:this})},g.toRuntimeSource=I,g};var L=r(47402);let F=[...Array(25)].map((e,t)=>{if(0===t)return"none";let r=(0,L.A)(t);return"linear-gradient(rgba(255 255 255 / ".concat(r,"), rgba(255 255 255 / ").concat(r,"))")});function D(e){return{inputPlaceholder:"dark"===e?.5:.42,inputUnderline:"dark"===e?.7:.42,switchTrackDisabled:"dark"===e?.2:.12,switchTrack:"dark"===e?.3:.38}}function W(e){return"dark"===e?F:[]}function z(e){var t;return!!e[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!e[0].match(/sxConfig$/)||"palette"===e[0]&&!!(null==(t=e[1])?void 0:t.match(/(mode|contrastThreshold|tonalOffset)/))}function K(e,t,r){!e[t]&&r&&(e[t]=r)}function H(e){return"string"==typeof e&&e.startsWith("hsl")?(0,i.YL)(e):e}function V(e,t){"".concat(t,"Channel")in e||(e["".concat(t,"Channel")]=(0,i.Me)(H(e[t]),"MUI: Can't create `palette.".concat(t,"Channel` because `palette.").concat(t,"` is not one of these formats: #nnn, #nnnnnn, rgb(), rgba(), hsl(), hsla(), color().")+"\n"+"To suppress this warning, you need to explicitly provide the `palette.".concat(t,'Channel` as a string (in rgb format, for example "12 12 12") or undefined if you want to remove the channel token.')))}let G=e=>{try{return e()}catch(e){}},X=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"mui";return function(e=""){return(t,...r)=>`var(--${e?`${e}-`:""}${t}${function t(...r){if(!r.length)return"";let n=r[0];return"string"!=typeof n||n.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, ${n}`:`, var(--${e?`${e}-`:""}${n}${t(...r.slice(1))})`}(...r)})`}(e)};function U(e,t,r,n){if(!t)return;t=!0===t?{}:t;let o="dark"===n?"dark":"light";if(!r){e[n]=function(e){let{palette:t={mode:"light"},opacity:r,overlays:n,...o}=e,i=v(t);return{palette:i,opacity:{...D(i.mode),...r},overlays:n||W(i.mode),...o}}({...t,palette:{mode:o,...null==t?void 0:t.palette}});return}let{palette:i,...a}=R({...r,palette:{mode:o,...null==t?void 0:t.palette}});return e[n]={...t,palette:i,opacity:{...D(o),...null==t?void 0:t.opacity},overlays:(null==t?void 0:t.overlays)||W(o)},a}function Y(e,t,r){e.colorSchemes&&r&&(e.colorSchemes[t]={...!0!==r&&r,palette:v({...!0===r?{}:r.palette,mode:t})})}function q(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];let{palette:l,cssVariables:s=!1,colorSchemes:c=!l?{light:!0}:void 0,defaultColorScheme:u=null==l?void 0:l.mode,...f}=e,d=u||"light",p=null==c?void 0:c[d],h={...c,...l?{[d]:{..."boolean"!=typeof p&&p,palette:l}}:void 0};if(!1===s){if(!("colorSchemes"in e))return R(e,...r);let t=l;"palette"in e||!h[d]||(!0!==h[d]?t=h[d].palette:"dark"===d&&(t={mode:"dark"}));let n=R({...e,palette:t},...r);return n.defaultColorScheme=d,n.colorSchemes=h,"light"===n.palette.mode&&(n.colorSchemes.light={...!0!==h.light&&h.light,palette:n.palette},Y(n,"dark",h.dark)),"dark"===n.palette.mode&&(n.colorSchemes.dark={...!0!==h.dark&&h.dark,palette:n.palette},Y(n,"light",h.light)),n}return l||"light"in h||"light"!==d||(h.light=!0),function(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};for(var r,a=arguments.length,l=Array(a>1?a-1:0),s=1;s<a;s++)l[s-1]=arguments[s];let{colorSchemes:c={light:!0},defaultColorScheme:u,disableCssColorScheme:f=!1,cssVarPrefix:d="mui",shouldSkipGeneratingVar:p=z,colorSchemeSelector:h=c.light&&c.dark?"media":void 0,rootSelector:m=":root",...y}=t,g=Object.keys(c)[0],b=u||(c.light&&"light"!==g?"light":g),v=X(d),{[b]:w,light:x,dark:$,...O}=c,j={...O},E=w;if(("dark"!==b||"dark"in c)&&("light"!==b||"light"in c)||(E=!0),!E)throw Error((0,n.A)(21,b));let T=U(j,E,y,b);x&&!j.light&&U(j,x,void 0,"light"),$&&!j.dark&&U(j,$,void 0,"dark");let M={defaultColorScheme:b,...T,cssVarPrefix:d,colorSchemeSelector:h,rootSelector:m,getCssVar:v,colorSchemes:j,font:{...function(e){let t={};return Object.entries(e).forEach(e=>{let[r,n]=e;"object"==typeof n&&(t[r]=`${n.fontStyle?`${n.fontStyle} `:""}${n.fontVariant?`${n.fontVariant} `:""}${n.fontWeight?`${n.fontWeight} `:""}${n.fontStretch?`${n.fontStretch} `:""}${n.fontSize||""}${n.lineHeight?`/${n.lineHeight} `:""}${n.fontFamily||""}`)}),t}(T.typography),...T.font},spacing:"number"==typeof(r=y.spacing)?"".concat(r,"px"):"string"==typeof r||"function"==typeof r||Array.isArray(r)?r:"8px"};Object.keys(M.colorSchemes).forEach(e=>{let t=M.colorSchemes[e].palette,r=e=>{let r=e.split("-"),n=r[1],o=r[2];return v(e,t[n][o])};if("light"===t.mode&&(K(t.common,"background","#fff"),K(t.common,"onBackground","#000")),"dark"===t.mode&&(K(t.common,"background","#000"),K(t.common,"onBackground","#fff")),["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"].forEach(e=>{t[e]||(t[e]={})}),"light"===t.mode){K(t.Alert,"errorColor",(0,i.Nd)(t.error.light,.6)),K(t.Alert,"infoColor",(0,i.Nd)(t.info.light,.6)),K(t.Alert,"successColor",(0,i.Nd)(t.success.light,.6)),K(t.Alert,"warningColor",(0,i.Nd)(t.warning.light,.6)),K(t.Alert,"errorFilledBg",r("palette-error-main")),K(t.Alert,"infoFilledBg",r("palette-info-main")),K(t.Alert,"successFilledBg",r("palette-success-main")),K(t.Alert,"warningFilledBg",r("palette-warning-main")),K(t.Alert,"errorFilledColor",G(()=>t.getContrastText(t.error.main))),K(t.Alert,"infoFilledColor",G(()=>t.getContrastText(t.info.main))),K(t.Alert,"successFilledColor",G(()=>t.getContrastText(t.success.main))),K(t.Alert,"warningFilledColor",G(()=>t.getContrastText(t.warning.main))),K(t.Alert,"errorStandardBg",(0,i.j4)(t.error.light,.9)),K(t.Alert,"infoStandardBg",(0,i.j4)(t.info.light,.9)),K(t.Alert,"successStandardBg",(0,i.j4)(t.success.light,.9)),K(t.Alert,"warningStandardBg",(0,i.j4)(t.warning.light,.9)),K(t.Alert,"errorIconColor",r("palette-error-main")),K(t.Alert,"infoIconColor",r("palette-info-main")),K(t.Alert,"successIconColor",r("palette-success-main")),K(t.Alert,"warningIconColor",r("palette-warning-main")),K(t.AppBar,"defaultBg",r("palette-grey-100")),K(t.Avatar,"defaultBg",r("palette-grey-400")),K(t.Button,"inheritContainedBg",r("palette-grey-300")),K(t.Button,"inheritContainedHoverBg",r("palette-grey-A100")),K(t.Chip,"defaultBorder",r("palette-grey-400")),K(t.Chip,"defaultAvatarColor",r("palette-grey-700")),K(t.Chip,"defaultIconColor",r("palette-grey-700")),K(t.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),K(t.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),K(t.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),K(t.LinearProgress,"primaryBg",(0,i.j4)(t.primary.main,.62)),K(t.LinearProgress,"secondaryBg",(0,i.j4)(t.secondary.main,.62)),K(t.LinearProgress,"errorBg",(0,i.j4)(t.error.main,.62)),K(t.LinearProgress,"infoBg",(0,i.j4)(t.info.main,.62)),K(t.LinearProgress,"successBg",(0,i.j4)(t.success.main,.62)),K(t.LinearProgress,"warningBg",(0,i.j4)(t.warning.main,.62)),K(t.Skeleton,"bg","rgba(".concat(r("palette-text-primaryChannel")," / 0.11)")),K(t.Slider,"primaryTrack",(0,i.j4)(t.primary.main,.62)),K(t.Slider,"secondaryTrack",(0,i.j4)(t.secondary.main,.62)),K(t.Slider,"errorTrack",(0,i.j4)(t.error.main,.62)),K(t.Slider,"infoTrack",(0,i.j4)(t.info.main,.62)),K(t.Slider,"successTrack",(0,i.j4)(t.success.main,.62)),K(t.Slider,"warningTrack",(0,i.j4)(t.warning.main,.62));let e=(0,i.Y9)(t.background.default,.8);K(t.SnackbarContent,"bg",e),K(t.SnackbarContent,"color",G(()=>t.getContrastText(e))),K(t.SpeedDialAction,"fabHoverBg",(0,i.Y9)(t.background.paper,.15)),K(t.StepConnector,"border",r("palette-grey-400")),K(t.StepContent,"border",r("palette-grey-400")),K(t.Switch,"defaultColor",r("palette-common-white")),K(t.Switch,"defaultDisabledColor",r("palette-grey-100")),K(t.Switch,"primaryDisabledColor",(0,i.j4)(t.primary.main,.62)),K(t.Switch,"secondaryDisabledColor",(0,i.j4)(t.secondary.main,.62)),K(t.Switch,"errorDisabledColor",(0,i.j4)(t.error.main,.62)),K(t.Switch,"infoDisabledColor",(0,i.j4)(t.info.main,.62)),K(t.Switch,"successDisabledColor",(0,i.j4)(t.success.main,.62)),K(t.Switch,"warningDisabledColor",(0,i.j4)(t.warning.main,.62)),K(t.TableCell,"border",(0,i.j4)((0,i.Cg)(t.divider,1),.88)),K(t.Tooltip,"bg",(0,i.Cg)(t.grey[700],.92))}if("dark"===t.mode){K(t.Alert,"errorColor",(0,i.j4)(t.error.light,.6)),K(t.Alert,"infoColor",(0,i.j4)(t.info.light,.6)),K(t.Alert,"successColor",(0,i.j4)(t.success.light,.6)),K(t.Alert,"warningColor",(0,i.j4)(t.warning.light,.6)),K(t.Alert,"errorFilledBg",r("palette-error-dark")),K(t.Alert,"infoFilledBg",r("palette-info-dark")),K(t.Alert,"successFilledBg",r("palette-success-dark")),K(t.Alert,"warningFilledBg",r("palette-warning-dark")),K(t.Alert,"errorFilledColor",G(()=>t.getContrastText(t.error.dark))),K(t.Alert,"infoFilledColor",G(()=>t.getContrastText(t.info.dark))),K(t.Alert,"successFilledColor",G(()=>t.getContrastText(t.success.dark))),K(t.Alert,"warningFilledColor",G(()=>t.getContrastText(t.warning.dark))),K(t.Alert,"errorStandardBg",(0,i.Nd)(t.error.light,.9)),K(t.Alert,"infoStandardBg",(0,i.Nd)(t.info.light,.9)),K(t.Alert,"successStandardBg",(0,i.Nd)(t.success.light,.9)),K(t.Alert,"warningStandardBg",(0,i.Nd)(t.warning.light,.9)),K(t.Alert,"errorIconColor",r("palette-error-main")),K(t.Alert,"infoIconColor",r("palette-info-main")),K(t.Alert,"successIconColor",r("palette-success-main")),K(t.Alert,"warningIconColor",r("palette-warning-main")),K(t.AppBar,"defaultBg",r("palette-grey-900")),K(t.AppBar,"darkBg",r("palette-background-paper")),K(t.AppBar,"darkColor",r("palette-text-primary")),K(t.Avatar,"defaultBg",r("palette-grey-600")),K(t.Button,"inheritContainedBg",r("palette-grey-800")),K(t.Button,"inheritContainedHoverBg",r("palette-grey-700")),K(t.Chip,"defaultBorder",r("palette-grey-700")),K(t.Chip,"defaultAvatarColor",r("palette-grey-300")),K(t.Chip,"defaultIconColor",r("palette-grey-300")),K(t.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),K(t.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),K(t.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),K(t.LinearProgress,"primaryBg",(0,i.Nd)(t.primary.main,.5)),K(t.LinearProgress,"secondaryBg",(0,i.Nd)(t.secondary.main,.5)),K(t.LinearProgress,"errorBg",(0,i.Nd)(t.error.main,.5)),K(t.LinearProgress,"infoBg",(0,i.Nd)(t.info.main,.5)),K(t.LinearProgress,"successBg",(0,i.Nd)(t.success.main,.5)),K(t.LinearProgress,"warningBg",(0,i.Nd)(t.warning.main,.5)),K(t.Skeleton,"bg","rgba(".concat(r("palette-text-primaryChannel")," / 0.13)")),K(t.Slider,"primaryTrack",(0,i.Nd)(t.primary.main,.5)),K(t.Slider,"secondaryTrack",(0,i.Nd)(t.secondary.main,.5)),K(t.Slider,"errorTrack",(0,i.Nd)(t.error.main,.5)),K(t.Slider,"infoTrack",(0,i.Nd)(t.info.main,.5)),K(t.Slider,"successTrack",(0,i.Nd)(t.success.main,.5)),K(t.Slider,"warningTrack",(0,i.Nd)(t.warning.main,.5));let e=(0,i.Y9)(t.background.default,.98);K(t.SnackbarContent,"bg",e),K(t.SnackbarContent,"color",G(()=>t.getContrastText(e))),K(t.SpeedDialAction,"fabHoverBg",(0,i.Y9)(t.background.paper,.15)),K(t.StepConnector,"border",r("palette-grey-600")),K(t.StepContent,"border",r("palette-grey-600")),K(t.Switch,"defaultColor",r("palette-grey-300")),K(t.Switch,"defaultDisabledColor",r("palette-grey-600")),K(t.Switch,"primaryDisabledColor",(0,i.Nd)(t.primary.main,.55)),K(t.Switch,"secondaryDisabledColor",(0,i.Nd)(t.secondary.main,.55)),K(t.Switch,"errorDisabledColor",(0,i.Nd)(t.error.main,.55)),K(t.Switch,"infoDisabledColor",(0,i.Nd)(t.info.main,.55)),K(t.Switch,"successDisabledColor",(0,i.Nd)(t.success.main,.55)),K(t.Switch,"warningDisabledColor",(0,i.Nd)(t.warning.main,.55)),K(t.TableCell,"border",(0,i.Nd)((0,i.Cg)(t.divider,1),.68)),K(t.Tooltip,"bg",(0,i.Cg)(t.grey[700],.92))}V(t.background,"default"),V(t.background,"paper"),V(t.common,"background"),V(t.common,"onBackground"),V(t,"divider"),Object.keys(t).forEach(e=>{let r=t[e];"tonalOffset"!==e&&r&&"object"==typeof r&&(r.main&&K(t[e],"mainChannel",(0,i.Me)(H(r.main))),r.light&&K(t[e],"lightChannel",(0,i.Me)(H(r.light))),r.dark&&K(t[e],"darkChannel",(0,i.Me)(H(r.dark))),r.contrastText&&K(t[e],"contrastTextChannel",(0,i.Me)(H(r.contrastText))),"text"===e&&(V(t[e],"primary"),V(t[e],"secondary")),"action"===e&&(r.active&&V(t[e],"active"),r.selected&&V(t[e],"selected")))})});let P={prefix:d,disableCssColorScheme:f,shouldSkipGeneratingVar:p,getSelector:(e=M=l.reduce((e,t)=>(0,o.A)(e,t),M),(t,r)=>{let n=e.rootSelector||":root",o=e.colorSchemeSelector,i=o;if("class"===o&&(i=".%s"),"data"===o&&(i="[data-%s]"),(null==o?void 0:o.startsWith("data-"))&&!o.includes("%s")&&(i="[".concat(o,'="%s"]')),e.defaultColorScheme===t){if("dark"===t){let o,a={};return((o=e.cssVarPrefix,[...[...Array(25)].map((e,t)=>"--".concat(o?"".concat(o,"-"):"","overlays-").concat(t)),"--".concat(o?"".concat(o,"-"):"","palette-AppBar-darkBg"),"--".concat(o?"".concat(o,"-"):"","palette-AppBar-darkColor")]).forEach(e=>{a[e]=r[e],delete r[e]}),"media"===i)?{[n]:r,"@media (prefers-color-scheme: dark)":{[n]:a}}:i?{[i.replace("%s",t)]:a,["".concat(n,", ").concat(i.replace("%s",t))]:r}:{[n]:{...r,...a}}}if(i&&"media"!==i)return"".concat(n,", ").concat(i.replace("%s",String(t)))}else if(t){if("media"===i)return{["@media (prefers-color-scheme: ".concat(String(t),")")]:{[n]:r}};if(i)return i.replace("%s",String(t))}return n})},{vars:N,generateThemeVars:B,generateStyleSheets:R}=k(M,P);return M.vars=N,Object.entries(M.colorSchemes[M.defaultColorScheme]).forEach(e=>{let[t,r]=e;M[t]=r}),M.generateThemeVars=B,M.generateStyleSheets=R,M.generateSpacing=function(){return(0,S.A)(y.spacing,(0,A.LX)(this))},M.getColorSchemeSelector=function(e){return"media"===h?`@media (prefers-color-scheme: ${e})`:h?h.startsWith("data-")&&!h.includes("%s")?`[${h}="${e}"] &`:"class"===h?`.${e} &`:"data"===h?`[data-${e}] &`:`${h.replace("%s",e)} &`:"&"},M.spacing=M.generateSpacing(),M.shouldSkipGeneratingVar=p,M.unstable_sxConfig={...C.A,...null==y?void 0:y.unstable_sxConfig},M.unstable_sx=function(e){return(0,_.A)({sx:e,theme:this})},M.toRuntimeSource=I,M}({...f,colorSchemes:h,defaultColorScheme:d,..."boolean"!=typeof s&&s},...r)}},69135:(e,t,r)=>{"use strict";function n(e,...t){let r=new URL(`https://mui.com/production-error/?code=${e}`);return t.forEach(e=>r.searchParams.append("args[]",e)),`Minified MUI error #${e}; visit ${r} for the full message.`}r.d(t,{A:()=>n})},72487:(e,t,r)=>{"use strict";r.d(t,{EU:()=>s,NI:()=>l,kW:()=>u,vf:()=>c,zu:()=>o});var n=r(903);let o={xs:0,sm:600,md:900,lg:1200,xl:1536},i={keys:["xs","sm","md","lg","xl"],up:e=>`@media (min-width:${o[e]}px)`},a={containerQueries:e=>({up:t=>{let r="number"==typeof t?t:o[t]||t;return"number"==typeof r&&(r=`${r}px`),e?`@container ${e} (min-width:${r})`:`@container (min-width:${r})`}})};function l(e,t,r){let l=e.theme||{};if(Array.isArray(t)){let e=l.breakpoints||i;return t.reduce((n,o,i)=>(n[e.up(e.keys[i])]=r(t[i]),n),{})}if("object"==typeof t){let e=l.breakpoints||i;return Object.keys(t).reduce((i,s)=>{if((0,n.ob)(e.keys,s)){let e=(0,n.CT)(l.containerQueries?l:a,s);e&&(i[e]=r(t[s],s))}else Object.keys(e.values||o).includes(s)?i[e.up(s)]=r(t[s],s):i[s]=t[s];return i},{})}return r(t)}function s(e={}){return e.keys?.reduce((t,r)=>(t[e.up(r)]={},t),{})||{}}function c(e,t){return e.reduce((e,t)=>{let r=e[t];return r&&0!==Object.keys(r).length||delete e[t],e},t)}function u({values:e,breakpoints:t,base:r}){let n,o=Object.keys(r||function(e,t){if("object"!=typeof e)return{};let r={},n=Object.keys(t);return Array.isArray(e)?n.forEach((t,n)=>{n<e.length&&(r[t]=!0)}):n.forEach(t=>{null!=e[t]&&(r[t]=!0)}),r}(e,t));return 0===o.length?e:o.reduce((t,r,o)=>(Array.isArray(e)?(t[r]=null!=e[o]?e[o]:e[n],n=o):"object"==typeof e?(t[r]=null!=e[r]?e[r]:e[n],n=r):t[r]=e,t),{})}},74363:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var n=r(37876),o=r(31777);r(36048);var i=r(10339),a=r(83129),l=r(40345),s=r(80400),c=r(84593);let u=(0,a.HY)({transcription:l.Ay,aiResponse:s.Ay,history:c.A}),f=(0,i.U1)({reducer:u});var d=r(50161),p=r(68050),h=r(98291),m=r(743);let y=(0,r(68988).A)({palette:{primary:{main:"#4F46E5",light:"#7C3AED",dark:"#3730A3",contrastText:"#ffffff"},secondary:{main:"#6366F1",light:"#8B5CF6",dark:"#4338CA",contrastText:"#ffffff"},error:{main:h.A.A400},background:{default:m.A[100],paper:"#ffffff"},text:{primary:m.A[900],secondary:m.A[700]}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h4:{fontWeight:700,color:"#333333",marginBottom:"0.75em"},h5:{fontWeight:600,color:"#444444",marginBottom:"0.5em"},h6:{fontWeight:600,color:"#555555",marginBottom:"0.5em"},button:{textTransform:"none",fontWeight:500,letterSpacing:"0.5px"},body1:{lineHeight:1.6},caption:{color:m.A[600]}},shape:{borderRadius:8},components:{MuiPaper:{styleOverrides:{root:{boxShadow:"0px 5px 15px rgba(0,0,0,0.08)"}}},MuiButton:{styleOverrides:{root:{padding:"10px 20px",boxShadow:"none","&:hover":{boxShadow:"0px 2px 8px rgba(0,0,0,0.1)"}},containedPrimary:{"&:hover":{backgroundColor:"#3730A3"}},containedSecondary:{"&:hover":{backgroundColor:"#4338CA"}}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{"& fieldset":{},"&:hover fieldset":{borderColor:"#4F46E5"}},"& .MuiInputLabel-root.Mui-focused":{color:"#4F46E5"}}}},MuiIconButton:{styleOverrides:{root:{"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.06)"}}}},MuiChip:{styleOverrides:{root:{fontWeight:500}}},MuiAppBar:{styleOverrides:{root:{boxShadow:"0px 2px 4px -1px rgba(0,0,0,0.06), 0px 4px 5px 0px rgba(0,0,0,0.04), 0px 1px 10px 0px rgba(0,0,0,0.03)"}}},MuiList:{styleOverrides:{root:{"& .MuiListItem-root":{borderRadius:8}}}},MuiCard:{styleOverrides:{root:{}}}}});function g(e){let{Component:t,pageProps:r}=e;return(0,n.jsx)(o.Kq,{store:f,children:(0,n.jsxs)(d.A,{theme:y,children:[(0,n.jsx)(p.Ay,{}),(0,n.jsx)(t,{...r})]})})}},74615:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(93725),o=r(45519),i=r(50327),a=r(72487),l=r(903),s=r(31061);let c=function(){function e(e,t,r,o){let l={[e]:t,theme:r},s=o[e];if(!s)return{[e]:t};let{cssProperty:c=e,themeKey:u,transform:f,style:d}=s;if(null==t)return null;if("typography"===u&&"inherit"===t)return{[e]:t};let p=(0,i.Yn)(r,u)||{};return d?d(l):(0,a.NI)(l,t,t=>{let r=(0,i.BO)(p,f,t);return(t===r&&"string"==typeof t&&(r=(0,i.BO)(p,f,`${e}${"default"===t?"":(0,n.A)(t)}`,t)),!1===c)?r:{[c]:r}})}return function t(r){let{sx:n,theme:i={}}=r||{};if(!n)return null;let c=i.unstable_sxConfig??s.A;function u(r){let n=r;if("function"==typeof r)n=r(i);else if("object"!=typeof r)return r;if(!n)return null;let s=(0,a.EU)(i.breakpoints),u=Object.keys(s),f=s;return Object.keys(n).forEach(r=>{var l;let s=(l=n[r],"function"==typeof l?l(i):l);if(null!=s)if("object"==typeof s)if(c[r])f=(0,o.A)(f,e(r,s,i,c));else{let e=(0,a.NI)({theme:i},s,e=>({[r]:e}));!function(...e){let t=new Set(e.reduce((e,t)=>e.concat(Object.keys(t)),[]));return e.every(e=>t.size===Object.keys(e).length)}(e,s)?f=(0,o.A)(f,e):f[r]=t({sx:s,theme:i})}else f=(0,o.A)(f,e(r,s,i,c))}),(0,l._S)(i,(0,a.vf)(u,f))}return Array.isArray(n)?n.map(u):u(n)}}();c.filterProps=["sx"];let u=c},74849:(e,t,r)=>{"use strict";function n(e,t,r){var n="";return r.split(" ").forEach(function(r){void 0!==e[r]?t.push(e[r]+";"):r&&(n+=r+" ")}),n}r.d(t,{Rk:()=>n,SF:()=>o,sk:()=>i});var o=function(e,t,r){var n=e.key+"-"+t.name;!1===r&&void 0===e.registered[n]&&(e.registered[n]=t.styles)},i=function(e,t,r){o(e,t,r);var n=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var i=t;do e.insert(t===i?"."+n:"",i,e.sheet,!0),i=i.next;while(void 0!==i)}}},76975:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(14232),o=r(21398);let i=function(e=null){let t=n.useContext(o.T);return t&&0!==Object.keys(t).length?t:e}},78455:(e,t,r)=>{"use strict";r.d(t,{i:()=>l,s:()=>a});var n,o=r(14232),i=!!(n||(n=r.t(o,2))).useInsertionEffect&&(n||(n=r.t(o,2))).useInsertionEffect,a=i||function(e){return e()},l=i||o.useLayoutEffect},80400:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a,UM:()=>o});let n=(0,r(10339).Z0)({name:"aiResponse",initialState:"",reducers:{setAIResponse:(e,t)=>t.payload,appendAIResponse:(e,t)=>e+t.payload}}),{setAIResponse:o,appendAIResponse:i}=n.actions,a=n.reducer},83129:(e,t,r)=>{"use strict";function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>c,Qd:()=>l,Tw:()=>f,Zz:()=>u,ve:()=>d,y$:()=>s});var o="function"==typeof Symbol&&Symbol.observable||"@@observable",i=()=>Math.random().toString(36).substring(7).split("").join("."),a={INIT:`@@redux/INIT${i()}`,REPLACE:`@@redux/REPLACE${i()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${i()}`};function l(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function s(e,t,r){if("function"!=typeof e)throw Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(s)(e,t)}let i=e,c=t,u=new Map,f=u,d=0,p=!1;function h(){f===u&&(f=new Map,u.forEach((e,t)=>{f.set(t,e)}))}function m(){if(p)throw Error(n(3));return c}function y(e){if("function"!=typeof e)throw Error(n(4));if(p)throw Error(n(5));let t=!0;h();let r=d++;return f.set(r,e),function(){if(t){if(p)throw Error(n(6));t=!1,h(),f.delete(r),u=null}}}function g(e){if(!l(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(p)throw Error(n(9));try{p=!0,c=i(c,e)}finally{p=!1}return(u=f).forEach(e=>{e()}),e}return g({type:a.INIT}),{dispatch:g,subscribe:y,getState:m,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));i=e,g({type:a.REPLACE})},[o]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(m())}return t(),{unsubscribe:y(t)}},[o](){return this}}}}}function c(e){let t,r=Object.keys(e),o={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(o[n]=e[n])}let i=Object.keys(o);try{Object.keys(o).forEach(e=>{let t=o[e];if(void 0===t(void 0,{type:a.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:a.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let a=!1,l={};for(let t=0;t<i.length;t++){let s=i[t],c=o[s],u=e[s],f=c(u,r);if(void 0===f)throw r&&r.type,Error(n(14));l[s]=f,a=a||f!==u}return(a=a||i.length!==Object.keys(e).length)?l:e}}function u(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function f(...e){return t=>(r,o)=>{let i=t(r,o),a=()=>{throw Error(n(15))},l={getState:i.getState,dispatch:(e,...t)=>a(e,...t)};return a=u(...e.map(e=>e(l)))(i.dispatch),{...i,dispatch:a}}}function d(e){return l(e)&&"type"in e&&"string"==typeof e.type}},84593:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,P:()=>o});let n=(0,r(10339).Z0)({name:"history",initialState:[],reducers:{addToHistory:(e,t)=>{e.push(t.payload)}}}),{addToHistory:o}=n.actions,i=n.reducer},87282:(e,t,r)=>{"use strict";e.exports=r(24802)},88707:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(12535);let o={textTransform:"uppercase"},i='"Roboto", "Helvetica", "Arial", sans-serif';function a(e,t){let{fontFamily:r=i,fontSize:a=14,fontWeightLight:l=300,fontWeightRegular:s=400,fontWeightMedium:c=500,fontWeightBold:u=700,htmlFontSize:f=16,allVariants:d,pxToRem:p,...h}="function"==typeof t?t(e):t,m=a/14,y=p||(e=>"".concat(e/f*m,"rem")),g=(e,t,n,o,a)=>({fontFamily:r,fontWeight:e,fontSize:y(t),lineHeight:n,...r===i?{letterSpacing:"".concat(Math.round(o/t*1e5)/1e5,"em")}:{},...a,...d}),b={h1:g(l,96,1.167,-1.5),h2:g(l,60,1.2,-.5),h3:g(s,48,1.167,0),h4:g(s,34,1.235,.25),h5:g(s,24,1.334,0),h6:g(c,20,1.6,.15),subtitle1:g(s,16,1.75,.15),subtitle2:g(c,14,1.57,.1),body1:g(s,16,1.5,.15),body2:g(s,14,1.43,.15),button:g(c,14,1.75,.4,o),caption:g(s,12,1.66,.4),overline:g(s,12,2.66,1,o),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return(0,n.A)({htmlFontSize:f,pxToRem:y,fontFamily:r,fontSize:a,fontWeightLight:l,fontWeightRegular:s,fontWeightMedium:c,fontWeightBold:u,...b},h,{clone:!1})}},89856:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(12535),o=r(903);let i={borderRadius:4};var a=r(53111),l=r(74615),s=r(31061);function c(e,t){if(this.vars){if(!this.colorSchemes?.[e]||"function"!=typeof this.getColorSchemeSelector)return{};let r=this.getColorSchemeSelector(e);return"&"===r?t:((r.includes("data-")||r.includes("."))&&(r=`*:where(${r.replace(/\s*&$/,"")}) &`),{[r]:t})}return this.palette.mode===e?t:{}}let u=function(e={},...t){let{breakpoints:r={},palette:u={},spacing:f,shape:d={},...p}=e,h=function(e){let{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:r="px",step:n=5,...o}=e,i=(e=>{let t=Object.keys(e).map(t=>({key:t,val:e[t]}))||[];return t.sort((e,t)=>e.val-t.val),t.reduce((e,t)=>({...e,[t.key]:t.val}),{})})(t),a=Object.keys(i);function l(e){let n="number"==typeof t[e]?t[e]:e;return`@media (min-width:${n}${r})`}function s(e){let o="number"==typeof t[e]?t[e]:e;return`@media (max-width:${o-n/100}${r})`}function c(e,o){let i=a.indexOf(o);return`@media (min-width:${"number"==typeof t[e]?t[e]:e}${r}) and (max-width:${(-1!==i&&"number"==typeof t[a[i]]?t[a[i]]:o)-n/100}${r})`}return{keys:a,values:i,up:l,down:s,between:c,only:function(e){return a.indexOf(e)+1<a.length?c(e,a[a.indexOf(e)+1]):l(e)},not:function(e){let t=a.indexOf(e);return 0===t?l(a[1]):t===a.length-1?s(a[t]):c(e,a[a.indexOf(e)+1]).replace("@media","@media not all and")},unit:r,...o}}(r),m=(0,a.A)(f),y=(0,n.A)({breakpoints:h,direction:"ltr",components:{},palette:{mode:"light",...u},spacing:m,shape:{...i,...d}},p);return(y=(0,o.Ay)(y)).applyStyles=c,(y=t.reduce((e,t)=>(0,n.A)(e,t),y)).unstable_sxConfig={...s.A,...p?.unstable_sxConfig},y.unstable_sx=function(e){return(0,l.A)({sx:e,theme:this})},y}},90809:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(12535),o=r(31061);function i(e){let t,{sx:r,...i}=e,{systemProps:a,otherProps:l}=(e=>{let t={systemProps:{},otherProps:{}},r=e?.theme?.unstable_sxConfig??o.A;return Object.keys(e).forEach(n=>{r[n]?t.systemProps[n]=e[n]:t.otherProps[n]=e[n]}),t})(i);return t=Array.isArray(r)?[a,...r]:"function"==typeof r?(...e)=>{let t=r(...e);return(0,n.Q)(t)?{...a,...t}:a}:{...a,...r},{...l,sx:t}}},93725:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(69135);function o(e){if("string"!=typeof e)throw Error((0,n.A)(7));return e.charAt(0).toUpperCase()+e.slice(1)}},98291:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n={50:"#ffebee",100:"#ffcdd2",200:"#ef9a9a",300:"#e57373",400:"#ef5350",500:"#f44336",600:"#e53935",700:"#d32f2f",800:"#c62828",900:"#b71c1c",A100:"#ff8a80",A200:"#ff5252",A400:"#ff1744",A700:"#d50000"}},99659:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(14232);let o="undefined"!=typeof window?n.useLayoutEffect:n.useEffect}},e=>{var t=t=>e(e.s=t);e.O(0,[593,792],()=>(t(56556),t(48253))),_N_E=e.O()}]);