(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1],{31026:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(14232);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=i(e,r)),t&&(o.current=i(t,r))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42343:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return s}});let r=n(14232),o=n(74754),i="function"==typeof IntersectionObserver,a=new Map,l=[];function s(e){let{rootRef:t,rootMargin:n,disabled:s}=e,c=s||!i,[u,d]=(0,r.useState)(!1),p=(0,r.useRef)(null),f=(0,r.useCallback)(e=>{p.current=e},[]);return(0,r.useEffect)(()=>{if(i){if(c||u)return;let e=p.current;if(e&&e.tagName)return function(e,t,n){let{id:r,observer:o,elements:i}=function(e){let t,n={root:e.root||null,margin:e.rootMargin||""},r=l.find(e=>e.root===n.root&&e.margin===n.margin);if(r&&(t=a.get(r)))return t;let o=new Map;return t={id:n,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),n=e.isIntersecting||e.intersectionRatio>0;t&&n&&t(n)})},e),elements:o},l.push(n),a.set(n,t),t}(n);return i.set(e,t),o.observe(e),function(){if(i.delete(e),o.unobserve(e),0===i.size){o.disconnect(),a.delete(r);let e=l.findIndex(e=>e.root===r.root&&e.margin===r.margin);e>-1&&l.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:n})}else if(!u){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[c,n,t,u,p.current]),[f,u,(0,r.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48230:(e,t,n)=>{e.exports=n(81639)},63724:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},72022:(e,t,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/landing",function(){return n(75945)}])},75945:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>P});var r=n(37876),o=n(31057);let i=(0,o.A)((0,r.jsx)("path",{d:"m12 4-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"}),"ArrowForward"),a=(0,o.A)((0,r.jsx)("path",{d:"M9.4 16.6 4.8 12l4.6-4.6L8 6l-6 6 6 6zm5.2 0 4.6-4.6-4.6-4.6L16 6l6 6-6 6z"}),"Code"),l=(0,o.A)((0,r.jsx)("path",{d:"M21 6h-2v9H6v2c0 .55.45 1 1 1h11l4 4V7c0-.55-.45-1-1-1m-4 6V3c0-.55-.45-1-1-1H3c-.55 0-1 .45-1 1v14l4-4h10c.55 0 1-.45 1-1"}),"QuestionAnswer"),s=(0,o.A)([(0,r.jsx)("circle",{cx:"9",cy:"9",r:"4"},"0"),(0,r.jsx)("path",{d:"M9 15c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4m7.76-9.64-1.68 1.69c.84 1.18.84 2.71 0 3.89l1.68 1.69c2.02-2.02 2.02-5.07 0-7.27M20.07 2l-1.63 1.63c2.77 3.02 2.77 7.56 0 10.74L20.07 16c3.9-3.89 3.91-9.95 0-14"},"1")],"RecordVoiceOver");var c=n(63817);let u=(0,o.A)((0,r.jsx)("path",{d:"m20.38 8.57-1.23 1.85a8 8 0 0 1-.22 7.58H5.07A8 8 0 0 1 15.58 6.85l1.85-1.23A10 10 0 0 0 3.35 19a2 2 0 0 0 1.72 1h13.85a2 2 0 0 0 1.74-1 10 10 0 0 0-.27-10.44zm-9.79 6.84a2 2 0 0 0 2.83 0l5.66-8.49-8.49 5.66a2 2 0 0 0 0 2.83"}),"Speed");var d=n(11494),p=n(11951),f=n(24422),h=n(712),g=n(77018),x=n(2487),b=n(64410),m=n(54773),v=n(77328),y=n.n(v),j=n(48230),A=n.n(j);let w=(0,m.Ay)(d.A)(e=>{let{theme:t}=e;return{background:"linear-gradient(135deg, ".concat(t.palette.primary.main," 0%, ").concat(t.palette.secondary.main," 100%)"),color:t.palette.primary.contrastText,padding:t.spacing(16,2),textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"80vh",position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(1px)"}}}),_=(0,m.Ay)(p.A)(e=>{let{theme:t}=e;return{padding:t.spacing(4),textAlign:"center",height:"100%",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"flex-start",borderRadius:t.spacing(2),border:"1px solid ".concat(t.palette.grey[200]),boxShadow:"0px 2px 8px rgba(0,0,0,0.04)",transition:"all 0.3s ease-in-out","&:hover":{transform:"translateY(-8px)",boxShadow:"0px 8px 25px rgba(79, 70, 229, 0.15)",borderColor:t.palette.primary.light}}}),C=(0,m.Ay)(f.A)(e=>{let{theme:t}=e;return{backgroundColor:"".concat(t.palette.primary.main,"15"),color:t.palette.primary.main,width:t.spacing(8),height:t.spacing(8),marginBottom:t.spacing(3),border:"2px solid ".concat(t.palette.primary.main,"25")}}),k=(0,m.Ay)(d.A)(e=>{let{theme:t}=e;return{padding:t.spacing(8,2)}}),I=(0,m.Ay)(d.A)(e=>{let{theme:t}=e;return{backgroundColor:t.palette.background.paper,color:t.palette.text.secondary,padding:t.spacing(4,2),textAlign:"center",borderTop:"1px solid ".concat(t.palette.divider)}}),M=[{icon:(0,r.jsx)(s,{fontSize:"large"}),title:"Real-time Transcription",description:"Accurate voice-to-text for both interviewer and candidate, powered by Azure Cognitive Services."},{icon:(0,r.jsx)(l,{fontSize:"large"}),title:"AI-Powered Insights",description:"Intelligent responses and suggestions with conversational context awareness using OpenAI/Gemini models."},{icon:(0,r.jsx)(a,{fontSize:"large"}),title:"Code Formatting",description:"Clear syntax highlighting for technical discussions, making code easy to read and understand."},{icon:(0,r.jsx)(u,{fontSize:"large"}),title:"Silence Detection",description:"Automatically submit questions or responses after a configurable period of silence for a smoother flow."},{icon:(0,r.jsx)(c.A,{fontSize:"large"}),title:"Customizable Settings",description:"Tailor AI models, API keys, and behavior to your specific needs and preferences."}];function P(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(y(),{children:[(0,r.jsx)("title",{children:"Interview Copilot - Your AI-Powered Interview Assistant"}),(0,r.jsx)("meta",{name:"description",content:"Elevate your technical interviews with real-time transcription, AI insights, and seamless assistance. Perfect for interviewers and candidates."}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.ico"})," "]}),(0,r.jsx)(w,{children:(0,r.jsxs)(h.A,{maxWidth:"md",children:[(0,r.jsx)(g.A,{variant:"h2",component:"h1",gutterBottom:!0,sx:{fontWeight:700,letterSpacing:"-1px",color:"white",position:"relative",zIndex:1},children:"Interview Copilot"}),(0,r.jsx)(g.A,{variant:"h5",component:"p",paragraph:!0,sx:{mb:4,color:"rgba(255, 255, 255, 0.95)",position:"relative",zIndex:1,lineHeight:1.6,maxWidth:"800px",margin:"0 auto 32px auto"},children:"Elevate your technical interviews with AI-powered real-time transcription, intelligent suggestions, and seamless assistance. Focus on the conversation, let us handle the notes."}),(0,r.jsx)(A(),{href:"/interview",passHref:!0,children:(0,r.jsx)(x.A,{variant:"contained",size:"large",endIcon:(0,r.jsx)(i,{}),sx:{background:"rgba(255, 255, 255, 0.95)",color:e=>e.palette.primary.main,padding:"16px 40px",fontSize:"1.2rem",fontWeight:600,borderRadius:"50px",boxShadow:"0px 8px 25px rgba(255, 255, 255, 0.3)",backdropFilter:"blur(10px)",border:"1px solid rgba(255, 255, 255, 0.2)",position:"relative",zIndex:1,"&:hover":{background:"rgba(255, 255, 255, 1)",boxShadow:"0px 12px 35px rgba(255, 255, 255, 0.4)",transform:"translateY(-3px)"}},children:"Start Assisting"})})]})}),(0,r.jsx)(k,{id:"features",sx:{backgroundColor:"#fafafa"},children:(0,r.jsxs)(h.A,{maxWidth:"lg",children:[(0,r.jsx)(g.A,{variant:"h3",component:"h2",align:"center",gutterBottom:!0,sx:{mb:8,fontWeight:700,color:e=>e.palette.primary.main,position:"relative","&::after":{content:'""',position:"absolute",bottom:"-16px",left:"50%",transform:"translateX(-50%)",width:"80px",height:"4px",background:e=>"linear-gradient(90deg, ".concat(e.palette.primary.main,", ").concat(e.palette.secondary.main,")"),borderRadius:"2px"}},children:"Why Choose Interview Copilot?"}),(0,r.jsx)(b.Ay,{container:!0,spacing:4,children:M.map(e=>(0,r.jsx)(b.Ay,{item:!0,xs:12,sm:6,md:4,children:(0,r.jsxs)(_,{elevation:3,children:[" ",(0,r.jsx)(C,{children:e.icon}),(0,r.jsx)(g.A,{variant:"h6",component:"h3",gutterBottom:!0,children:e.title}),(0,r.jsx)(g.A,{variant:"body1",color:"textSecondary",children:e.description})]})},e.title))})]})}),(0,r.jsx)(k,{id:"about",sx:{backgroundColor:"white"},children:(0,r.jsxs)(h.A,{maxWidth:"md",children:[(0,r.jsx)(g.A,{variant:"h3",component:"h2",align:"center",gutterBottom:!0,sx:{mb:6,fontWeight:700,color:e=>e.palette.primary.main},children:"About the Tool"}),(0,r.jsx)(g.A,{variant:"h5",component:"p",align:"center",color:"textSecondary",paragraph:!0,sx:{mb:4,lineHeight:1.8,fontWeight:400},children:"Interview Copilot is designed to be an indispensable assistant for technical interviews. Whether you're conducting interviews and need to capture key details, or you're a candidate wanting to review your performance, our tool provides the support you need."}),(0,r.jsx)(g.A,{variant:"h5",component:"p",align:"center",color:"textSecondary",paragraph:!0,sx:{lineHeight:1.8,fontWeight:400},children:"Our mission is to make interviews more productive and insightful by leveraging the power of AI, allowing participants to focus on what truly matters: the skills, experience, and potential being discussed."})]})}),(0,r.jsxs)(I,{children:[(0,r.jsxs)(g.A,{variant:"body2",children:["\xa9 ",new Date().getFullYear()," Interview Copilot. All rights reserved."]}),(0,r.jsx)(g.A,{variant:"caption",display:"block",sx:{mt:1},children:"Powered by AI for smarter interviews."})]})]})}},78940:(e,t,n)=>{"use strict";function r(e,t,n,r){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return r}}),n(87810),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81639:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return A},useLinkStatus:function(){return j}});let r=n(88365),o=n(37876),i=r._(n(14232)),a=n(46658),l=n(64232),s=n(96225),c=n(8407),u=n(2696),d=n(98265),p=n(42343),f=n(78940),h=n(27469),g=n(31026);n(63724);let x=new Set;function b(e,t,n,r){if((0,l.isLocalURL)(t)){if(!r.bypassPrefetchedCheck){let o=t+"%"+n+"%"+(void 0!==r.locale?r.locale:"locale"in e?e.locale:void 0);if(x.has(o))return;x.add(o)}e.prefetch(t,n,r).catch(e=>{})}}function m(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}let v=i.default.forwardRef(function(e,t){let n,r,{href:s,as:x,children:v,prefetch:y=null,passHref:j,replace:A,shallow:w,scroll:_,locale:C,onClick:k,onNavigate:I,onMouseEnter:M,onTouchStart:P,legacyBehavior:S=!1,...O}=e;n=v,S&&("string"==typeof n||"number"==typeof n)&&(n=(0,o.jsx)("a",{children:n}));let z=i.default.useContext(d.RouterContext),E=!1!==y,{href:R,as:L}=i.default.useMemo(()=>{if(!z){let e=m(s);return{href:e,as:x?m(x):e}}let[e,t]=(0,a.resolveHref)(z,s,!0);return{href:e,as:x?(0,a.resolveHref)(z,x):t||e}},[z,s,x]),T=i.default.useRef(R),W=i.default.useRef(L);S&&(r=i.default.Children.only(n));let H=S?r&&"object"==typeof r&&r.ref:t,[D,F,B]=(0,p.useIntersection)({rootMargin:"200px"}),N=i.default.useCallback(e=>{(W.current!==L||T.current!==R)&&(B(),W.current=L,T.current=R),D(e)},[L,R,B,D]),U=(0,g.useMergedRef)(N,H);i.default.useEffect(()=>{z&&F&&E&&b(z,R,L,{locale:C})},[L,R,F,C,E,null==z?void 0:z.locale,z]);let K={ref:U,onClick(e){S||"function"!=typeof k||k(e),S&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),z&&(e.defaultPrevented||function(e,t,n,r,o,i,a,s,c){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,l.isLocalURL)(n)){o&&(e.preventDefault(),location.replace(n));return}e.preventDefault(),(()=>{if(c){let e=!1;if(c({preventDefault:()=>{e=!0}}),e)return}let e=null==a||a;"beforePopState"in t?t[o?"replace":"push"](n,r,{shallow:i,locale:s,scroll:e}):t[o?"replace":"push"](r||n,{scroll:e})})()}}(e,z,R,L,A,w,_,C,I))},onMouseEnter(e){S||"function"!=typeof M||M(e),S&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),z&&b(z,R,L,{locale:C,priority:!0,bypassPrefetchedCheck:!0})},onTouchStart:function(e){S||"function"!=typeof P||P(e),S&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),z&&b(z,R,L,{locale:C,priority:!0,bypassPrefetchedCheck:!0})}};if((0,c.isAbsoluteUrl)(L))K.href=L;else if(!S||j||"a"===r.type&&!("href"in r.props)){let e=void 0!==C?C:null==z?void 0:z.locale;K.href=(null==z?void 0:z.isLocaleDomain)&&(0,f.getDomainLocale)(L,e,null==z?void 0:z.locales,null==z?void 0:z.domainLocales)||(0,h.addBasePath)((0,u.addLocale)(L,e,null==z?void 0:z.defaultLocale))}return S?i.default.cloneElement(r,K):(0,o.jsx)("a",{...O,...K,children:n})}),y=(0,i.createContext)({pending:!1}),j=()=>(0,i.useContext)(y),A=v;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}},e=>{e.O(0,[442,300,793,636,593,792],()=>e(e.s=72022)),_N_E=e.O()}]);