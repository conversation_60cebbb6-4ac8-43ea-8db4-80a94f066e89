(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[545],{10952:()=>{},13678:()=>{},26308:()=>{},29645:()=>{},42250:()=>{},44573:()=>{},76985:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eI});var o=r(37876),n=r(94250),i=r.n(n),s=r(14232),l=r(77328),a=r.n(l);r(89099);var c=r(31777),d=r(30566),u=r(11494),p=r(77018),m=r(16640),x=r(24422),g=r(11951),h=r(82260),y=r(93256),f=r(75500),A=r(69390),b=r(80412),v=r(70946),w=r(712),j=r(64410),S=r(54538),k=r(1073),C=r(24933),M=r(35656),I=r(81104),P=r(77935),T=r(2487),R=r(7957),E=r(63009),z=r(95684),D=r(58865),W=r(31771),G=r(17838),N=r(90757),L=r(65924),O=r(75383),_=r(58492),B=r(75953),q=r(98780),K=r(75963),F=r(54087),U=r(9818),H=r(63817),Y=r(88667),X=r(687),J=r(54338),Q=r(10231);r(32234);var Z=r(77170),$=r.n(Z),V=r(28646),ee=r(77022),et=r(29720),er=r(73919),eo=r(95062),en=r.n(eo),ei=r(84147),es=r(47866),el=r(3034),ea=r(71538),ec=r(88713),ed=r(90405),eu=r(61927),ep=r(69209),em=r(12317),ex=r(15319),eg=r(99386),eh=r(78523),ey=r(48504),ef=r(409),eA=r(87919),eb=r(60455);let ev=[{name:"OpenAI Models",models:[{value:"gpt-3.5-turbo",label:"GPT-3.5 Turbo"},{value:"gpt-4",label:"GPT-4"},{value:"gpt-4-turbo-preview",label:"GPT-4 Turbo Preview"},{value:"gpt-4o",label:"GPT-4o (Omni)"}]},{name:"Gemini Models",models:[{value:"gemini-1.5-flash",label:"Gemini 1.5 Flash"},{value:"gemini-1.5-pro",label:"Gemini 1.5 Pro"},{value:"gemini-2.0-flash",label:"Gemini 2.0 Flash "},{value:"gemini-2.0-pro",label:"Gemini 2.0 Pro "},{value:"gemini-2.5-flash-preview-05-20",label:"Gemini 2.5 Flash Preview (05-20)"},{value:"gemini-2.5-pro-preview-05-06",label:"Gemini 2.5 Pro Preview (05-06)"}]}],ew={openaiKey:"",geminiKey:"",aiModel:"gpt-3.5-turbo",silenceTimerDuration:1.2,responseLength:"medium",gptSystemPrompt:"You are an AI interview assistant. Your role is to:\n- Highlight key points in responses\n- Suggest related technical concepts to explore\n- Maintain professional tone",azureToken:"",azureRegion:"eastus",azureLanguage:"en-US",customModels:[],systemAutoMode:!0,isManualMode:!1};function ej(){{let e=localStorage.getItem("interviewCopilotConfig"),t=e?JSON.parse(e):{};return t.gptModel&&!t.aiModel&&(t.aiModel=t.gptModel,delete t.gptModel),Array.isArray(t.customModels)||(t.customModels=[]),{...ew,...t}}}function eS(e){let{open:t,onClose:r,onSave:n}=e,[i,l]=(0,s.useState)(ej()),[a,c]=(0,s.useState)(""),[d,m]=(0,s.useState)(""),[x,g]=(0,s.useState)("openai");(0,s.useEffect)(()=>{t&&(l(ej()),c(""),m(""),g("openai"))},[t]);let h=e=>{l({...i,[e.target.name]:e.target.value})};return(0,o.jsxs)(ei.A,{open:t,onClose:r,maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:3}},children:[(0,o.jsxs)(es.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid",borderColor:"divider",pb:1.5},children:["Application Settings",(0,o.jsx)(v.A,{"aria-label":"close",onClick:r,size:"small",children:(0,o.jsx)(ey.A,{})})]}),(0,o.jsxs)(el.A,{sx:{pt:2.5},children:[(0,o.jsx)(p.A,{variant:"h6",gutterBottom:!0,children:"API Keys"}),(0,o.jsx)(P.A,{fullWidth:!0,margin:"dense",name:"openaiKey",label:"OpenAI API Key",type:"password",value:i.openaiKey||"",onChange:h,helperText:"Required for OpenAI models."}),(0,o.jsx)(P.A,{fullWidth:!0,margin:"dense",name:"geminiKey",label:"Gemini API Key",type:"password",value:i.geminiKey||"",onChange:h,helperText:"Required for Gemini models.",sx:{mt:2}}),(0,o.jsx)(ea.A,{sx:{my:3}}),(0,o.jsx)(p.A,{variant:"h6",gutterBottom:!0,children:"AI Configuration"}),(0,o.jsxs)(ec.A,{fullWidth:!0,margin:"dense",children:[(0,o.jsx)(ed.A,{id:"ai-model-select-label",children:"AI Model"}),(0,o.jsxs)(eu.A,{labelId:"ai-model-select-label",name:"aiModel",value:i.aiModel,onChange:h,label:"AI Model",children:[ev.map(e=>[(0,o.jsx)(ep.A,{sx:{fontWeight:"bold",color:"text.primary",bgcolor:"transparent",lineHeight:"2.5em"},children:e.name},e.name),...e.models.map(e=>(0,o.jsx)(em.A,{value:e.value,children:e.label},e.value))]),i.customModels&&i.customModels.length>0&&(0,o.jsx)(ep.A,{sx:{fontWeight:"bold",color:"text.primary",bgcolor:"transparent",lineHeight:"2.5em",mt:1},children:"Custom Models"}),(i.customModels||[]).map((e,t)=>(0,o.jsxs)(em.A,{value:e.value,children:[e.label," (","gemini"===e.type?"Gemini":"OpenAI",")"]},"custom-".concat(e.value,"-").concat(t)))]})]}),(0,o.jsx)(P.A,{fullWidth:!0,margin:"dense",name:"gptSystemPrompt",label:"AI System Prompt",multiline:!0,rows:3,value:i.gptSystemPrompt,onChange:h,helperText:"Instructions for the AI assistant.",sx:{mt:2}}),(0,o.jsxs)(ec.A,{fullWidth:!0,margin:"dense",sx:{mt:2},children:[(0,o.jsx)(ed.A,{id:"response-length-label",children:"AI Response Length"}),(0,o.jsxs)(eu.A,{labelId:"response-length-label",name:"responseLength",value:i.responseLength,onChange:h,label:"AI Response Length",children:[(0,o.jsx)(em.A,{value:"concise",children:"Concise (Brief & to the point)"}),(0,o.jsx)(em.A,{value:"medium",children:"Medium (Balanced detail)"}),(0,o.jsx)(em.A,{value:"lengthy",children:"Lengthy (Detailed explanations)"})]})]}),(0,o.jsx)(ea.A,{sx:{my:3}}),(0,o.jsx)(p.A,{variant:"h6",gutterBottom:!0,children:"Manage Custom AI Models"}),(0,o.jsxs)(u.A,{sx:{p:2,border:"1px dashed",borderColor:"divider",borderRadius:1,mb:2},children:[(0,o.jsx)(p.A,{variant:"subtitle1",gutterBottom:!0,children:"Add New Model"}),(0,o.jsxs)(j.Ay,{container:!0,spacing:2,alignItems:"center",children:[(0,o.jsx)(j.Ay,{item:!0,xs:12,sm:4,children:(0,o.jsx)(P.A,{fullWidth:!0,margin:"dense",label:"Model Display Name",value:a,onChange:e=>c(e.target.value)})}),(0,o.jsx)(j.Ay,{item:!0,xs:12,sm:4,children:(0,o.jsx)(P.A,{fullWidth:!0,margin:"dense",label:"Model ID / Path",value:d,onChange:e=>m(e.target.value)})}),(0,o.jsx)(j.Ay,{item:!0,xs:12,sm:3,children:(0,o.jsx)(ec.A,{component:"fieldset",margin:"dense",children:(0,o.jsxs)(ex.A,{row:!0,name:"newModelType",value:x,onChange:e=>g(e.target.value),children:[(0,o.jsx)(M.A,{value:"openai",control:(0,o.jsx)(eg.A,{size:"small"}),label:"OpenAI"}),(0,o.jsx)(M.A,{value:"gemini",control:(0,o.jsx)(eg.A,{size:"small"}),label:"Gemini"})]})})}),(0,o.jsx)(j.Ay,{item:!0,xs:12,sm:1,children:(0,o.jsx)(b.A,{title:"Add Model",children:(0,o.jsx)(v.A,{color:"primary",onClick:()=>{if(!a.trim()||!d.trim())return void alert("Please provide both a display name and an ID for the new model.");let e={label:a.trim(),value:d.trim(),type:x},t=[...i.customModels||[],e];l({...i,customModels:t}),c(""),m("")},disabled:!a.trim()||!d.trim(),children:(0,o.jsx)(eA.A,{})})})})]})]}),i.customModels&&i.customModels.length>0&&(0,o.jsxs)(u.A,{children:[(0,o.jsx)(p.A,{variant:"subtitle1",gutterBottom:!0,children:"Your Custom Models:"}),(0,o.jsx)(List,{dense:!0,children:(i.customModels||[]).map((e,t)=>(0,o.jsx)(ListItem,{secondaryAction:(0,o.jsx)(b.A,{title:"Remove Model",children:(0,o.jsx)(v.A,{edge:"end","aria-label":"delete",onClick:()=>(e=>{var t,r,o;let n=(i.customModels||[]).filter((t,r)=>r!==e),s=i.aiModel;(null==(t=i.customModels[e])?void 0:t.value)===s&&(s=(null==(o=ev[0])||null==(r=o.models[0])?void 0:r.value)||"gpt-3.5-turbo"),l({...i,customModels:n,aiModel:s})})(t),size:"small",children:(0,o.jsx)(eb.A,{fontSize:"small"})})}),sx:{mb:.5,bgcolor:"action.hover",borderRadius:1,p:1},children:(0,o.jsx)(ListItemText,{primary:e.label,secondary:"".concat(e.value," (").concat("gemini"===e.type?"Gemini":"OpenAI",")")})},t))})]}),(0,o.jsx)(ea.A,{sx:{my:3}}),(0,o.jsx)(p.A,{variant:"h6",gutterBottom:!0,children:"Speech Configuration"}),(0,o.jsx)(P.A,{fullWidth:!0,margin:"dense",name:"silenceTimerDuration",label:"Silence Detection (seconds)",type:"number",inputProps:{step:.1,min:.5,max:5},value:i.silenceTimerDuration,onChange:h,helperText:"Auto-submit after this duration of silence (e.g., 1.2)."}),(0,o.jsx)(P.A,{fullWidth:!0,margin:"dense",name:"azureToken",label:"Azure Speech API Key",type:"password",value:i.azureToken||"",onChange:h,helperText:"Required for voice transcription.",sx:{mt:2}}),(0,o.jsx)(P.A,{fullWidth:!0,margin:"dense",name:"azureRegion",label:"Azure Region",value:i.azureRegion||"",onChange:h,helperText:"E.g., eastus, westus.",sx:{mt:2}}),(0,o.jsx)(P.A,{fullWidth:!0,margin:"dense",name:"azureLanguage",label:"Azure Recognition Language",value:i.azureLanguage||"",onChange:h,helperText:"E.g., en-US, es-ES.",sx:{mt:2}})]}),(0,o.jsxs)(eh.A,{sx:{p:2,borderTop:"1px solid",borderColor:"divider"},children:[(0,o.jsx)(T.A,{onClick:r,color:"inherit",children:"Cancel"}),(0,o.jsx)(T.A,{onClick:()=>{let e=i.aiModel,t=e.startsWith("gemini");(i.customModels||[]).find(t=>t.value===e&&"gemini"===t.type)&&(t=!0);let o=(i.customModels||[]).find(t=>t.value===e&&"openai"===t.type);if(t&&!i.geminiKey)return void alert("Selected Gemini model requires a Gemini API key. Please enter a key or select a different model.");if(!t&&!o&&!i.openaiKey&&!e.startsWith("gemini"))return void alert("Selected OpenAI model requires an OpenAI API key. Please enter a key or select a different model.");if(o&&!i.openaiKey)return void alert("Selected custom OpenAI-type model requires an OpenAI API key.");i.azureToken&&i.azureRegion||alert("Azure Speech Token and Region are required for voice transcription.");{let e={...i,customModels:Array.isArray(i.customModels)?i.customModels:[]};localStorage.setItem("interviewCopilotConfig",JSON.stringify(e))}n&&n(),r()},color:"primary",variant:"contained",startIcon:(0,o.jsx)(ef.A,{}),children:"Save Settings"})]})]})}eS.propTypes={open:en().bool.isRequired,onClose:en().func.isRequired,onSave:en().func};var ek=r(80400),eC=r(84593),eM=r(40345);function eI(){let e,t=(0,c.wA)(),r=(0,c.d4)(e=>e.transcription),n=(0,c.d4)(e=>e.aiResponse),l=(0,c.d4)(e=>e.history),Z=(0,d.A)(),[eo,en]=(0,s.useState)(ej()),[ei,es]=(0,s.useState)(null),[el,ea]=(0,s.useState)(null),[ec,ed]=(0,s.useState)(void 0===eo.systemAutoMode||eo.systemAutoMode),[eu,ep]=(0,s.useState)(null),[em,ex]=(0,s.useState)(!1),[eg,eh]=(0,s.useState)(!1),[ey,ef]=(0,s.useState)(!1),[eA,eb]=(0,s.useState)(!1),[ev,ew]=(0,s.useState)(""),[eI,eP]=(0,s.useState)("info"),[eT,eR]=(0,s.useState)([]),[eE,ez]=(0,s.useState)(void 0!==eo.isManualMode&&eo.isManualMode),[eD,eW]=(0,s.useState)(""),[eG,eN]=(0,s.useState)(!1),[eL,eO]=(0,s.useState)(!0),[e_,eB]=(0,s.useState)(!0),[eq,eK]=(0,s.useState)("newestAtTop"),[eF,eU]=(0,s.useState)(!1),eH=(0,s.useRef)(null),eY=(0,s.useRef)(null),eX=(0,s.useRef)(null),eJ=(0,s.useRef)(""),eQ=(0,s.useRef)(""),eZ=(0,s.useRef)(null),e$=(0,s.useRef)({system:"",microphone:""}),eV=(0,s.useRef)(eE),e0=(0,s.useRef)(ec),e1=(0,s.useRef)(null),e2=(0,s.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";ew(e),eP(t),eb(!0)},[]);(0,s.useEffect)(()=>{eL&&(()=>{try{if(eo.aiModel.startsWith("gemini")){if(!eo.geminiKey){e2("Gemini API key required. Please set it in Settings.","error"),ep(null);return}let e=new J.ij(eo.geminiKey);ep(e)}else{if(!eo.openaiKey){e2("OpenAI API key required. Please set it in Settings.","error"),ep(null);return}let e=new ee.Ay({apiKey:eo.openaiKey,dangerouslyAllowBrowser:!0});ep(e)}}catch(e){console.error("Error initializing AI client:",e),e2("Error initializing AI client: "+e.message,"error"),ep(null)}finally{eO(!1)}})()},[eo,eL,e2]),(0,s.useEffect)(()=>{eV.current=eE},[eE]),(0,s.useEffect)(()=>{e0.current=ec},[ec]),(0,s.useEffect)(()=>(e1.current=$()(e=>{t((0,ek.UM)(e))},250,{leading:!0,trailing:!0}),()=>{e1.current&&"function"==typeof e1.current.cancel&&e1.current.cancel()}),[t]);let e5=()=>eb(!1),e4=async e=>{let t="system"===e?ei:el;if(t&&"function"==typeof t.stopContinuousRecognitionAsync)try{if(await t.stopContinuousRecognitionAsync(),t.audioConfig&&t.audioConfig.privSource&&t.audioConfig.privSource.privStream){let e=t.audioConfig.privSource.privStream;e instanceof MediaStream&&e.getTracks().forEach(e=>{e.stop()})}t.audioConfig&&"function"==typeof t.audioConfig.close&&t.audioConfig.close()}catch(t){console.error("Error stopping ".concat(e," recognition:"),t),e2("Error stopping ".concat(e," audio: ").concat(t.message),"error")}finally{"system"===e?(ef(!1),es(null)):(eh(!1),ea(null))}},e3=(e,r)=>{"system"===r?(t((0,eM.nx)(e)),e$.current.system=e):(eW(e),e$.current.microphone=e)},e8=e=>{let t="system"===e?r:eD;t.trim()?tt(t.trim(),e):e2("Input is empty.","warning")},e7=(e,t)=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),e8(t))},e6=async(e,r)=>{let o,n=ej();if(!n.azureToken||!n.azureRegion)return e2("Azure Speech credentials missing. Please set them in Settings.","error"),e.getTracks().forEach(e=>e.stop()),null;try{o=V.AudioConfig.fromStreamInput(e)}catch(t){return console.error("Error creating AudioConfig for ".concat(r,":"),t),e2("Error setting up audio for ".concat(r,": ").concat(t.message),"error"),e.getTracks().forEach(e=>e.stop()),null}let i=V.SpeechConfig.fromSubscription(n.azureToken,n.azureRegion);i.speechRecognitionLanguage=n.azureLanguage;let s=new V.SpeechRecognizer(i,o);s.recognizing=(e,o)=>{if(o.result.reason===V.ResultReason.RecognizingSpeech){let e=o.result.text;"system"===r?(eJ.current=e,t((0,eM.nx)(e$.current.system+e))):(eQ.current=e,eW(e$.current.microphone+e))}},s.recognized=(e,o)=>{o.result.reason===V.ResultReason.RecognizedSpeech&&o.result.text?("system"===r?eJ.current="":eQ.current="",((e,r)=>{let o=e.replace(/\s+/g," ").trim();if(!o)return;e$.current[r]+=o+" ","system"===r?t((0,eM.nx)(e$.current.system+eJ.current)):eW(e$.current.microphone+eQ.current);let n=ej().silenceTimerDuration;("system"===r&&e0.current||"microphone"===r&&!eV.current)&&(clearTimeout(eZ.current),eZ.current=setTimeout(()=>{tt(e$.current[r].trim(),r)},1e3*n))})(o.result.text,r)):(o.result.reason,V.ResultReason.NoMatch)},s.canceled=(e,t)=>{console.log("CANCELED: Reason=".concat(t.reason," for ").concat(r)),t.reason===V.CancellationReason.Error&&(console.error("CANCELED: ErrorCode=".concat(t.errorCode)),console.error("CANCELED: ErrorDetails=".concat(t.errorDetails)),e2("Speech recognition error for ".concat(r,": ").concat(t.errorDetails),"error")),e4(r)},s.sessionStopped=(e,t)=>{console.log("Session stopped event for ".concat(r,".")),e4(r)};try{return await s.startContinuousRecognitionAsync(),s}catch(t){return console.error("Error starting ".concat(r," continuous recognition:"),t),e2("Failed to start ".concat(r," recognition: ").concat(t.message),"error"),o&&"function"==typeof o.close&&o.close(),e.getTracks().forEach(e=>e.stop()),null}},e9=async()=>{if(ey)return void await e4("system");if(!navigator.mediaDevices||!navigator.mediaDevices.getDisplayMedia){e2("Screen sharing is not supported by your browser.","error"),ef(!1);return}try{let e=await navigator.mediaDevices.getDisplayMedia({audio:!0,video:{displaySurface:"browser",logicalSurface:!0}}),t=e.getAudioTracks();if(0===t.length){e2("No audio track detected. Please ensure you share a tab with audio.","warning"),e.getTracks().forEach(e=>e.stop());return}ei&&await e4("system");let r=await e6(e,"system");r?(es(r),ef(!0),e2("System audio recording started.","success"),e.getTracks().forEach(e=>{e.onended=()=>{e2("Tab sharing ended.","info"),e4("system")}})):e.getTracks().forEach(e=>e.stop())}catch(e){console.error("System audio capture error:",e),"NotAllowedError"===e.name?e2("Permission denied for screen recording. Please allow access.","error"):"NotFoundError"===e.name?e2("No suitable tab/window found to share.","error"):"NotSupportedError"===e.name?e2("System audio capture not supported by your browser.","error"):e2("Failed to start system audio capture: ".concat(e.message||"Unknown error"),"error"),ef(!1)}},te=async()=>{if(eg)return void await e4("microphone");try{let e=await navigator.mediaDevices.getUserMedia({audio:!0});el&&await e4("microphone");let t=await e6(e,"microphone");t?(ea(t),eh(!0),e2("Microphone recording started.","success")):e.getTracks().forEach(e=>e.stop())}catch(e){console.error("Microphone capture error:",e),"NotAllowedError"===e.name||"NotFoundError"===e.name?e2("Permission denied for microphone. Please allow access.","error"):e2("Failed to access microphone: ".concat(e.message||"Unknown error"),"error"),eh(!1)}},tt=async(e,r)=>{if(!e.trim())return void e2("No input text to process.","warning");if(!eu||eL)return void e2("AI client is not ready. Please wait or check settings.","warning");let o=ej(),{temperature:n,maxTokens:i}={concise:{temperature:.4,maxTokens:250},medium:{temperature:.6,maxTokens:500},lengthy:{temperature:.8,maxTokens:1e3}}[o.responseLength||"medium"];eN(!0);let s=new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),a="";t((0,eC.P)({type:"question",text:e,timestamp:s,source:r,status:"pending"})),t((0,ek.UM)(""));try{let r=l.filter(e=>e.text&&("question"===e.type||"response"===e.type)&&"pending"!==e.status).slice(-6).map(e=>({role:"question"===e.type?"user":"assistant",content:e.text}));if(o.aiModel.startsWith("gemini")){let t=eu.getGenerativeModel({model:o.aiModel,generationConfig:{temperature:n,maxOutputTokens:i},systemInstruction:{parts:[{text:o.gptSystemPrompt}]}}).startChat({history:r.map(e=>({role:"user"===e.role?"user":"model",parts:[{text:e.content}]}))});for await(let r of(await t.sendMessageStream(e)).stream)if(r&&"function"==typeof r.text){let e=r.text();a+=e,e1.current&&e1.current(a)}}else{let t=[{role:"system",content:o.gptSystemPrompt},...r,{role:"user",content:e}];for await(let e of(await eu.chat.completions.create({model:o.aiModel,messages:t,temperature:n,max_tokens:i,stream:!0}))){var c,d;let t=(null==(d=e.choices[0])||null==(c=d.delta)?void 0:c.content)||"";a+=t,e1.current&&e1.current(a)}}e1.current&&"function"==typeof e1.current.cancel&&e1.current.cancel(),t((0,ek.UM)(a));let s=new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});t((0,eC.P)({type:"response",text:a,timestamp:s,status:"completed"}))}catch(r){console.error("AI request error:",r);let e="AI request failed: ".concat(r.message||"Unknown error");e2(e,"error"),t((0,ek.UM)("Error: ".concat(e))),t((0,eC.P)({type:"response",text:"Error: ".concat(e),timestamp:new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),status:"error"}))}finally{("system"===r&&e0.current||"microphone"===r&&!eV.current)&&(e$.current[r]="","system"===r?(eJ.current="",t((0,eM.nx)(""))):(eQ.current="",eW(""))),eN(!1)}},tr=(0,s.useCallback)(e=>e?(0,o.jsx)(et.oz,{components:{code(e){let{node:t,inline:r,className:n,children:i,...s}=e,l=/language-(\w+)/.exec(n||"");return!r&&l?(0,o.jsx)(u.A,{sx:{my:1,position:"relative","& pre":{borderRadius:"4px",padding:"12px !important",fontSize:"0.875rem",overflowX:"auto",whiteSpace:"pre-wrap",wordBreak:"break-all"}},children:(0,o.jsx)("pre",{children:(0,o.jsx)("code",{className:n,...s,dangerouslySetInnerHTML:{__html:Q.A.highlight(String(i).replace(/\n$/,""),{language:l[1],ignoreIllegals:!0}).value}})})}):(0,o.jsx)("code",{className:n,...s,style:{backgroundColor:"rgba(0,0,0,0.05)",padding:"2px 4px",borderRadius:"4px",fontFamily:"monospace",fontSize:"0.875rem",wordBreak:"break-all"},children:i})},p:e=>{let{node:t,...r}=e;return(0,o.jsx)(p.A,{paragraph:!0,...r,sx:{mb:1,fontSize:"0.95rem",wordBreak:"break-word"}})},strong:e=>{let{node:t,...r}=e;return(0,o.jsx)(p.A,{component:"strong",fontWeight:"bold",...r})},em:e=>{let{node:t,...r}=e;return(0,o.jsx)(p.A,{component:"em",fontStyle:"italic",...r})},ul:e=>{let{node:t,...r}=e;return(0,o.jsx)(p.A,{component:"ul",sx:{pl:2.5,mb:1,fontSize:"0.95rem",wordBreak:"break-word"},...r})},ol:e=>{let{node:t,...r}=e;return(0,o.jsx)(p.A,{component:"ol",sx:{pl:2.5,mb:1,fontSize:"0.95rem",wordBreak:"break-word"},...r})},li:e=>{let{node:t,...r}=e;return(0,o.jsx)(p.A,{component:"li",sx:{mb:.25,fontSize:"0.95rem",wordBreak:"break-word"},...r})}},children:e}):null,[]),to=async()=>{if(eF){if(eY.current&&"function"==typeof eY.current.close)try{await eY.current.close()}catch(e){console.error("Error closing document PiP window:",e)}else eH.current&&!eH.current.closed&&eH.current.close();return}let e=e=>{let t=function(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;var o=this;return function(){for(var n=arguments.length,i=Array(n),s=0;s<n;s++)i[s]=arguments[s];clearTimeout(t),t=setTimeout(()=>{e.apply(o,i)},r)}}(()=>{if(!e||e.closed)return;let t=eX.current?eX.current.contentWindow:e;t&&t.postMessage({type:"PIP_RESIZE",payload:{width:e.innerWidth,height:e.innerHeight}},"*")},50);return e.addEventListener("resize",t),()=>e.removeEventListener("resize",t)};if(window.documentPictureInPicture&&"function"==typeof window.documentPictureInPicture.requestWindow)try{eY.current=await window.documentPictureInPicture.requestWindow({width:400,height:300}),eU(!0);let t=eY.current.document.createElement("iframe");t.src="/pip-log",t.style.width="100%",t.style.height="100%",t.style.border="none",eY.current.document.body.style.margin="0",eY.current.document.body.style.overflow="hidden",eY.current.document.body.append(t),eX.current=t;let r=e(eY.current);t.onload=()=>{eX.current&&eX.current.contentWindow&&eX.current.contentWindow.postMessage({type:"AI_LOG_DATA",payload:{historicalResponses:l.filter(e=>"response"===e.type),currentStreamingText:eG?n:"",isProcessing:eG,sortOrder:eq}},"*")},eY.current.addEventListener("pagehide",()=>{r(),eU(!1),eY.current=null,eX.current=null}),e2("Native PiP window opened.","success");return}catch(e){console.error("Document Picture-in-Picture API error:",e),e2("Native PiP not available or failed. Trying popup. (".concat(e.message,")"),"warning")}if(eH.current=window.open("/pip-log","AIResponsePiP","width=400,height=550,resizable=yes,scrollbars=yes,status=no,toolbar=no,menubar=no,location=no,noopener,noreferrer,popup=yes"),eH.current){eU(!0);let t=e(eH.current);eH.current.onload=()=>{eH.current&&!eH.current.closed&&eH.current.postMessage({type:"AI_LOG_DATA",payload:{historicalResponses:l.filter(e=>"response"===e.type),currentStreamingText:eG?n:"",isProcessing:eG,sortOrder:eq}},"*")};let r=setInterval(()=>{eH.current&&eH.current.closed&&(clearInterval(r),t(),eU(!1),eH.current=null)},500);eH.current&&(eH.current._pipIntervalId=r)}else e2("Failed to open PiP window. Please check popup blocker settings.","error"),eU(!1)};return(0,s.useEffect)(()=>()=>{if(eH.current&&eH.current._pipIntervalId&&clearInterval(eH.current._pipIntervalId),eY.current&&"function"==typeof eY.current.close)try{eY.current.close()}catch(e){}},[]),(0,s.useEffect)(()=>{let e=null;if(eY.current&&eX.current&&eX.current.contentWindow?e=eX.current.contentWindow:eH.current&&!eH.current.closed&&(e=eH.current),eF&&e)try{e.postMessage({type:"AI_LOG_DATA",payload:{historicalResponses:l.filter(e=>"response"===e.type),currentStreamingText:eG?n:"",isProcessing:eG,sortOrder:eq}},"*")}catch(e){console.warn("Could not post message to PiP window:",e)}},[l,n,eF,eq,eG]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(a(),{children:(0,o.jsx)("title",{className:i().dynamic([["88c8eba4614332d8",[Z.palette.background.paper,Z.palette.grey[400],Z.palette.background.paper,Z.palette.grey[500],Z.palette.grey[400],Z.palette.background.paper]]]),children:"Interview Copilot - Active Session"})}),(0,o.jsxs)(u.A,{sx:{display:"flex",flexDirection:"column",height:"100vh"},children:[(0,o.jsx)(f.A,{position:"static",color:"default",elevation:1,children:(0,o.jsxs)(A.A,{children:[(0,o.jsx)(Y.A,{sx:{mr:2,color:"primary.main"}}),(0,o.jsx)(p.A,{variant:"h6",component:"div",sx:{flexGrow:1,color:"text.primary"},children:"Interview Copilot"}),(0,o.jsx)(b.A,{title:"Settings",children:(0,o.jsx)(v.A,{color:"primary",onClick:()=>ex(!0),"aria-label":"settings",children:(0,o.jsx)(H.A,{})})})]})}),(0,o.jsx)(w.A,{maxWidth:"xl",sx:{flexGrow:1,py:2,display:"flex",flexDirection:"column"},children:(0,o.jsxs)(j.Ay,{container:!0,spacing:2,sx:{flexGrow:1},children:[(0,o.jsxs)(j.Ay,{item:!0,xs:12,md:3,sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,o.jsxs)(S.A,{children:[(0,o.jsx)(k.A,{title:"System Audio (Interviewer)",avatar:(0,o.jsx)(L.A,{}),sx:{pb:1}}),(0,o.jsxs)(C.A,{children:[(0,o.jsx)(M.A,{control:(0,o.jsx)(I.A,{checked:ec,onChange:e=>ed(e.target.checked),color:"primary"}),label:"Auto-Submit Question",sx:{mb:1}}),(0,o.jsx)(P.A,{fullWidth:!0,multiline:!0,rows:3,variant:"outlined",value:r,onChange:e=>e3(e.target.value,"system"),onKeyDown:e=>e7(e,"system"),placeholder:"Interviewer's speech...",sx:{mb:2}}),(0,o.jsxs)(u.A,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:[(0,o.jsx)(T.A,{onClick:e9,variant:"contained",color:ey?"error":"primary",startIcon:ey?(0,o.jsx)(X.A,{}):(0,o.jsx)(F.A,{}),sx:{flexGrow:1},children:ey?"Stop System Audio":"Record System Audio"}),(0,o.jsx)(p.A,{variant:"caption",sx:{mt:1,display:"block",width:"100%"},children:ey?"Recording system audio...":'Select "Chrome Tab" and check "Share audio" when prompted.'}),(0,o.jsx)(b.A,{title:"Clear System Transcription",children:(0,o.jsx)(v.A,{onClick:()=>{e$.current.system="",eJ.current="",t((0,eM.gs)())},children:(0,o.jsx)(N.A,{})})}),!ec&&(0,o.jsx)(T.A,{onClick:()=>e8("system"),variant:"outlined",color:"primary",startIcon:(0,o.jsx)(U.A,{}),disabled:eG||!r.trim(),children:"Submit"})]})]})]}),(0,o.jsxs)(S.A,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[(0,o.jsx)(k.A,{title:"Question History",avatar:(0,o.jsx)(K.A,{}),action:(0,o.jsx)(T.A,{variant:"contained",size:"small",onClick:()=>{if(0===eT.length)return void e2("No questions selected to combine.","warning");let e=l.filter(e=>"question"===e.type).slice().reverse(),t=eT.map(t=>{var r;return null==(r=e[t])?void 0:r.text}).filter(e=>e);if(0===t.length)return void e2("Could not retrieve selected question texts.","warning");tt(t.join("\n\n---\n\n"),"combined"),eR([])},disabled:0===eT.length||eG,startIcon:eG?(0,o.jsx)(R.A,{size:16,color:"inherit"}):(0,o.jsx)(U.A,{}),children:"Ask Combined"}),sx:{pb:1,borderBottom:"1px solid ".concat(Z.palette.divider)}}),(0,o.jsx)(C.A,{sx:{flexGrow:1,overflow:"hidden",p:0},children:(0,o.jsx)(er.default,{className:"scroll-to-bottom",followButtonClassName:"hidden-follow-button",children:(0,o.jsx)(E.A,{dense:!0,sx:{pt:0,px:1},children:l.filter(e=>"question"===e.type).slice().reverse().map((e,t)=>{let r="system"===e.source?L.A:B.A,n="system"===e.source?"Interviewer":"Candidate",i="system"===e.source?Z.palette.info.light:Z.palette.success.light;return(0,o.jsxs)(m.Ay,{secondaryAction:(0,o.jsx)(h.A,{edge:"end",checked:eT.includes(t),onChange:()=>{eR(e=>e.includes(t)?e.filter(e=>e!==t):[...e,t])},color:"secondary",size:"small"}),disablePadding:!0,sx:{py:.5,display:"flex",alignItems:"center"},children:[(0,o.jsx)(x.A,{sx:{bgcolor:i,mr:1.5,width:32,height:32,fontSize:"1rem"},children:(0,o.jsx)(r,{fontSize:"small"})}),(0,o.jsx)(y.A,{primary:(0,o.jsx)(p.A,{variant:"body2",noWrap:!0,sx:{fontWeight:eT.includes(t)?"bold":"normal",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden",textOverflow:"ellipsis"},children:e.text}),secondary:"".concat(n," - ").concat(e.timestamp)})]},"question-hist-".concat(t))})})})})]})]}),(0,o.jsx)(j.Ay,{item:!0,xs:12,md:6,sx:{display:"flex",flexDirection:"column"},children:(0,o.jsxs)(S.A,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[(0,o.jsx)(k.A,{title:"AI Assistant Log",avatar:(0,o.jsx)(Y.A,{}),action:(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(b.A,{title:eF?"Close PiP Log":"Open PiP Log",children:(0,o.jsx)(v.A,{onClick:to,size:"small",color:eF?"secondary":"default",children:(0,o.jsx)(q.A,{})})}),(0,o.jsx)(b.A,{title:"newestAtTop"===eq?"Sort: Newest at Bottom":"Sort: Newest on Top",children:(0,o.jsx)(v.A,{onClick:()=>{eK(e=>"newestAtBottom"===e?"newestAtTop":"newestAtBottom")},size:"small",children:"newestAtTop"===eq?(0,o.jsx)(W.A,{}):(0,o.jsx)(G.A,{})})}),(0,o.jsx)(p.A,{variant:"caption",sx:{mr:1,fontStyle:"italic"},children:"newestAtTop"===eq?"Newest First":"Oldest First"}),(0,o.jsx)(M.A,{control:(0,o.jsx)(I.A,{checked:e_,onChange:e=>eB(e.target.checked),color:"primary"}),label:"Auto Scroll",sx:{ml:1}})]}),sx:{borderBottom:"1px solid ".concat(Z.palette.divider)}}),(0,o.jsx)(C.A,{sx:{flexGrow:1,overflow:"hidden",p:0},children:(0,o.jsx)(er.default,{className:"scroll-to-bottom",mode:e_?"newestAtTop"===eq?"top":"bottom":void 0,followButtonClassName:"hidden-follow-button",children:(0,o.jsxs)(E.A,{sx:{px:2,py:1},children:[(e=l.filter(e=>"response"===e.type).slice(),(eG&&n&&""!==n.trim()&&e.push({text:n,timestamp:"Streaming...",type:"current_streaming"}),"newestAtTop"===eq)?e.reverse():e).map((e,t)=>{if("response"!==e.type)return null;let r=Y.A,n=Z.palette.secondary.light;return(0,o.jsxs)(m.Ay,{sx:{alignItems:"flex-start",px:0,py:1.5},children:[(0,o.jsx)(x.A,{sx:{bgcolor:n,mr:2,mt:.5},children:(0,o.jsx)(r,{sx:{color:Z.palette.getContrastText(n)}})}),(0,o.jsxs)(g.A,{variant:"outlined",sx:{p:1.5,flexGrow:1,bgcolor:Z.palette.background.default,borderColor:Z.palette.divider,overflowX:"auto"},children:[(0,o.jsxs)(u.A,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:.5},children:[(0,o.jsx)(p.A,{variant:"subtitle2",fontWeight:"bold",children:"AI Assistant"}),(0,o.jsx)(p.A,{variant:"caption",color:"text.secondary",children:e.timestamp})]}),tr(e.text)]})]},"response-".concat(t))}),eG&&(0,o.jsxs)(m.Ay,{sx:{justifyContent:"center",py:2},children:[(0,o.jsx)(R.A,{size:24}),(0,o.jsx)(p.A,{variant:"caption",sx:{ml:1},children:"AI is thinking..."})]})]})})})]})}),(0,o.jsx)(j.Ay,{item:!0,xs:12,md:3,sx:{display:"flex",flexDirection:"column"},children:(0,o.jsxs)(S.A,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[(0,o.jsx)(k.A,{title:"Your Mic (Candidate)",avatar:(0,o.jsx)(B.A,{}),sx:{pb:1}}),(0,o.jsxs)(C.A,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[(0,o.jsx)(M.A,{control:(0,o.jsx)(I.A,{checked:eE,onChange:e=>ez(e.target.checked),color:"primary"}),label:"Manual Input Mode",sx:{mb:1}}),(0,o.jsx)(P.A,{fullWidth:!0,multiline:!0,rows:8,variant:"outlined",value:eD,onChange:e=>e3(e.target.value,"microphone"),onKeyDown:e=>e7(e,"microphone"),placeholder:"Your speech or manual input...",sx:{mb:2,flexGrow:1}}),(0,o.jsxs)(u.A,{sx:{display:"flex",gap:1,flexWrap:"wrap",mt:"auto"},children:[(0,o.jsx)(T.A,{onClick:te,variant:"contained",color:eg?"error":"primary",startIcon:eg?(0,o.jsx)(_.A,{}):(0,o.jsx)(O.A,{}),sx:{flexGrow:1},children:eg?"Stop Mic":"Start Mic"}),(0,o.jsx)(b.A,{title:"Clear Your Transcription",children:(0,o.jsx)(v.A,{onClick:()=>{e$.current.microphone="",eQ.current="",eW("")},children:(0,o.jsx)(N.A,{})})}),eE&&(0,o.jsx)(T.A,{onClick:()=>e8("microphone"),variant:"outlined",color:"primary",startIcon:(0,o.jsx)(U.A,{}),disabled:eG||!eD.trim(),children:"Submit"})]})]})]})})]})}),(0,o.jsx)(eS,{open:em,onClose:()=>ex(!1),onSave:()=>{let e=ej();en(e),eO(!0),ed(void 0===e.systemAutoMode||e.systemAutoMode),ez(void 0!==e.isManualMode&&e.isManualMode)}}),(0,o.jsx)(z.A,{open:eA,autoHideDuration:4e3,onClose:e5,anchorOrigin:{vertical:"bottom",horizontal:"center"},children:(0,o.jsx)(D.A,{onClose:e5,severity:eI,sx:{width:"100%",boxShadow:Z.shadows[6]},children:ev})})]}),(0,o.jsx)(i(),{id:"88c8eba4614332d8",dynamic:[Z.palette.background.paper,Z.palette.grey[400],Z.palette.background.paper,Z.palette.grey[500],Z.palette.grey[400],Z.palette.background.paper],children:".scroll-to-bottom{height:100%;width:100%;overflow-y:auto}.hidden-follow-button{display:none}.scroll-to-bottom::-webkit-scrollbar{width:8px;height:8px}.scroll-to-bottom::-webkit-scrollbar-track{background:".concat(Z.palette.background.paper,";border-radius:10px}.scroll-to-bottom::-webkit-scrollbar-thumb{background-color:").concat(Z.palette.grey[400],";border-radius:10px;border:2px solid ").concat(Z.palette.background.paper,"}.scroll-to-bottom::-webkit-scrollbar-thumb:hover{background-color:").concat(Z.palette.grey[500],"}.scroll-to-bottom{scrollbar-width:thin;scrollbar-color:").concat(Z.palette.grey[400]," ").concat(Z.palette.background.paper,"}")})]})}},83490:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/interview",function(){return r(76985)}])},88632:()=>{}},e=>{e.O(0,[442,300,77,919,793,371,636,593,792],()=>e(e.s=83490)),_N_E=e.O()}]);