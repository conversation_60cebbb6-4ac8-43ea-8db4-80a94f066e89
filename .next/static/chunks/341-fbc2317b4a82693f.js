(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[341],{199:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=r(14232).createContext({})},409:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3m3-10H5V5h10z"}),"Save")},632:(e,t,r)=>{"use strict";r.d(t,{A:()=>s,b:()=>i});var n=r(47951),o=r(45879);function i(e){return(0,o.Ay)("MuiListItemText",e)}let s=(0,n.A)("MuiListItemText",["root","multiline","dense","inset","primary","secondary"])},687:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"m21.22 18.02 2 2H24v-2zm.77-2 .01-10c0-1.11-.9-2-2-2H7.22l5.23 5.23c.18-.04.36-.07.55-.1V7.02l4 3.73-1.58 1.47 5.54 5.54c.61-.33 1.03-.99 1.03-1.74M2.39 1.73 1.11 3l1.54 1.54c-.4.36-.65.89-.65 1.48v10c0 1.1.89 2 2 2H0v2h18.13l2.71 2.71 1.27-1.27zM7 15.02c.31-1.48.92-2.95 2.07-4.06l1.59 1.59c-1.54.38-2.7 1.18-3.66 2.47"}),"StopScreenShare")},1073:(e,t,r)=>{"use strict";r.d(t,{A:()=>b});var n=r(14232),o=r(4697),i=r(54638),s=r(77018),a=r(54773),l=r(52196),c=r(47951),u=r(45879);function d(e){return(0,u.Ay)("MuiCardHeader",e)}let p=(0,c.A)("MuiCardHeader",["root","avatar","action","content","title","subheader"]);var f=r(67360),h=r(37876);let m=(0,a.Ay)("div",{name:"MuiCardHeader",slot:"Root",overridesResolver:(e,t)=>[{["& .".concat(p.title)]:t.title},{["& .".concat(p.subheader)]:t.subheader},t.root]})({display:"flex",alignItems:"center",padding:16}),g=(0,a.Ay)("div",{name:"MuiCardHeader",slot:"Avatar",overridesResolver:(e,t)=>t.avatar})({display:"flex",flex:"0 0 auto",marginRight:16}),y=(0,a.Ay)("div",{name:"MuiCardHeader",slot:"Action",overridesResolver:(e,t)=>t.action})({flex:"0 0 auto",alignSelf:"flex-start",marginTop:-4,marginRight:-8,marginBottom:-4}),v=(0,a.Ay)("div",{name:"MuiCardHeader",slot:"Content",overridesResolver:(e,t)=>t.content})({flex:"1 1 auto",[".".concat(i.A.root,":where(& .").concat(p.title,")")]:{display:"block"},[".".concat(i.A.root,":where(& .").concat(p.subheader,")")]:{display:"block"}}),b=n.forwardRef(function(e,t){let r=(0,l.b)({props:e,name:"MuiCardHeader"}),{action:n,avatar:i,component:a="div",disableTypography:c=!1,subheader:u,subheaderTypographyProps:p,title:b,titleTypographyProps:A,slots:w={},slotProps:x={},...S}=r,E={...r,component:a,disableTypography:c},_=(e=>{let{classes:t}=e;return(0,o.A)({root:["root"],avatar:["avatar"],action:["action"],content:["content"],title:["title"],subheader:["subheader"]},d,t)})(E),C={slots:w,slotProps:{title:A,subheader:p,...x}},R=b,[O,k]=(0,f.A)("title",{className:_.title,elementType:s.A,externalForwardedProps:C,ownerState:E,additionalProps:{variant:i?"body2":"h5",component:"span"}});null==R||R.type===s.A||c||(R=(0,h.jsx)(O,{...k,children:R}));let I=u,[T,M]=(0,f.A)("subheader",{className:_.subheader,elementType:s.A,externalForwardedProps:C,ownerState:E,additionalProps:{variant:i?"body2":"body1",color:"textSecondary",component:"span"}});null==I||I.type===s.A||c||(I=(0,h.jsx)(T,{...M,children:I}));let[P,j]=(0,f.A)("root",{ref:t,className:_.root,elementType:m,externalForwardedProps:{...C,...S,component:a},ownerState:E}),[N,L]=(0,f.A)("avatar",{className:_.avatar,elementType:g,externalForwardedProps:C,ownerState:E}),[B,F]=(0,f.A)("content",{className:_.content,elementType:v,externalForwardedProps:C,ownerState:E}),[D,$]=(0,f.A)("action",{className:_.action,elementType:y,externalForwardedProps:C,ownerState:E});return(0,h.jsxs)(P,{...j,children:[i&&(0,h.jsx)(N,{...L,children:i}),(0,h.jsxs)(B,{...F,children:[R,I]}),n&&(0,h.jsx)(D,{...$,children:n})]})})},3034:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var n=r(14232),o=r(69241),i=r(4697),s=r(54773),a=r(82987),l=r(52196),c=r(47951),u=r(45879);function d(e){return(0,u.Ay)("MuiDialogContent",e)}(0,c.A)("MuiDialogContent",["root","dividers"]);var p=r(12526),f=r(37876);let h=(0,s.Ay)("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dividers&&t.dividers]}})((0,a.A)(e=>{let{theme:t}=e;return{flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:e=>{let{ownerState:t}=e;return t.dividers},style:{padding:"16px 24px",borderTop:"1px solid ".concat((t.vars||t).palette.divider),borderBottom:"1px solid ".concat((t.vars||t).palette.divider)}},{props:e=>{let{ownerState:t}=e;return!t.dividers},style:{[".".concat(p.A.root," + &")]:{paddingTop:0}}}]}})),m=n.forwardRef(function(e,t){let r=(0,l.b)({props:e,name:"MuiDialogContent"}),{className:n,dividers:s=!1,...a}=r,c={...r,dividers:s},u=(e=>{let{classes:t,dividers:r}=e;return(0,i.A)({root:["root",r&&"dividers"]},d,t)})(c);return(0,f.jsx)(h,{className:(0,o.A)(u.root,n),ownerState:c,ref:t,...a})})},3717:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},6299:(e,t,r)=>{"use strict";r.d(t,{c:()=>o,q:()=>n});let n=e=>e.scrollTop;function o(e,t){var r,n;let{timeout:o,easing:i,style:s={}}=e;return{duration:null!=(r=s.transitionDuration)?r:"number"==typeof o?o:o[t.mode]||0,easing:null!=(n=s.transitionTimingFunction)?n:"object"==typeof i?i[t.mode]:i,delay:s.transitionDelay}}},9050:(e,t,r)=>{"use strict";r.d(t,{A:()=>B});var n=r(14232),o=r(69241),i=r(4697),s=r(61637),a=r(43165),l=r(44471),c=r(37876);function u(e){let t=[],r=[];return Array.from(e.querySelectorAll('input,select,textarea,a[href],button,[tabindex],audio[controls],video[controls],[contenteditable]:not([contenteditable="false"])')).forEach((e,n)=>{let o=function(e){let t=parseInt(e.getAttribute("tabindex")||"",10);return Number.isNaN(t)?"true"===e.contentEditable||("AUDIO"===e.nodeName||"VIDEO"===e.nodeName||"DETAILS"===e.nodeName)&&null===e.getAttribute("tabindex")?0:e.tabIndex:t}(e);-1===o||e.disabled||"INPUT"===e.tagName&&"hidden"===e.type||function(e){if("INPUT"!==e.tagName||"radio"!==e.type||!e.name)return!1;let t=t=>e.ownerDocument.querySelector('input[type="radio"]'.concat(t)),r=t('[name="'.concat(e.name,'"]:checked'));return r||(r=t('[name="'.concat(e.name,'"]'))),r!==e}(e)||(0===o?t.push(e):r.push({documentOrder:n,tabIndex:o,node:e}))}),r.sort((e,t)=>e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex).map(e=>e.node).concat(t)}function d(){return!0}let p=function(e){let{children:t,disableAutoFocus:r=!1,disableEnforceFocus:o=!1,disableRestoreFocus:i=!1,getTabbable:p=u,isEnabled:f=d,open:h}=e,m=n.useRef(!1),g=n.useRef(null),y=n.useRef(null),v=n.useRef(null),b=n.useRef(null),A=n.useRef(!1),w=n.useRef(null),x=(0,s.A)((0,a.A)(t),w),S=n.useRef(null);n.useEffect(()=>{h&&w.current&&(A.current=!r)},[r,h]),n.useEffect(()=>{if(!h||!w.current)return;let e=(0,l.A)(w.current);return!w.current.contains(e.activeElement)&&(w.current.hasAttribute("tabIndex")||w.current.setAttribute("tabIndex","-1"),A.current&&w.current.focus()),()=>{i||(v.current&&v.current.focus&&(m.current=!0,v.current.focus()),v.current=null)}},[h]),n.useEffect(()=>{if(!h||!w.current)return;let e=(0,l.A)(w.current),t=t=>{S.current=t,!o&&f()&&"Tab"===t.key&&e.activeElement===w.current&&t.shiftKey&&(m.current=!0,y.current&&y.current.focus())},r=()=>{let t=w.current;if(null===t)return;if(!e.hasFocus()||!f()||m.current){m.current=!1;return}if(t.contains(e.activeElement)||o&&e.activeElement!==g.current&&e.activeElement!==y.current)return;if(e.activeElement!==b.current)b.current=null;else if(null!==b.current)return;if(!A.current)return;let r=[];if((e.activeElement===g.current||e.activeElement===y.current)&&(r=p(w.current)),r.length>0){var n,i;let e=!!((null==(n=S.current)?void 0:n.shiftKey)&&(null==(i=S.current)?void 0:i.key)==="Tab"),t=r[0],o=r[r.length-1];"string"!=typeof t&&"string"!=typeof o&&(e?o.focus():t.focus())}else t.focus()};e.addEventListener("focusin",r),e.addEventListener("keydown",t,!0);let n=setInterval(()=>{e.activeElement&&"BODY"===e.activeElement.tagName&&r()},50);return()=>{clearInterval(n),e.removeEventListener("focusin",r),e.removeEventListener("keydown",t,!0)}},[r,o,i,f,h,p]);let E=e=>{null===v.current&&(v.current=e.relatedTarget),A.current=!0};return(0,c.jsxs)(n.Fragment,{children:[(0,c.jsx)("div",{tabIndex:h?0:-1,onFocus:E,ref:g,"data-testid":"sentinelStart"}),n.cloneElement(t,{ref:x,onFocus:e=>{null===v.current&&(v.current=e.relatedTarget),A.current=!0,b.current=e.target;let r=t.props.onFocus;r&&r(e)}}),(0,c.jsx)("div",{tabIndex:h?0:-1,onFocus:E,ref:y,"data-testid":"sentinelEnd"})]})};var f=r(68043),h=r(54773),m=r(82987),g=r(52196),y=r(30929),v=r(7061),b=r(62435),A=r(92233),w=r(43583),x=r(15783);function S(e,t){t?e.setAttribute("aria-hidden","true"):e.removeAttribute("aria-hidden")}function E(e){return parseInt((0,w.A)(e).getComputedStyle(e).paddingRight,10)||0}function _(e,t,r,n,o){let i=[t,r,...n];[].forEach.call(e.children,e=>{let t=!i.includes(e),r=!function(e){let t=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(e.tagName),r="INPUT"===e.tagName&&"hidden"===e.getAttribute("type");return t||r}(e);t&&r&&S(e,o)})}function C(e,t){let r=-1;return e.some((e,n)=>!!t(e)&&(r=n,!0)),r}class R{add(e,t){let r=this.modals.indexOf(e);if(-1!==r)return r;r=this.modals.length,this.modals.push(e),e.modalRef&&S(e.modalRef,!1);let n=function(e){let t=[];return[].forEach.call(e.children,e=>{"true"===e.getAttribute("aria-hidden")&&t.push(e)}),t}(t);_(t,e.mount,e.modalRef,n,!0);let o=C(this.containers,e=>e.container===t);return -1!==o?this.containers[o].modals.push(e):this.containers.push({modals:[e],container:t,restore:null,hiddenSiblings:n}),r}mount(e,t){let r=C(this.containers,t=>t.modals.includes(e)),n=this.containers[r];n.restore||(n.restore=function(e,t){let r=[],n=e.container;if(!t.disableScrollLock){let e;if(function(e){let t=(0,l.A)(e);return t.body===e?(0,w.A)(e).innerWidth>t.documentElement.clientWidth:e.scrollHeight>e.clientHeight}(n)){let e=(0,x.A)((0,w.A)(n));r.push({value:n.style.paddingRight,property:"padding-right",el:n}),n.style.paddingRight="".concat(E(n)+e,"px");let t=(0,l.A)(n).querySelectorAll(".mui-fixed");[].forEach.call(t,t=>{r.push({value:t.style.paddingRight,property:"padding-right",el:t}),t.style.paddingRight="".concat(E(t)+e,"px")})}if(n.parentNode instanceof DocumentFragment)e=(0,l.A)(n).body;else{let t=n.parentElement,r=(0,w.A)(n);e=(null==t?void 0:t.nodeName)==="HTML"&&"scroll"===r.getComputedStyle(t).overflowY?t:n}r.push({value:e.style.overflow,property:"overflow",el:e},{value:e.style.overflowX,property:"overflow-x",el:e},{value:e.style.overflowY,property:"overflow-y",el:e}),e.style.overflow="hidden"}return()=>{r.forEach(e=>{let{value:t,el:r,property:n}=e;t?r.style.setProperty(n,t):r.style.removeProperty(n)})}}(n,t))}remove(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],r=this.modals.indexOf(e);if(-1===r)return r;let n=C(this.containers,t=>t.modals.includes(e)),o=this.containers[n];if(o.modals.splice(o.modals.indexOf(e),1),this.modals.splice(r,1),0===o.modals.length)o.restore&&o.restore(),e.modalRef&&S(e.modalRef,t),_(o.container,e.mount,e.modalRef,o.hiddenSiblings,!1),this.containers.splice(n,1);else{let e=o.modals[o.modals.length-1];e.modalRef&&S(e.modalRef,!1)}return r}isTopModal(e){return this.modals.length>0&&this.modals[this.modals.length-1]===e}constructor(){this.modals=[],this.containers=[]}}let O=()=>{},k=new R,I=function(e){let{container:t,disableEscapeKeyDown:r=!1,disableScrollLock:o=!1,closeAfterTransition:i=!1,onTransitionEnter:a,onTransitionExited:c,children:u,onClose:d,open:p,rootRef:f}=e,h=n.useRef({}),m=n.useRef(null),g=n.useRef(null),y=(0,s.A)(g,f),[w,x]=n.useState(!p),E=!!u&&u.props.hasOwnProperty("in"),_=!0;("false"===e["aria-hidden"]||!1===e["aria-hidden"])&&(_=!1);let C=()=>(h.current.modalRef=g.current,h.current.mount=m.current,h.current),R=()=>{k.mount(C(),{disableScrollLock:o}),g.current&&(g.current.scrollTop=0)},I=(0,v.A)(()=>{let e=("function"==typeof t?t():t)||(0,l.A)(m.current).body;k.add(C(),e),g.current&&R()}),T=()=>k.isTopModal(C()),M=(0,v.A)(e=>{m.current=e,e&&(p&&T()?R():g.current&&S(g.current,_))}),P=n.useCallback(()=>{k.remove(C(),_)},[_]);return n.useEffect(()=>()=>{P()},[P]),n.useEffect(()=>{p?I():E&&i||P()},[p,P,E,i,I]),{getRootProps:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,A.A)(e);delete n.onTransitionEnter,delete n.onTransitionExited;let o={...n,...t};return{role:"presentation",...o,onKeyDown:e=>{var t;null==(t=o.onKeyDown)||t.call(o,e),"Escape"===e.key&&229!==e.which&&T()&&!r&&(e.stopPropagation(),d&&d(e,"escapeKeyDown"))},ref:y}},getBackdropProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"aria-hidden":!0,...e,onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),t.target===t.currentTarget&&d&&d(t,"backdropClick")},open:p}},getTransitionProps:()=>{var e,t;return{onEnter:(0,b.A)(()=>{x(!1),a&&a()},null!=(e=null==u?void 0:u.props.onEnter)?e:O),onExited:(0,b.A)(()=>{x(!0),c&&c(),i&&P()},null!=(t=null==u?void 0:u.props.onExited)?t:O)}},rootRef:y,portalRef:M,isTopModal:T,exited:w,hasTransition:E}};var T=r(47951),M=r(45879);function P(e){return(0,M.Ay)("MuiModal",e)}(0,T.A)("MuiModal",["root","hidden","backdrop"]);var j=r(67360);let N=(0,h.Ay)("div",{name:"MuiModal",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.open&&r.exited&&t.hidden]}})((0,m.A)(e=>{let{theme:t}=e;return{position:"fixed",zIndex:(t.vars||t).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:e=>{let{ownerState:t}=e;return!t.open&&t.exited},style:{visibility:"hidden"}}]}})),L=(0,h.Ay)(y.A,{name:"MuiModal",slot:"Backdrop",overridesResolver:(e,t)=>t.backdrop})({zIndex:-1}),B=n.forwardRef(function(e,t){let r=(0,g.b)({name:"MuiModal",props:e}),{BackdropComponent:s=L,BackdropProps:a,classes:l,className:u,closeAfterTransition:d=!1,children:h,container:m,component:y,components:v={},componentsProps:b={},disableAutoFocus:A=!1,disableEnforceFocus:w=!1,disableEscapeKeyDown:x=!1,disablePortal:S=!1,disableRestoreFocus:E=!1,disableScrollLock:_=!1,hideBackdrop:C=!1,keepMounted:R=!1,onBackdropClick:O,onClose:k,onTransitionEnter:T,onTransitionExited:M,open:B,slotProps:F={},slots:D={},theme:$,...z}=r,W={...r,closeAfterTransition:d,disableAutoFocus:A,disableEnforceFocus:w,disableEscapeKeyDown:x,disablePortal:S,disableRestoreFocus:E,disableScrollLock:_,hideBackdrop:C,keepMounted:R},{getRootProps:U,getBackdropProps:H,getTransitionProps:q,portalRef:V,isTopModal:X,exited:K,hasTransition:G}=I({...W,rootRef:t}),Y={...W,exited:K},J=(e=>{let{open:t,exited:r,classes:n}=e;return(0,i.A)({root:["root",!t&&r&&"hidden"],backdrop:["backdrop"]},P,n)})(Y),Q={};if(void 0===h.props.tabIndex&&(Q.tabIndex="-1"),G){let{onEnter:e,onExited:t}=q();Q.onEnter=e,Q.onExited=t}let Z={slots:{root:v.Root,backdrop:v.Backdrop,...D},slotProps:{...b,...F}},[ee,et]=(0,j.A)("root",{ref:t,elementType:N,externalForwardedProps:{...Z,...z,component:y},getSlotProps:U,ownerState:Y,className:(0,o.A)(u,null==J?void 0:J.root,!Y.open&&Y.exited&&(null==J?void 0:J.hidden))}),[er,en]=(0,j.A)("backdrop",{ref:null==a?void 0:a.ref,elementType:s,externalForwardedProps:Z,shouldForwardComponentProp:!0,additionalProps:a,getSlotProps:e=>H({...e,onClick:t=>{O&&O(t),(null==e?void 0:e.onClick)&&e.onClick(t)}}),className:(0,o.A)(null==a?void 0:a.className,null==J?void 0:J.backdrop),ownerState:Y});return R||B||G&&!K?(0,c.jsx)(f.A,{ref:V,container:m,disablePortal:S,children:(0,c.jsxs)(ee,{...et,children:[!C&&s?(0,c.jsx)(er,{...en}):null,(0,c.jsx)(p,{disableEnforceFocus:w,disableAutoFocus:A,disableRestoreFocus:E,isEnabled:X,open:B,children:n.cloneElement(h,Q)})]})}):null})},9818:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M2.01 21 23 12 2.01 3 2 10l15 2-15 2z"}),"Send")},12317:(e,t,r)=>{"use strict";r.d(t,{A:()=>E});var n=r(14232),o=r(69241),i=r(4697),s=r(126),a=r(68275),l=r(54773),c=r(82987),u=r(52196),d=r(99003),p=r(68197),f=r(91411),h=r(66313),m=r(78630),g=r(47951);let y=(0,g.A)("MuiListItemIcon",["root","alignItemsFlexStart"]);var v=r(632),b=r(45879);function A(e){return(0,b.Ay)("MuiMenuItem",e)}let w=(0,g.A)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]);var x=r(37876);let S=(0,l.Ay)(p.A,{shouldForwardProp:e=>(0,a.A)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.dense&&t.dense,r.divider&&t.divider,!r.disableGutters&&t.gutters]}})((0,c.A)(e=>{let{theme:t}=e;return{...t.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},["&.".concat(w.selected)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):(0,s.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),["&.".concat(w.focusVisible)]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.focusOpacity,"))"):(0,s.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},["&.".concat(w.selected,":hover")]:{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / calc(").concat(t.vars.palette.action.selectedOpacity," + ").concat(t.vars.palette.action.hoverOpacity,"))"):(0,s.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.primary.mainChannel," / ").concat(t.vars.palette.action.selectedOpacity,")"):(0,s.X4)(t.palette.primary.main,t.palette.action.selectedOpacity)}},["&.".concat(w.focusVisible)]:{backgroundColor:(t.vars||t).palette.action.focus},["&.".concat(w.disabled)]:{opacity:(t.vars||t).palette.action.disabledOpacity},["& + .".concat(m.A.root)]:{marginTop:t.spacing(1),marginBottom:t.spacing(1)},["& + .".concat(m.A.inset)]:{marginLeft:52},["& .".concat(v.A.root)]:{marginTop:0,marginBottom:0},["& .".concat(v.A.inset)]:{paddingLeft:36},["& .".concat(y.root)]:{minWidth:36},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:"1px solid ".concat((t.vars||t).palette.divider),backgroundClip:"padding-box"}},{props:e=>{let{ownerState:t}=e;return!t.dense},style:{[t.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:e=>{let{ownerState:t}=e;return t.dense},style:{minHeight:32,paddingTop:4,paddingBottom:4,...t.typography.body2,["& .".concat(y.root," svg")]:{fontSize:"1.25rem"}}}]}})),E=n.forwardRef(function(e,t){let r,s=(0,u.b)({props:e,name:"MuiMenuItem"}),{autoFocus:a=!1,component:l="li",dense:c=!1,divider:p=!1,disableGutters:m=!1,focusVisibleClassName:g,role:y="menuitem",tabIndex:v,className:b,...w}=s,E=n.useContext(d.A),_=n.useMemo(()=>({dense:c||E.dense||!1,disableGutters:m}),[E.dense,c,m]),C=n.useRef(null);(0,f.A)(()=>{a&&C.current&&C.current.focus()},[a]);let R={...s,dense:_.dense,divider:p,disableGutters:m},O=(e=>{let{disabled:t,dense:r,divider:n,disableGutters:o,selected:s,classes:a}=e,l=(0,i.A)({root:["root",r&&"dense",t&&"disabled",!o&&"gutters",n&&"divider",s&&"selected"]},A,a);return{...a,...l}})(s),k=(0,h.A)(C,t);return s.disabled||(r=void 0!==v?v:-1),(0,x.jsx)(d.A.Provider,{value:_,children:(0,x.jsx)(S,{ref:k,role:y,tabIndex:r,component:l,focusVisibleClassName:(0,o.A)(O.focusVisible,g),className:(0,o.A)(O.root,b),...w,ownerState:R,classes:O})})})},12526:(e,t,r)=>{"use strict";r.d(t,{A:()=>s,t:()=>i});var n=r(47951),o=r(45879);function i(e){return(0,o.Ay)("MuiDialogTitle",e)}let s=(0,n.A)("MuiDialogTitle",["root"])},15319:(e,t,r)=>{"use strict";r.d(t,{A:()=>w});var n=r(14232),o=r(69241),i=r(4697),s=r(54773),a=r(52196),l=r(47951),c=r(45879);function u(e){return(0,c.Ay)("MuiFormGroup",e)}(0,l.A)("MuiFormGroup",["root","row","error"]);var d=r(74073),p=r(27367),f=r(37876);let h=(0,s.Ay)("div",{name:"MuiFormGroup",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.row&&t.row]}})({display:"flex",flexDirection:"column",flexWrap:"wrap",variants:[{props:{row:!0},style:{flexDirection:"row"}}]}),m=n.forwardRef(function(e,t){let r=(0,a.b)({props:e,name:"MuiFormGroup"}),{className:n,row:s=!1,...l}=r,c=(0,d.A)(),m=(0,p.A)({props:r,muiFormControl:c,states:["error"]}),g={...r,row:s,error:m.error},y=(e=>{let{classes:t,row:r,error:n}=e;return(0,i.A)({root:["root",r&&"row",n&&"error"]},u,t)})(g);return(0,f.jsx)(h,{className:(0,o.A)(y.root,n),ownerState:g,ref:t,...l})});function g(e){return(0,c.Ay)("MuiRadioGroup",e)}(0,l.A)("MuiRadioGroup",["root","row","error"]);var y=r(66313),v=r(53322),b=r(71655),A=r(27449);let w=n.forwardRef(function(e,t){let{actions:r,children:s,className:a,defaultValue:l,name:c,onChange:u,value:d,...p}=e,h=n.useRef(null),w=(e=>{let{classes:t,row:r,error:n}=e;return(0,i.A)({root:["root",r&&"row",n&&"error"]},g,t)})(e),[x,S]=(0,v.A)({controlled:d,default:l,name:"RadioGroup"});n.useImperativeHandle(r,()=>({focus:()=>{let e=h.current.querySelector("input:not(:disabled):checked");e||(e=h.current.querySelector("input:not(:disabled)")),e&&e.focus()}}),[]);let E=(0,y.A)(t,h),_=(0,A.A)(c),C=n.useMemo(()=>({name:_,onChange(e){S(e.target.value),u&&u(e,e.target.value)},value:x}),[_,u,S,x]);return(0,f.jsx)(b.A.Provider,{value:C,children:(0,f.jsx)(m,{role:"radiogroup",ref:E,className:(0,o.A)(w.root,a),...p,children:s})})})},15783:(e,t,r)=>{"use strict";function n(e=window){let t=e.document.documentElement.clientWidth;return e.innerWidth-t}r.d(t,{A:()=>n})},17838:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"m4 12 1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8z"}),"ArrowUpward")},24751:(e,t,r)=>{"use strict";function n(e,t=166){let r;function o(...n){let i=()=>{e.apply(this,n)};clearTimeout(r),r=setTimeout(i,t)}return o.clear=()=>{clearTimeout(r)},o}r.d(t,{A:()=>n})},24766:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var n=r(14232),o=r(53880),i=r(43165),s=r(74253),a=r(30566),l=r(6299),c=r(66313),u=r(37876);function d(e){return"scale(".concat(e,", ").concat(e**2,")")}let p={entering:{opacity:1,transform:d(1)},entered:{opacity:1,transform:"none"}},f="undefined"!=typeof navigator&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),h=n.forwardRef(function(e,t){let{addEndListener:r,appear:h=!0,children:m,easing:g,in:y,onEnter:v,onEntered:b,onEntering:A,onExit:w,onExited:x,onExiting:S,style:E,timeout:_="auto",TransitionComponent:C=s.Ay,...R}=e,O=(0,o.A)(),k=n.useRef(),I=(0,a.A)(),T=n.useRef(null),M=(0,c.A)(T,(0,i.A)(m),t),P=e=>t=>{if(e){let r=T.current;void 0===t?e(r):e(r,t)}},j=P(A),N=P((e,t)=>{let r;(0,l.q)(e);let{duration:n,delay:o,easing:i}=(0,l.c)({style:E,timeout:_,easing:g},{mode:"enter"});"auto"===_?k.current=r=I.transitions.getAutoHeightDuration(e.clientHeight):r=n,e.style.transition=[I.transitions.create("opacity",{duration:r,delay:o}),I.transitions.create("transform",{duration:f?r:.666*r,delay:o,easing:i})].join(","),v&&v(e,t)}),L=P(b),B=P(S),F=P(e=>{let t,{duration:r,delay:n,easing:o}=(0,l.c)({style:E,timeout:_,easing:g},{mode:"exit"});"auto"===_?k.current=t=I.transitions.getAutoHeightDuration(e.clientHeight):t=r,e.style.transition=[I.transitions.create("opacity",{duration:t,delay:n}),I.transitions.create("transform",{duration:f?t:.666*t,delay:f?n:n||.333*t,easing:o})].join(","),e.style.opacity=0,e.style.transform=d(.75),w&&w(e)}),D=P(x);return(0,u.jsx)(C,{appear:h,in:y,nodeRef:T,onEnter:N,onEntered:L,onEntering:j,onExit:F,onExited:D,onExiting:B,addEndListener:e=>{"auto"===_&&O.start(k.current||0,e),r&&r(T.current,e)},timeout:"auto"===_?null:_,...R,children:(e,t)=>{let{ownerState:r,...o}=t;return n.cloneElement(m,{style:{opacity:0,transform:d(.75),visibility:"exited"!==e||y?void 0:"hidden",...p[e],...E,...m.props.style},ref:M,...o})}})});h&&(h.muiSupportAuto=!0);let m=h},24933:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(14232),o=r(69241),i=r(4697),s=r(54773),a=r(52196),l=r(47951),c=r(45879);function u(e){return(0,c.Ay)("MuiCardContent",e)}(0,l.A)("MuiCardContent",["root"]);var d=r(37876);let p=(0,s.Ay)("div",{name:"MuiCardContent",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:16,"&:last-child":{paddingBottom:24}}),f=n.forwardRef(function(e,t){let r=(0,a.b)({props:e,name:"MuiCardContent"}),{className:n,component:s="div",...l}=r,c={...r,component:s},f=(e=>{let{classes:t}=e;return(0,i.A)({root:["root"]},u,t)})(c);return(0,d.jsx)(p,{as:s,className:(0,o.A)(f.root,n),ownerState:c,ref:t,...l})})},26872:(e,t,r)=>{"use strict";r.d(t,{A:()=>v});var n=r(14232),o=r(4697),i=r(80027),s=r(68275),a=r(54773),l=r(53322),c=r(74073),u=r(68197),d=r(47951),p=r(45879);function f(e){return(0,p.Ay)("PrivateSwitchBase",e)}(0,d.A)("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);var h=r(67360),m=r(37876);let g=(0,a.Ay)(u.A)({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:e=>{let{edge:t,ownerState:r}=e;return"start"===t&&"small"!==r.size},style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:e=>{let{edge:t,ownerState:r}=e;return"end"===t&&"small"!==r.size},style:{marginRight:-12}}]}),y=(0,a.Ay)("input",{shouldForwardProp:s.A})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),v=n.forwardRef(function(e,t){let{autoFocus:r,checked:n,checkedIcon:s,defaultChecked:a,disabled:u,disableFocusRipple:d=!1,edge:p=!1,icon:v,id:b,inputProps:A,inputRef:w,name:x,onBlur:S,onChange:E,onFocus:_,readOnly:C,required:R=!1,tabIndex:O,type:k,value:I,slots:T={},slotProps:M={},...P}=e,[j,N]=(0,l.A)({controlled:n,default:!!a,name:"SwitchBase",state:"checked"}),L=(0,c.A)(),B=u;L&&void 0===B&&(B=L.disabled);let F="checkbox"===k||"radio"===k,D={...e,checked:j,disabled:B,disableFocusRipple:d,edge:p},$=(e=>{let{classes:t,checked:r,disabled:n,edge:s}=e,a={root:["root",r&&"checked",n&&"disabled",s&&"edge".concat((0,i.A)(s))],input:["input"]};return(0,o.A)(a,f,t)})(D),z={slots:T,slotProps:{input:A,...M}},[W,U]=(0,h.A)("root",{ref:t,elementType:g,className:$.root,shouldForwardComponentProp:!0,externalForwardedProps:{...z,component:"span",...P},getSlotProps:e=>({...e,onFocus:t=>{var r;null==(r=e.onFocus)||r.call(e,t),_&&_(t),L&&L.onFocus&&L.onFocus(t)},onBlur:t=>{var r;null==(r=e.onBlur)||r.call(e,t),S&&S(t),L&&L.onBlur&&L.onBlur(t)}}),ownerState:D,additionalProps:{centerRipple:!0,focusRipple:!d,disabled:B,role:void 0,tabIndex:null}}),[H,q]=(0,h.A)("input",{ref:w,elementType:y,className:$.input,externalForwardedProps:z,getSlotProps:e=>({onChange:t=>{var r;null==(r=e.onChange)||r.call(e,t),(e=>{if(e.nativeEvent.defaultPrevented)return;let t=e.target.checked;N(t),E&&E(e,t)})(t)}}),ownerState:D,additionalProps:{autoFocus:r,checked:n,defaultChecked:a,disabled:B,id:F?b:void 0,name:x,readOnly:C,required:R,tabIndex:O,type:k,..."checkbox"===k&&void 0===I?{}:{value:I}}});return(0,m.jsxs)(W,{...U,children:[(0,m.jsx)(H,{...q}),j?s:v]})})},27367:(e,t,r)=>{"use strict";function n(e){let{props:t,states:r,muiFormControl:n}=e;return r.reduce((e,r)=>(e[r]=t[r],n&&void 0===t[r]&&(e[r]=n[r]),e),{})}r.d(t,{A:()=>n})},30929:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var n=r(14232),o=r(69241),i=r(4697),s=r(54773),a=r(52196),l=r(67360),c=r(36540),u=r(47951),d=r(45879);function p(e){return(0,d.Ay)("MuiBackdrop",e)}(0,u.A)("MuiBackdrop",["root","invisible"]);var f=r(37876);let h=(0,s.Ay)("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.invisible&&t.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),m=n.forwardRef(function(e,t){let r=(0,a.b)({props:e,name:"MuiBackdrop"}),{children:n,className:s,component:u="div",invisible:d=!1,open:m,components:g={},componentsProps:y={},slotProps:v={},slots:b={},TransitionComponent:A,transitionDuration:w,...x}=r,S={...r,component:u,invisible:d},E=(e=>{let{classes:t,invisible:r}=e;return(0,i.A)({root:["root",r&&"invisible"]},p,t)})(S),_={slots:{transition:A,root:g.Root,...b},slotProps:{...y,...v}},[C,R]=(0,l.A)("root",{elementType:h,externalForwardedProps:_,className:(0,o.A)(E.root,s),ownerState:S}),[O,k]=(0,l.A)("transition",{elementType:c.A,externalForwardedProps:_,ownerState:S});return(0,f.jsx)(O,{in:m,timeout:w,...x,...k,children:(0,f.jsx)(C,{"aria-hidden":!0,...R,classes:E,ref:t,children:n})})})},31771:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"m20 12-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8z"}),"ArrowDownward")},35656:(e,t,r)=>{"use strict";r.d(t,{A:()=>w});var n=r(14232),o=r(69241),i=r(4697),s=r(74073),a=r(54773),l=r(82987),c=r(52196),u=r(77018),d=r(80027),p=r(47951),f=r(45879);function h(e){return(0,f.Ay)("MuiFormControlLabel",e)}let m=(0,p.A)("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]);var g=r(27367),y=r(67360),v=r(37876);let b=(0,a.Ay)("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{["& .".concat(m.label)]:t.label},t.root,t["labelPlacement".concat((0,d.A)(r.labelPlacement))]]}})((0,l.A)(e=>{let{theme:t}=e;return{display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,["&.".concat(m.disabled)]:{cursor:"default"},["& .".concat(m.label)]:{["&.".concat(m.disabled)]:{color:(t.vars||t).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:e=>{let{labelPlacement:t}=e;return"start"===t||"top"===t||"bottom"===t},style:{marginLeft:16}}]}})),A=(0,a.Ay)("span",{name:"MuiFormControlLabel",slot:"Asterisk",overridesResolver:(e,t)=>t.asterisk})((0,l.A)(e=>{let{theme:t}=e;return{["&.".concat(m.error)]:{color:(t.vars||t).palette.error.main}}})),w=n.forwardRef(function(e,t){var r;let a=(0,c.b)({props:e,name:"MuiFormControlLabel"}),{checked:l,className:p,componentsProps:f={},control:m,disabled:w,disableTypography:x,inputRef:S,label:E,labelPlacement:_="end",name:C,onChange:R,required:O,slots:k={},slotProps:I={},value:T,...M}=a,P=(0,s.A)(),j=null!=(r=null!=w?w:m.props.disabled)?r:null==P?void 0:P.disabled,N=null!=O?O:m.props.required,L={disabled:j,required:N};["checked","name","onChange","value","inputRef"].forEach(e=>{void 0===m.props[e]&&void 0!==a[e]&&(L[e]=a[e])});let B=(0,g.A)({props:a,muiFormControl:P,states:["error"]}),F={...a,disabled:j,labelPlacement:_,required:N,error:B.error},D=(e=>{let{classes:t,disabled:r,labelPlacement:n,error:o,required:s}=e,a={root:["root",r&&"disabled","labelPlacement".concat((0,d.A)(n)),o&&"error",s&&"required"],label:["label",r&&"disabled"],asterisk:["asterisk",o&&"error"]};return(0,i.A)(a,h,t)})(F),$={slots:k,slotProps:{...f,...I}},[z,W]=(0,y.A)("typography",{elementType:u.A,externalForwardedProps:$,ownerState:F}),U=E;return null==U||U.type===u.A||x||(U=(0,v.jsx)(z,{component:"span",...W,className:(0,o.A)(D.label,null==W?void 0:W.className),children:U})),(0,v.jsxs)(b,{className:(0,o.A)(D.root,p),ownerState:F,ref:t,...M,children:[n.cloneElement(m,L),N?(0,v.jsxs)("div",{children:[U,(0,v.jsxs)(A,{ownerState:F,"aria-hidden":!0,className:D.asterisk,children:[" ","*"]})]}):U]})})},36540:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(14232),o=r(74253),i=r(43165),s=r(30566),a=r(6299),l=r(66313),c=r(37876);let u={entering:{opacity:1},entered:{opacity:1}},d=n.forwardRef(function(e,t){let r=(0,s.A)(),d={enter:r.transitions.duration.enteringScreen,exit:r.transitions.duration.leavingScreen},{addEndListener:p,appear:f=!0,children:h,easing:m,in:g,onEnter:y,onEntered:v,onEntering:b,onExit:A,onExited:w,onExiting:x,style:S,timeout:E=d,TransitionComponent:_=o.Ay,...C}=e,R=n.useRef(null),O=(0,l.A)(R,(0,i.A)(h),t),k=e=>t=>{if(e){let r=R.current;void 0===t?e(r):e(r,t)}},I=k(b),T=k((e,t)=>{(0,a.q)(e);let n=(0,a.c)({style:S,timeout:E,easing:m},{mode:"enter"});e.style.webkitTransition=r.transitions.create("opacity",n),e.style.transition=r.transitions.create("opacity",n),y&&y(e,t)}),M=k(v),P=k(x),j=k(e=>{let t=(0,a.c)({style:S,timeout:E,easing:m},{mode:"exit"});e.style.webkitTransition=r.transitions.create("opacity",t),e.style.transition=r.transitions.create("opacity",t),A&&A(e)}),N=k(w);return(0,c.jsx)(_,{appear:f,in:g,nodeRef:R,onEnter:T,onEntered:M,onEntering:I,onExit:j,onExited:N,onExiting:P,addEndListener:e=>{p&&p(R.current,e)},timeout:E,...C,children:(e,t)=>{let{ownerState:r,...o}=t;return n.cloneElement(h,{style:{opacity:0,visibility:"exited"!==e||g?void 0:"hidden",...u[e],...S,...h.props.style},ref:O,...o})}})})},40642:(e,t,r)=>{"use strict";r.d(t,{A:()=>s,g:()=>i});var n=r(47951),o=r(45879);function i(e){return(0,o.Ay)("MuiInputBase",e)}let s=(0,n.A)("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"])},43165:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(14232);function o(e){return parseInt(n.version,10)>=19?e?.props?.ref||null:e?.ref||null}},43583:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(44471);function o(e){return(0,n.A)(e).defaultView||window}},44471:(e,t,r)=>{"use strict";function n(e){return e&&e.ownerDocument||document}r.d(t,{A:()=>n})},45867:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(69241);function o(e,t){if(!e)return t;if("function"==typeof e||"function"==typeof t)return r=>{let o="function"==typeof t?t(r):t,i="function"==typeof e?e({...r,...o}):e,s=(0,n.A)(null==r?void 0:r.className,null==o?void 0:o.className,null==i?void 0:i.className);return{...o,...i,...!!s&&{className:s},...(null==o?void 0:o.style)&&(null==i?void 0:i.style)&&{style:{...o.style,...i.style}},...(null==o?void 0:o.sx)&&(null==i?void 0:i.sx)&&{sx:[...Array.isArray(o.sx)?o.sx:[o.sx],...Array.isArray(i.sx)?i.sx:[i.sx]]}}};let r=(0,n.A)(null==t?void 0:t.className,null==e?void 0:e.className);return{...t,...e,...!!r&&{className:r},...(null==t?void 0:t.style)&&(null==e?void 0:e.style)&&{style:{...t.style,...e.style}},...(null==t?void 0:t.sx)&&(null==e?void 0:e.sx)&&{sx:[...Array.isArray(t.sx)?t.sx:[t.sx],...Array.isArray(e.sx)?e.sx:[e.sx]]}}}},47866:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(14232),o=r(69241),i=r(4697),s=r(77018),a=r(54773),l=r(52196),c=r(12526),u=r(199),d=r(37876);let p=(0,a.Ay)(s.A,{name:"MuiDialogTitle",slot:"Root",overridesResolver:(e,t)=>t.root})({padding:"16px 24px",flex:"0 0 auto"}),f=n.forwardRef(function(e,t){let r=(0,l.b)({props:e,name:"MuiDialogTitle"}),{className:s,id:a,...f}=r,h=(e=>{let{classes:t}=e;return(0,i.A)({root:["root"]},c.t,t)})(r),{titleId:m=a}=n.useContext(u.A);return(0,d.jsx)(p,{component:"h2",className:(0,o.A)(h.root,s),ownerState:r,ref:t,variant:"h6",id:null!=a?a:m,...f})})},48504:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},49790:(e,t,r)=>{"use strict";function n(e){return null!=e&&!(Array.isArray(e)&&0===e.length)}function o(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e&&(n(e.value)&&""!==e.value||t&&n(e.defaultValue)&&""!==e.defaultValue)}function i(e){return e.startAdornment}r.d(t,{gr:()=>i,lq:()=>o})},53322:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(14232);let o=function({controlled:e,default:t,name:r,state:o="value"}){let{current:i}=n.useRef(void 0!==e),[s,a]=n.useState(t),l=n.useCallback(e=>{i||a(e)},[]);return[i?e:s,l]}},54087:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.11-.9-2-2-2H4c-1.11 0-2 .89-2 2v10c0 1.1.89 2 2 2H0v2h24v-2zm-7-3.53v-2.19c-2.78 0-4.61.85-6 2.72.56-2.67 2.11-5.33 6-5.87V7l4 3.73z"}),"ScreenShare")},54338:(e,t,r)=>{"use strict";var n,o,i,s,a,l,c,u,d,p,f,h;r.d(t,{ij:()=>G}),function(e){e.STRING="string",e.NUMBER="number",e.INTEGER="integer",e.BOOLEAN="boolean",e.ARRAY="array",e.OBJECT="object"}(n||(n={})),function(e){e.LANGUAGE_UNSPECIFIED="language_unspecified",e.PYTHON="python"}(o||(o={})),function(e){e.OUTCOME_UNSPECIFIED="outcome_unspecified",e.OUTCOME_OK="outcome_ok",e.OUTCOME_FAILED="outcome_failed",e.OUTCOME_DEADLINE_EXCEEDED="outcome_deadline_exceeded"}(i||(i={}));let m=["user","model","function","system"];!function(e){e.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",e.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",e.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",e.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",e.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",e.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY"}(s||(s={})),function(e){e.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",e.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",e.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",e.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",e.BLOCK_NONE="BLOCK_NONE"}(a||(a={})),function(e){e.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",e.NEGLIGIBLE="NEGLIGIBLE",e.LOW="LOW",e.MEDIUM="MEDIUM",e.HIGH="HIGH"}(l||(l={})),function(e){e.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",e.SAFETY="SAFETY",e.OTHER="OTHER"}(c||(c={})),function(e){e.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",e.STOP="STOP",e.MAX_TOKENS="MAX_TOKENS",e.SAFETY="SAFETY",e.RECITATION="RECITATION",e.LANGUAGE="LANGUAGE",e.BLOCKLIST="BLOCKLIST",e.PROHIBITED_CONTENT="PROHIBITED_CONTENT",e.SPII="SPII",e.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",e.OTHER="OTHER"}(u||(u={})),function(e){e.TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",e.RETRIEVAL_QUERY="RETRIEVAL_QUERY",e.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",e.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",e.CLASSIFICATION="CLASSIFICATION",e.CLUSTERING="CLUSTERING"}(d||(d={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.AUTO="AUTO",e.ANY="ANY",e.NONE="NONE"}(p||(p={})),function(e){e.MODE_UNSPECIFIED="MODE_UNSPECIFIED",e.MODE_DYNAMIC="MODE_DYNAMIC"}(f||(f={}));class g extends Error{constructor(e){super(`[GoogleGenerativeAI Error]: ${e}`)}}class y extends g{constructor(e,t){super(e),this.response=t}}class v extends g{constructor(e,t,r,n){super(e),this.status=t,this.statusText=r,this.errorDetails=n}}class b extends g{}class A extends g{}!function(e){e.GENERATE_CONTENT="generateContent",e.STREAM_GENERATE_CONTENT="streamGenerateContent",e.COUNT_TOKENS="countTokens",e.EMBED_CONTENT="embedContent",e.BATCH_EMBED_CONTENTS="batchEmbedContents"}(h||(h={}));class w{constructor(e,t,r,n,o){this.model=e,this.task=t,this.apiKey=r,this.stream=n,this.requestOptions=o}toString(){var e,t;let r=(null==(e=this.requestOptions)?void 0:e.apiVersion)||"v1beta",n=(null==(t=this.requestOptions)?void 0:t.baseUrl)||"https://generativelanguage.googleapis.com",o=`${n}/${r}/${this.model}:${this.task}`;return this.stream&&(o+="?alt=sse"),o}}async function x(e){var t;let r=new Headers;r.append("Content-Type","application/json"),r.append("x-goog-api-client",function(e){let t=[];return(null==e?void 0:e.apiClient)&&t.push(e.apiClient),t.push("genai-js/0.24.1"),t.join(" ")}(e.requestOptions)),r.append("x-goog-api-key",e.apiKey);let n=null==(t=e.requestOptions)?void 0:t.customHeaders;if(n){if(!(n instanceof Headers))try{n=new Headers(n)}catch(e){throw new b(`unable to convert customHeaders value ${JSON.stringify(n)} to Headers: ${e.message}`)}for(let[e,t]of n.entries()){if("x-goog-api-key"===e)throw new b(`Cannot set reserved header name ${e}`);if("x-goog-api-client"===e)throw new b(`Header name ${e} can only be set using the apiClient field`);r.append(e,t)}}return r}async function S(e,t,r,n,o,i){let s=new w(e,t,r,n,i);return{url:s.toString(),fetchOptions:Object.assign(Object.assign({},function(e){let t={};if((null==e?void 0:e.signal)!==void 0||(null==e?void 0:e.timeout)>=0){let r=new AbortController;(null==e?void 0:e.timeout)>=0&&setTimeout(()=>r.abort(),e.timeout),(null==e?void 0:e.signal)&&e.signal.addEventListener("abort",()=>{r.abort()}),t.signal=r.signal}return t}(i)),{method:"POST",headers:await x(s),body:o})}}async function E(e,t,r,n,o,i={},s=fetch){let{url:a,fetchOptions:l}=await S(e,t,r,n,o,i);return _(a,l,s)}async function _(e,t,r=fetch){let n;try{n=await r(e,t)}catch(r){var o=r,i=e;let t=o;throw"AbortError"===t.name?(t=new A(`Request aborted when fetching ${i.toString()}: ${o.message}`)).stack=o.stack:o instanceof v||o instanceof b||((t=new g(`Error fetching from ${i.toString()}: ${o.message}`)).stack=o.stack),t}return n.ok||await C(n,e),n}async function C(e,t){let r,n="";try{let t=await e.json();n=t.error.message,t.error.details&&(n+=` ${JSON.stringify(t.error.details)}`,r=t.error.details)}catch(e){}throw new v(`Error fetching from ${t.toString()}: [${e.status} ${e.statusText}] ${n}`,e.status,e.statusText,r)}function R(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),I(e.candidates[0]))throw new y(`${T(e)}`,e);return function(e){var t,r,n,o;let i=[];if(null==(r=null==(t=e.candidates)?void 0:t[0].content)?void 0:r.parts)for(let t of null==(o=null==(n=e.candidates)?void 0:n[0].content)?void 0:o.parts)t.text&&i.push(t.text),t.executableCode&&i.push("\n```"+t.executableCode.language+"\n"+t.executableCode.code+"\n```\n"),t.codeExecutionResult&&i.push("\n```\n"+t.codeExecutionResult.output+"\n```\n");return i.length>0?i.join(""):""}(e)}if(e.promptFeedback)throw new y(`Text not available. ${T(e)}`,e);return""},e.functionCall=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),I(e.candidates[0]))throw new y(`${T(e)}`,e);return console.warn("response.functionCall() is deprecated. Use response.functionCalls() instead."),O(e)[0]}if(e.promptFeedback)throw new y(`Function call not available. ${T(e)}`,e)},e.functionCalls=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&console.warn(`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),I(e.candidates[0]))throw new y(`${T(e)}`,e);return O(e)}if(e.promptFeedback)throw new y(`Function call not available. ${T(e)}`,e)},e}function O(e){var t,r,n,o;let i=[];if(null==(r=null==(t=e.candidates)?void 0:t[0].content)?void 0:r.parts)for(let t of null==(o=null==(n=e.candidates)?void 0:n[0].content)?void 0:o.parts)t.functionCall&&i.push(t.functionCall);return i.length>0?i:void 0}let k=[u.RECITATION,u.SAFETY,u.LANGUAGE];function I(e){return!!e.finishReason&&k.includes(e.finishReason)}function T(e){var t,r,n;let o="";if((!e.candidates||0===e.candidates.length)&&e.promptFeedback)o+="Response was blocked",(null==(t=e.promptFeedback)?void 0:t.blockReason)&&(o+=` due to ${e.promptFeedback.blockReason}`),(null==(r=e.promptFeedback)?void 0:r.blockReasonMessage)&&(o+=`: ${e.promptFeedback.blockReasonMessage}`);else if(null==(n=e.candidates)?void 0:n[0]){let t=e.candidates[0];I(t)&&(o+=`Candidate was blocked due to ${t.finishReason}`,t.finishMessage&&(o+=`: ${t.finishMessage}`))}return o}function M(e){return this instanceof M?(this.v=e,this):new M(e)}"function"==typeof SuppressedError&&SuppressedError;let P=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;async function j(e){let t=[],r=e.getReader();for(;;){let{done:e,value:n}=await r.read();if(e)return R(function(e){let t=e[e.length-1],r={promptFeedback:null==t?void 0:t.promptFeedback};for(let t of e){if(t.candidates){let e=0;for(let n of t.candidates)if(r.candidates||(r.candidates=[]),r.candidates[e]||(r.candidates[e]={index:e}),r.candidates[e].citationMetadata=n.citationMetadata,r.candidates[e].groundingMetadata=n.groundingMetadata,r.candidates[e].finishReason=n.finishReason,r.candidates[e].finishMessage=n.finishMessage,r.candidates[e].safetyRatings=n.safetyRatings,n.content&&n.content.parts){r.candidates[e].content||(r.candidates[e].content={role:n.content.role||"user",parts:[]});let t={};for(let o of n.content.parts)o.text&&(t.text=o.text),o.functionCall&&(t.functionCall=o.functionCall),o.executableCode&&(t.executableCode=o.executableCode),o.codeExecutionResult&&(t.codeExecutionResult=o.codeExecutionResult),0===Object.keys(t).length&&(t.text=""),r.candidates[e].content.parts.push(t)}e++}t.usageMetadata&&(r.usageMetadata=t.usageMetadata)}return r}(t));t.push(n)}}async function N(e,t,r,n){let[o,i]=(function(e){let t=e.getReader();return new ReadableStream({start(e){let r="";return function n(){return t.read().then(({value:t,done:o})=>{let i;if(o)return r.trim()?void e.error(new g("Failed to parse stream")):void e.close();let s=(r+=t).match(P);for(;s;){try{i=JSON.parse(s[1])}catch(t){e.error(new g(`Error parsing JSON response: "${s[1]}"`));return}e.enqueue(i),s=(r=r.substring(s[0].length)).match(P)}return n()}).catch(e=>{let t=e;throw t.stack=e.stack,t="AbortError"===t.name?new A("Request aborted when reading from the stream"):new g("Error reading from the stream")})}()}})})((await E(t,h.STREAM_GENERATE_CONTENT,e,!0,JSON.stringify(r),n)).body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))).tee();return{stream:function(e){return function(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),i=[];return n={},s("next"),s("throw"),s("return"),n[Symbol.asyncIterator]=function(){return this},n;function s(e){o[e]&&(n[e]=function(t){return new Promise(function(r,n){i.push([e,t,r,n])>1||a(e,t)})})}function a(e,t){try{var r;(r=o[e](t)).value instanceof M?Promise.resolve(r.value.v).then(l,c):u(i[0][2],r)}catch(e){u(i[0][3],e)}}function l(e){a("next",e)}function c(e){a("throw",e)}function u(e,t){e(t),i.shift(),i.length&&a(i[0][0],i[0][1])}}(this,arguments,function*(){let t=e.getReader();for(;;){let{value:e,done:r}=yield M(t.read());if(r)break;yield yield M(R(e))}})}(o),response:j(i)}}async function L(e,t,r,n){let o=await E(t,h.GENERATE_CONTENT,e,!1,JSON.stringify(r),n);return{response:R(await o.json())}}function B(e){if(null!=e){if("string"==typeof e)return{role:"system",parts:[{text:e}]};if(e.text)return{role:"system",parts:[e]};if(e.parts)if(!e.role)return{role:"system",parts:e.parts};else return e}}function F(e){let t=[];if("string"==typeof e)t=[{text:e}];else for(let r of e)"string"==typeof r?t.push({text:r}):t.push(r);var r=t;let n={role:"user",parts:[]},o={role:"function",parts:[]},i=!1,s=!1;for(let e of r)"functionResponse"in e?(o.parts.push(e),s=!0):(n.parts.push(e),i=!0);if(i&&s)throw new g("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!i&&!s)throw new g("No content is provided for sending chat message.");return i?n:o}function D(e){let t;return t=e.contents?e:{contents:[F(e)]},e.systemInstruction&&(t.systemInstruction=B(e.systemInstruction)),t}let $=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],z={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function W(e){var t;if(void 0===e.candidates||0===e.candidates.length)return!1;let r=null==(t=e.candidates[0])?void 0:t.content;if(void 0===r||void 0===r.parts||0===r.parts.length)return!1;for(let e of r.parts)if(void 0===e||0===Object.keys(e).length||void 0!==e.text&&""===e.text)return!1;return!0}let U="SILENT_ERROR";class H{constructor(e,t,r,n={}){this.model=t,this.params=r,this._requestOptions=n,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=e,(null==r?void 0:r.history)&&(!function(e){let t=!1;for(let r of e){let{role:e,parts:n}=r;if(!t&&"user"!==e)throw new g(`First content should be with role 'user', got ${e}`);if(!m.includes(e))throw new g(`Each item should include role field. Got ${e} but valid roles are: ${JSON.stringify(m)}`);if(!Array.isArray(n))throw new g("Content should have 'parts' property with an array of Parts");if(0===n.length)throw new g("Each Content should have at least one part");let o={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(let e of n)for(let t of $)t in e&&(o[t]+=1);let i=z[e];for(let t of $)if(!i.includes(t)&&o[t]>0)throw new g(`Content with role '${e}' can't contain '${t}' part`);t=!0}}(r.history),this._history=r.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(e,t={}){var r,n,o,i,s,a;let l;await this._sendPromise;let c=F(e),u={safetySettings:null==(r=this.params)?void 0:r.safetySettings,generationConfig:null==(n=this.params)?void 0:n.generationConfig,tools:null==(o=this.params)?void 0:o.tools,toolConfig:null==(i=this.params)?void 0:i.toolConfig,systemInstruction:null==(s=this.params)?void 0:s.systemInstruction,cachedContent:null==(a=this.params)?void 0:a.cachedContent,contents:[...this._history,c]},d=Object.assign(Object.assign({},this._requestOptions),t);return this._sendPromise=this._sendPromise.then(()=>L(this._apiKey,this.model,u,d)).then(e=>{var t;if(W(e.response)){this._history.push(c);let r=Object.assign({parts:[],role:"model"},null==(t=e.response.candidates)?void 0:t[0].content);this._history.push(r)}else{let t=T(e.response);t&&console.warn(`sendMessage() was unsuccessful. ${t}. Inspect response object for details.`)}l=e}).catch(e=>{throw this._sendPromise=Promise.resolve(),e}),await this._sendPromise,l}async sendMessageStream(e,t={}){var r,n,o,i,s,a;await this._sendPromise;let l=F(e),c={safetySettings:null==(r=this.params)?void 0:r.safetySettings,generationConfig:null==(n=this.params)?void 0:n.generationConfig,tools:null==(o=this.params)?void 0:o.tools,toolConfig:null==(i=this.params)?void 0:i.toolConfig,systemInstruction:null==(s=this.params)?void 0:s.systemInstruction,cachedContent:null==(a=this.params)?void 0:a.cachedContent,contents:[...this._history,l]},u=Object.assign(Object.assign({},this._requestOptions),t),d=N(this._apiKey,this.model,c,u);return this._sendPromise=this._sendPromise.then(()=>d).catch(e=>{throw Error(U)}).then(e=>e.response).then(e=>{if(W(e)){this._history.push(l);let t=Object.assign({},e.candidates[0].content);t.role||(t.role="model"),this._history.push(t)}else{let t=T(e);t&&console.warn(`sendMessageStream() was unsuccessful. ${t}. Inspect response object for details.`)}}).catch(e=>{e.message!==U&&console.error(e)}),d}}async function q(e,t,r,n){return(await E(t,h.COUNT_TOKENS,e,!1,JSON.stringify(r),n)).json()}async function V(e,t,r,n){return(await E(t,h.EMBED_CONTENT,e,!1,JSON.stringify(r),n)).json()}async function X(e,t,r,n){let o=r.requests.map(e=>Object.assign(Object.assign({},e),{model:t}));return(await E(t,h.BATCH_EMBED_CONTENTS,e,!1,JSON.stringify({requests:o}),n)).json()}class K{constructor(e,t,r={}){this.apiKey=e,this._requestOptions=r,t.model.includes("/")?this.model=t.model:this.model=`models/${t.model}`,this.generationConfig=t.generationConfig||{},this.safetySettings=t.safetySettings||[],this.tools=t.tools,this.toolConfig=t.toolConfig,this.systemInstruction=B(t.systemInstruction),this.cachedContent=t.cachedContent}async generateContent(e,t={}){var r;let n=D(e),o=Object.assign(Object.assign({},this._requestOptions),t);return L(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(r=this.cachedContent)?void 0:r.name},n),o)}async generateContentStream(e,t={}){var r;let n=D(e),o=Object.assign(Object.assign({},this._requestOptions),t);return N(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(r=this.cachedContent)?void 0:r.name},n),o)}startChat(e){var t;return new H(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null==(t=this.cachedContent)?void 0:t.name},e),this._requestOptions)}async countTokens(e,t={}){let r=function(e,t){var r;let n={model:null==t?void 0:t.model,generationConfig:null==t?void 0:t.generationConfig,safetySettings:null==t?void 0:t.safetySettings,tools:null==t?void 0:t.tools,toolConfig:null==t?void 0:t.toolConfig,systemInstruction:null==t?void 0:t.systemInstruction,cachedContent:null==(r=null==t?void 0:t.cachedContent)?void 0:r.name,contents:[]},o=null!=e.generateContentRequest;if(e.contents){if(o)throw new b("CountTokensRequest must have one of contents or generateContentRequest, not both.");n.contents=e.contents}else if(o)n=Object.assign(Object.assign({},n),e.generateContentRequest);else{let t=F(e);n.contents=[t]}return{generateContentRequest:n}}(e,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),n=Object.assign(Object.assign({},this._requestOptions),t);return q(this.apiKey,this.model,r,n)}async embedContent(e,t={}){let r="string"==typeof e||Array.isArray(e)?{content:F(e)}:e,n=Object.assign(Object.assign({},this._requestOptions),t);return V(this.apiKey,this.model,r,n)}async batchEmbedContents(e,t={}){let r=Object.assign(Object.assign({},this._requestOptions),t);return X(this.apiKey,this.model,e,r)}}class G{constructor(e){this.apiKey=e}getGenerativeModel(e,t){if(!e.model)throw new g("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new K(this.apiKey,e,t)}getGenerativeModelFromCachedContent(e,t,r){if(!e.name)throw new b("Cached content must contain a `name` field.");if(!e.model)throw new b("Cached content must contain a `model` field.");for(let r of["model","systemInstruction"])if((null==t?void 0:t[r])&&e[r]&&(null==t?void 0:t[r])!==e[r]){if("model"===r&&(t.model.startsWith("models/")?t.model.replace("models/",""):t.model)===(e.model.startsWith("models/")?e.model.replace("models/",""):e.model))continue;throw new b(`Different value for "${r}" specified in modelParams (${t[r]}) and cachedContent (${e[r]})`)}let n=Object.assign(Object.assign({},t),{model:e.model,tools:e.tools,toolConfig:e.toolConfig,systemInstruction:e.systemInstruction,cachedContent:e});return new K(this.apiKey,n,r)}}},54538:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(14232),o=r(69241),i=r(4697),s=r(54773),a=r(52196),l=r(11951),c=r(47951),u=r(45879);function d(e){return(0,u.Ay)("MuiCard",e)}(0,c.A)("MuiCard",["root"]);var p=r(37876);let f=(0,s.Ay)(l.A,{name:"MuiCard",slot:"Root",overridesResolver:(e,t)=>t.root})({overflow:"hidden"}),h=n.forwardRef(function(e,t){let r=(0,a.b)({props:e,name:"MuiCard"}),{className:n,raised:s=!1,...l}=r,c={...r,raised:s},u=(e=>{let{classes:t}=e;return(0,i.A)({root:["root"]},d,t)})(c);return(0,p.jsx)(f,{className:(0,o.A)(u.root,n),elevation:s?8:void 0,ref:t,ownerState:c,...l})})},55509:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=r(14232).createContext(void 0)},58492:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M19 11h-1.7c0 .74-.16 1.43-.43 2.05l1.23 1.23c.56-.98.9-2.09.9-3.28m-4.02.17c0-.06.02-.11.02-.17V5c0-1.66-1.34-3-3-3S9 3.34 9 5v.18zM4.27 3 3 4.27l6.01 6.01V11c0 1.66 1.33 3 2.99 3 .22 0 .44-.03.65-.08l1.66 1.66c-.71.33-1.5.52-2.31.52-2.76 0-5.3-2.1-5.3-5.1H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c.91-.13 1.77-.45 2.54-.9L19.73 21 21 19.73z"}),"MicOff")},58865:(e,t,r)=>{"use strict";r.d(t,{A:()=>T});var n=r(14232),o=r(69241),i=r(4697),s=r(126),a=r(54773),l=r(82987),c=r(52196),u=r(67360),d=r(80027),p=r(78457),f=r(11951),h=r(47951),m=r(45879);function g(e){return(0,m.Ay)("MuiAlert",e)}let y=(0,h.A)("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);var v=r(70946),b=r(31057),A=r(37876);let w=(0,b.A)((0,A.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),x=(0,b.A)((0,A.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),S=(0,b.A)((0,A.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),E=(0,b.A)((0,A.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),_=(0,b.A)((0,A.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close"),C=(0,a.Ay)(f.A,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t[r.variant],t["".concat(r.variant).concat((0,d.A)(r.color||r.severity))]]}})((0,l.A)(e=>{let{theme:t}=e,r="light"===t.palette.mode?s.e$:s.a,n="light"===t.palette.mode?s.a:s.e$;return{...t.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(t.palette).filter((0,p.A)(["light"])).map(e=>{let[o]=e;return{props:{colorSeverity:o,variant:"standard"},style:{color:t.vars?t.vars.palette.Alert["".concat(o,"Color")]:r(t.palette[o].light,.6),backgroundColor:t.vars?t.vars.palette.Alert["".concat(o,"StandardBg")]:n(t.palette[o].light,.9),["& .".concat(y.icon)]:t.vars?{color:t.vars.palette.Alert["".concat(o,"IconColor")]}:{color:t.palette[o].main}}}}),...Object.entries(t.palette).filter((0,p.A)(["light"])).map(e=>{let[n]=e;return{props:{colorSeverity:n,variant:"outlined"},style:{color:t.vars?t.vars.palette.Alert["".concat(n,"Color")]:r(t.palette[n].light,.6),border:"1px solid ".concat((t.vars||t).palette[n].light),["& .".concat(y.icon)]:t.vars?{color:t.vars.palette.Alert["".concat(n,"IconColor")]}:{color:t.palette[n].main}}}}),...Object.entries(t.palette).filter((0,p.A)(["dark"])).map(e=>{let[r]=e;return{props:{colorSeverity:r,variant:"filled"},style:{fontWeight:t.typography.fontWeightMedium,...t.vars?{color:t.vars.palette.Alert["".concat(r,"FilledColor")],backgroundColor:t.vars.palette.Alert["".concat(r,"FilledBg")]}:{backgroundColor:"dark"===t.palette.mode?t.palette[r].dark:t.palette[r].main,color:t.palette.getContrastText(t.palette[r].main)}}}})]}})),R=(0,a.Ay)("div",{name:"MuiAlert",slot:"Icon",overridesResolver:(e,t)=>t.icon})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),O=(0,a.Ay)("div",{name:"MuiAlert",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0",minWidth:0,overflow:"auto"}),k=(0,a.Ay)("div",{name:"MuiAlert",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),I={success:(0,A.jsx)(w,{fontSize:"inherit"}),warning:(0,A.jsx)(x,{fontSize:"inherit"}),error:(0,A.jsx)(S,{fontSize:"inherit"}),info:(0,A.jsx)(E,{fontSize:"inherit"})},T=n.forwardRef(function(e,t){let r=(0,c.b)({props:e,name:"MuiAlert"}),{action:n,children:s,className:a,closeText:l="Close",color:p,components:f={},componentsProps:h={},icon:m,iconMapping:y=I,onClose:b,role:w="alert",severity:x="success",slotProps:S={},slots:E={},variant:T="standard",...M}=r,P={...r,color:p,severity:x,variant:T,colorSeverity:p||x},j=(e=>{let{variant:t,color:r,severity:n,classes:o}=e,s={root:["root","color".concat((0,d.A)(r||n)),"".concat(t).concat((0,d.A)(r||n)),"".concat(t)],icon:["icon"],message:["message"],action:["action"]};return(0,i.A)(s,g,o)})(P),N={slots:{closeButton:f.CloseButton,closeIcon:f.CloseIcon,...E},slotProps:{...h,...S}},[L,B]=(0,u.A)("root",{ref:t,shouldForwardComponentProp:!0,className:(0,o.A)(j.root,a),elementType:C,externalForwardedProps:{...N,...M},ownerState:P,additionalProps:{role:w,elevation:0}}),[F,D]=(0,u.A)("icon",{className:j.icon,elementType:R,externalForwardedProps:N,ownerState:P}),[$,z]=(0,u.A)("message",{className:j.message,elementType:O,externalForwardedProps:N,ownerState:P}),[W,U]=(0,u.A)("action",{className:j.action,elementType:k,externalForwardedProps:N,ownerState:P}),[H,q]=(0,u.A)("closeButton",{elementType:v.A,externalForwardedProps:N,ownerState:P}),[V,X]=(0,u.A)("closeIcon",{elementType:_,externalForwardedProps:N,ownerState:P});return(0,A.jsxs)(L,{...B,children:[!1!==m?(0,A.jsx)(F,{...D,children:m||y[x]||I[x]}):null,(0,A.jsx)($,{...z,children:s}),null!=n?(0,A.jsx)(W,{...U,children:n}):null,null==n&&b?(0,A.jsx)(W,{...U,children:(0,A.jsx)(H,{size:"small","aria-label":l,title:l,color:"inherit",onClick:b,...q,children:(0,A.jsx)(V,{fontSize:"small",...X})})}):null]})})},60129:(e,t,r)=>{"use strict";r.d(t,{ck:()=>j,Sh:()=>P,Ay:()=>L,Oj:()=>M,WC:()=>T});var n,o=r(69135),i=r(14232),s=r(69241),a=r(4697),l=r(61637),c=r(43583),u=r(7061),d=r(99659),p=r(24751),f=r(37876);function h(e){return parseInt(e,10)||0}let m={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function g(e){return function(e){for(let t in e)return!1;return!0}(e)||0===e.outerHeightStyle&&!e.overflowing}let y=i.forwardRef(function(e,t){let{onChange:r,maxRows:n,minRows:o=1,style:s,value:a,...y}=e,{current:v}=i.useRef(null!=a),b=i.useRef(null),A=(0,l.A)(t,b),w=i.useRef(null),x=i.useRef(null),S=i.useCallback(()=>{let t=b.current,r=x.current;if(!t||!r)return;let i=(0,c.A)(t).getComputedStyle(t);if("0px"===i.width)return{outerHeightStyle:0,overflowing:!1};r.style.width=i.width,r.value=t.value||e.placeholder||"x","\n"===r.value.slice(-1)&&(r.value+=" ");let s=i.boxSizing,a=h(i.paddingBottom)+h(i.paddingTop),l=h(i.borderBottomWidth)+h(i.borderTopWidth),u=r.scrollHeight;r.value="x";let d=r.scrollHeight,p=u;return o&&(p=Math.max(Number(o)*d,p)),n&&(p=Math.min(Number(n)*d,p)),{outerHeightStyle:(p=Math.max(p,d))+("border-box"===s?a+l:0),overflowing:1>=Math.abs(p-u)}},[n,o,e.placeholder]),E=(0,u.A)(()=>{let e=b.current,t=S();if(!e||!t||g(t))return!1;let r=t.outerHeightStyle;return null!=w.current&&w.current!==r}),_=i.useCallback(()=>{let e=b.current,t=S();if(!e||!t||g(t))return;let r=t.outerHeightStyle;w.current!==r&&(w.current=r,e.style.height="".concat(r,"px")),e.style.overflow=t.overflowing?"hidden":""},[S]),C=i.useRef(-1);return(0,d.A)(()=>{let e,t=(0,p.A)(_),r=null==b?void 0:b.current;if(!r)return;let n=(0,c.A)(r);return n.addEventListener("resize",t),"undefined"!=typeof ResizeObserver&&(e=new ResizeObserver(()=>{E()&&(e.unobserve(r),cancelAnimationFrame(C.current),_(),C.current=requestAnimationFrame(()=>{e.observe(r)}))})).observe(r),()=>{t.clear(),cancelAnimationFrame(C.current),n.removeEventListener("resize",t),e&&e.disconnect()}},[S,_,E]),(0,d.A)(()=>{_()}),(0,f.jsxs)(i.Fragment,{children:[(0,f.jsx)("textarea",{value:a,onChange:e=>{v||_(),r&&r(e)},ref:A,rows:o,style:s,...y}),(0,f.jsx)("textarea",{"aria-hidden":!0,className:e.className,readOnly:!0,ref:x,tabIndex:-1,style:{...m.shadow,...s,paddingTop:0,paddingBottom:0}})]})});var v=r(79550),b=r(27367),A=r(55509),w=r(74073),x=r(54773),S=r(37551),E=r(82987),_=r(52196),C=r(80027),R=r(66313),O=r(91411),k=r(49790),I=r(40642);let T=(e,t)=>{let{ownerState:r}=e;return[t.root,r.formControl&&t.formControl,r.startAdornment&&t.adornedStart,r.endAdornment&&t.adornedEnd,r.error&&t.error,"small"===r.size&&t.sizeSmall,r.multiline&&t.multiline,r.color&&t["color".concat((0,C.A)(r.color))],r.fullWidth&&t.fullWidth,r.hiddenLabel&&t.hiddenLabel]},M=(e,t)=>{let{ownerState:r}=e;return[t.input,"small"===r.size&&t.inputSizeSmall,r.multiline&&t.inputMultiline,"search"===r.type&&t.inputTypeSearch,r.startAdornment&&t.inputAdornedStart,r.endAdornment&&t.inputAdornedEnd,r.hiddenLabel&&t.inputHiddenLabel]},P=(0,x.Ay)("div",{name:"MuiInputBase",slot:"Root",overridesResolver:T})((0,E.A)(e=>{let{theme:t}=e;return{...t.typography.body1,color:(t.vars||t).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",["&.".concat(I.A.disabled)]:{color:(t.vars||t).palette.text.disabled,cursor:"default"},variants:[{props:e=>{let{ownerState:t}=e;return t.multiline},style:{padding:"4px 0 5px"}},{props:e=>{let{ownerState:t,size:r}=e;return t.multiline&&"small"===r},style:{paddingTop:1}},{props:e=>{let{ownerState:t}=e;return t.fullWidth},style:{width:"100%"}}]}})),j=(0,x.Ay)("input",{name:"MuiInputBase",slot:"Input",overridesResolver:M})((0,E.A)(e=>{let{theme:t}=e,r="light"===t.palette.mode,n={color:"currentColor",...t.vars?{opacity:t.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5},transition:t.transitions.create("opacity",{duration:t.transitions.duration.shorter})},o={opacity:"0 !important"},i=t.vars?{opacity:t.vars.opacity.inputPlaceholder}:{opacity:r?.42:.5};return{font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%","&::-webkit-input-placeholder":n,"&::-moz-placeholder":n,"&::-ms-input-placeholder":n,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},["label[data-shrink=false] + .".concat(I.A.formControl," &")]:{"&::-webkit-input-placeholder":o,"&::-moz-placeholder":o,"&::-ms-input-placeholder":o,"&:focus::-webkit-input-placeholder":i,"&:focus::-moz-placeholder":i,"&:focus::-ms-input-placeholder":i},["&.".concat(I.A.disabled)]:{opacity:1,WebkitTextFillColor:(t.vars||t).palette.text.disabled},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableInjectingGlobalStyles},style:{animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}}},{props:{size:"small"},style:{paddingTop:1}},{props:e=>{let{ownerState:t}=e;return t.multiline},style:{height:"auto",resize:"none",padding:0,paddingTop:0}},{props:{type:"search"},style:{MozAppearance:"textfield"}}]}})),N=(0,S.Dp)({"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}),L=i.forwardRef(function(e,t){var r;let l=(0,_.b)({props:e,name:"MuiInputBase"}),{"aria-describedby":c,autoComplete:u,autoFocus:d,className:p,color:h,components:m={},componentsProps:g={},defaultValue:x,disabled:S,disableInjectingGlobalStyles:E,endAdornment:T,error:M,fullWidth:L=!1,id:B,inputComponent:F="input",inputProps:D={},inputRef:$,margin:z,maxRows:W,minRows:U,multiline:H=!1,name:q,onBlur:V,onChange:X,onClick:K,onFocus:G,onKeyDown:Y,onKeyUp:J,placeholder:Q,readOnly:Z,renderSuffix:ee,rows:et,size:er,slotProps:en={},slots:eo={},startAdornment:ei,type:es="text",value:ea,...el}=l,ec=null!=D.value?D.value:ea,{current:eu}=i.useRef(null!=ec),ed=i.useRef(),ep=i.useCallback(e=>{},[]),ef=(0,R.A)(ed,$,D.ref,ep),[eh,em]=i.useState(!1),eg=(0,w.A)(),ey=(0,b.A)({props:l,muiFormControl:eg,states:["color","disabled","error","hiddenLabel","size","required","filled"]});ey.focused=eg?eg.focused:eh,i.useEffect(()=>{!eg&&S&&eh&&(em(!1),V&&V())},[eg,S,eh,V]);let ev=eg&&eg.onFilled,eb=eg&&eg.onEmpty,eA=i.useCallback(e=>{(0,k.lq)(e)?ev&&ev():eb&&eb()},[ev,eb]);(0,O.A)(()=>{eu&&eA({value:ec})},[ec,eA,eu]),i.useEffect(()=>{eA(ed.current)},[]);let ew=F,ex=D;H&&"input"===ew&&(ex=et?{type:void 0,minRows:et,maxRows:et,...ex}:{type:void 0,maxRows:W,minRows:U,...ex},ew=y),i.useEffect(()=>{eg&&eg.setAdornedStart(!!ei)},[eg,ei]);let eS={...l,color:ey.color||"primary",disabled:ey.disabled,endAdornment:T,error:ey.error,focused:ey.focused,formControl:eg,fullWidth:L,hiddenLabel:ey.hiddenLabel,multiline:H,size:ey.size,startAdornment:ei,type:es},eE=(e=>{let{classes:t,color:r,disabled:n,error:o,endAdornment:i,focused:s,formControl:l,fullWidth:c,hiddenLabel:u,multiline:d,readOnly:p,size:f,startAdornment:h,type:m}=e,g={root:["root","color".concat((0,C.A)(r)),n&&"disabled",o&&"error",c&&"fullWidth",s&&"focused",l&&"formControl",f&&"medium"!==f&&"size".concat((0,C.A)(f)),d&&"multiline",h&&"adornedStart",i&&"adornedEnd",u&&"hiddenLabel",p&&"readOnly"],input:["input",n&&"disabled","search"===m&&"inputTypeSearch",d&&"inputMultiline","small"===f&&"inputSizeSmall",u&&"inputHiddenLabel",h&&"inputAdornedStart",i&&"inputAdornedEnd",p&&"readOnly"]};return(0,a.A)(g,I.g,t)})(eS),e_=eo.root||m.Root||P,eC=en.root||g.root||{},eR=eo.input||m.Input||j;return ex={...ex,...null!=(r=en.input)?r:g.input},(0,f.jsxs)(i.Fragment,{children:[!E&&"function"==typeof N&&(n||(n=(0,f.jsx)(N,{}))),(0,f.jsxs)(e_,{...eC,ref:t,onClick:e=>{ed.current&&e.currentTarget===e.target&&ed.current.focus(),K&&K(e)},...el,...!(0,v.A)(e_)&&{ownerState:{...eS,...eC.ownerState}},className:(0,s.A)(eE.root,eC.className,p,Z&&"MuiInputBase-readOnly"),children:[ei,(0,f.jsx)(A.A.Provider,{value:null,children:(0,f.jsx)(eR,{"aria-invalid":ey.error,"aria-describedby":c,autoComplete:u,autoFocus:d,defaultValue:x,disabled:ey.disabled,id:B,onAnimationStart:e=>{eA("mui-auto-fill-cancel"===e.animationName?ed.current:{value:"x"})},name:q,placeholder:Q,readOnly:Z,required:ey.required,rows:et,value:ec,onKeyDown:Y,onKeyUp:J,type:es,...ex,...!(0,v.A)(eR)&&{as:ew,ownerState:{...eS,...ex.ownerState}},ref:ef,className:(0,s.A)(eE.input,ex.className,Z&&"MuiInputBase-readOnly"),onBlur:e=>{V&&V(e),D.onBlur&&D.onBlur(e),eg&&eg.onBlur?eg.onBlur(e):em(!1)},onChange:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];if(!eu){let t=e.target||ed.current;if(null==t)throw Error((0,o.A)(1));eA({value:t.value})}D.onChange&&D.onChange(e,...r),X&&X(e,...r)},onFocus:e=>{G&&G(e),D.onFocus&&D.onFocus(e),eg&&eg.onFocus?eg.onFocus(e):em(!0)}})}),T,ee?ee({...ey,startAdornment:ei}):null]})]})})},60455:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"}),"Delete")},61927:(e,t,r)=>{"use strict";r.d(t,{A:()=>eR});var n,o=r(14232),i=r(69241),s=r(12535),a=r(4697),l=r(43165),c=r(69135),u=r(53855);let d=r(44471).A;var p=r(80027),f=r(62844),h=r(86863),m=r(63009);let g=r(15783).A;var y=r(66313),v=r(91411);let b=r(43583).A;var A=r(37876);function w(e,t,r){return e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:r?null:e.firstChild}function x(e,t,r){return e===t?r?e.firstChild:e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:r?null:e.lastChild}function S(e,t){if(void 0===t)return!0;let r=e.innerText;return void 0===r&&(r=e.textContent),0!==(r=r.trim().toLowerCase()).length&&(t.repeating?r[0]===t.keys[0]:r.startsWith(t.keys.join("")))}function E(e,t,r,n,o,i){let s=!1,a=o(e,t,!!t&&r);for(;a;){if(a===e.firstChild){if(s)return!1;s=!0}let t=!n&&(a.disabled||"true"===a.getAttribute("aria-disabled"));if(a.hasAttribute("tabindex")&&S(a,i)&&!t)return a.focus(),!0;a=o(e,a,r)}return!1}let _=o.forwardRef(function(e,t){let{actions:r,autoFocus:n=!1,autoFocusItem:i=!1,children:s,className:a,disabledItemsFocusable:l=!1,disableListWrap:c=!1,onKeyDown:u,variant:p="selectedMenu",...f}=e,h=o.useRef(null),_=o.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});(0,v.A)(()=>{n&&h.current.focus()},[n]),o.useImperativeHandle(r,()=>({adjustStyleForScrollbar:(e,t)=>{let{direction:r}=t,n=!h.current.style.width;if(e.clientHeight<h.current.clientHeight&&n){let t="".concat(g(b(e)),"px");h.current.style["rtl"===r?"paddingLeft":"paddingRight"]=t,h.current.style.width="calc(100% + ".concat(t,")")}return h.current}}),[]);let C=(0,y.A)(h,t),R=-1;o.Children.forEach(s,(e,t)=>{if(!o.isValidElement(e)){R===t&&(R+=1)>=s.length&&(R=-1);return}e.props.disabled||("selectedMenu"===p&&e.props.selected?R=t:-1===R&&(R=t)),R===t&&(e.props.disabled||e.props.muiSkipListHighlight||e.type.muiSkipListHighlight)&&(R+=1)>=s.length&&(R=-1)});let O=o.Children.map(s,(e,t)=>{if(t===R){let t={};return i&&(t.autoFocus=!0),void 0===e.props.tabIndex&&"selectedMenu"===p&&(t.tabIndex=0),o.cloneElement(e,t)}return e});return(0,A.jsx)(m.A,{role:"menu",ref:C,className:a,onKeyDown:e=>{let t=h.current,r=e.key;if(e.ctrlKey||e.metaKey||e.altKey){u&&u(e);return}let n=d(t).activeElement;if("ArrowDown"===r)e.preventDefault(),E(t,n,c,l,w);else if("ArrowUp"===r)e.preventDefault(),E(t,n,c,l,x);else if("Home"===r)e.preventDefault(),E(t,null,c,l,w);else if("End"===r)e.preventDefault(),E(t,null,c,l,x);else if(1===r.length){let o=_.current,i=r.toLowerCase(),s=performance.now();o.keys.length>0&&(s-o.lastTime>500?(o.keys=[],o.repeating=!0,o.previousKeyMatched=!0):o.repeating&&i!==o.keys[0]&&(o.repeating=!1)),o.lastTime=s,o.keys.push(i);let a=n&&!o.repeating&&S(n,o);o.previousKeyMatched&&(a||E(t,n,!1,l,w,o))?e.preventDefault():o.previousKeyMatched=!1}u&&u(e)},tabIndex:n?0:-1,...f,children:O})});var C=r(79550),R=r(54773),O=r(52196);let k=r(24751).A;var I=r(24766),T=r(9050),M=r(11951),P=r(47951),j=r(45879);function N(e){return(0,j.Ay)("MuiPopover",e)}(0,P.A)("MuiPopover",["root","paper"]);var L=r(67360),B=r(45867);function F(e,t){let r=0;return"number"==typeof t?r=t:"center"===t?r=e.height/2:"bottom"===t&&(r=e.height),r}function D(e,t){let r=0;return"number"==typeof t?r=t:"center"===t?r=e.width/2:"right"===t&&(r=e.width),r}function $(e){return[e.horizontal,e.vertical].map(e=>"number"==typeof e?"".concat(e,"px"):e).join(" ")}function z(e){return"function"==typeof e?e():e}let W=(0,R.Ay)(T.A,{name:"MuiPopover",slot:"Root",overridesResolver:(e,t)=>t.root})({}),U=(0,R.Ay)(M.A,{name:"MuiPopover",slot:"Paper",overridesResolver:(e,t)=>t.paper})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),H=o.forwardRef(function(e,t){let r=(0,O.b)({props:e,name:"MuiPopover"}),{action:n,anchorEl:s,anchorOrigin:l={vertical:"top",horizontal:"left"},anchorPosition:c,anchorReference:u="anchorEl",children:p,className:f,container:h,elevation:m=8,marginThreshold:g=16,open:y,PaperProps:v={},slots:w={},slotProps:x={},transformOrigin:S={vertical:"top",horizontal:"left"},TransitionComponent:E,transitionDuration:_="auto",TransitionProps:R={},disableScrollLock:T=!1,...M}=r,P=o.useRef(),j={...r,anchorOrigin:l,anchorReference:u,elevation:m,marginThreshold:g,transformOrigin:S,TransitionComponent:E,transitionDuration:_,TransitionProps:R},H=(e=>{let{classes:t}=e;return(0,a.A)({root:["root"],paper:["paper"]},N,t)})(j),q=o.useCallback(()=>{if("anchorPosition"===u)return c;let e=z(s),t=(e&&1===e.nodeType?e:d(P.current).body).getBoundingClientRect();return{top:t.top+F(t,l.vertical),left:t.left+D(t,l.horizontal)}},[s,l.horizontal,l.vertical,c,u]),V=o.useCallback(e=>({vertical:F(e,S.vertical),horizontal:D(e,S.horizontal)}),[S.horizontal,S.vertical]),X=o.useCallback(e=>{let t={width:e.offsetWidth,height:e.offsetHeight},r=V(t);if("none"===u)return{top:null,left:null,transformOrigin:$(r)};let n=q(),o=n.top-r.vertical,i=n.left-r.horizontal,a=o+t.height,l=i+t.width,c=b(z(s)),d=c.innerHeight-g,p=c.innerWidth-g;if(null!==g&&o<g){let e=o-g;o-=e,r.vertical+=e}else if(null!==g&&a>d){let e=a-d;o-=e,r.vertical+=e}if(null!==g&&i<g){let e=i-g;i-=e,r.horizontal+=e}else if(l>p){let e=l-p;i-=e,r.horizontal+=e}return{top:"".concat(Math.round(o),"px"),left:"".concat(Math.round(i),"px"),transformOrigin:$(r)}},[s,u,q,V,g]),[K,G]=o.useState(y),Y=o.useCallback(()=>{let e=P.current;if(!e)return;let t=X(e);null!==t.top&&e.style.setProperty("top",t.top),null!==t.left&&(e.style.left=t.left),e.style.transformOrigin=t.transformOrigin,G(!0)},[X]);o.useEffect(()=>(T&&window.addEventListener("scroll",Y),()=>window.removeEventListener("scroll",Y)),[s,T,Y]),o.useEffect(()=>{y&&Y()}),o.useImperativeHandle(n,()=>y?{updatePosition:()=>{Y()}}:null,[y,Y]),o.useEffect(()=>{if(!y)return;let e=k(()=>{Y()}),t=b(z(s));return t.addEventListener("resize",e),()=>{e.clear(),t.removeEventListener("resize",e)}},[s,y,Y]);let J=_,Q={slots:{transition:E,...w},slotProps:{transition:R,paper:v,...x}},[Z,ee]=(0,L.A)("transition",{elementType:I.A,externalForwardedProps:Q,ownerState:j,getSlotProps:e=>({...e,onEntering:(t,r)=>{var n;null==(n=e.onEntering)||n.call(e,t,r),Y()},onExited:t=>{var r;null==(r=e.onExited)||r.call(e,t),G(!1)}}),additionalProps:{appear:!0,in:y}});"auto"!==_||Z.muiSupportAuto||(J=void 0);let et=h||(s?d(z(s)).body:void 0),[er,{slots:en,slotProps:eo,...ei}]=(0,L.A)("root",{ref:t,elementType:W,externalForwardedProps:{...Q,...M},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:w.backdrop},slotProps:{backdrop:(0,B.A)("function"==typeof x.backdrop?x.backdrop(j):x.backdrop,{invisible:!0})},container:et,open:y},ownerState:j,className:(0,i.A)(H.root,f)}),[es,ea]=(0,L.A)("paper",{ref:P,className:H.paper,elementType:U,externalForwardedProps:Q,shouldForwardComponentProp:!0,additionalProps:{elevation:m,style:K?void 0:{opacity:0}},ownerState:j});return(0,A.jsx)(er,{...ei,...!(0,C.A)(er)&&{slots:en,slotProps:eo,disableScrollLock:T},children:(0,A.jsx)(Z,{...ee,timeout:J,children:(0,A.jsx)(es,{...ea,children:p})})})});var q=r(68275);function V(e){return(0,j.Ay)("MuiMenu",e)}(0,P.A)("MuiMenu",["root","paper","list"]);let X={vertical:"top",horizontal:"right"},K={vertical:"top",horizontal:"left"},G=(0,R.Ay)(H,{shouldForwardProp:e=>(0,q.A)(e)||"classes"===e,name:"MuiMenu",slot:"Root",overridesResolver:(e,t)=>t.root})({}),Y=(0,R.Ay)(U,{name:"MuiMenu",slot:"Paper",overridesResolver:(e,t)=>t.paper})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),J=(0,R.Ay)(_,{name:"MuiMenu",slot:"List",overridesResolver:(e,t)=>t.list})({outline:0}),Q=o.forwardRef(function(e,t){let r=(0,O.b)({props:e,name:"MuiMenu"}),{autoFocus:n=!0,children:s,className:l,disableAutoFocusItem:c=!1,MenuListProps:u={},onClose:d,open:p,PaperProps:m={},PopoverClasses:g,transitionDuration:y="auto",TransitionProps:{onEntering:v,...b}={},variant:w="selectedMenu",slots:x={},slotProps:S={},...E}=r,_=(0,f.I)(),C={...r,autoFocus:n,disableAutoFocusItem:c,MenuListProps:u,onEntering:v,PaperProps:m,transitionDuration:y,TransitionProps:b,variant:w},R=(e=>{let{classes:t}=e;return(0,a.A)({root:["root"],paper:["paper"],list:["list"]},V,t)})(C),k=n&&!c&&p,I=o.useRef(null),T=-1;o.Children.map(s,(e,t)=>{o.isValidElement(e)&&(e.props.disabled||("selectedMenu"===w&&e.props.selected?T=t:-1===T&&(T=t)))});let M={slots:x,slotProps:{list:u,transition:b,paper:m,...S}},P=(0,h.A)({elementType:x.root,externalSlotProps:S.root,ownerState:C,className:[R.root,l]}),[j,N]=(0,L.A)("paper",{className:R.paper,elementType:Y,externalForwardedProps:M,shouldForwardComponentProp:!0,ownerState:C}),[B,F]=(0,L.A)("list",{className:(0,i.A)(R.list,u.className),elementType:J,shouldForwardComponentProp:!0,externalForwardedProps:M,getSlotProps:e=>({...e,onKeyDown:t=>{var r;"Tab"===t.key&&(t.preventDefault(),d&&d(t,"tabKeyDown")),null==(r=e.onKeyDown)||r.call(e,t)}}),ownerState:C}),D="function"==typeof M.slotProps.transition?M.slotProps.transition(C):M.slotProps.transition;return(0,A.jsx)(G,{onClose:d,anchorOrigin:{vertical:"bottom",horizontal:_?"right":"left"},transformOrigin:_?X:K,slots:{root:x.root,paper:j,backdrop:x.backdrop,...x.transition&&{transition:x.transition}},slotProps:{root:P,paper:N,backdrop:"function"==typeof S.backdrop?S.backdrop(C):S.backdrop,transition:{...D,onEntering:function(){for(var e,t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];((e,t)=>{I.current&&I.current.adjustStyleForScrollbar(e,{direction:_?"rtl":"ltr"}),v&&v(e,t)})(...r),null==D||null==(e=D.onEntering)||e.call(D,...r)}}},open:p,ref:t,transitionDuration:y,ownerState:C,...E,classes:g,children:(0,A.jsx)(B,{actions:I,autoFocus:n&&(-1===T||c),autoFocusItem:k,variant:w,...F,children:s})})});function Z(e){return(0,j.Ay)("MuiNativeSelect",e)}let ee=(0,P.A)("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),et=(0,R.Ay)("select")(e=>{let{theme:t}=e;return{MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":{borderRadius:0},["&.".concat(ee.disabled)]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(t.vars||t).palette.background.paper},variants:[{props:e=>{let{ownerState:t}=e;return"filled"!==t.variant&&"outlined"!==t.variant},style:{"&&&":{paddingRight:24,minWidth:16}}},{props:{variant:"filled"},style:{"&&&":{paddingRight:32}}},{props:{variant:"outlined"},style:{borderRadius:(t.vars||t).shape.borderRadius,"&:focus":{borderRadius:(t.vars||t).shape.borderRadius},"&&&":{paddingRight:32}}}]}}),er=(0,R.Ay)(et,{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:q.A,overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.select,t[r.variant],r.error&&t.error,{["&.".concat(ee.multiple)]:t.multiple}]}})({}),en=(0,R.Ay)("svg")(e=>{let{theme:t}=e;return{position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(t.vars||t).palette.action.active,["&.".concat(ee.disabled)]:{color:(t.vars||t).palette.action.disabled},variants:[{props:e=>{let{ownerState:t}=e;return t.open},style:{transform:"rotate(180deg)"}},{props:{variant:"filled"},style:{right:7}},{props:{variant:"outlined"},style:{right:7}}]}}),eo=(0,R.Ay)(en,{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.icon,r.variant&&t["icon".concat((0,p.A)(r.variant))],r.open&&t.iconOpen]}})({}),ei=o.forwardRef(function(e,t){let{className:r,disabled:n,error:s,IconComponent:l,inputRef:c,variant:u="standard",...d}=e,f={...e,disabled:n,variant:u,error:s},h=(e=>{let{classes:t,variant:r,disabled:n,multiple:o,open:i,error:s}=e,l={select:["select",r,n&&"disabled",o&&"multiple",s&&"error"],icon:["icon","icon".concat((0,p.A)(r)),i&&"iconOpen",n&&"disabled"]};return(0,a.A)(l,Z,t)})(f);return(0,A.jsxs)(o.Fragment,{children:[(0,A.jsx)(er,{ownerState:f,className:(0,i.A)(h.select,r),disabled:n,ref:c||t,...d}),e.multiple?null:(0,A.jsx)(eo,{as:l,ownerState:f,className:h.icon})]})});var es=r(49790),ea=r(46871),el=r(53322);function ec(e){return(0,j.Ay)("MuiSelect",e)}let eu=(0,P.A)("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),ed=(0,R.Ay)(et,{name:"MuiSelect",slot:"Select",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{["&.".concat(eu.select)]:t.select},{["&.".concat(eu.select)]:t[r.variant]},{["&.".concat(eu.error)]:t.error},{["&.".concat(eu.multiple)]:t.multiple}]}})({["&.".concat(eu.select)]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),ep=(0,R.Ay)(en,{name:"MuiSelect",slot:"Icon",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.icon,r.variant&&t["icon".concat((0,p.A)(r.variant))],r.open&&t.iconOpen]}})({}),ef=(0,R.Ay)("input",{shouldForwardProp:e=>(0,ea.A)(e)&&"classes"!==e,name:"MuiSelect",slot:"NativeInput",overridesResolver:(e,t)=>t.nativeInput})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function eh(e,t){return"object"==typeof t&&null!==t?e===t:String(e)===String(t)}let em=o.forwardRef(function(e,t){var r,s;let l,f,{"aria-describedby":h,"aria-label":m,autoFocus:g,autoWidth:v,children:b,className:w,defaultOpen:x,defaultValue:S,disabled:E,displayEmpty:_,error:C=!1,IconComponent:R,inputRef:O,labelId:k,MenuProps:I={},multiple:T,name:M,onBlur:P,onChange:j,onClose:N,onFocus:L,onOpen:B,open:F,readOnly:D,renderValue:$,required:z,SelectDisplayProps:W={},tabIndex:U,type:H,value:q,variant:V="standard",...X}=e,[K,G]=(0,el.A)({controlled:q,default:S,name:"Select"}),[Y,J]=(0,el.A)({controlled:F,default:x,name:"Select"}),Z=o.useRef(null),ee=o.useRef(null),[et,er]=o.useState(null),{current:en}=o.useRef(null!=F),[eo,ei]=o.useState(),ea=(0,y.A)(t,O),eu=o.useCallback(e=>{ee.current=e,e&&er(e)},[]),em=null==et?void 0:et.parentNode;o.useImperativeHandle(ea,()=>({focus:()=>{ee.current.focus()},node:Z.current,value:K}),[K]),o.useEffect(()=>{x&&Y&&et&&!en&&(ei(v?null:em.clientWidth),ee.current.focus())},[et,v]),o.useEffect(()=>{g&&ee.current.focus()},[g]),o.useEffect(()=>{if(!k)return;let e=d(ee.current).getElementById(k);if(e){let t=()=>{getSelection().isCollapsed&&ee.current.focus()};return e.addEventListener("click",t),()=>{e.removeEventListener("click",t)}}},[k]);let eg=(e,t)=>{e?B&&B(t):N&&N(t),en||(ei(v?null:em.clientWidth),J(e))},ey=o.Children.toArray(b),ev=null!==et&&Y;delete X["aria-invalid"];let eb=[],eA=!1;((0,es.lq)({value:K})||_)&&($?l=$(K):eA=!0);let ew=ey.map(e=>{let t;if(!o.isValidElement(e))return null;if(T){if(!Array.isArray(K))throw Error((0,c.A)(2));(t=K.some(t=>eh(t,e.props.value)))&&eA&&eb.push(e.props.children)}else(t=eh(K,e.props.value))&&eA&&(f=e.props.children);return o.cloneElement(e,{"aria-selected":t?"true":"false",onClick:t=>{let r;if(t.currentTarget.hasAttribute("tabindex")){if(T){r=Array.isArray(K)?K.slice():[];let t=K.indexOf(e.props.value);-1===t?r.push(e.props.value):r.splice(t,1)}else r=e.props.value;if(e.props.onClick&&e.props.onClick(t),K!==r&&(G(r),j)){let n=t.nativeEvent||t,o=new n.constructor(n.type,n);Object.defineProperty(o,"target",{writable:!0,value:{value:r,name:M}}),j(o,e)}T||eg(!1,t)}},onKeyUp:t=>{" "===t.key&&t.preventDefault(),e.props.onKeyUp&&e.props.onKeyUp(t)},role:"option",selected:t,value:void 0,"data-value":e.props.value})});eA&&(l=T?0===eb.length?null:eb.reduce((e,t,r)=>(e.push(t),r<eb.length-1&&e.push(", "),e),[]):f);let ex=eo;!v&&en&&et&&(ex=em.clientWidth);let eS=W.id||(M?"mui-component-select-".concat(M):void 0),eE={...e,variant:V,value:K,open:ev,error:C},e_=(e=>{let{classes:t,variant:r,disabled:n,multiple:o,open:i,error:s}=e,l={select:["select",r,n&&"disabled",o&&"multiple",s&&"error"],icon:["icon","icon".concat((0,p.A)(r)),i&&"iconOpen",n&&"disabled"],nativeInput:["nativeInput"]};return(0,a.A)(l,ec,t)})(eE),eC={...I.PaperProps,...null==(r=I.slotProps)?void 0:r.paper},eR=(0,u.A)();return(0,A.jsxs)(o.Fragment,{children:[(0,A.jsx)(ed,{as:"div",ref:eu,tabIndex:void 0!==U?U:E?null:0,role:"combobox","aria-controls":ev?eR:void 0,"aria-disabled":E?"true":void 0,"aria-expanded":ev?"true":"false","aria-haspopup":"listbox","aria-label":m,"aria-labelledby":[k,eS].filter(Boolean).join(" ")||void 0,"aria-describedby":h,"aria-required":z?"true":void 0,"aria-invalid":C?"true":void 0,onKeyDown:e=>{!D&&[" ","ArrowUp","ArrowDown","Enter"].includes(e.key)&&(e.preventDefault(),eg(!0,e))},onMouseDown:E||D?null:e=>{0===e.button&&(e.preventDefault(),ee.current.focus(),eg(!0,e))},onBlur:e=>{!ev&&P&&(Object.defineProperty(e,"target",{writable:!0,value:{value:K,name:M}}),P(e))},onFocus:L,...W,ownerState:eE,className:(0,i.A)(W.className,e_.select,w),id:eS,children:null!=(s=l)&&("string"!=typeof s||s.trim())?l:n||(n=(0,A.jsx)("span",{className:"notranslate","aria-hidden":!0,children:"​"}))}),(0,A.jsx)(ef,{"aria-invalid":C,value:Array.isArray(K)?K.join(","):K,name:M,ref:Z,"aria-hidden":!0,onChange:e=>{let t=ey.find(t=>t.props.value===e.target.value);void 0!==t&&(G(t.props.value),j&&j(e,t))},tabIndex:-1,disabled:E,className:e_.nativeInput,autoFocus:g,required:z,...X,ownerState:eE}),(0,A.jsx)(ep,{as:R,className:e_.icon,ownerState:eE}),(0,A.jsx)(Q,{id:"menu-".concat(M||""),anchorEl:em,open:ev,onClose:e=>{eg(!1,e)},anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},...I,slotProps:{...I.slotProps,list:{"aria-labelledby":k,role:"listbox","aria-multiselectable":T?"true":void 0,disableListWrap:!0,id:eR,...I.MenuListProps},paper:{...eC,style:{minWidth:ex,...null!=eC?eC.style:null}}},children:ew})]})});var eg=r(27367),ey=r(74073);let ev=(0,r(31057).A)((0,A.jsx)("path",{d:"M7 10l5 5 5-5z"}),"ArrowDropDown");var eb=r(74673),eA=r(63657),ew=r(73650);let ex={name:"MuiSelect",overridesResolver:(e,t)=>t.root,shouldForwardProp:e=>(0,q.A)(e)&&"variant"!==e,slot:"Root"},eS=(0,R.Ay)(eb.A,ex)(""),eE=(0,R.Ay)(ew.A,ex)(""),e_=(0,R.Ay)(eA.A,ex)(""),eC=o.forwardRef(function(e,t){let r=(0,O.b)({name:"MuiSelect",props:e}),{autoWidth:n=!1,children:c,classes:u={},className:d,defaultOpen:p=!1,displayEmpty:f=!1,IconComponent:h=ev,id:m,input:g,inputProps:v,label:b,labelId:w,MenuProps:x,multiple:S=!1,native:E=!1,onClose:_,onOpen:C,open:R,renderValue:k,SelectDisplayProps:I,variant:T="outlined",...M}=r,P=(0,ey.A)(),j=(0,eg.A)({props:r,muiFormControl:P,states:["variant","error"]}),N=j.variant||T,L={...r,variant:N,classes:u},B=(e=>{let{classes:t}=e,r=(0,a.A)({root:["root"]},ec,t);return{...t,...r}})(L),{root:F,...D}=B,$=g||({standard:(0,A.jsx)(eS,{ownerState:L}),outlined:(0,A.jsx)(eE,{label:b,ownerState:L}),filled:(0,A.jsx)(e_,{ownerState:L})})[N],z=(0,y.A)(t,(0,l.A)($));return(0,A.jsx)(o.Fragment,{children:o.cloneElement($,{inputComponent:E?ei:em,inputProps:{children:c,error:j.error,IconComponent:h,variant:N,type:void 0,multiple:S,...E?{id:m}:{autoWidth:n,defaultOpen:p,displayEmpty:f,labelId:w,MenuProps:x,onClose:_,onOpen:C,open:R,renderValue:k,SelectDisplayProps:{id:m,...I}},...v,classes:v?(0,s.A)(D,v.classes):D,...g?g.props.inputProps:{}},...(S&&E||f)&&"outlined"===N?{notched:!0}:{},ref:z,className:(0,i.A)($.props.className,d,B.root),...!g&&{variant:N},...M})})});eC.muiName="Select";let eR=eC},62435:(e,t,r)=>{"use strict";function n(...e){return e.reduce((e,t)=>null==t?e:function(...r){e.apply(this,r),t.apply(this,r)},()=>{})}r.d(t,{A:()=>n})},63657:(e,t,r)=>{"use strict";r.d(t,{A:()=>w});var n=r(14232),o=r(12535),i=r(4697),s=r(60129),a=r(68275),l=r(54773),c=r(82987),u=r(78457),d=r(52196),p=r(47951),f=r(45879);function h(e){return(0,f.Ay)("MuiFilledInput",e)}let m={...r(40642).A,...(0,p.A)("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])};var g=r(80027),y=r(37876);let v=(0,l.Ay)(s.Sh,{shouldForwardProp:e=>(0,a.A)(e)||"classes"===e,name:"MuiFilledInput",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[...(0,s.WC)(e,t),!r.disableUnderline&&t.underline]}})((0,c.A)(e=>{let{theme:t}=e,r="light"===t.palette.mode,n=r?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)";return{position:"relative",backgroundColor:t.vars?t.vars.palette.FilledInput.bg:n,borderTopLeftRadius:(t.vars||t).shape.borderRadius,borderTopRightRadius:(t.vars||t).shape.borderRadius,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut}),"&:hover":{backgroundColor:t.vars?t.vars.palette.FilledInput.hoverBg:r?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)","@media (hover: none)":{backgroundColor:t.vars?t.vars.palette.FilledInput.bg:n}},["&.".concat(m.focused)]:{backgroundColor:t.vars?t.vars.palette.FilledInput.bg:n},["&.".concat(m.disabled)]:{backgroundColor:t.vars?t.vars.palette.FilledInput.disabledBg:r?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)"},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableUnderline},style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:t.transitions.create("transform",{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut}),pointerEvents:"none"},["&.".concat(m.focused,":after")]:{transform:"scaleX(1) translateX(0)"},["&.".concat(m.error)]:{"&::before, &::after":{borderBottomColor:(t.vars||t).palette.error.main}},"&::before":{borderBottom:"1px solid ".concat(t.vars?"rgba(".concat(t.vars.palette.common.onBackgroundChannel," / ").concat(t.vars.opacity.inputUnderline,")"):r?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)"),left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:t.transitions.create("border-bottom-color",{duration:t.transitions.duration.shorter}),pointerEvents:"none"},["&:hover:not(.".concat(m.disabled,", .").concat(m.error,"):before")]:{borderBottom:"1px solid ".concat((t.vars||t).palette.text.primary)},["&.".concat(m.disabled,":before")]:{borderBottomStyle:"dotted"}}},...Object.entries(t.palette).filter((0,u.A)()).map(e=>{var r;let[n]=e;return{props:{disableUnderline:!1,color:n},style:{"&::after":{borderBottom:"2px solid ".concat(null==(r=(t.vars||t).palette[n])?void 0:r.main)}}}}),{props:e=>{let{ownerState:t}=e;return t.startAdornment},style:{paddingLeft:12}},{props:e=>{let{ownerState:t}=e;return t.endAdornment},style:{paddingRight:12}},{props:e=>{let{ownerState:t}=e;return t.multiline},style:{padding:"25px 12px 8px"}},{props:e=>{let{ownerState:t,size:r}=e;return t.multiline&&"small"===r},style:{paddingTop:21,paddingBottom:4}},{props:e=>{let{ownerState:t}=e;return t.multiline&&t.hiddenLabel},style:{paddingTop:16,paddingBottom:17}},{props:e=>{let{ownerState:t}=e;return t.multiline&&t.hiddenLabel&&"small"===t.size},style:{paddingTop:8,paddingBottom:9}}]}})),b=(0,l.Ay)(s.ck,{name:"MuiFilledInput",slot:"Input",overridesResolver:s.Oj})((0,c.A)(e=>{let{theme:t}=e;return{paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12,...!t.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:"light"===t.palette.mode?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:"light"===t.palette.mode?null:"#fff",caretColor:"light"===t.palette.mode?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}},...t.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[t.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{paddingTop:21,paddingBottom:4}},{props:e=>{let{ownerState:t}=e;return t.hiddenLabel},style:{paddingTop:16,paddingBottom:17}},{props:e=>{let{ownerState:t}=e;return t.startAdornment},style:{paddingLeft:0}},{props:e=>{let{ownerState:t}=e;return t.endAdornment},style:{paddingRight:0}},{props:e=>{let{ownerState:t}=e;return t.hiddenLabel&&"small"===t.size},style:{paddingTop:8,paddingBottom:9}},{props:e=>{let{ownerState:t}=e;return t.multiline},style:{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0}}]}})),A=n.forwardRef(function(e,t){var r,n,a,l;let c=(0,d.b)({props:e,name:"MuiFilledInput"}),{disableUnderline:u=!1,components:p={},componentsProps:f,fullWidth:m=!1,hiddenLabel:A,inputComponent:w="input",multiline:x=!1,slotProps:S,slots:E={},type:_="text",...C}=c,R={...c,disableUnderline:u,fullWidth:m,inputComponent:w,multiline:x,type:_},O=(e=>{let{classes:t,disableUnderline:r,startAdornment:n,endAdornment:o,size:s,hiddenLabel:a,multiline:l}=e,c={root:["root",!r&&"underline",n&&"adornedStart",o&&"adornedEnd","small"===s&&"size".concat((0,g.A)(s)),a&&"hiddenLabel",l&&"multiline"],input:["input"]},u=(0,i.A)(c,h,t);return{...t,...u}})(c),k={root:{ownerState:R},input:{ownerState:R}},I=(null!=S?S:f)?(0,o.A)(k,null!=S?S:f):k,T=null!=(n=null!=(r=E.root)?r:p.Root)?n:v,M=null!=(l=null!=(a=E.input)?a:p.Input)?l:b;return(0,y.jsx)(s.Ay,{slots:{root:T,input:M},slotProps:I,fullWidth:m,inputComponent:w,multiline:x,ref:t,type:_,...C,classes:O})});A.muiName="Input";let w=A},65774:(e,t,r)=>{"use strict";r.d(t,{A:()=>b});var n,o=r(14232),i=r(69241),s=r(4697),a=r(27367),l=r(74073),c=r(54773),u=r(82987),d=r(52196),p=r(80027),f=r(47951),h=r(45879);function m(e){return(0,h.Ay)("MuiFormHelperText",e)}let g=(0,f.A)("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var y=r(37876);let v=(0,c.Ay)("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.size&&t["size".concat((0,p.A)(r.size))],r.contained&&t.contained,r.filled&&t.filled]}})((0,u.A)(e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,...t.typography.caption,textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,["&.".concat(g.disabled)]:{color:(t.vars||t).palette.text.disabled},["&.".concat(g.error)]:{color:(t.vars||t).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:e=>{let{ownerState:t}=e;return t.contained},style:{marginLeft:14,marginRight:14}}]}})),b=o.forwardRef(function(e,t){let r=(0,d.b)({props:e,name:"MuiFormHelperText"}),{children:o,className:c,component:u="p",disabled:f,error:h,filled:g,focused:b,margin:A,required:w,variant:x,...S}=r,E=(0,l.A)(),_=(0,a.A)({props:r,muiFormControl:E,states:["variant","size","disabled","error","filled","focused","required"]}),C={...r,component:u,contained:"filled"===_.variant||"outlined"===_.variant,variant:_.variant,size:_.size,disabled:_.disabled,error:_.error,filled:_.filled,focused:_.focused,required:_.required};delete C.ownerState;let R=(e=>{let{classes:t,contained:r,size:n,disabled:o,error:i,filled:a,focused:l,required:c}=e,u={root:["root",o&&"disabled",i&&"error",n&&"size".concat((0,p.A)(n)),r&&"contained",l&&"focused",a&&"filled",c&&"required"]};return(0,s.A)(u,m,t)})(C);return(0,y.jsx)(v,{as:u,className:(0,i.A)(R.root,c),ref:t,...S,ownerState:C,children:" "===o?n||(n=(0,y.jsx)("span",{className:"notranslate","aria-hidden":!0,children:"​"})):o})})},65924:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M17 20c-.29 0-.56-.06-.76-.15-.71-.37-1.21-.88-1.71-2.38-.51-1.56-1.47-2.29-2.39-3-.79-.61-1.61-1.24-2.32-2.53C9.29 10.98 9 9.93 9 9c0-2.8 2.2-5 5-5s5 2.2 5 5h2c0-3.93-3.07-7-7-7S7 5.07 7 9c0 1.26.38 2.65 1.07 3.9.91 1.65 1.98 2.48 2.85 3.15.81.62 1.39 1.07 1.71 2.05.6 1.82 1.37 2.84 2.73 3.55.51.23 1.07.35 1.64.35 2.21 0 4-1.79 4-4h-2c0 1.1-.9 2-2 2M7.64 2.64 6.22 1.22C4.23 3.21 3 5.96 3 9s1.23 5.79 3.22 7.78l1.41-1.41C6.01 13.74 5 11.49 5 9s1.01-4.74 2.64-6.36M11.5 9c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5-1.12-2.5-2.5-2.5-2.5 1.12-2.5 2.5"}),"Hearing")},68043:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(14232),o=r(98477),i=r(61637),s=r(43165),a=r(99659);function l(e,t){"function"==typeof e?e(t):e&&(e.current=t)}let c=n.forwardRef(function(e,t){let{children:r,container:c,disablePortal:u=!1}=e,[d,p]=n.useState(null),f=(0,i.A)(n.isValidElement(r)?(0,s.A)(r):null,t);return((0,a.A)(()=>{u||p(("function"==typeof c?c():c)||document.body)},[c,u]),(0,a.A)(()=>{if(d&&!u)return l(t,d),()=>{l(t,null)}},[t,d,u]),u)?n.isValidElement(r)?n.cloneElement(r,{ref:f}):r:d?o.createPortal(r,d):d})},68284:(e,t,r)=>{"use strict";r.d(t,{A:()=>S});var n=r(14232),o=r(69241),i=r(4697),s=r(53855),a=r(54773),l=r(52196),c=r(74673),u=r(63657),d=r(73650),p=r(90405),f=r(88713),h=r(65774),m=r(61927),g=r(47951),y=r(45879);function v(e){return(0,y.Ay)("MuiTextField",e)}(0,g.A)("MuiTextField",["root"]);var b=r(67360),A=r(37876);let w={standard:c.A,filled:u.A,outlined:d.A},x=(0,a.Ay)(f.A,{name:"MuiTextField",slot:"Root",overridesResolver:(e,t)=>t.root})({}),S=n.forwardRef(function(e,t){let r=(0,l.b)({props:e,name:"MuiTextField"}),{autoComplete:n,autoFocus:a=!1,children:c,className:u,color:d="primary",defaultValue:f,disabled:g=!1,error:y=!1,FormHelperTextProps:S,fullWidth:E=!1,helperText:_,id:C,InputLabelProps:R,inputProps:O,InputProps:k,inputRef:I,label:T,maxRows:M,minRows:P,multiline:j=!1,name:N,onBlur:L,onChange:B,onFocus:F,placeholder:D,required:$=!1,rows:z,select:W=!1,SelectProps:U,slots:H={},slotProps:q={},type:V,value:X,variant:K="outlined",...G}=r,Y={...r,autoFocus:a,color:d,disabled:g,error:y,fullWidth:E,multiline:j,required:$,select:W,variant:K},J=(e=>{let{classes:t}=e;return(0,i.A)({root:["root"]},v,t)})(Y),Q=(0,s.A)(C),Z=_&&Q?"".concat(Q,"-helper-text"):void 0,ee=T&&Q?"".concat(Q,"-label"):void 0,et=w[K],er={slots:H,slotProps:{input:k,inputLabel:R,htmlInput:O,formHelperText:S,select:U,...q}},en={},eo=er.slotProps.inputLabel;"outlined"===K&&(eo&&void 0!==eo.shrink&&(en.notched=eo.shrink),en.label=T),W&&(U&&U.native||(en.id=void 0),en["aria-describedby"]=void 0);let[ei,es]=(0,b.A)("root",{elementType:x,shouldForwardComponentProp:!0,externalForwardedProps:{...er,...G},ownerState:Y,className:(0,o.A)(J.root,u),ref:t,additionalProps:{disabled:g,error:y,fullWidth:E,required:$,color:d,variant:K}}),[ea,el]=(0,b.A)("input",{elementType:et,externalForwardedProps:er,additionalProps:en,ownerState:Y}),[ec,eu]=(0,b.A)("inputLabel",{elementType:p.A,externalForwardedProps:er,ownerState:Y}),[ed,ep]=(0,b.A)("htmlInput",{elementType:"input",externalForwardedProps:er,ownerState:Y}),[ef,eh]=(0,b.A)("formHelperText",{elementType:h.A,externalForwardedProps:er,ownerState:Y}),[em,eg]=(0,b.A)("select",{elementType:m.A,externalForwardedProps:er,ownerState:Y}),ey=(0,A.jsx)(ea,{"aria-describedby":Z,autoComplete:n,autoFocus:a,defaultValue:f,fullWidth:E,multiline:j,name:N,rows:z,maxRows:M,minRows:P,type:V,value:X,id:Q,inputRef:I,onBlur:L,onChange:B,onFocus:F,placeholder:D,inputProps:ep,slots:{input:H.htmlInput?ed:void 0},...el});return(0,A.jsxs)(ei,{...es,children:[null!=T&&""!==T&&(0,A.jsx)(ec,{htmlFor:Q,id:ee,...eu,children:T}),W?(0,A.jsx)(em,{"aria-describedby":Z,id:Q,labelId:ee,value:X,input:ey,...eg,children:c}):ey,_&&(0,A.jsx)(ef,{id:Z,...eh,children:_})]})})},69209:(e,t,r)=>{"use strict";r.d(t,{A:()=>g});var n=r(14232),o=r(69241),i=r(4697),s=r(54773),a=r(82987),l=r(52196),c=r(80027),u=r(47951),d=r(45879);function p(e){return(0,d.Ay)("MuiListSubheader",e)}(0,u.A)("MuiListSubheader",["root","colorPrimary","colorInherit","gutters","inset","sticky"]);var f=r(37876);let h=(0,s.Ay)("li",{name:"MuiListSubheader",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,"default"!==r.color&&t["color".concat((0,c.A)(r.color))],!r.disableGutters&&t.gutters,r.inset&&t.inset,!r.disableSticky&&t.sticky]}})((0,a.A)(e=>{let{theme:t}=e;return{boxSizing:"border-box",lineHeight:"48px",listStyle:"none",color:(t.vars||t).palette.text.secondary,fontFamily:t.typography.fontFamily,fontWeight:t.typography.fontWeightMedium,fontSize:t.typography.pxToRem(14),variants:[{props:{color:"primary"},style:{color:(t.vars||t).palette.primary.main}},{props:{color:"inherit"},style:{color:"inherit"}},{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.inset},style:{paddingLeft:72}},{props:e=>{let{ownerState:t}=e;return!t.disableSticky},style:{position:"sticky",top:0,zIndex:1,backgroundColor:(t.vars||t).palette.background.paper}}]}})),m=n.forwardRef(function(e,t){let r=(0,l.b)({props:e,name:"MuiListSubheader"}),{className:n,color:s="default",component:a="li",disableGutters:u=!1,disableSticky:d=!1,inset:m=!1,...g}=r,y={...r,color:s,component:a,disableGutters:u,disableSticky:d,inset:m},v=(e=>{let{classes:t,color:r,disableGutters:n,inset:o,disableSticky:s}=e,a={root:["root","default"!==r&&"color".concat((0,c.A)(r)),!n&&"gutters",o&&"inset",!s&&"sticky"]};return(0,i.A)(a,p,t)})(y);return(0,f.jsx)(h,{as:a,className:(0,o.A)(v.root,n),ref:t,ownerState:y,...g})});m&&(m.muiSkipListHighlight=!0);let g=m},69390:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var n=r(14232),o=r(69241),i=r(4697),s=r(54773),a=r(82987),l=r(52196),c=r(47951),u=r(45879);function d(e){return(0,u.Ay)("MuiToolbar",e)}(0,c.A)("MuiToolbar",["root","gutters","regular","dense"]);var p=r(37876);let f=(0,s.Ay)("div",{name:"MuiToolbar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.disableGutters&&t.gutters,t[r.variant]]}})((0,a.A)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",variants:[{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:t.spacing(2),paddingRight:t.spacing(2),[t.breakpoints.up("sm")]:{paddingLeft:t.spacing(3),paddingRight:t.spacing(3)}}},{props:{variant:"dense"},style:{minHeight:48}},{props:{variant:"regular"},style:t.mixins.toolbar}]}})),h=n.forwardRef(function(e,t){let r=(0,l.b)({props:e,name:"MuiToolbar"}),{className:n,component:s="div",disableGutters:a=!1,variant:c="regular",...u}=r,h={...r,component:s,disableGutters:a,variant:c},m=(e=>{let{classes:t,disableGutters:r,variant:n}=e;return(0,i.A)({root:["root",!r&&"gutters",n]},d,t)})(h);return(0,p.jsx)(f,{as:s,className:(0,o.A)(m.root,n),ref:t,ownerState:h,...u})})},70946:(e,t,r)=>{"use strict";r.d(t,{A:()=>x});var n=r(14232),o=r(69241),i=r(4697),s=r(27449),a=r(126),l=r(54773),c=r(82987),u=r(78457),d=r(52196),p=r(68197),f=r(7957),h=r(80027),m=r(47951),g=r(45879);function y(e){return(0,g.Ay)("MuiIconButton",e)}let v=(0,m.A)("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]);var b=r(37876);let A=(0,l.Ay)(p.A,{name:"MuiIconButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.loading&&t.loading,"default"!==r.color&&t["color".concat((0,h.A)(r.color))],r.edge&&t["edge".concat((0,h.A)(r.edge))],t["size".concat((0,h.A)(r.size))]]}})((0,c.A)(e=>{let{theme:t}=e;return{textAlign:"center",flex:"0 0 auto",fontSize:t.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(t.vars||t).palette.action.active,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),variants:[{props:e=>!e.disableRipple,style:{"--IconButton-hoverBg":t.vars?"rgba(".concat(t.vars.palette.action.activeChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):(0,a.X4)(t.palette.action.active,t.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]}}),(0,c.A)(e=>{let{theme:t}=e;return{variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(t.palette).filter((0,u.A)()).map(e=>{let[r]=e;return{props:{color:r},style:{color:(t.vars||t).palette[r].main}}}),...Object.entries(t.palette).filter((0,u.A)()).map(e=>{let[r]=e;return{props:{color:r},style:{"--IconButton-hoverBg":t.vars?"rgba(".concat((t.vars||t).palette[r].mainChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):(0,a.X4)((t.vars||t).palette[r].main,t.palette.action.hoverOpacity)}}}),{props:{size:"small"},style:{padding:5,fontSize:t.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:t.typography.pxToRem(28)}}],["&.".concat(v.disabled)]:{backgroundColor:"transparent",color:(t.vars||t).palette.action.disabled},["&.".concat(v.loading)]:{color:"transparent"}}})),w=(0,l.Ay)("span",{name:"MuiIconButton",slot:"LoadingIndicator",overridesResolver:(e,t)=>t.loadingIndicator})(e=>{let{theme:t}=e;return{display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(t.vars||t).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]}}),x=n.forwardRef(function(e,t){let r=(0,d.b)({props:e,name:"MuiIconButton"}),{edge:n=!1,children:a,className:l,color:c="default",disabled:u=!1,disableFocusRipple:p=!1,size:m="medium",id:g,loading:v=null,loadingIndicator:x,...S}=r,E=(0,s.A)(g),_=null!=x?x:(0,b.jsx)(f.A,{"aria-labelledby":E,color:"inherit",size:16}),C={...r,edge:n,color:c,disabled:u,disableFocusRipple:p,loading:v,loadingIndicator:_,size:m},R=(e=>{let{classes:t,disabled:r,color:n,edge:o,size:s,loading:a}=e,l={root:["root",a&&"loading",r&&"disabled","default"!==n&&"color".concat((0,h.A)(n)),o&&"edge".concat((0,h.A)(o)),"size".concat((0,h.A)(s))],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return(0,i.A)(l,y,t)})(C);return(0,b.jsxs)(A,{id:v?E:g,className:(0,o.A)(R.root,l),centerRipple:!0,focusRipple:!p,disabled:u||v,ref:t,...S,ownerState:C,children:["boolean"==typeof v&&(0,b.jsx)("span",{className:R.loadingWrapper,style:{display:"contents"},children:(0,b.jsx)(w,{className:R.loadingIndicator,ownerState:C,children:v&&_})}),a]})})},71538:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var n=r(14232),o=r(69241),i=r(4697),s=r(126),a=r(54773),l=r(82987),c=r(52196),u=r(78630),d=r(37876);let p=(0,a.Ay)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.absolute&&t.absolute,t[r.variant],r.light&&t.light,"vertical"===r.orientation&&t.vertical,r.flexItem&&t.flexItem,r.children&&t.withChildren,r.children&&"vertical"===r.orientation&&t.withChildrenVertical,"right"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignRight,"left"===r.textAlign&&"vertical"!==r.orientation&&t.textAlignLeft]}})((0,l.A)(e=>{let{theme:t}=e;return{margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(t.vars||t).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:t.vars?"rgba(".concat(t.vars.palette.dividerChannel," / 0.08)"):(0,s.X4)(t.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:t.spacing(2),marginRight:t.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:t.spacing(1),marginBottom:t.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:e=>{let{ownerState:t}=e;return!!t.children},style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:e=>{let{ownerState:t}=e;return t.children&&"vertical"!==t.orientation},style:{"&::before, &::after":{width:"100%",borderTop:"thin solid ".concat((t.vars||t).palette.divider),borderTopStyle:"inherit"}}},{props:e=>{let{ownerState:t}=e;return"vertical"===t.orientation&&t.children},style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:"thin solid ".concat((t.vars||t).palette.divider),borderLeftStyle:"inherit"}}},{props:e=>{let{ownerState:t}=e;return"right"===t.textAlign&&"vertical"!==t.orientation},style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:e=>{let{ownerState:t}=e;return"left"===t.textAlign&&"vertical"!==t.orientation},style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}})),f=(0,a.Ay)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.wrapper,"vertical"===r.orientation&&t.wrapperVertical]}})((0,l.A)(e=>{let{theme:t}=e;return{display:"inline-block",paddingLeft:"calc(".concat(t.spacing(1)," * 1.2)"),paddingRight:"calc(".concat(t.spacing(1)," * 1.2)"),whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:"calc(".concat(t.spacing(1)," * 1.2)"),paddingBottom:"calc(".concat(t.spacing(1)," * 1.2)")}}]}})),h=n.forwardRef(function(e,t){let r=(0,c.b)({props:e,name:"MuiDivider"}),{absolute:n=!1,children:s,className:a,orientation:l="horizontal",component:h=s||"vertical"===l?"div":"hr",flexItem:m=!1,light:g=!1,role:y="hr"!==h?"separator":void 0,textAlign:v="center",variant:b="fullWidth",...A}=r,w={...r,absolute:n,component:h,flexItem:m,light:g,orientation:l,role:y,textAlign:v,variant:b},x=(e=>{let{absolute:t,children:r,classes:n,flexItem:o,light:s,orientation:a,textAlign:l,variant:c}=e;return(0,i.A)({root:["root",t&&"absolute",c,s&&"light","vertical"===a&&"vertical",o&&"flexItem",r&&"withChildren",r&&"vertical"===a&&"withChildrenVertical","right"===l&&"vertical"!==a&&"textAlignRight","left"===l&&"vertical"!==a&&"textAlignLeft"],wrapper:["wrapper","vertical"===a&&"wrapperVertical"]},u.K,n)})(w);return(0,d.jsx)(p,{as:h,className:(0,o.A)(x.root,a),role:y,ref:t,ownerState:w,"aria-orientation":"separator"===y&&("hr"!==h||"vertical"===l)?l:void 0,...A,children:s?(0,d.jsx)(f,{className:x.wrapper,ownerState:w,children:s}):null})});h&&(h.muiSkipListHighlight=!0);let m=h},71655:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=r(14232).createContext(void 0)},73650:(e,t,r)=>{"use strict";r.d(t,{A:()=>C});var n,o=r(14232),i=r(4697),s=r(68275),a=r(54773),l=r(82987),c=r(37876);let u=(0,a.Ay)("fieldset",{shouldForwardProp:s.A})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),d=(0,a.Ay)("legend",{shouldForwardProp:s.A})((0,l.A)(e=>{let{theme:t}=e;return{float:"unset",width:"auto",overflow:"hidden",variants:[{props:e=>{let{ownerState:t}=e;return!t.withLabel},style:{padding:0,lineHeight:"11px",transition:t.transitions.create("width",{duration:150,easing:t.transitions.easing.easeOut})}},{props:e=>{let{ownerState:t}=e;return t.withLabel},style:{display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:t.transitions.create("max-width",{duration:50,easing:t.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}}},{props:e=>{let{ownerState:t}=e;return t.withLabel&&t.notched},style:{maxWidth:"100%",transition:t.transitions.create("max-width",{duration:100,easing:t.transitions.easing.easeOut,delay:50})}}]}}));var p=r(74073),f=r(27367),h=r(78457),m=r(52196),g=r(47951),y=r(45879);function v(e){return(0,y.Ay)("MuiOutlinedInput",e)}let b={...r(40642).A,...(0,g.A)("MuiOutlinedInput",["root","notchedOutline","input"])};var A=r(60129),w=r(67360);let x=(0,a.Ay)(A.Sh,{shouldForwardProp:e=>(0,s.A)(e)||"classes"===e,name:"MuiOutlinedInput",slot:"Root",overridesResolver:A.WC})((0,l.A)(e=>{let{theme:t}=e,r="light"===t.palette.mode?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{position:"relative",borderRadius:(t.vars||t).shape.borderRadius,["&:hover .".concat(b.notchedOutline)]:{borderColor:(t.vars||t).palette.text.primary},"@media (hover: none)":{["&:hover .".concat(b.notchedOutline)]:{borderColor:t.vars?"rgba(".concat(t.vars.palette.common.onBackgroundChannel," / 0.23)"):r}},["&.".concat(b.focused," .").concat(b.notchedOutline)]:{borderWidth:2},variants:[...Object.entries(t.palette).filter((0,h.A)()).map(e=>{let[r]=e;return{props:{color:r},style:{["&.".concat(b.focused," .").concat(b.notchedOutline)]:{borderColor:(t.vars||t).palette[r].main}}}}),{props:{},style:{["&.".concat(b.error," .").concat(b.notchedOutline)]:{borderColor:(t.vars||t).palette.error.main},["&.".concat(b.disabled," .").concat(b.notchedOutline)]:{borderColor:(t.vars||t).palette.action.disabled}}},{props:e=>{let{ownerState:t}=e;return t.startAdornment},style:{paddingLeft:14}},{props:e=>{let{ownerState:t}=e;return t.endAdornment},style:{paddingRight:14}},{props:e=>{let{ownerState:t}=e;return t.multiline},style:{padding:"16.5px 14px"}},{props:e=>{let{ownerState:t,size:r}=e;return t.multiline&&"small"===r},style:{padding:"8.5px 14px"}}]}})),S=(0,a.Ay)(function(e){let{children:t,classes:r,className:o,label:i,notched:s,...a}=e,l=null!=i&&""!==i,p={...e,notched:s,withLabel:l};return(0,c.jsx)(u,{"aria-hidden":!0,className:o,ownerState:p,...a,children:(0,c.jsx)(d,{ownerState:p,children:l?(0,c.jsx)("span",{children:i}):n||(n=(0,c.jsx)("span",{className:"notranslate","aria-hidden":!0,children:"​"}))})})},{name:"MuiOutlinedInput",slot:"NotchedOutline",overridesResolver:(e,t)=>t.notchedOutline})((0,l.A)(e=>{let{theme:t}=e,r="light"===t.palette.mode?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:t.vars?"rgba(".concat(t.vars.palette.common.onBackgroundChannel," / 0.23)"):r}})),E=(0,a.Ay)(A.ck,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:A.Oj})((0,l.A)(e=>{let{theme:t}=e;return{padding:"16.5px 14px",...!t.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:"light"===t.palette.mode?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:"light"===t.palette.mode?null:"#fff",caretColor:"light"===t.palette.mode?null:"#fff",borderRadius:"inherit"}},...t.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[t.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{padding:"8.5px 14px"}},{props:e=>{let{ownerState:t}=e;return t.multiline},style:{padding:0}},{props:e=>{let{ownerState:t}=e;return t.startAdornment},style:{paddingLeft:0}},{props:e=>{let{ownerState:t}=e;return t.endAdornment},style:{paddingRight:0}}]}})),_=o.forwardRef(function(e,t){var r,n,s,a;let l=(0,m.b)({props:e,name:"MuiOutlinedInput"}),{components:u={},fullWidth:d=!1,inputComponent:h="input",label:g,multiline:y=!1,notched:b,slots:_={},slotProps:C={},type:R="text",...O}=l,k=(e=>{let{classes:t}=e,r=(0,i.A)({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},v,t);return{...t,...r}})(l),I=(0,p.A)(),T=(0,f.A)({props:l,muiFormControl:I,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),M={...l,color:T.color||"primary",disabled:T.disabled,error:T.error,focused:T.focused,formControl:I,fullWidth:d,hiddenLabel:T.hiddenLabel,multiline:y,size:T.size,type:R},P=null!=(n=null!=(r=_.root)?r:u.Root)?n:x,j=null!=(a=null!=(s=_.input)?s:u.Input)?a:E,[N,L]=(0,w.A)("notchedOutline",{elementType:S,className:k.notchedOutline,shouldForwardComponentProp:!0,ownerState:M,externalForwardedProps:{slots:_,slotProps:C},additionalProps:{label:null!=g&&""!==g&&T.required?(0,c.jsxs)(o.Fragment,{children:[g," ","*"]}):g}});return(0,c.jsx)(A.Ay,{slots:{root:P,input:j},slotProps:C,renderSuffix:e=>(0,c.jsx)(N,{...L,notched:void 0!==b?b:!!(e.startAdornment||e.filled||e.focused)}),fullWidth:d,inputComponent:h,multiline:y,ref:t,type:R,...O,classes:{...k,notchedOutline:null}})});_.muiName="Input";let C=_},74073:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(14232),o=r(55509);function i(){return n.useContext(o.A)}},74253:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>g});var n=r(40670),o=r(16724),i=r(14232),s=r(98477);let a={disabled:!1};var l=r(4073),c="unmounted",u="exited",d="entering",p="entered",f="exiting",h=function(e){function t(t,r){var n,o=e.call(this,t,r)||this,i=r&&!r.isMounting?t.enter:t.appear;return o.appearStatus=null,t.in?i?(n=u,o.appearStatus=d):n=p:n=t.unmountOnExit||t.mountOnEnter?c:u,o.state={status:n},o.nextCallback=null,o}(0,o.A)(t,e),t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===c?{status:u}:null};var r=t.prototype;return r.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},r.componentDidUpdate=function(e){var t=null;if(e!==this.props){var r=this.state.status;this.props.in?r!==d&&r!==p&&(t=d):(r===d||r===p)&&(t=f)}this.updateStatus(!1,t)},r.componentWillUnmount=function(){this.cancelNextCallback()},r.getTimeouts=function(){var e,t,r,n=this.props.timeout;return e=t=r=n,null!=n&&"number"!=typeof n&&(e=n.exit,t=n.enter,r=void 0!==n.appear?n.appear:t),{exit:e,enter:t,appear:r}},r.updateStatus=function(e,t){if(void 0===e&&(e=!1),null!==t)if(this.cancelNextCallback(),t===d){if(this.props.unmountOnExit||this.props.mountOnEnter){var r=this.props.nodeRef?this.props.nodeRef.current:s.findDOMNode(this);r&&r.scrollTop}this.performEnter(e)}else this.performExit();else this.props.unmountOnExit&&this.state.status===u&&this.setState({status:c})},r.performEnter=function(e){var t=this,r=this.props.enter,n=this.context?this.context.isMounting:e,o=this.props.nodeRef?[n]:[s.findDOMNode(this),n],i=o[0],l=o[1],c=this.getTimeouts(),u=n?c.appear:c.enter;if(!e&&!r||a.disabled)return void this.safeSetState({status:p},function(){t.props.onEntered(i)});this.props.onEnter(i,l),this.safeSetState({status:d},function(){t.props.onEntering(i,l),t.onTransitionEnd(u,function(){t.safeSetState({status:p},function(){t.props.onEntered(i,l)})})})},r.performExit=function(){var e=this,t=this.props.exit,r=this.getTimeouts(),n=this.props.nodeRef?void 0:s.findDOMNode(this);if(!t||a.disabled)return void this.safeSetState({status:u},function(){e.props.onExited(n)});this.props.onExit(n),this.safeSetState({status:f},function(){e.props.onExiting(n),e.onTransitionEnd(r.exit,function(){e.safeSetState({status:u},function(){e.props.onExited(n)})})})},r.cancelNextCallback=function(){null!==this.nextCallback&&(this.nextCallback.cancel(),this.nextCallback=null)},r.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},r.setNextCallback=function(e){var t=this,r=!0;return this.nextCallback=function(n){r&&(r=!1,t.nextCallback=null,e(n))},this.nextCallback.cancel=function(){r=!1},this.nextCallback},r.onTransitionEnd=function(e,t){this.setNextCallback(t);var r=this.props.nodeRef?this.props.nodeRef.current:s.findDOMNode(this),n=null==e&&!this.props.addEndListener;if(!r||n)return void setTimeout(this.nextCallback,0);if(this.props.addEndListener){var o=this.props.nodeRef?[this.nextCallback]:[r,this.nextCallback],i=o[0],a=o[1];this.props.addEndListener(i,a)}null!=e&&setTimeout(this.nextCallback,e)},r.render=function(){var e=this.state.status;if(e===c)return null;var t=this.props,r=t.children,o=(t.in,t.mountOnEnter,t.unmountOnExit,t.appear,t.enter,t.exit,t.timeout,t.addEndListener,t.onEnter,t.onEntering,t.onEntered,t.onExit,t.onExiting,t.onExited,t.nodeRef,(0,n.A)(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]));return i.createElement(l.A.Provider,{value:null},"function"==typeof r?r(e,o):i.cloneElement(i.Children.only(r),o))},t}(i.Component);function m(){}h.contextType=l.A,h.propTypes={},h.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:m,onEntering:m,onEntered:m,onExit:m,onExiting:m,onExited:m},h.UNMOUNTED=c,h.EXITED=u,h.ENTERING=d,h.ENTERED=p,h.EXITING=f;let g=h},74673:(e,t,r)=>{"use strict";r.d(t,{A:()=>A});var n=r(14232),o=r(4697),i=r(12535),s=r(60129),a=r(68275),l=r(54773),c=r(82987),u=r(78457),d=r(52196),p=r(47951),f=r(45879);function h(e){return(0,f.Ay)("MuiInput",e)}let m={...r(40642).A,...(0,p.A)("MuiInput",["root","underline","input"])};var g=r(37876);let y=(0,l.Ay)(s.Sh,{shouldForwardProp:e=>(0,a.A)(e)||"classes"===e,name:"MuiInput",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[...(0,s.WC)(e,t),!r.disableUnderline&&t.underline]}})((0,c.A)(e=>{let{theme:t}=e,r="light"===t.palette.mode?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return t.vars&&(r="rgba(".concat(t.vars.palette.common.onBackgroundChannel," / ").concat(t.vars.opacity.inputUnderline,")")),{position:"relative",variants:[{props:e=>{let{ownerState:t}=e;return t.formControl},style:{"label + &":{marginTop:16}}},{props:e=>{let{ownerState:t}=e;return!t.disableUnderline},style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:t.transitions.create("transform",{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut}),pointerEvents:"none"},["&.".concat(m.focused,":after")]:{transform:"scaleX(1) translateX(0)"},["&.".concat(m.error)]:{"&::before, &::after":{borderBottomColor:(t.vars||t).palette.error.main}},"&::before":{borderBottom:"1px solid ".concat(r),left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:t.transitions.create("border-bottom-color",{duration:t.transitions.duration.shorter}),pointerEvents:"none"},["&:hover:not(.".concat(m.disabled,", .").concat(m.error,"):before")]:{borderBottom:"2px solid ".concat((t.vars||t).palette.text.primary),"@media (hover: none)":{borderBottom:"1px solid ".concat(r)}},["&.".concat(m.disabled,":before")]:{borderBottomStyle:"dotted"}}},...Object.entries(t.palette).filter((0,u.A)()).map(e=>{let[r]=e;return{props:{color:r,disableUnderline:!1},style:{"&::after":{borderBottom:"2px solid ".concat((t.vars||t).palette[r].main)}}}})]}})),v=(0,l.Ay)(s.ck,{name:"MuiInput",slot:"Input",overridesResolver:s.Oj})({}),b=n.forwardRef(function(e,t){var r,n,a,l;let c=(0,d.b)({props:e,name:"MuiInput"}),{disableUnderline:u=!1,components:p={},componentsProps:f,fullWidth:m=!1,inputComponent:b="input",multiline:A=!1,slotProps:w,slots:x={},type:S="text",...E}=c,_=(e=>{let{classes:t,disableUnderline:r}=e,n=(0,o.A)({root:["root",!r&&"underline"],input:["input"]},h,t);return{...t,...n}})(c),C={root:{ownerState:{disableUnderline:u}}},R=(null!=w?w:f)?(0,i.A)(null!=w?w:f,C):C,O=null!=(n=null!=(r=x.root)?r:p.Root)?n:y,k=null!=(l=null!=(a=x.input)?a:p.Input)?l:v;return(0,g.jsx)(s.Ay,{slots:{root:O,input:k},slotProps:R,fullWidth:m,inputComponent:b,multiline:A,ref:t,type:S,...E,classes:_})});b.muiName="Input";let A=b},75383:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3m5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72z"}),"Mic")},75500:(e,t,r)=>{"use strict";r.d(t,{A:()=>v});var n=r(14232),o=r(69241),i=r(4697),s=r(54773),a=r(82987),l=r(52196),c=r(80027),u=r(78457),d=r(11951),p=r(47951),f=r(45879);function h(e){return(0,f.Ay)("MuiAppBar",e)}(0,p.A)("MuiAppBar",["root","positionFixed","positionAbsolute","positionSticky","positionStatic","positionRelative","colorDefault","colorPrimary","colorSecondary","colorInherit","colorTransparent","colorError","colorInfo","colorSuccess","colorWarning"]);var m=r(37876);let g=(e,t)=>e?"".concat(null==e?void 0:e.replace(")",""),", ").concat(t,")"):t,y=(0,s.Ay)(d.A,{name:"MuiAppBar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t["position".concat((0,c.A)(r.position))],t["color".concat((0,c.A)(r.color))]]}})((0,a.A)(e=>{let{theme:t}=e;return{display:"flex",flexDirection:"column",width:"100%",boxSizing:"border-box",flexShrink:0,variants:[{props:{position:"fixed"},style:{position:"fixed",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0,"@media print":{position:"absolute"}}},{props:{position:"absolute"},style:{position:"absolute",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"sticky"},style:{position:"sticky",zIndex:(t.vars||t).zIndex.appBar,top:0,left:"auto",right:0}},{props:{position:"static"},style:{position:"static"}},{props:{position:"relative"},style:{position:"relative"}},{props:{color:"inherit"},style:{"--AppBar-color":"inherit"}},{props:{color:"default"},style:{"--AppBar-background":t.vars?t.vars.palette.AppBar.defaultBg:t.palette.grey[100],"--AppBar-color":t.vars?t.vars.palette.text.primary:t.palette.getContrastText(t.palette.grey[100]),...t.applyStyles("dark",{"--AppBar-background":t.vars?t.vars.palette.AppBar.defaultBg:t.palette.grey[900],"--AppBar-color":t.vars?t.vars.palette.text.primary:t.palette.getContrastText(t.palette.grey[900])})}},...Object.entries(t.palette).filter((0,u.A)(["contrastText"])).map(e=>{var r,n;let[o]=e;return{props:{color:o},style:{"--AppBar-background":(null!=(r=t.vars)?r:t).palette[o].main,"--AppBar-color":(null!=(n=t.vars)?n:t).palette[o].contrastText}}}),{props:e=>!0===e.enableColorOnDark&&!["inherit","transparent"].includes(e.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)"}},{props:e=>!1===e.enableColorOnDark&&!["inherit","transparent"].includes(e.color),style:{backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...t.applyStyles("dark",{backgroundColor:t.vars?g(t.vars.palette.AppBar.darkBg,"var(--AppBar-background)"):null,color:t.vars?g(t.vars.palette.AppBar.darkColor,"var(--AppBar-color)"):null})}},{props:{color:"transparent"},style:{"--AppBar-background":"transparent","--AppBar-color":"inherit",backgroundColor:"var(--AppBar-background)",color:"var(--AppBar-color)",...t.applyStyles("dark",{backgroundImage:"none"})}}]}})),v=n.forwardRef(function(e,t){let r=(0,l.b)({props:e,name:"MuiAppBar"}),{className:n,color:s="primary",enableColorOnDark:a=!1,position:u="fixed",...d}=r,p={...r,color:s,position:u,enableColorOnDark:a},f=(e=>{let{color:t,position:r,classes:n}=e,o={root:["root","color".concat((0,c.A)(t)),"position".concat((0,c.A)(r))]};return(0,i.A)(o,h,n)})(p);return(0,m.jsx)(y,{square:!0,component:"header",ownerState:p,elevation:4,className:(0,o.A)(f.root,n,"fixed"===u&&"mui-fixed"),ref:t,...d})})},75953:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4"}),"Person")},75963:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M3 10h11v2H3zm0-4h11v2H3zm0 8h7v2H3zm17.59-2.07-4.25 4.24-2.12-2.12-1.41 1.41L16.34 19 22 13.34z"}),"PlaylistAddCheck")},77022:(e,t,r)=>{"use strict";let n,o,i,s,a,l,c,u,d,p;r.d(t,{Ay:()=>n_});let f="RFC3986",h={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)};Object.prototype.hasOwnProperty;let m=Array.isArray,g=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function y(e,t){if(m(e)){let r=[];for(let n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)}let v=Object.prototype.hasOwnProperty,b={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},A=Array.isArray,w=Array.prototype.push,x=function(e,t){w.apply(e,A(t)?t:[t])},S=Date.prototype.toISOString,E={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,r,n,o)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let s="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,r=[];for(let e=0;e<t.length;++e){let n=t.charCodeAt(e);if(45===n||46===n||95===n||126===n||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||"RFC1738"===o&&(40===n||41===n)){r[r.length]=t.charAt(e);continue}if(n<128){r[r.length]=g[n];continue}if(n<2048){r[r.length]=g[192|n>>6]+g[128|63&n];continue}if(n<55296||n>=57344){r[r.length]=g[224|n>>12]+g[128|n>>6&63]+g[128|63&n];continue}e+=1,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),r[r.length]=g[240|n>>18]+g[128|n>>12&63]+g[128|n>>6&63]+g[128|63&n]}s+=r.join("")}return s},encodeValuesOnly:!1,format:f,formatter:h[f],indices:!1,serializeDate:e=>S.call(e),skipNulls:!1,strictNullHandling:!1},_={},C="4.104.0",R=!1;class O{constructor(e){this.body=e}get[Symbol.toStringTag](){return"MultipartBody"}}let k=()=>{o||function(e,t={auto:!1}){if(R)throw Error(`you must \`import 'openai/shims/${e.kind}'\` before importing anything else from openai`);if(o)throw Error(`can't \`import 'openai/shims/${e.kind}'\` after \`import 'openai/shims/${o}'\``);R=t.auto,o=e.kind,i=e.fetch,e.Request,e.Response,e.Headers,s=e.FormData,e.Blob,a=e.File,l=e.ReadableStream,c=e.getMultipartRequestOptions,u=e.getDefaultAgent,d=e.fileFromPath,p=e.isFsReadStream}(function({manuallyImported:e}={}){let t,r,n,o,i=e?"You may need to use polyfills":`Add one of these imports before your first \`import … from 'openai'\`:
- \`import 'openai/shims/node'\` (if you're running on Node)
- \`import 'openai/shims/web'\` (otherwise)
`;try{t=fetch,r=Request,n=Response,o=Headers}catch(e){throw Error(`this environment is missing the following Web Fetch API type: ${e.message}. ${i}`)}return{kind:"web",fetch:t,Request:r,Response:n,Headers:o,FormData:"undefined"!=typeof FormData?FormData:class{constructor(){throw Error(`file uploads aren't supported in this environment yet as 'FormData' is undefined. ${i}`)}},Blob:"undefined"!=typeof Blob?Blob:class{constructor(){throw Error(`file uploads aren't supported in this environment yet as 'Blob' is undefined. ${i}`)}},File:"undefined"!=typeof File?File:class{constructor(){throw Error(`file uploads aren't supported in this environment yet as 'File' is undefined. ${i}`)}},ReadableStream:"undefined"!=typeof ReadableStream?ReadableStream:class{constructor(){throw Error(`streaming isn't supported in this environment yet as 'ReadableStream' is undefined. ${i}`)}},getMultipartRequestOptions:async(e,t)=>({...t,body:new O(e)}),getDefaultAgent:e=>void 0,fileFromPath:()=>{throw Error("The `fileFromPath` function is only supported in Node. See the README for more details: https://www.github.com/openai/openai-node#file-uploads")},isFsReadStream:e=>!1}}(),{auto:!0})};k();class I extends Error{}class T extends I{constructor(e,t,r,n){super(`${T.makeMessage(e,t,r)}`),this.status=e,this.headers=n,this.request_id=n?.["x-request-id"],this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,r){let n=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):r;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,r,n){if(!e||!n)return new P({message:r,cause:tL(t)});let o=t?.error;return 400===e?new N(e,o,r,n):401===e?new L(e,o,r,n):403===e?new B(e,o,r,n):404===e?new F(e,o,r,n):409===e?new D(e,o,r,n):422===e?new $(e,o,r,n):429===e?new z(e,o,r,n):e>=500?new W(e,o,r,n):new T(e,o,r,n)}}class M extends T{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class P extends T{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class j extends P{constructor({message:e}={}){super({message:e??"Request timed out."})}}class N extends T{}class L extends T{}class B extends T{}class F extends T{}class D extends T{}class $ extends T{}class z extends T{}class W extends T{}class U extends I{constructor(){super("Could not parse response content as the length limit was reached")}}class H extends I{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}var q,V,X,K,G,Y,J,Q,Z,ee,et,er,en,eo,ei,es,ea,el,ec,eu,ed,ep,ef,eh,em,eg,ey,ev,eb,eA,ew,ex,eS,eE,e_,eC,eR,eO,ek,eI,eT,eM,eP,ej,eN,eL,eB,eF,eD,e$,ez,eW,eU,eH,eq,eV,eX,eK,eG,eY,eJ,eQ,eZ,e0,e1,e2=r(88220).Buffer,e5=function(e,t,r,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r},e4=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class e7{constructor(){q.set(this,void 0),this.buffer=new Uint8Array,e5(this,q,null,"f")}decode(e){let t;if(null==e)return[];let r=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?new TextEncoder().encode(e):e,n=new Uint8Array(this.buffer.length+r.length);n.set(this.buffer),n.set(r,this.buffer.length),this.buffer=n;let o=[];for(;null!=(t=function(e,t){for(let r=t??0;r<e.length;r++){if(10===e[r])return{preceding:r,index:r+1,carriage:!1};if(13===e[r])return{preceding:r,index:r+1,carriage:!0}}return null}(this.buffer,e4(this,q,"f")));){if(t.carriage&&null==e4(this,q,"f")){e5(this,q,t.index,"f");continue}if(null!=e4(this,q,"f")&&(t.index!==e4(this,q,"f")+1||t.carriage)){o.push(this.decodeText(this.buffer.slice(0,e4(this,q,"f")-1))),this.buffer=this.buffer.slice(e4(this,q,"f")),e5(this,q,null,"f");continue}let e=null!==e4(this,q,"f")?t.preceding-1:t.preceding,r=this.decodeText(this.buffer.slice(0,e));o.push(r),this.buffer=this.buffer.slice(t.index),e5(this,q,null,"f")}return o}decodeText(e){if(null==e)return"";if("string"==typeof e)return e;if(void 0!==e2){if(e instanceof e2)return e.toString();if(e instanceof Uint8Array)return e2.from(e).toString();throw new I(`Unexpected: received non-Uint8Array (${e.constructor.name}) stream chunk in an environment with a global "Buffer" defined, which this library assumes to be Node. Please report this error.`)}if("undefined"!=typeof TextDecoder){if(e instanceof Uint8Array||e instanceof ArrayBuffer)return this.textDecoder??(this.textDecoder=new TextDecoder("utf8")),this.textDecoder.decode(e);throw new I(`Unexpected: received non-Uint8Array/ArrayBuffer (${e.constructor.name}) in a web platform. Please report this error.`)}throw new I("Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.")}flush(){return this.buffer.length?this.decode("\n"):[]}}function e6(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}q=new WeakMap,e7.NEWLINE_CHARS=new Set(["\n","\r"]),e7.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class e3{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let r=!1;async function*n(){if(r)throw Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let n=!1;try{for await(let r of e8(e,t))if(!n){if(r.data.startsWith("[DONE]")){n=!0;continue}if(null===r.event||r.event.startsWith("response.")||r.event.startsWith("transcript.")){let t;try{t=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if(t&&t.error)throw new T(void 0,t.error,void 0,t_(e.headers));yield t}else{let e;try{e=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if("error"==r.event)throw new T(void 0,e.error,e.message,void 0);yield{event:r.event,data:e}}}n=!0}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;throw e}finally{n||t.abort()}}return new e3(n,t)}static fromReadableStream(e,t){let r=!1;async function*n(){let t=new e7;for await(let r of e6(e))for(let e of t.decode(r))yield e;for(let e of t.flush())yield e}return new e3(async function*(){if(r)throw Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let e=!1;try{for await(let t of n())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],r=this.iterator(),n=n=>({next:()=>{if(0===n.length){let n=r.next();e.push(n),t.push(n)}return n.shift()}});return[new e3(()=>n(e),this.controller),new e3(()=>n(t),this.controller)]}toReadableStream(){let e,t=this,r=new TextEncoder;return new l({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:n,done:o}=await e.next();if(o)return t.close();let i=r.encode(JSON.stringify(n)+"\n");t.enqueue(i)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*e8(e,t){if(!e.body)throw t.abort(),new I("Attempted to iterate over a response with no body");let r=new te,n=new e7;for await(let t of e9(e6(e.body)))for(let e of n.decode(t)){let t=r.decode(e);t&&(yield t)}for(let e of n.flush()){let t=r.decode(e);t&&(yield t)}}async function*e9(e){let t=new Uint8Array;for await(let r of e){let e;if(null==r)continue;let n=r instanceof ArrayBuffer?new Uint8Array(r):"string"==typeof r?new TextEncoder().encode(r):r,o=new Uint8Array(t.length+n.length);for(o.set(t),o.set(n,t.length),t=o;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class te{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,r,n]=function(e,t){let r=e.indexOf(":");return -1!==r?[e.substring(0,r),t,e.substring(r+t.length)]:[e,"",""]}(e,":");return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}var tt=r(88220).Buffer;let tr=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob,tn=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&to(e),to=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,ti=e=>tn(e)||tr(e)||p(e);async function ts(e,t,r){var n;if(tn(e=await e))return e;if(tr(e)){let n=await e.blob();t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()??"unknown_file");let o=to(n)?[await n.arrayBuffer()]:[n];return new a(o,t,r)}let o=await ta(e);if(t||(t=(tl((n=e).name)||tl(n.filename)||tl(n.path)?.split(/[\\/]/).pop())??"unknown_file"),!r?.type){let e=o[0]?.type;"string"==typeof e&&(r={...r,type:e})}return new a(o,t,r)}async function ta(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(to(e))t.push(await e.arrayBuffer());else if(tc(e))for await(let r of e)t.push(r);else throw Error(`Unexpected data type: ${typeof e}; constructor: ${e?.constructor?.name}; props: ${function(e){let t=Object.getOwnPropertyNames(e);return`[${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`);return t}let tl=e=>"string"==typeof e?e:void 0!==tt&&e instanceof tt?String(e):void 0,tc=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],tu=e=>e&&"object"==typeof e&&e.body&&"MultipartBody"===e[Symbol.toStringTag],td=async e=>{let t=await tp(e.body);return c(t,e)},tp=async e=>{let t=new s;return await Promise.all(Object.entries(e||{}).map(([e,r])=>th(t,e,r))),t},tf=e=>{if(ti(e))return!0;if(Array.isArray(e))return e.some(tf);if(e&&"object"==typeof e){for(let t in e)if(tf(e[t]))return!0}return!1},th=async(e,t,r)=>{if(void 0!==r){if(null==r)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof r||"number"==typeof r||"boolean"==typeof r)e.append(t,String(r));else if(ti(r)){let n=await ts(r);e.append(t,n)}else if(Array.isArray(r))await Promise.all(r.map(r=>th(e,t+"[]",r)));else if("object"==typeof r)await Promise.all(Object.entries(r).map(([r,n])=>th(e,`${t}[${r}]`,n)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${r} instead`)}};var tm=r(88220).Buffer,tg=r(65364),ty=function(e,t,r,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r},tv=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};async function tb(e){let{response:t}=e;if(e.options.stream)return(tW("response",t.status,t.url,t.headers,t.body),e.options.__streamClass)?e.options.__streamClass.fromSSEResponse(t,e.controller):e3.fromSSEResponse(t,e.controller);if(204===t.status)return null;if(e.options.__binaryResponse)return t;let r=t.headers.get("content-type"),n=r?.split(";")[0]?.trim();if(n?.includes("application/json")||n?.endsWith("+json")){let e=await t.json();return tW("response",t.status,t.url,t.headers,e),tA(e,t)}let o=await t.text();return tW("response",t.status,t.url,t.headers,o),o}function tA(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}k();class tw extends Promise{constructor(e,t=tb){super(e=>{e(null)}),this.responsePromise=e,this.parseResponse=t}_thenUnwrap(e){return new tw(this.responsePromise,async t=>tA(e(await this.parseResponse(t),t),t.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(this.parseResponse)),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}class tx{constructor({baseURL:e,maxRetries:t=2,timeout:r=6e5,httpAgent:n,fetch:o}){this.baseURL=e,this.maxRetries=tN("maxRetries",t),this.timeout=tN("timeout",r),this.httpAgent=n,this.fetch=o??i}authHeaders(e){return{}}defaultHeaders(e){return{Accept:"application/json","Content-Type":"application/json","User-Agent":this.getUserAgent(),...tI(),...this.authHeaders(e)}}validateHeaders(e,t){}defaultIdempotencyKey(){return`stainless-node-retry-${tU()}`}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,r){return this.request(Promise.resolve(r).then(async r=>{let n=r&&to(r?.body)?new DataView(await r.body.arrayBuffer()):r?.body instanceof DataView?r.body:r?.body instanceof ArrayBuffer?new DataView(r.body):r&&ArrayBuffer.isView(r?.body)?new DataView(r.body.buffer):r?.body;return{method:e,path:t,...r,body:n}}))}getAPIList(e,t,r){return this.requestAPIList(t,{method:"get",path:e,...r})}calculateContentLength(e){if("string"==typeof e){if(void 0!==tm)return tm.byteLength(e,"utf8").toString();if("undefined"!=typeof TextEncoder)return new TextEncoder().encode(e).length.toString()}else if(ArrayBuffer.isView(e))return e.byteLength.toString();return null}buildRequest(e,{retryCount:t=0}={}){let r={...e},{method:n,path:o,query:i,headers:s={}}=r,a=ArrayBuffer.isView(r.body)||r.__binaryRequest&&"string"==typeof r.body?r.body:tu(r.body)?r.body.body:r.body?JSON.stringify(r.body,null,2):null,l=this.calculateContentLength(a),c=this.buildURL(o,i);"timeout"in r&&tN("timeout",r.timeout),r.timeout=r.timeout??this.timeout;let d=r.httpAgent??this.httpAgent??u(c),p=r.timeout+1e3;"number"==typeof d?.options?.timeout&&p>(d.options.timeout??0)&&(d.options.timeout=p),this.idempotencyHeader&&"get"!==n&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),s[this.idempotencyHeader]=e.idempotencyKey);let f=this.buildHeaders({options:r,headers:s,contentLength:l,retryCount:t});return{req:{method:n,...a&&{body:a},headers:f,...d&&{agent:d},signal:r.signal??null},url:c,timeout:r.timeout}}buildHeaders({options:e,headers:t,contentLength:r,retryCount:n}){let i={};r&&(i["content-length"]=r);let s=this.defaultHeaders(e);return t$(i,s),t$(i,t),tu(e.body)&&"node"!==o&&delete i["content-type"],void 0===tH(s,"x-stainless-retry-count")&&void 0===tH(t,"x-stainless-retry-count")&&(i["x-stainless-retry-count"]=String(n)),void 0===tH(s,"x-stainless-timeout")&&void 0===tH(t,"x-stainless-timeout")&&e.timeout&&(i["x-stainless-timeout"]=String(Math.trunc(e.timeout/1e3))),this.validateHeaders(i,t),i}async prepareOptions(e){}async prepareRequest(e,{url:t,options:r}){}parseHeaders(e){return e?Symbol.iterator in e?Object.fromEntries(Array.from(e).map(e=>[...e])):{...e}:{}}makeStatusError(e,t,r,n){return T.generate(e,t,r,n)}request(e,t=null){return new tw(this.makeRequest(e,t))}async makeRequest(e,t){let r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);let{req:o,url:i,timeout:s}=this.buildRequest(r,{retryCount:n-t});if(await this.prepareRequest(o,{url:i,options:r}),tW("request",i,r,o.headers),r.signal?.aborted)throw new M;let a=new AbortController,l=await this.fetchWithTimeout(i,o,s,a).catch(tL);if(l instanceof Error){if(r.signal?.aborted)throw new M;if(t)return this.retryRequest(r,t);if("AbortError"===l.name)throw new j;throw new P({cause:l})}let c=t_(l.headers);if(!l.ok){if(t&&this.shouldRetry(l)){let e=`retrying, ${t} attempts remaining`;return tW(`response (error; ${e})`,l.status,i,c),this.retryRequest(r,t,c)}let e=await l.text().catch(e=>tL(e).message),n=tT(e),o=n?void 0:e,s=t?"(error; no more retries left)":"(error; not retryable)";throw tW(`response (error; ${s})`,l.status,i,c,o),this.makeStatusError(l.status,n,o,c)}return{response:l,options:r,controller:a}}requestAPIList(e,t){return new tE(this,this.makeRequest(t,null),e)}buildURL(e,t){let r=new URL(tP(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),n=this.defaultQuery();return tF(n)||(t={...n,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}stringifyQuery(e){return Object.entries(e).filter(([e,t])=>void 0!==t).map(([e,t])=>{if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return`${encodeURIComponent(e)}=${encodeURIComponent(t)}`;if(null===t)return`${encodeURIComponent(e)}=`;throw new I(`Cannot stringify type ${typeof t}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}async fetchWithTimeout(e,t,r,n){let{signal:o,...i}=t||{};o&&o.addEventListener("abort",()=>n.abort());let s=setTimeout(()=>n.abort(),r),a={signal:n.signal,...i};return a.method&&(a.method=a.method.toUpperCase()),this.fetch.call(void 0,e,a).finally(()=>{clearTimeout(s)})}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,r){let n,o=r?.["retry-after-ms"];if(o){let e=parseFloat(o);Number.isNaN(e)||(n=e)}let i=r?.["retry-after"];if(i&&!n){let e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let r=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,r)}return await tj(n),this.makeRequest(e,t-1)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}getUserAgent(){return`${this.constructor.name}/JS ${C}`}}class tS{constructor(e,t,r,n){V.set(this,void 0),ty(this,V,e,"f"),this.options=n,this.response=t,this.body=r}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageInfo()}async getNextPage(){let e=this.nextPageInfo();if(!e)throw new I("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");let t={...this.options};if("params"in e&&"object"==typeof t.query)t.query={...t.query,...e.params};else if("url"in e){for(let[r,n]of[...Object.entries(t.query||{}),...e.url.searchParams.entries()])e.url.searchParams.set(r,n);t.query=void 0,t.path=e.url.toString()}return await tv(this,V,"f").requestAPIList(this.constructor,t)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(V=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class tE extends tw{constructor(e,t,r){super(t,async t=>new r(e,t.response,await tb(t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}let t_=e=>new Proxy(Object.fromEntries(e.entries()),{get(e,t){let r=t.toString();return e[r.toLowerCase()]||e[r]}}),tC={method:!0,path:!0,query:!0,body:!0,headers:!0,maxRetries:!0,stream:!0,timeout:!0,httpAgent:!0,signal:!0,idempotencyKey:!0,__metadata:!0,__binaryRequest:!0,__binaryResponse:!0,__streamClass:!0},tR=e=>"object"==typeof e&&null!==e&&!tF(e)&&Object.keys(e).every(e=>tD(tC,e)),tO=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",tk=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",tI=()=>n??(n=(()=>{if("undefined"!=typeof Deno&&null!=Deno.build)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":C,"X-Stainless-OS":tk(Deno.build.os),"X-Stainless-Arch":tO(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":C,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":tg.version};if("[object process]"===Object.prototype.toString.call(void 0!==tg?tg:0))return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":C,"X-Stainless-OS":tk(tg.platform),"X-Stainless-Arch":tO(tg.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":tg.version};let e=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let r=t.exec(navigator.userAgent);if(r){let t=r[1]||0,n=r[2]||0,o=r[3]||0;return{browser:e,version:`${t}.${n}.${o}`}}}return null}();return e?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":C,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${e.browser}`,"X-Stainless-Runtime-Version":e.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":C,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}})()),tT=e=>{try{return JSON.parse(e)}catch(e){return}},tM=/^[a-z][a-z0-9+.-]*:/i,tP=e=>tM.test(e),tj=e=>new Promise(t=>setTimeout(t,e)),tN=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new I(`${e} must be an integer`);if(t<0)throw new I(`${e} must be a positive integer`);return t},tL=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e)try{return Error(JSON.stringify(e))}catch{}return Error(e)},tB=e=>void 0!==tg?tg.env?.[e]?.trim()??void 0:"undefined"!=typeof Deno?Deno.env?.get?.(e)?.trim():void 0;function tF(e){if(!e)return!0;for(let t in e)return!1;return!0}function tD(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function t$(e,t){for(let r in t){if(!tD(t,r))continue;let n=r.toLowerCase();if(!n)continue;let o=t[r];null===o?delete e[n]:void 0!==o&&(e[n]=o)}}let tz=new Set(["authorization","api-key"]);function tW(e,...t){void 0!==tg&&tg?.env?.DEBUG==="true"&&console.log(`OpenAI:DEBUG:${e}`,...t.map(e=>{if(!e)return e;if(e.headers){let t={...e,headers:{...e.headers}};for(let r in e.headers)tz.has(r.toLowerCase())&&(t.headers[r]="REDACTED");return t}let t=null;for(let r in e)tz.has(r.toLowerCase())&&(t??(t={...e}),t[r]="REDACTED");return t??e}))}let tU=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}),tH=(e,t)=>{let r=t.toLowerCase();if("function"==typeof e?.get){let n=t[0]?.toUpperCase()+t.substring(1).replace(/([^\w])(\w)/g,(e,t,r)=>t+r.toUpperCase());for(let o of[t,r,t.toUpperCase(),n]){let t=e.get(o);if(t)return t}}for(let[n,o]of Object.entries(e))if(n.toLowerCase()===r){if(Array.isArray(o)){if(o.length<=1)return o[0];return console.warn(`Received ${o.length} entries for the ${t} header, using the first entry.`),o[0]}return o}};function tq(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}class tV{constructor(e){this._client=e}}class tX extends tV{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class tK extends tV{list(e,t={},r){return tR(t)?this.list(e,{},t):this._client.getAPIList(`/chat/completions/${e}/messages`,tZ,{query:t,...r})}}class tG extends tS{constructor(e,t,r,n){super(e,t,r,n),this.data=r.data||[],this.object=r.object}getPaginatedItems(){return this.data??[]}nextPageParams(){return null}nextPageInfo(){return null}}class tY extends tS{constructor(e,t,r,n){super(e,t,r,n),this.data=r.data||[],this.has_more=r.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageParams(){let e=this.nextPageInfo();if(!e)return null;if("params"in e)return e.params;let t=Object.fromEntries(e.url.searchParams);return Object.keys(t).length?t:null}nextPageInfo(){let e=this.getPaginatedItems();if(!e.length)return null;let t=e[e.length-1]?.id;return t?{params:{after:t}}:null}}class tJ extends tV{constructor(){super(...arguments),this.messages=new tK(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(`/chat/completions/${e}`,t)}update(e,t,r){return this._client.post(`/chat/completions/${e}`,{body:t,...r})}list(e={},t){return tR(e)?this.list({},e):this._client.getAPIList("/chat/completions",tQ,{query:e,...t})}del(e,t){return this._client.delete(`/chat/completions/${e}`,t)}}class tQ extends tY{}class tZ extends tY{}tJ.ChatCompletionsPage=tQ,tJ.Messages=tK;class t0 extends tV{constructor(){super(...arguments),this.completions=new tJ(this._client)}}t0.Completions=tJ,t0.ChatCompletionsPage=tQ;class t1 extends tV{create(e,t){let r=!!e.encoding_format,n=r?e.encoding_format:"base64";r&&tW("Request","User defined encoding_format:",e.encoding_format);let o=this._client.post("/embeddings",{body:{...e,encoding_format:n},...t});return r?o:(tW("response","Decoding base64 embeddings to float32 array"),o._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=(e=>{if(void 0!==tm){let t=tm.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),r=t.length,n=new Uint8Array(r);for(let e=0;e<r;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}})(t)}),e)))}}class t2 extends tV{create(e,t){return this._client.post("/files",td({body:e,...t}))}retrieve(e,t){return this._client.get(`/files/${e}`,t)}list(e={},t){return tR(e)?this.list({},e):this._client.getAPIList("/files",t5,{query:e,...t})}del(e,t){return this._client.delete(`/files/${e}`,t)}content(e,t){return this._client.get(`/files/${e}/content`,{...t,headers:{Accept:"application/binary",...t?.headers},__binaryResponse:!0})}retrieveContent(e,t){return this._client.get(`/files/${e}/content`,t)}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:r=18e5}={}){let n=new Set(["processed","error","deleted"]),o=Date.now(),i=await this.retrieve(e);for(;!i.status||!n.has(i.status);)if(await tj(t),i=await this.retrieve(e),Date.now()-o>r)throw new j({message:`Giving up on waiting for file ${e} to finish processing after ${r} milliseconds.`});return i}}class t5 extends tY{}t2.FileObjectsPage=t5;class t4 extends tV{createVariation(e,t){return this._client.post("/images/variations",td({body:e,...t}))}edit(e,t){return this._client.post("/images/edits",td({body:e,...t}))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class t7 extends tV{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:{Accept:"application/octet-stream",...t?.headers},__binaryResponse:!0})}}class t6 extends tV{create(e,t){return this._client.post("/audio/transcriptions",td({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}}))}}class t3 extends tV{create(e,t){return this._client.post("/audio/translations",td({body:e,...t,__metadata:{model:e.model}}))}}class t8 extends tV{constructor(){super(...arguments),this.transcriptions=new t6(this._client),this.translations=new t3(this._client),this.speech=new t7(this._client)}}t8.Transcriptions=t6,t8.Translations=t3,t8.Speech=t7;class t9 extends tV{create(e,t){return this._client.post("/moderations",{body:e,...t})}}class re extends tV{retrieve(e,t){return this._client.get(`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",rt,e)}del(e,t){return this._client.delete(`/models/${e}`,t)}}class rt extends tG{}re.ModelsPage=rt;class rr extends tV{}class rn extends tV{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class ro extends tV{constructor(){super(...arguments),this.graders=new rn(this._client)}}ro.Graders=rn;class ri extends tV{create(e,t,r){return this._client.getAPIList(`/fine_tuning/checkpoints/${e}/permissions`,rs,{body:t,method:"post",...r})}retrieve(e,t={},r){return tR(t)?this.retrieve(e,{},t):this._client.get(`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...r})}del(e,t,r){return this._client.delete(`/fine_tuning/checkpoints/${e}/permissions/${t}`,r)}}class rs extends tG{}ri.PermissionCreateResponsesPage=rs;class ra extends tV{constructor(){super(...arguments),this.permissions=new ri(this._client)}}ra.Permissions=ri,ra.PermissionCreateResponsesPage=rs;class rl extends tV{list(e,t={},r){return tR(t)?this.list(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/checkpoints`,rc,{query:t,...r})}}class rc extends tY{}rl.FineTuningJobCheckpointsPage=rc;class ru extends tV{constructor(){super(...arguments),this.checkpoints=new rl(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(`/fine_tuning/jobs/${e}`,t)}list(e={},t){return tR(e)?this.list({},e):this._client.getAPIList("/fine_tuning/jobs",rd,{query:e,...t})}cancel(e,t){return this._client.post(`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},r){return tR(t)?this.listEvents(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/events`,rp,{query:t,...r})}pause(e,t){return this._client.post(`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(`/fine_tuning/jobs/${e}/resume`,t)}}class rd extends tY{}class rp extends tY{}ru.FineTuningJobsPage=rd,ru.FineTuningJobEventsPage=rp,ru.Checkpoints=rl,ru.FineTuningJobCheckpointsPage=rc;class rf extends tV{constructor(){super(...arguments),this.methods=new rr(this._client),this.jobs=new ru(this._client),this.checkpoints=new ra(this._client),this.alpha=new ro(this._client)}}rf.Methods=rr,rf.Jobs=ru,rf.FineTuningJobsPage=rd,rf.FineTuningJobEventsPage=rp,rf.Checkpoints=ra,rf.Alpha=ro;class rh extends tV{}class rm extends tV{constructor(){super(...arguments),this.graderModels=new rh(this._client)}}rm.GraderModels=rh;let rg=async e=>{let t=await Promise.allSettled(e),r=t.filter(e=>"rejected"===e.status);if(r.length){for(let e of r)console.error(e.reason);throw Error(`${r.length} promise(s) failed - see the above errors`)}let n=[];for(let e of t)"fulfilled"===e.status&&n.push(e.value);return n};class ry extends tV{create(e,t,r){return this._client.post(`/vector_stores/${e}/files`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}retrieve(e,t,r){return this._client.get(`/vector_stores/${e}/files/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}update(e,t,r,n){return this._client.post(`/vector_stores/${e}/files/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}list(e,t={},r){return tR(t)?this.list(e,{},t):this._client.getAPIList(`/vector_stores/${e}/files`,rv,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}del(e,t,r){return this._client.delete(`/vector_stores/${e}/files/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}async createAndPoll(e,t,r){let n=await this.create(e,t,r);return await this.poll(e,n.id,r)}async poll(e,t,r){let n={...r?.headers,"X-Stainless-Poll-Helper":"true"};for(r?.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){let o=await this.retrieve(e,t,{...r,headers:n}).withResponse(),i=o.data;switch(i.status){case"in_progress":let s=5e3;if(r?.pollIntervalMs)s=r.pollIntervalMs;else{let e=o.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(s=t)}}await tj(s);break;case"failed":case"completed":return i}}}async upload(e,t,r){let n=await this._client.files.create({file:t,purpose:"assistants"},r);return this.create(e,{file_id:n.id},r)}async uploadAndPoll(e,t,r){let n=await this.upload(e,t,r);return await this.poll(e,n.id,r)}content(e,t,r){return this._client.getAPIList(`/vector_stores/${e}/files/${t}/content`,rb,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}}class rv extends tY{}class rb extends tG{}ry.VectorStoreFilesPage=rv,ry.FileContentResponsesPage=rb;class rA extends tV{create(e,t,r){return this._client.post(`/vector_stores/${e}/file_batches`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}retrieve(e,t,r){return this._client.get(`/vector_stores/${e}/file_batches/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}cancel(e,t,r){return this._client.post(`/vector_stores/${e}/file_batches/${t}/cancel`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}async createAndPoll(e,t,r){let n=await this.create(e,t);return await this.poll(e,n.id,r)}listFiles(e,t,r={},n){return tR(r)?this.listFiles(e,t,{},r):this._client.getAPIList(`/vector_stores/${e}/file_batches/${t}/files`,rv,{query:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}async poll(e,t,r){let n={...r?.headers,"X-Stainless-Poll-Helper":"true"};for(r?.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){let{data:o,response:i}=await this.retrieve(e,t,{...r,headers:n}).withResponse();switch(o.status){case"in_progress":let s=5e3;if(r?.pollIntervalMs)s=r.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(s=t)}}await tj(s);break;case"failed":case"cancelled":case"completed":return o}}}async uploadAndPoll(e,{files:t,fileIds:r=[]},n){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let o=Math.min(n?.maxConcurrency??5,t.length),i=this._client,s=t.values(),a=[...r];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},n);a.push(e.id)}}let c=Array(o).fill(s).map(l);return await rg(c),await this.createAndPoll(e,{file_ids:a})}}class rw extends tV{constructor(){super(...arguments),this.files=new ry(this._client),this.fileBatches=new rA(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}retrieve(e,t){return this._client.get(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}update(e,t,r){return this._client.post(`/vector_stores/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}list(e={},t){return tR(e)?this.list({},e):this._client.getAPIList("/vector_stores",rx,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}del(e,t){return this._client.delete(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}search(e,t,r){return this._client.getAPIList(`/vector_stores/${e}/search`,rS,{body:t,method:"post",...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}}class rx extends tY{}class rS extends tG{}rw.VectorStoresPage=rx,rw.VectorStoreSearchResponsesPage=rS,rw.Files=ry,rw.VectorStoreFilesPage=rv,rw.FileContentResponsesPage=rb,rw.FileBatches=rA;class rE extends tV{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}retrieve(e,t){return this._client.get(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}update(e,t,r){return this._client.post(`/assistants/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}list(e={},t){return tR(e)?this.list({},e):this._client.getAPIList("/assistants",r_,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}del(e,t){return this._client.delete(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}}class r_ extends tY{}function rC(e){return"function"==typeof e.parse}rE.AssistantsPage=r_;let rR=e=>e?.role==="assistant",rO=e=>e?.role==="function",rk=e=>e?.role==="tool";var rI=function(e,t,r,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r},rT=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class rM{constructor(){X.add(this),this.controller=new AbortController,K.set(this,void 0),G.set(this,()=>{}),Y.set(this,()=>{}),J.set(this,void 0),Q.set(this,()=>{}),Z.set(this,()=>{}),ee.set(this,{}),et.set(this,!1),er.set(this,!1),en.set(this,!1),eo.set(this,!1),rI(this,K,new Promise((e,t)=>{rI(this,G,e,"f"),rI(this,Y,t,"f")}),"f"),rI(this,J,new Promise((e,t)=>{rI(this,Q,e,"f"),rI(this,Z,t,"f")}),"f"),rT(this,K,"f").catch(()=>{}),rT(this,J,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},rT(this,X,"m",ei).bind(this))},0)}_connected(){this.ended||(rT(this,G,"f").call(this),this._emit("connect"))}get ended(){return rT(this,et,"f")}get errored(){return rT(this,er,"f")}get aborted(){return rT(this,en,"f")}abort(){this.controller.abort()}on(e,t){return(rT(this,ee,"f")[e]||(rT(this,ee,"f")[e]=[])).push({listener:t}),this}off(e,t){let r=rT(this,ee,"f")[e];if(!r)return this;let n=r.findIndex(e=>e.listener===t);return n>=0&&r.splice(n,1),this}once(e,t){return(rT(this,ee,"f")[e]||(rT(this,ee,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,r)=>{rI(this,eo,!0,"f"),"error"!==e&&this.once("error",r),this.once(e,t)})}async done(){rI(this,eo,!0,"f"),await rT(this,J,"f")}_emit(e,...t){if(rT(this,et,"f"))return;"end"===e&&(rI(this,et,!0,"f"),rT(this,Q,"f").call(this));let r=rT(this,ee,"f")[e];if(r&&(rT(this,ee,"f")[e]=r.filter(e=>!e.once),r.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];rT(this,eo,"f")||r?.length||Promise.reject(e),rT(this,Y,"f").call(this,e),rT(this,Z,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];rT(this,eo,"f")||r?.length||Promise.reject(e),rT(this,Y,"f").call(this,e),rT(this,Z,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function rP(e){return e?.$brand==="auto-parseable-response-format"}function rj(e){return e?.$brand==="auto-parseable-tool"}function rN(e,t){let r=e.choices.map(e=>{var r,n;if("length"===e.finish_reason)throw new U;if("content_filter"===e.finish_reason)throw new H;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let r=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:rj(r)?r.$parseRaw(t.function.arguments):r?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(r=t,n=e.message.content,r.response_format?.type!=="json_schema"?null:r.response_format?.type==="json_schema"?"$parseRaw"in r.response_format?r.response_format.$parseRaw(n):JSON.parse(n):null):null}}});return{...e,choices:r}}function rL(e){return!!rP(e.response_format)||(e.tools?.some(e=>rj(e)||"function"===e.type&&!0===e.function.strict)??!1)}K=new WeakMap,G=new WeakMap,Y=new WeakMap,J=new WeakMap,Q=new WeakMap,Z=new WeakMap,ee=new WeakMap,et=new WeakMap,er=new WeakMap,en=new WeakMap,eo=new WeakMap,X=new WeakSet,ei=function(e){if(rI(this,er,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new M),e instanceof M)return rI(this,en,!0,"f"),this._emit("abort",e);if(e instanceof I)return this._emit("error",e);if(e instanceof Error){let t=new I(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new I(String(e)))};var rB=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class rF extends rM{constructor(){super(...arguments),es.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),(rO(e)||rk(e))&&e.content)this._emit("functionCallResult",e.content);else if(rR(e)&&e.function_call)this._emit("functionCall",e.function_call);else if(rR(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new I("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),rB(this,es,"m",ea).call(this)}async finalMessage(){return await this.done(),rB(this,es,"m",el).call(this)}async finalFunctionCall(){return await this.done(),rB(this,es,"m",ec).call(this)}async finalFunctionCallResult(){return await this.done(),rB(this,es,"m",eu).call(this)}async totalUsage(){return await this.done(),rB(this,es,"m",ed).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=rB(this,es,"m",el).call(this);t&&this._emit("finalMessage",t);let r=rB(this,es,"m",ea).call(this);r&&this._emit("finalContent",r);let n=rB(this,es,"m",ec).call(this);n&&this._emit("finalFunctionCall",n);let o=rB(this,es,"m",eu).call(this);null!=o&&this._emit("finalFunctionCallResult",o),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",rB(this,es,"m",ed).call(this))}async _createChatCompletion(e,t,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),rB(this,es,"m",ep).call(this,t);let o=await e.chat.completions.create({...t,stream:!1},{...r,signal:this.controller.signal});return this._connected(),this._addChatCompletion(rN(o,t))}async _runChatCompletion(e,t,r){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,r)}async _runFunctions(e,t,r){let n="function",{function_call:o="auto",stream:i,...s}=t,a="string"!=typeof o&&o?.name,{maxChatCompletions:l=10}=r||{},c={};for(let e of t.functions)c[e.name||e.function.name]=e;let u=t.functions.map(e=>({name:e.name||e.function.name,parameters:e.parameters,description:e.description}));for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t,i=await this._createChatCompletion(e,{...s,function_call:o,functions:u,messages:[...this.messages]},r),l=i.choices[0]?.message;if(!l)throw new I("missing message in ChatCompletion response");if(!l.function_call)return;let{name:d,arguments:p}=l.function_call,f=c[d];if(f){if(a&&a!==d){let e=`Invalid function_call: ${JSON.stringify(d)}. ${JSON.stringify(a)} requested. Please try again`;this._addMessage({role:n,name:d,content:e});continue}}else{let e=`Invalid function_call: ${JSON.stringify(d)}. Available options are: ${u.map(e=>JSON.stringify(e.name)).join(", ")}. Please try again`;this._addMessage({role:n,name:d,content:e});continue}try{t=rC(f)?await f.parse(p):p}catch(e){this._addMessage({role:n,name:d,content:e instanceof Error?e.message:String(e)});continue}let h=await f.function(t,this),m=rB(this,es,"m",ef).call(this,h);if(this._addMessage({role:n,name:d,content:m}),a)return}}async _runTools(e,t,r){let n="tool",{tool_choice:o="auto",stream:i,...s}=t,a="string"!=typeof o&&o?.function?.name,{maxChatCompletions:l=10}=r||{},c=t.tools.map(e=>{if(rj(e)){if(!e.$callback)throw new I("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(let e of c)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);let d="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...s,tool_choice:o,tools:d,messages:[...this.messages]},r),i=t.choices[0]?.message;if(!i)throw new I("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let r=e.id,{name:o,arguments:i}=e.function,s=u[o];if(s){if(a&&a!==o){let e=`Invalid tool_call: ${JSON.stringify(o)}. ${JSON.stringify(a)} requested. Please try again`;this._addMessage({role:n,tool_call_id:r,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(o)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:r,content:e});continue}try{t=rC(s)?await s.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:n,tool_call_id:r,content:e});continue}let l=await s.function(t,this),c=rB(this,es,"m",ef).call(this,l);if(this._addMessage({role:n,tool_call_id:r,content:c}),a)return}}}}es=new WeakSet,ea=function(){return rB(this,es,"m",el).call(this).content??null},el=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(rR(t)){let{function_call:e,...r}=t,n={...r,content:t.content??null,refusal:t.refusal??null};return e&&(n.function_call=e),n}}throw new I("stream ended without producing a ChatCompletionMessage with role=assistant")},ec=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(rR(t)&&t?.function_call)return t.function_call;if(rR(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},eu=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(rO(t)&&null!=t.content||rk(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},ed=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},ep=function(e){if(null!=e.n&&e.n>1)throw new I("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},ef=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class rD extends rF{static runFunctions(e,t,r){let n=new rD,o={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runFunctions"}};return n._run(()=>n._runFunctions(e,t,o)),n}static runTools(e,t,r){let n=new rD,o={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,o)),n}_addMessage(e,t=!0){super._addMessage(e,t),rR(e)&&e.content&&this._emit("content",e.content)}}let r$={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,INF:384,ALL:511};class rz extends Error{}class rW extends Error{}let rU=e=>(function(e,t=r$.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return((e,t)=>{let r=e.length,n=0,o=e=>{throw new rz(`${e} at position ${n}`)},i=e=>{throw new rW(`${e} at position ${n}`)},s=()=>(d(),n>=r&&o("Unexpected end of input"),'"'===e[n])?a():"{"===e[n]?l():"["===e[n]?c():"null"===e.substring(n,n+4)||r$.NULL&t&&r-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||r$.BOOL&t&&r-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||r$.BOOL&t&&r-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||r$.INFINITY&t&&r-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||r$.MINUS_INFINITY&t&&1<r-n&&r-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||r$.NAN&t&&r-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u(),a=()=>{let s=n,a=!1;for(n++;n<r&&('"'!==e[n]||a&&"\\"===e[n-1]);)a="\\"===e[n]&&!a,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(s,++n-Number(a)))}catch(e){i(String(e))}else if(r$.STR&t)try{return JSON.parse(e.substring(s,n-Number(a))+'"')}catch(t){return JSON.parse(e.substring(s,e.lastIndexOf("\\"))+'"')}o("Unterminated string literal")},l=()=>{n++,d();let i={};try{for(;"}"!==e[n];){if(d(),n>=r&&r$.OBJ&t)return i;let o=a();d(),n++;try{let e=s();Object.defineProperty(i,o,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(r$.OBJ&t)return i;throw e}d(),","===e[n]&&n++}}catch(e){if(r$.OBJ&t)return i;o("Expected '}' at end of object")}return n++,i},c=()=>{n++;let r=[];try{for(;"]"!==e[n];)r.push(s()),d(),","===e[n]&&n++}catch(e){if(r$.ARR&t)return r;o("Expected ']' at end of array")}return n++,r},u=()=>{if(0===n){"-"===e&&r$.NUM&t&&o("Not sure what '-' is");try{return JSON.parse(e)}catch(r){if(r$.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(r))}}let s=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=r||r$.NUM&t||o("Unterminated number literal");try{return JSON.parse(e.substring(s,n))}catch(r){"-"===e.substring(s,n)&&r$.NUM&t&&o("Not sure what '-' is");try{return JSON.parse(e.substring(s,e.lastIndexOf("e")))}catch(e){i(String(e))}}},d=()=>{for(;n<r&&" \n\r	".includes(e[n]);)n++};return s()})(e.trim(),t)})(e,r$.ALL^r$.NUM);var rH=function(e,t,r,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r},rq=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class rV extends rF{constructor(e){super(),eh.add(this),em.set(this,void 0),eg.set(this,void 0),ey.set(this,void 0),rH(this,em,e,"f"),rH(this,eg,[],"f")}get currentChatCompletionSnapshot(){return rq(this,ey,"f")}static fromReadableStream(e){let t=new rV(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,r){let n=new rV(t);return n._run(()=>n._runChatCompletion(e,{...t,stream:!0},{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(e,t,r){super._createChatCompletion;let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),rq(this,eh,"m",ev).call(this);let o=await e.chat.completions.create({...t,stream:!0},{...r,signal:this.controller.signal});for await(let e of(this._connected(),o))rq(this,eh,"m",eA).call(this,e);if(o.controller.signal?.aborted)throw new M;return this._addChatCompletion(rq(this,eh,"m",eS).call(this))}async _fromReadableStream(e,t){let r,n=t?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),rq(this,eh,"m",ev).call(this),this._connected();let o=e3.fromReadableStream(e,this.controller);for await(let e of o)r&&r!==e.id&&this._addChatCompletion(rq(this,eh,"m",eS).call(this)),rq(this,eh,"m",eA).call(this,e),r=e.id;if(o.controller.signal?.aborted)throw new M;return this._addChatCompletion(rq(this,eh,"m",eS).call(this))}[(em=new WeakMap,eg=new WeakMap,ey=new WeakMap,eh=new WeakSet,ev=function(){this.ended||rH(this,ey,void 0,"f")},eb=function(e){let t=rq(this,eg,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},rq(this,eg,"f")[e.index]=t),t},eA=function(e){if(this.ended)return;let t=rq(this,eh,"m",e_).call(this,e);for(let r of(this._emit("chunk",e,t),e.choices)){let e=t.choices[r.index];null!=r.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",r.delta.content,e.message.content),this._emit("content.delta",{delta:r.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=r.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:r.delta.refusal,snapshot:e.message.refusal}),r.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:r.logprobs?.content,snapshot:e.logprobs?.content??[]}),r.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:r.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let n=rq(this,eh,"m",eb).call(this,e);for(let t of(e.finish_reason&&(rq(this,eh,"m",ex).call(this,e),null!=n.current_tool_call_index&&rq(this,eh,"m",ew).call(this,e,n.current_tool_call_index)),r.delta.tool_calls??[]))n.current_tool_call_index!==t.index&&(rq(this,eh,"m",ex).call(this,e),null!=n.current_tool_call_index&&rq(this,eh,"m",ew).call(this,e,n.current_tool_call_index)),n.current_tool_call_index=t.index;for(let t of r.delta.tool_calls??[]){let r=e.message.tool_calls?.[t.index];r?.type&&(r?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:r.function?.name,index:t.index,arguments:r.function.arguments,parsed_arguments:r.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):r?.type)}}},ew=function(e,t){if(rq(this,eh,"m",eb).call(this,e).done_tool_calls.has(t))return;let r=e.message.tool_calls?.[t];if(!r)throw Error("no tool call snapshot");if(!r.type)throw Error("tool call snapshot missing `type`");if("function"===r.type){let e=rq(this,em,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===r.function.name);this._emit("tool_calls.function.arguments.done",{name:r.function.name,index:t,arguments:r.function.arguments,parsed_arguments:rj(e)?e.$parseRaw(r.function.arguments):e?.function.strict?JSON.parse(r.function.arguments):null})}else r.type},ex=function(e){let t=rq(this,eh,"m",eb).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let r=rq(this,eh,"m",eE).call(this);this._emit("content.done",{content:e.message.content,parsed:r?r.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},eS=function(){if(this.ended)throw new I("stream has ended, this shouldn't happen");let e=rq(this,ey,"f");if(!e)throw new I("request ended without sending any chunks");return rH(this,ey,void 0,"f"),rH(this,eg,[],"f"),function(e,t){var r;let{id:n,choices:o,created:i,model:s,system_fingerprint:a,...l}=e;return r={...l,id:n,choices:o.map(({message:t,finish_reason:r,index:n,logprobs:o,...i})=>{if(!r)throw new I(`missing finish_reason for choice ${n}`);let{content:s=null,function_call:a,tool_calls:l,...c}=t,u=t.role;if(!u)throw new I(`missing role for choice ${n}`);if(a){let{arguments:e,name:l}=a;if(null==e)throw new I(`missing function_call.arguments for choice ${n}`);if(!l)throw new I(`missing function_call.name for choice ${n}`);return{...i,message:{content:s,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:r,index:n,logprobs:o}}return l?{...i,index:n,finish_reason:r,logprobs:o,message:{...c,role:u,content:s,refusal:t.refusal??null,tool_calls:l.map((t,r)=>{let{function:o,type:i,id:s,...a}=t,{arguments:l,name:c,...u}=o||{};if(null==s)throw new I(`missing choices[${n}].tool_calls[${r}].id
${rX(e)}`);if(null==i)throw new I(`missing choices[${n}].tool_calls[${r}].type
${rX(e)}`);if(null==c)throw new I(`missing choices[${n}].tool_calls[${r}].function.name
${rX(e)}`);if(null==l)throw new I(`missing choices[${n}].tool_calls[${r}].function.arguments
${rX(e)}`);return{...a,id:s,type:i,function:{...u,name:c,arguments:l}}})}}:{...i,message:{...c,content:s,role:u,refusal:t.refusal??null},finish_reason:r,index:n,logprobs:o}}),created:i,model:s,object:"chat.completion",...a?{system_fingerprint:a}:{}},t&&rL(t)?rN(r,t):{...r,choices:r.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,rq(this,em,"f"))},eE=function(){let e=rq(this,em,"f")?.response_format;return rP(e)?e:null},e_=function(e){var t,r,n,o;let i=rq(this,ey,"f"),{choices:s,...a}=e;for(let{delta:s,finish_reason:l,index:c,logprobs:u=null,...d}of(i?Object.assign(i,a):i=rH(this,ey,{...a,choices:[]},"f"),e.choices)){let e=i.choices[c];if(e||(e=i.choices[c]={finish_reason:l,index:c,message:{},logprobs:u,...d}),u)if(e.logprobs){let{content:n,refusal:o,...i}=u;Object.assign(e.logprobs,i),n&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...n)),o&&((r=e.logprobs).refusal??(r.refusal=[]),e.logprobs.refusal.push(...o))}else e.logprobs=Object.assign({},u);if(l&&(e.finish_reason=l,rq(this,em,"f")&&rL(rq(this,em,"f")))){if("length"===l)throw new U;if("content_filter"===l)throw new H}if(Object.assign(e,d),!s)continue;let{content:a,refusal:p,function_call:f,role:h,tool_calls:m,...g}=s;if(Object.assign(e.message,g),p&&(e.message.refusal=(e.message.refusal||"")+p),h&&(e.message.role=h),f&&(e.message.function_call?(f.name&&(e.message.function_call.name=f.name),f.arguments&&((n=e.message.function_call).arguments??(n.arguments=""),e.message.function_call.arguments+=f.arguments)):e.message.function_call=f),a&&(e.message.content=(e.message.content||"")+a,!e.message.refusal&&rq(this,eh,"m",eE).call(this)&&(e.message.parsed=rU(e.message.content))),m)for(let{index:t,id:r,type:n,function:i,...s}of(e.message.tool_calls||(e.message.tool_calls=[]),m)){let a=(o=e.message.tool_calls)[t]??(o[t]={});Object.assign(a,s),r&&(a.id=r),n&&(a.type=n),i&&(a.function??(a.function={name:i.name??"",arguments:""})),i?.name&&(a.function.name=i.name),i?.arguments&&(a.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let r=e.tools?.find(e=>e.function?.name===t.function.name);return rj(r)||r?.function.strict||!1}(rq(this,em,"f"),a)&&(a.function.parsed_arguments=rU(a.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("chunk",r=>{let n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new e3(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function rX(e){return JSON.stringify(e)}class rK extends rV{static fromReadableStream(e){let t=new rK(null);return t._run(()=>t._fromReadableStream(e)),t}static runFunctions(e,t,r){let n=new rK(null),o={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runFunctions"}};return n._run(()=>n._runFunctions(e,t,o)),n}static runTools(e,t,r){let n=new rK(t),o={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,o)),n}}class rG extends tV{parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new I(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new I(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"beta.chat.completions.parse"}})._thenUnwrap(t=>rN(t,e))}runFunctions(e,t){return e.stream?rK.runFunctions(this._client,e,t):rD.runFunctions(this._client,e,t)}runTools(e,t){return e.stream?rK.runTools(this._client,e,t):rD.runTools(this._client,e,t)}stream(e,t){return rV.createChatCompletion(this._client,e,t)}}class rY extends tV{constructor(){super(...arguments),this.completions=new rG(this._client)}}(rY||(rY={})).Completions=rG;class rJ extends tV{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}}class rQ extends tV{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}}class rZ extends tV{constructor(){super(...arguments),this.sessions=new rJ(this._client),this.transcriptionSessions=new rQ(this._client)}}rZ.Sessions=rJ,rZ.TranscriptionSessions=rQ;var r0=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)},r1=function(e,t,r,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r};class r2 extends rM{constructor(){super(...arguments),eC.add(this),eR.set(this,[]),eO.set(this,{}),ek.set(this,{}),eI.set(this,void 0),eT.set(this,void 0),eM.set(this,void 0),eP.set(this,void 0),ej.set(this,void 0),eN.set(this,void 0),eL.set(this,void 0),eB.set(this,void 0),eF.set(this,void 0)}[(eR=new WeakMap,eO=new WeakMap,ek=new WeakMap,eI=new WeakMap,eT=new WeakMap,eM=new WeakMap,eP=new WeakMap,ej=new WeakMap,eN=new WeakMap,eL=new WeakMap,eB=new WeakMap,eF=new WeakMap,eC=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",r=>{let n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new r2;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),this._connected();let n=e3.fromReadableStream(e,this.controller);for await(let e of n)r0(this,eC,"m",eD).call(this,e);if(n.controller.signal?.aborted)throw new M;return this._addRun(r0(this,eC,"m",e$).call(this))}toReadableStream(){return new e3(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,r,n,o){let i=new r2;return i._run(()=>i._runToolAssistantStream(e,t,r,n,{...o,headers:{...o?.headers,"X-Stainless-Helper-Method":"stream"}})),i}async _createToolAssistantStream(e,t,r,n,o){let i=o?.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort()));let s={...n,stream:!0},a=await e.submitToolOutputs(t,r,s,{...o,signal:this.controller.signal});for await(let e of(this._connected(),a))r0(this,eC,"m",eD).call(this,e);if(a.controller.signal?.aborted)throw new M;return this._addRun(r0(this,eC,"m",e$).call(this))}static createThreadAssistantStream(e,t,r){let n=new r2;return n._run(()=>n._threadAssistantStream(e,t,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(e,t,r,n){let o=new r2;return o._run(()=>o._runAssistantStream(e,t,r,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),o}currentEvent(){return r0(this,eL,"f")}currentRun(){return r0(this,eB,"f")}currentMessageSnapshot(){return r0(this,eI,"f")}currentRunStepSnapshot(){return r0(this,eF,"f")}async finalRunSteps(){return await this.done(),Object.values(r0(this,eO,"f"))}async finalMessages(){return await this.done(),Object.values(r0(this,ek,"f"))}async finalRun(){if(await this.done(),!r0(this,eT,"f"))throw Error("Final run was not received.");return r0(this,eT,"f")}async _createThreadAssistantStream(e,t,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let o={...t,stream:!0},i=await e.createAndRun(o,{...r,signal:this.controller.signal});for await(let e of(this._connected(),i))r0(this,eC,"m",eD).call(this,e);if(i.controller.signal?.aborted)throw new M;return this._addRun(r0(this,eC,"m",e$).call(this))}async _createAssistantStream(e,t,r,n){let o=n?.signal;o&&(o.aborted&&this.controller.abort(),o.addEventListener("abort",()=>this.controller.abort()));let i={...r,stream:!0},s=await e.create(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),s))r0(this,eC,"m",eD).call(this,e);if(s.controller.signal?.aborted)throw new M;return this._addRun(r0(this,eC,"m",e$).call(this))}static accumulateDelta(e,t){for(let[r,n]of Object.entries(t)){if(!e.hasOwnProperty(r)){e[r]=n;continue}let t=e[r];if(null==t||"index"===r||"type"===r){e[r]=n;continue}if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else if(tq(t)&&tq(n))t=this.accumulateDelta(t,n);else if(Array.isArray(t)&&Array.isArray(n)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...n);continue}for(let e of n){if(!tq(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let r=e.index;if(null==r)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof r)throw Error(`Expected array delta entry \`index\` property to be a number but got ${r}`);let n=t[r];null==n?t.push(e):t[r]=this.accumulateDelta(n,e)}continue}else throw Error(`Unhandled record type: ${r}, deltaValue: ${n}, accValue: ${t}`);e[r]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,r){return await this._createThreadAssistantStream(t,e,r)}async _runAssistantStream(e,t,r,n){return await this._createAssistantStream(t,e,r,n)}async _runToolAssistantStream(e,t,r,n,o){return await this._createToolAssistantStream(r,e,t,n,o)}}eD=function(e){if(!this.ended)switch(r1(this,eL,e,"f"),r0(this,eC,"m",eU).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":r0(this,eC,"m",eX).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":r0(this,eC,"m",eW).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":r0(this,eC,"m",ez).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},e$=function(){if(this.ended)throw new I("stream has ended, this shouldn't happen");if(!r0(this,eT,"f"))throw Error("Final run has not been received");return r0(this,eT,"f")},ez=function(e){let[t,r]=r0(this,eC,"m",eq).call(this,e,r0(this,eI,"f"));for(let e of(r1(this,eI,t,"f"),r0(this,ek,"f")[t.id]=t,r)){let r=t.content[e.index];r?.type=="text"&&this._emit("textCreated",r.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let r of e.data.delta.content){if("text"==r.type&&r.text){let e=r.text,n=t.content[r.index];if(n&&"text"==n.type)this._emit("textDelta",e,n.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(r.index!=r0(this,eM,"f")){if(r0(this,eP,"f"))switch(r0(this,eP,"f").type){case"text":this._emit("textDone",r0(this,eP,"f").text,r0(this,eI,"f"));break;case"image_file":this._emit("imageFileDone",r0(this,eP,"f").image_file,r0(this,eI,"f"))}r1(this,eM,r.index,"f")}r1(this,eP,t.content[r.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==r0(this,eM,"f")){let t=e.data.content[r0(this,eM,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,r0(this,eI,"f"));break;case"text":this._emit("textDone",t.text,r0(this,eI,"f"))}}r0(this,eI,"f")&&this._emit("messageDone",e.data),r1(this,eI,void 0,"f")}},eW=function(e){let t=r0(this,eC,"m",eH).call(this,e);switch(r1(this,eF,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let r=e.data.delta;if(r.step_details&&"tool_calls"==r.step_details.type&&r.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of r.step_details.tool_calls)e.index==r0(this,ej,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(r0(this,eN,"f")&&this._emit("toolCallDone",r0(this,eN,"f")),r1(this,ej,e.index,"f"),r1(this,eN,t.step_details.tool_calls[e.index],"f"),r0(this,eN,"f")&&this._emit("toolCallCreated",r0(this,eN,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":r1(this,eF,void 0,"f"),"tool_calls"==e.data.step_details.type&&r0(this,eN,"f")&&(this._emit("toolCallDone",r0(this,eN,"f")),r1(this,eN,void 0,"f")),this._emit("runStepDone",e.data,t)}},eU=function(e){r0(this,eR,"f").push(e),this._emit("event",e)},eH=function(e){switch(e.event){case"thread.run.step.created":return r0(this,eO,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=r0(this,eO,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let r=e.data;if(r.delta){let n=r2.accumulateDelta(t,r.delta);r0(this,eO,"f")[e.data.id]=n}return r0(this,eO,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":r0(this,eO,"f")[e.data.id]=e.data}if(r0(this,eO,"f")[e.data.id])return r0(this,eO,"f")[e.data.id];throw Error("No snapshot available")},eq=function(e,t){let r=[];switch(e.event){case"thread.message.created":return[e.data,r];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(let e of n.delta.content)if(e.index in t.content){let r=t.content[e.index];t.content[e.index]=r0(this,eC,"m",eV).call(this,e,r)}else t.content[e.index]=e,r.push(e);return[t,r];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,r];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},eV=function(e,t){return r2.accumulateDelta(t,e)},eX=function(e){switch(r1(this,eB,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":r1(this,eT,e.data,"f"),r0(this,eN,"f")&&(this._emit("toolCallDone",r0(this,eN,"f")),r1(this,eN,void 0,"f"))}};class r5 extends tV{create(e,t,r){return this._client.post(`/threads/${e}/messages`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}retrieve(e,t,r){return this._client.get(`/threads/${e}/messages/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}update(e,t,r,n){return this._client.post(`/threads/${e}/messages/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}list(e,t={},r){return tR(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/messages`,r4,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}del(e,t,r){return this._client.delete(`/threads/${e}/messages/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}}class r4 extends tY{}r5.MessagesPage=r4;class r7 extends tV{retrieve(e,t,r,n={},o){return tR(n)?this.retrieve(e,t,r,{},n):this._client.get(`/threads/${e}/runs/${t}/steps/${r}`,{query:n,...o,headers:{"OpenAI-Beta":"assistants=v2",...o?.headers}})}list(e,t,r={},n){return tR(r)?this.list(e,t,{},r):this._client.getAPIList(`/threads/${e}/runs/${t}/steps`,r6,{query:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}}class r6 extends tY{}r7.RunStepsPage=r6;class r3 extends tV{constructor(){super(...arguments),this.steps=new r7(this._client)}create(e,t,r){let{include:n,...o}=t;return this._client.post(`/threads/${e}/runs`,{query:{include:n},body:o,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers},stream:t.stream??!1})}retrieve(e,t,r){return this._client.get(`/threads/${e}/runs/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}update(e,t,r,n){return this._client.post(`/threads/${e}/runs/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}list(e,t={},r){return tR(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/runs`,r8,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}cancel(e,t,r){return this._client.post(`/threads/${e}/runs/${t}/cancel`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}async createAndPoll(e,t,r){let n=await this.create(e,t,r);return await this.poll(e,n.id,r)}createAndStream(e,t,r){return r2.createAssistantStream(e,this._client.beta.threads.runs,t,r)}async poll(e,t,r){let n={...r?.headers,"X-Stainless-Poll-Helper":"true"};for(r?.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){let{data:o,response:i}=await this.retrieve(e,t,{...r,headers:{...r?.headers,...n}}).withResponse();switch(o.status){case"queued":case"in_progress":case"cancelling":let s=5e3;if(r?.pollIntervalMs)s=r.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(s=t)}}await tj(s);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return o}}}stream(e,t,r){return r2.createAssistantStream(e,this._client.beta.threads.runs,t,r)}submitToolOutputs(e,t,r,n){return this._client.post(`/threads/${e}/runs/${t}/submit_tool_outputs`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers},stream:r.stream??!1})}async submitToolOutputsAndPoll(e,t,r,n){let o=await this.submitToolOutputs(e,t,r,n);return await this.poll(e,o.id,n)}submitToolOutputsStream(e,t,r,n){return r2.createToolAssistantStream(e,t,this._client.beta.threads.runs,r,n)}}class r8 extends tY{}r3.RunsPage=r8,r3.Steps=r7,r3.RunStepsPage=r6;class r9 extends tV{constructor(){super(...arguments),this.runs=new r3(this._client),this.messages=new r5(this._client)}create(e={},t){return tR(e)?this.create({},e):this._client.post("/threads",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}retrieve(e,t){return this._client.get(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}update(e,t,r){return this._client.post(`/threads/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}del(e,t){return this._client.delete(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers},stream:e.stream??!1})}async createAndRunPoll(e,t){let r=await this.createAndRun(e,t);return await this.runs.poll(r.thread_id,r.id,t)}createAndRunStream(e,t){return r2.createThreadAssistantStream(e,this._client.beta.threads,t)}}r9.Runs=r3,r9.RunsPage=r8,r9.Messages=r5,r9.MessagesPage=r4;class ne extends tV{constructor(){super(...arguments),this.realtime=new rZ(this._client),this.chat=new rY(this._client),this.assistants=new rE(this._client),this.threads=new r9(this._client)}}ne.Realtime=rZ,ne.Assistants=rE,ne.AssistantsPage=r_,ne.Threads=r9;class nt extends tV{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(`/batches/${e}`,t)}list(e={},t){return tR(e)?this.list({},e):this._client.getAPIList("/batches",nr,{query:e,...t})}cancel(e,t){return this._client.post(`/batches/${e}/cancel`,t)}}class nr extends tY{}nt.BatchesPage=nr;class nn extends tV{create(e,t,r){return this._client.post(`/uploads/${e}/parts`,td({body:t,...r}))}}class no extends tV{constructor(){super(...arguments),this.parts=new nn(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(`/uploads/${e}/cancel`,t)}complete(e,t,r){return this._client.post(`/uploads/${e}/complete`,{body:t,...r})}}function ni(e,t){let r=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){var r,n;let o=(r=e.tools??[],n=t.name,r.find(e=>"function"===e.type&&e.name===n));return{...t,...t,parsed_arguments:o?.$brand==="auto-parseable-tool"?o.$parseRaw(t.arguments):o?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let r=e.content.map(e=>{var r,n;return"output_text"===e.type?{...e,parsed:(r=t,n=e.text,r.text?.format?.type!=="json_schema"?null:"$parseRaw"in r.text?.format?(r.text?.format).$parseRaw(n):JSON.parse(n))}:e});return{...e,content:r}}return e}),n=Object.assign({},e,{output:r});return Object.getOwnPropertyDescriptor(e,"output_text")||ns(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(let e of n.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),n}function ns(e){let t=[];for(let r of e.output)if("message"===r.type)for(let e of r.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}no.Parts=nn;class na extends tV{list(e,t={},r){return tR(t)?this.list(e,{},t):this._client.getAPIList(`/responses/${e}/input_items`,np,{query:t,...r})}}var nl=function(e,t,r,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(e,r):o?o.value=r:t.set(e,r),r},nc=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class nu extends rM{constructor(e){super(),eK.add(this),eG.set(this,void 0),eY.set(this,void 0),eJ.set(this,void 0),nl(this,eG,e,"f")}static createResponse(e,t,r){let n=new nu(t);return n._run(()=>n._createOrRetrieveResponse(e,t,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createOrRetrieveResponse(e,t,r){let n,o=r?.signal;o&&(o.aborted&&this.controller.abort(),o.addEventListener("abort",()=>this.controller.abort())),nc(this,eK,"m",eQ).call(this);let i=null;for await(let o of("response_id"in t?(n=await e.responses.retrieve(t.response_id,{stream:!0},{...r,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):n=await e.responses.create({...t,stream:!0},{...r,signal:this.controller.signal}),this._connected(),n))nc(this,eK,"m",eZ).call(this,o,i);if(n.controller.signal?.aborted)throw new M;return nc(this,eK,"m",e0).call(this)}[(eG=new WeakMap,eY=new WeakMap,eJ=new WeakMap,eK=new WeakSet,eQ=function(){this.ended||nl(this,eY,void 0,"f")},eZ=function(e,t){if(this.ended)return;let r=(e,r)=>{(null==t||r.sequence_number>t)&&this._emit(e,r)},n=nc(this,eK,"m",e1).call(this,e);switch(r("event",e),e.type){case"response.output_text.delta":{let t=n.output[e.output_index];if(!t)throw new I(`missing output at index ${e.output_index}`);if("message"===t.type){let n=t.content[e.content_index];if(!n)throw new I(`missing content at index ${e.content_index}`);if("output_text"!==n.type)throw new I(`expected content to be 'output_text', got ${n.type}`);r("response.output_text.delta",{...e,snapshot:n.text})}break}case"response.function_call_arguments.delta":{let t=n.output[e.output_index];if(!t)throw new I(`missing output at index ${e.output_index}`);"function_call"===t.type&&r("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:r(e.type,e)}},e0=function(){if(this.ended)throw new I("stream has ended, this shouldn't happen");let e=nc(this,eY,"f");if(!e)throw new I("request ended without sending any events");nl(this,eY,void 0,"f");let t=function(e,t){var r;return t&&(r=t,rP(r.text?.format))?ni(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,nc(this,eG,"f"));return nl(this,eJ,t,"f"),t},e1=function(e){let t=nc(this,eY,"f");if(!t){if("response.created"!==e.type)throw new I(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return nl(this,eY,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let r=t.output[e.output_index];if(!r)throw new I(`missing output at index ${e.output_index}`);"message"===r.type&&r.content.push(e.part);break}case"response.output_text.delta":{let r=t.output[e.output_index];if(!r)throw new I(`missing output at index ${e.output_index}`);if("message"===r.type){let t=r.content[e.content_index];if(!t)throw new I(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new I(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let r=t.output[e.output_index];if(!r)throw new I(`missing output at index ${e.output_index}`);"function_call"===r.type&&(r.arguments+=e.delta);break}case"response.completed":nl(this,eY,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",r=>{let n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=nc(this,eJ,"f");if(!e)throw new I("stream ended without producing a ChatCompletion");return e}}class nd extends tV{constructor(){super(...arguments),this.inputItems=new na(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&ns(e),e))}retrieve(e,t={},r){return this._client.get(`/responses/${e}`,{query:t,...r,stream:t?.stream??!1})}del(e,t){return this._client.delete(`/responses/${e}`,{...t,headers:{Accept:"*/*",...t?.headers}})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>ni(t,e))}stream(e,t){return nu.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(`/responses/${e}/cancel`,{...t,headers:{Accept:"*/*",...t?.headers}})}}class np extends tY{}nd.InputItems=na;class nf extends tV{retrieve(e,t,r,n){return this._client.get(`/evals/${e}/runs/${t}/output_items/${r}`,n)}list(e,t,r={},n){return tR(r)?this.list(e,t,{},r):this._client.getAPIList(`/evals/${e}/runs/${t}/output_items`,nh,{query:r,...n})}}class nh extends tY{}nf.OutputItemListResponsesPage=nh;class nm extends tV{constructor(){super(...arguments),this.outputItems=new nf(this._client)}create(e,t,r){return this._client.post(`/evals/${e}/runs`,{body:t,...r})}retrieve(e,t,r){return this._client.get(`/evals/${e}/runs/${t}`,r)}list(e,t={},r){return tR(t)?this.list(e,{},t):this._client.getAPIList(`/evals/${e}/runs`,ng,{query:t,...r})}del(e,t,r){return this._client.delete(`/evals/${e}/runs/${t}`,r)}cancel(e,t,r){return this._client.post(`/evals/${e}/runs/${t}`,r)}}class ng extends tY{}nm.RunListResponsesPage=ng,nm.OutputItems=nf,nm.OutputItemListResponsesPage=nh;class ny extends tV{constructor(){super(...arguments),this.runs=new nm(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(`/evals/${e}`,t)}update(e,t,r){return this._client.post(`/evals/${e}`,{body:t,...r})}list(e={},t){return tR(e)?this.list({},e):this._client.getAPIList("/evals",nv,{query:e,...t})}del(e,t){return this._client.delete(`/evals/${e}`,t)}}class nv extends tY{}ny.EvalListResponsesPage=nv,ny.Runs=nm,ny.RunListResponsesPage=ng;class nb extends tV{retrieve(e,t,r){return this._client.get(`/containers/${e}/files/${t}/content`,{...r,headers:{Accept:"application/binary",...r?.headers},__binaryResponse:!0})}}class nA extends tV{constructor(){super(...arguments),this.content=new nb(this._client)}create(e,t,r){return this._client.post(`/containers/${e}/files`,td({body:t,...r}))}retrieve(e,t,r){return this._client.get(`/containers/${e}/files/${t}`,r)}list(e,t={},r){return tR(t)?this.list(e,{},t):this._client.getAPIList(`/containers/${e}/files`,nw,{query:t,...r})}del(e,t,r){return this._client.delete(`/containers/${e}/files/${t}`,{...r,headers:{Accept:"*/*",...r?.headers}})}}class nw extends tY{}nA.FileListResponsesPage=nw,nA.Content=nb;class nx extends tV{constructor(){super(...arguments),this.files=new nA(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(`/containers/${e}`,t)}list(e={},t){return tR(e)?this.list({},e):this._client.getAPIList("/containers",nS,{query:e,...t})}del(e,t){return this._client.delete(`/containers/${e}`,{...t,headers:{Accept:"*/*",...t?.headers}})}}class nS extends tY{}nx.ContainerListResponsesPage=nS,nx.Files=nA,nx.FileListResponsesPage=nw,r(65364);class nE extends tx{constructor({baseURL:e=tB("OPENAI_BASE_URL"),apiKey:t=tB("OPENAI_API_KEY"),organization:r=tB("OPENAI_ORG_ID")??null,project:n=tB("OPENAI_PROJECT_ID")??null,...o}={}){if(void 0===t)throw new I("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let i={apiKey:t,organization:r,project:n,...o,baseURL:e||"https://api.openai.com/v1"};if(!i.dangerouslyAllowBrowser&&"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator)throw new I("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");super({baseURL:i.baseURL,timeout:i.timeout??6e5,httpAgent:i.httpAgent,maxRetries:i.maxRetries,fetch:i.fetch}),this.completions=new tX(this),this.chat=new t0(this),this.embeddings=new t1(this),this.files=new t2(this),this.images=new t4(this),this.audio=new t8(this),this.moderations=new t9(this),this.models=new re(this),this.fineTuning=new rf(this),this.graders=new rm(this),this.vectorStores=new rw(this),this.beta=new ne(this),this.batches=new nt(this),this.uploads=new no(this),this.responses=new nd(this),this.evals=new ny(this),this.containers=new nx(this),this._options=i,this.apiKey=t,this.organization=r,this.project=n}defaultQuery(){return this._options.defaultQuery}defaultHeaders(e){return{...super.defaultHeaders(e),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project,...this._options.defaultHeaders}}authHeaders(e){return{Authorization:`Bearer ${this.apiKey}`}}stringifyQuery(e){return function(e,t={}){let r,n=e,o=function(e=E){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let r=e.charset||E.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=f;if(void 0!==e.format){if(!v.call(h,e.format))throw TypeError("Unknown format option provided.");n=e.format}let o=h[n],i=E.filter;if(("function"==typeof e.filter||A(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in b?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":E.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let s=void 0===e.allowDots?!0==!!e.encodeDotInKeys||E.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:E.addQueryPrefix,allowDots:s,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:E.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:E.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?E.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:E.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:E.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:E.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:E.encodeValuesOnly,filter:i,format:n,formatter:o,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:E.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:E.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:E.strictNullHandling}}(t);"function"==typeof o.filter?n=(0,o.filter)("",n):A(o.filter)&&(r=o.filter);let i=[];if("object"!=typeof n||null===n)return"";let s=b[o.arrayFormat],a="comma"===s&&o.commaRoundTrip;r||(r=Object.keys(n)),o.sort&&r.sort(o.sort);let l=new WeakMap;for(let e=0;e<r.length;++e){let t=r[e];o.skipNulls&&null===n[t]||x(i,function e(t,r,n,o,i,s,a,l,c,u,d,p,f,h,m,g,v,b){var w,S;let C,R=t,O=b,k=0,I=!1;for(;void 0!==(O=O.get(_))&&!I;){let e=O.get(t);if(k+=1,void 0!==e)if(e===k)throw RangeError("Cyclic object value");else I=!0;void 0===O.get(_)&&(k=0)}if("function"==typeof u?R=u(r,R):R instanceof Date?R=f?.(R):"comma"===n&&A(R)&&(R=y(R,function(e){return e instanceof Date?f?.(e):e})),null===R){if(s)return c&&!g?c(r,E.encoder,v,"key",h):r;R=""}if("string"==typeof(w=R)||"number"==typeof w||"boolean"==typeof w||"symbol"==typeof w||"bigint"==typeof w||(S=R)&&"object"==typeof S&&S.constructor&&S.constructor.isBuffer&&S.constructor.isBuffer(S)){if(c){let e=g?r:c(r,E.encoder,v,"key",h);return[m?.(e)+"="+m?.(c(R,E.encoder,v,"value",h))]}return[m?.(r)+"="+m?.(String(R))]}let T=[];if(void 0===R)return T;if("comma"===n&&A(R))g&&c&&(R=y(R,c)),C=[{value:R.length>0?R.join(",")||null:void 0}];else if(A(u))C=u;else{let e=Object.keys(R);C=d?e.sort(d):e}let M=l?String(r).replace(/\./g,"%2E"):String(r),P=o&&A(R)&&1===R.length?M+"[]":M;if(i&&A(R)&&0===R.length)return P+"[]";for(let r=0;r<C.length;++r){let y=C[r],w="object"==typeof y&&void 0!==y.value?y.value:R[y];if(a&&null===w)continue;let S=p&&l?y.replace(/\./g,"%2E"):y,E=A(R)?"function"==typeof n?n(P,S):P:P+(p?"."+S:"["+S+"]");b.set(t,k);let O=new WeakMap;O.set(_,b),x(T,e(w,E,n,o,i,s,a,l,"comma"===n&&g&&A(R)?null:c,u,d,p,f,h,m,g,v,O))}return T}(n[t],t,s,a,o.allowEmptyArrays,o.strictNullHandling,o.skipNulls,o.encodeDotInKeys,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset,l))}let c=i.join(o.delimiter),u=!0===o.addQueryPrefix?"?":"";return o.charsetSentinel&&("iso-8859-1"===o.charset?u+="utf8=%26%2310003%3B&":u+="utf8=%E2%9C%93&"),c.length>0?u+c:""}(e,{arrayFormat:"brackets"})}}nE.OpenAI=nE,nE.DEFAULT_TIMEOUT=6e5,nE.OpenAIError=I,nE.APIError=T,nE.APIConnectionError=P,nE.APIConnectionTimeoutError=j,nE.APIUserAbortError=M,nE.NotFoundError=F,nE.ConflictError=D,nE.RateLimitError=z,nE.BadRequestError=N,nE.AuthenticationError=L,nE.InternalServerError=W,nE.PermissionDeniedError=B,nE.UnprocessableEntityError=$,nE.toFile=ts,nE.fileFromPath=d,nE.Completions=tX,nE.Chat=t0,nE.ChatCompletionsPage=tQ,nE.Embeddings=t1,nE.Files=t2,nE.FileObjectsPage=t5,nE.Images=t4,nE.Audio=t8,nE.Moderations=t9,nE.Models=re,nE.ModelsPage=rt,nE.FineTuning=rf,nE.Graders=rm,nE.VectorStores=rw,nE.VectorStoresPage=rx,nE.VectorStoreSearchResponsesPage=rS,nE.Beta=ne,nE.Batches=nt,nE.BatchesPage=nr,nE.Uploads=no,nE.Responses=nd,nE.Evals=ny,nE.EvalListResponsesPage=nv,nE.Containers=nx,nE.ContainerListResponsesPage=nS;let n_=nE},77170:(e,t,r)=>{var n="Expected a function",o=0/0,i=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,c=parseInt,u="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,d="object"==typeof self&&self&&self.Object===Object&&self,p=u||d||Function("return this")(),f=Object.prototype.toString,h=Math.max,m=Math.min,g=function(){return p.Date.now()};function y(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function v(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==f.call(t))return o;if(y(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=y(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var n=a.test(e);return n||l.test(e)?c(e.slice(2),n?2:8):s.test(e)?o:+e}e.exports=function(e,t,r){var o=!0,i=!0;if("function"!=typeof e)throw TypeError(n);return y(r)&&(o="leading"in r?!!r.leading:o,i="trailing"in r?!!r.trailing:i),function(e,t,r){var o,i,s,a,l,c,u=0,d=!1,p=!1,f=!0;if("function"!=typeof e)throw TypeError(n);function b(t){var r=o,n=i;return o=i=void 0,u=t,a=e.apply(n,r)}function A(e){var r=e-c,n=e-u;return void 0===c||r>=t||r<0||p&&n>=s}function w(){var e,r,n,o=g();if(A(o))return x(o);l=setTimeout(w,(e=o-c,r=o-u,n=t-e,p?m(n,s-r):n))}function x(e){return(l=void 0,f&&o)?b(e):(o=i=void 0,a)}function S(){var e,r=g(),n=A(r);if(o=arguments,i=this,c=r,n){if(void 0===l)return u=e=c,l=setTimeout(w,t),d?b(e):a;if(p)return l=setTimeout(w,t),b(c)}return void 0===l&&(l=setTimeout(w,t)),a}return t=v(t)||0,y(r)&&(d=!!r.leading,s=(p="maxWait"in r)?h(v(r.maxWait)||0,t):s,f="trailing"in r?!!r.trailing:f),S.cancel=function(){void 0!==l&&clearTimeout(l),u=0,o=c=i=l=void 0},S.flush=function(){return void 0===l?a:x(g())},S}(e,t,{leading:o,maxWait:t,trailing:i})}},78523:(e,t,r)=>{"use strict";r.d(t,{A:()=>f});var n=r(14232),o=r(69241),i=r(4697),s=r(54773),a=r(52196),l=r(47951),c=r(45879);function u(e){return(0,c.Ay)("MuiDialogActions",e)}(0,l.A)("MuiDialogActions",["root","spacing"]);var d=r(37876);let p=(0,s.Ay)("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,!r.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:e=>{let{ownerState:t}=e;return!t.disableSpacing},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),f=n.forwardRef(function(e,t){let r=(0,a.b)({props:e,name:"MuiDialogActions"}),{className:n,disableSpacing:s=!1,...l}=r,c={...r,disableSpacing:s},f=(e=>{let{classes:t,disableSpacing:r}=e;return(0,i.A)({root:["root",!r&&"spacing"]},u,t)})(c);return(0,d.jsx)(p,{className:(0,o.A)(f.root,n),ownerState:c,ref:t,...l})})},78630:(e,t,r)=>{"use strict";r.d(t,{A:()=>s,K:()=>i});var n=r(47951),o=r(45879);function i(e){return(0,o.Ay)("MuiDivider",e)}let s=(0,n.A)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])},79706:(e,t,r)=>{"use strict";var n=r(3717);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,i,s){if(s!==n){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},80412:(e,t,r)=>{"use strict";r.d(t,{A:()=>eK});var n=r(14232),o=r(69241),i=r(53880),s=r(4697),a=r(126),l=r(62844),c=r(97369),u=r(43165),d=r(54773),p=r(30566),f=r(82987),h=r(52196),m=r(80027),g=r(24766),y=r(61637),v=r(99659),b=r(44471);function A(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function w(e){var t=A(e).Element;return e instanceof t||e instanceof Element}function x(e){var t=A(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function S(e){if("undefined"==typeof ShadowRoot)return!1;var t=A(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var E=Math.max,_=Math.min,C=Math.round;function R(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function O(){return!/^((?!chrome|android).)*safari/i.test(R())}function k(e,t,r){void 0===t&&(t=!1),void 0===r&&(r=!1);var n=e.getBoundingClientRect(),o=1,i=1;t&&x(e)&&(o=e.offsetWidth>0&&C(n.width)/e.offsetWidth||1,i=e.offsetHeight>0&&C(n.height)/e.offsetHeight||1);var s=(w(e)?A(e):window).visualViewport,a=!O()&&r,l=(n.left+(a&&s?s.offsetLeft:0))/o,c=(n.top+(a&&s?s.offsetTop:0))/i,u=n.width/o,d=n.height/i;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l,x:l,y:c}}function I(e){var t=A(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function T(e){return e?(e.nodeName||"").toLowerCase():null}function M(e){return((w(e)?e.ownerDocument:e.document)||window.document).documentElement}function P(e){return k(M(e)).left+I(e).scrollLeft}function j(e){return A(e).getComputedStyle(e)}function N(e){var t=j(e),r=t.overflow,n=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+o+n)}function L(e){var t=k(e),r=e.offsetWidth,n=e.offsetHeight;return 1>=Math.abs(t.width-r)&&(r=t.width),1>=Math.abs(t.height-n)&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function B(e){return"html"===T(e)?e:e.assignedSlot||e.parentNode||(S(e)?e.host:null)||M(e)}function F(e,t){void 0===t&&(t=[]);var r,n=function e(t){return["html","body","#document"].indexOf(T(t))>=0?t.ownerDocument.body:x(t)&&N(t)?t:e(B(t))}(e),o=n===(null==(r=e.ownerDocument)?void 0:r.body),i=A(n),s=o?[i].concat(i.visualViewport||[],N(n)?n:[]):n,a=t.concat(s);return o?a:a.concat(F(B(s)))}function D(e){return x(e)&&"fixed"!==j(e).position?e.offsetParent:null}function $(e){for(var t=A(e),r=D(e);r&&["table","td","th"].indexOf(T(r))>=0&&"static"===j(r).position;)r=D(r);return r&&("html"===T(r)||"body"===T(r)&&"static"===j(r).position)?t:r||function(e){var t=/firefox/i.test(R());if(/Trident/i.test(R())&&x(e)&&"fixed"===j(e).position)return null;var r=B(e);for(S(r)&&(r=r.host);x(r)&&0>["html","body"].indexOf(T(r));){var n=j(r);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||t&&"filter"===n.willChange||t&&n.filter&&"none"!==n.filter)return r;r=r.parentNode}return null}(e)||t}var z="bottom",W="right",U="left",H="auto",q=["top",z,W,U],V="start",X="viewport",K="popper",G=q.reduce(function(e,t){return e.concat([t+"-"+V,t+"-end"])},[]),Y=[].concat(q,[H]).reduce(function(e,t){return e.concat([t,t+"-"+V,t+"-end"])},[]),J=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"],Q={placement:"bottom",modifiers:[],strategy:"absolute"};function Z(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var ee={passive:!0};function et(e){return e.split("-")[0]}function er(e){return e.split("-")[1]}function en(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function eo(e){var t,r=e.reference,n=e.element,o=e.placement,i=o?et(o):null,s=o?er(o):null,a=r.x+r.width/2-n.width/2,l=r.y+r.height/2-n.height/2;switch(i){case"top":t={x:a,y:r.y-n.height};break;case z:t={x:a,y:r.y+r.height};break;case W:t={x:r.x+r.width,y:l};break;case U:t={x:r.x-n.width,y:l};break;default:t={x:r.x,y:r.y}}var c=i?en(i):null;if(null!=c){var u="y"===c?"height":"width";switch(s){case V:t[c]=t[c]-(r[u]/2-n[u]/2);break;case"end":t[c]=t[c]+(r[u]/2-n[u]/2)}}return t}var ei={top:"auto",right:"auto",bottom:"auto",left:"auto"};function es(e){var t,r,n,o,i,s,a,l=e.popper,c=e.popperRect,u=e.placement,d=e.variation,p=e.offsets,f=e.position,h=e.gpuAcceleration,m=e.adaptive,g=e.roundOffsets,y=e.isFixed,v=p.x,b=void 0===v?0:v,w=p.y,x=void 0===w?0:w,S="function"==typeof g?g({x:b,y:x}):{x:b,y:x};b=S.x,x=S.y;var E=p.hasOwnProperty("x"),_=p.hasOwnProperty("y"),R=U,O="top",k=window;if(m){var I=$(l),T="clientHeight",P="clientWidth";I===A(l)&&"static"!==j(I=M(l)).position&&"absolute"===f&&(T="scrollHeight",P="scrollWidth"),("top"===u||(u===U||u===W)&&"end"===d)&&(O=z,x-=(y&&I===k&&k.visualViewport?k.visualViewport.height:I[T])-c.height,x*=h?1:-1),(u===U||("top"===u||u===z)&&"end"===d)&&(R=W,b-=(y&&I===k&&k.visualViewport?k.visualViewport.width:I[P])-c.width,b*=h?1:-1)}var N=Object.assign({position:f},m&&ei),L=!0===g?(t={x:b,y:x},r=A(l),n=t.x,o=t.y,{x:C(n*(i=r.devicePixelRatio||1))/i||0,y:C(o*i)/i||0}):{x:b,y:x};return(b=L.x,x=L.y,h)?Object.assign({},N,((a={})[O]=_?"0":"",a[R]=E?"0":"",a.transform=1>=(k.devicePixelRatio||1)?"translate("+b+"px, "+x+"px)":"translate3d("+b+"px, "+x+"px, 0)",a)):Object.assign({},N,((s={})[O]=_?x+"px":"",s[R]=E?b+"px":"",s.transform="",s))}var ea={left:"right",right:"left",bottom:"top",top:"bottom"};function el(e){return e.replace(/left|right|bottom|top/g,function(e){return ea[e]})}var ec={start:"end",end:"start"};function eu(e){return e.replace(/start|end/g,function(e){return ec[e]})}function ed(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&S(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function ep(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ef(e,t,r){var n,o,i,s,a,l,c,u,d,p;return t===X?ep(function(e,t){var r=A(e),n=M(e),o=r.visualViewport,i=n.clientWidth,s=n.clientHeight,a=0,l=0;if(o){i=o.width,s=o.height;var c=O();(c||!c&&"fixed"===t)&&(a=o.offsetLeft,l=o.offsetTop)}return{width:i,height:s,x:a+P(e),y:l}}(e,r)):w(t)?((n=k(t,!1,"fixed"===r)).top=n.top+t.clientTop,n.left=n.left+t.clientLeft,n.bottom=n.top+t.clientHeight,n.right=n.left+t.clientWidth,n.width=t.clientWidth,n.height=t.clientHeight,n.x=n.left,n.y=n.top,n):ep((o=M(e),s=M(o),a=I(o),l=null==(i=o.ownerDocument)?void 0:i.body,c=E(s.scrollWidth,s.clientWidth,l?l.scrollWidth:0,l?l.clientWidth:0),u=E(s.scrollHeight,s.clientHeight,l?l.scrollHeight:0,l?l.clientHeight:0),d=-a.scrollLeft+P(o),p=-a.scrollTop,"rtl"===j(l||s).direction&&(d+=E(s.clientWidth,l?l.clientWidth:0)-c),{width:c,height:u,x:d,y:p}))}function eh(){return{top:0,right:0,bottom:0,left:0}}function em(e){return Object.assign({},eh(),e)}function eg(e,t){return t.reduce(function(t,r){return t[r]=e,t},{})}function ey(e,t){void 0===t&&(t={});var r,n,o,i,s,a,l,c,u=t,d=u.placement,p=void 0===d?e.placement:d,f=u.strategy,h=void 0===f?e.strategy:f,m=u.boundary,g=u.rootBoundary,y=u.elementContext,v=void 0===y?K:y,b=u.altBoundary,A=u.padding,S=void 0===A?0:A,C=em("number"!=typeof S?S:eg(S,q)),R=e.rects.popper,O=e.elements[void 0!==b&&b?v===K?"reference":K:v],I=(r=w(O)?O:O.contextElement||M(e.elements.popper),n=void 0===m?"clippingParents":m,o=void 0===g?X:g,l=(a=[].concat("clippingParents"===n?(i=F(B(r)),!w(s=["absolute","fixed"].indexOf(j(r).position)>=0&&x(r)?$(r):r)?[]:i.filter(function(e){return w(e)&&ed(e,s)&&"body"!==T(e)})):[].concat(n),[o]))[0],(c=a.reduce(function(e,t){var n=ef(r,t,h);return e.top=E(n.top,e.top),e.right=_(n.right,e.right),e.bottom=_(n.bottom,e.bottom),e.left=E(n.left,e.left),e},ef(r,l,h))).width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c),P=k(e.elements.reference),N=eo({reference:P,element:R,strategy:"absolute",placement:p}),L=ep(Object.assign({},R,N)),D=v===K?L:P,U={top:I.top-D.top+C.top,bottom:D.bottom-I.bottom+C.bottom,left:I.left-D.left+C.left,right:D.right-I.right+C.right},H=e.modifiersData.offset;if(v===K&&H){var V=H[p];Object.keys(U).forEach(function(e){var t=[W,z].indexOf(e)>=0?1:-1,r=["top",z].indexOf(e)>=0?"y":"x";U[e]+=V[r]*t})}return U}function ev(e,t,r){return E(e,_(t,r))}function eb(e,t,r){return void 0===r&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function eA(e){return["top",W,z,U].some(function(t){return e[t]>=0})}var ew=function(e){void 0===e&&(e={});var t=e,r=t.defaultModifiers,n=void 0===r?[]:r,o=t.defaultOptions,i=void 0===o?Q:o;return function(e,t,r){void 0===r&&(r=i);var o,s,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},Q,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:a,setOptions:function(r){var o,s,c,p,f,h,m="function"==typeof r?r(a.options):r;d(),a.options=Object.assign({},i,a.options,m),a.scrollParents={reference:w(e)?F(e):e.contextElement?F(e.contextElement):[],popper:F(t)};var g=(s=Object.keys(o=[].concat(n,a.options.modifiers).reduce(function(e,t){var r=e[t.name];return e[t.name]=r?Object.assign({},r,t,{options:Object.assign({},r.options,t.options),data:Object.assign({},r.data,t.data)}):t,e},{})).map(function(e){return o[e]}),c=new Map,p=new Set,f=[],s.forEach(function(e){c.set(e.name,e)}),s.forEach(function(e){p.has(e.name)||function e(t){p.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!p.has(t)){var r=c.get(t);r&&e(r)}}),f.push(t)}(e)}),h=f,J.reduce(function(e,t){return e.concat(h.filter(function(e){return e.phase===t}))},[]));return a.orderedModifiers=g.filter(function(e){return e.enabled}),a.orderedModifiers.forEach(function(e){var t=e.name,r=e.options,n=e.effect;if("function"==typeof n){var o=n({state:a,name:t,instance:u,options:void 0===r?{}:r});l.push(o||function(){})}}),u.update()},forceUpdate:function(){if(!c){var e=a.elements,t=e.reference,r=e.popper;if(Z(t,r)){a.rects={reference:(n=$(r),o="fixed"===a.options.strategy,i=x(n),p=x(n)&&(l=C((s=n.getBoundingClientRect()).width)/n.offsetWidth||1,d=C(s.height)/n.offsetHeight||1,1!==l||1!==d),f=M(n),h=k(t,p,o),m={scrollLeft:0,scrollTop:0},g={x:0,y:0},(i||!i&&!o)&&(("body"!==T(n)||N(f))&&(m=function(e){return e!==A(e)&&x(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:I(e)}(n)),x(n)?(g=k(n,!0),g.x+=n.clientLeft,g.y+=n.clientTop):f&&(g.x=P(f))),{x:h.left+m.scrollLeft-g.x,y:h.top+m.scrollTop-g.y,width:h.width,height:h.height}),popper:L(r)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach(function(e){return a.modifiersData[e.name]=Object.assign({},e.data)});for(var n,o,i,s,l,d,p,f,h,m,g,y=0;y<a.orderedModifiers.length;y++){if(!0===a.reset){a.reset=!1,y=-1;continue}var v=a.orderedModifiers[y],b=v.fn,w=v.options,S=void 0===w?{}:w,E=v.name;"function"==typeof b&&(a=b({state:a,options:S,name:E,instance:u})||a)}}}},update:(o=function(){return new Promise(function(e){u.forceUpdate(),e(a)})},function(){return s||(s=new Promise(function(e){Promise.resolve().then(function(){s=void 0,e(o())})})),s}),destroy:function(){d(),c=!0}};if(!Z(e,t))return u;function d(){l.forEach(function(e){return e()}),l=[]}return u.setOptions(r).then(function(e){!c&&r.onFirstUpdate&&r.onFirstUpdate(e)}),u}}({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,r=e.instance,n=e.options,o=n.scroll,i=void 0===o||o,s=n.resize,a=void 0===s||s,l=A(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&c.forEach(function(e){e.addEventListener("scroll",r.update,ee)}),a&&l.addEventListener("resize",r.update,ee),function(){i&&c.forEach(function(e){e.removeEventListener("scroll",r.update,ee)}),a&&l.removeEventListener("resize",r.update,ee)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,r=e.name;t.modifiersData[r]=eo({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,r=e.options,n=r.gpuAcceleration,o=r.adaptive,i=r.roundOffsets,s=void 0===i||i,a={placement:et(t.placement),variation:er(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===n||n,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,es(Object.assign({},a,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===o||o,roundOffsets:s})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,es(Object.assign({},a,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:s})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var r=t.styles[e]||{},n=t.attributes[e]||{},o=t.elements[e];x(o)&&T(o)&&(Object.assign(o.style,r),Object.keys(n).forEach(function(e){var t=n[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(e){var n=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:r[e]).reduce(function(e,t){return e[t]="",e},{});x(n)&&T(n)&&(Object.assign(n.style,i),Object.keys(o).forEach(function(e){n.removeAttribute(e)}))})}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,r=e.options,n=e.name,o=r.offset,i=void 0===o?[0,0]:o,s=Y.reduce(function(e,r){var n,o,s,a,l,c;return e[r]=(n=t.rects,s=[U,"top"].indexOf(o=et(r))>=0?-1:1,l=(a="function"==typeof i?i(Object.assign({},n,{placement:r})):i)[0],c=a[1],l=l||0,c=(c||0)*s,[U,W].indexOf(o)>=0?{x:c,y:l}:{x:l,y:c}),e},{}),a=s[t.placement],l=a.x,c=a.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[n]=s}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var o=r.mainAxis,i=void 0===o||o,s=r.altAxis,a=void 0===s||s,l=r.fallbackPlacements,c=r.padding,u=r.boundary,d=r.rootBoundary,p=r.altBoundary,f=r.flipVariations,h=void 0===f||f,m=r.allowedAutoPlacements,g=t.options.placement,y=et(g)===g,v=l||(y||!h?[el(g)]:function(e){if(et(e)===H)return[];var t=el(e);return[eu(e),t,eu(t)]}(g)),b=[g].concat(v).reduce(function(e,r){var n,o,i,s,a,l,p,f,g,y,v,b;return e.concat(et(r)===H?(o=(n={placement:r,boundary:u,rootBoundary:d,padding:c,flipVariations:h,allowedAutoPlacements:m}).placement,i=n.boundary,s=n.rootBoundary,a=n.padding,l=n.flipVariations,f=void 0===(p=n.allowedAutoPlacements)?Y:p,0===(v=(y=(g=er(o))?l?G:G.filter(function(e){return er(e)===g}):q).filter(function(e){return f.indexOf(e)>=0})).length&&(v=y),Object.keys(b=v.reduce(function(e,r){return e[r]=ey(t,{placement:r,boundary:i,rootBoundary:s,padding:a})[et(r)],e},{})).sort(function(e,t){return b[e]-b[t]})):r)},[]),A=t.rects.reference,w=t.rects.popper,x=new Map,S=!0,E=b[0],_=0;_<b.length;_++){var C=b[_],R=et(C),O=er(C)===V,k=["top",z].indexOf(R)>=0,I=k?"width":"height",T=ey(t,{placement:C,boundary:u,rootBoundary:d,altBoundary:p,padding:c}),M=k?O?W:U:O?z:"top";A[I]>w[I]&&(M=el(M));var P=el(M),j=[];if(i&&j.push(T[R]<=0),a&&j.push(T[M]<=0,T[P]<=0),j.every(function(e){return e})){E=C,S=!1;break}x.set(C,j)}if(S)for(var N=h?3:1,L=function(e){var t=b.find(function(t){var r=x.get(t);if(r)return r.slice(0,e).every(function(e){return e})});if(t)return E=t,"break"},B=N;B>0&&"break"!==L(B);B--);t.placement!==E&&(t.modifiersData[n]._skip=!0,t.placement=E,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,r=e.options,n=e.name,o=r.mainAxis,i=r.altAxis,s=r.boundary,a=r.rootBoundary,l=r.altBoundary,c=r.padding,u=r.tether,d=void 0===u||u,p=r.tetherOffset,f=void 0===p?0:p,h=ey(t,{boundary:s,rootBoundary:a,padding:c,altBoundary:l}),m=et(t.placement),g=er(t.placement),y=!g,v=en(m),b="x"===v?"y":"x",A=t.modifiersData.popperOffsets,w=t.rects.reference,x=t.rects.popper,S="function"==typeof f?f(Object.assign({},t.rects,{placement:t.placement})):f,C="number"==typeof S?{mainAxis:S,altAxis:S}:Object.assign({mainAxis:0,altAxis:0},S),R=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,O={x:0,y:0};if(A){if(void 0===o||o){var k,I="y"===v?"top":U,T="y"===v?z:W,M="y"===v?"height":"width",P=A[v],j=P+h[I],N=P-h[T],B=d?-x[M]/2:0,F=g===V?w[M]:x[M],D=g===V?-x[M]:-w[M],H=t.elements.arrow,q=d&&H?L(H):{width:0,height:0},X=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:eh(),K=X[I],G=X[T],Y=ev(0,w[M],q[M]),J=y?w[M]/2-B-Y-K-C.mainAxis:F-Y-K-C.mainAxis,Q=y?-w[M]/2+B+Y+G+C.mainAxis:D+Y+G+C.mainAxis,Z=t.elements.arrow&&$(t.elements.arrow),ee=Z?"y"===v?Z.clientTop||0:Z.clientLeft||0:0,eo=null!=(k=null==R?void 0:R[v])?k:0,ei=ev(d?_(j,P+J-eo-ee):j,P,d?E(N,P+Q-eo):N);A[v]=ei,O[v]=ei-P}if(void 0!==i&&i){var es,ea,el="x"===v?"top":U,ec="x"===v?z:W,eu=A[b],ed="y"===b?"height":"width",ep=eu+h[el],ef=eu-h[ec],em=-1!==["top",U].indexOf(m),eg=null!=(ea=null==R?void 0:R[b])?ea:0,eb=em?ep:eu-w[ed]-x[ed]-eg+C.altAxis,eA=em?eu+w[ed]+x[ed]-eg-C.altAxis:ef,ew=d&&em?(es=ev(eb,eu,eA))>eA?eA:es:ev(d?eb:ep,eu,d?eA:ef);A[b]=ew,O[b]=ew-eu}t.modifiersData[n]=O}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,r=e.state,n=e.name,o=e.options,i=r.elements.arrow,s=r.modifiersData.popperOffsets,a=et(r.placement),l=en(a),c=[U,W].indexOf(a)>=0?"height":"width";if(i&&s){var u,d=(u=o.padding,em("number"!=typeof(u="function"==typeof u?u(Object.assign({},r.rects,{placement:r.placement})):u)?u:eg(u,q))),p=L(i),f="y"===l?"top":U,h="y"===l?z:W,m=r.rects.reference[c]+r.rects.reference[l]-s[l]-r.rects.popper[c],g=s[l]-r.rects.reference[l],y=$(i),v=y?"y"===l?y.clientHeight||0:y.clientWidth||0:0,b=d[f],A=v-p[c]-d[h],w=v/2-p[c]/2+(m/2-g/2),x=ev(b,w,A);r.modifiersData[n]=((t={})[l]=x,t.centerOffset=x-w,t)}},effect:function(e){var t=e.state,r=e.options.element,n=void 0===r?"[data-popper-arrow]":r;if(null!=n)("string"!=typeof n||(n=t.elements.popper.querySelector(n)))&&ed(t.elements.popper,n)&&(t.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,r=e.name,n=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,s=ey(t,{elementContext:"reference"}),a=ey(t,{altBoundary:!0}),l=eb(s,n),c=eb(a,o,i),u=eA(l),d=eA(c);t.modifiersData[r]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}}]}),ex=r(86863),eS=r(68043),eE=r(47951),e_=r(45879);function eC(e){return(0,e_.Ay)("MuiPopper",e)}(0,eE.A)("MuiPopper",["root"]);var eR=r(37876);function eO(e){return"function"==typeof e?e():e}let ek={},eI=n.forwardRef(function(e,t){var r;let{anchorEl:o,children:i,direction:a,disablePortal:l,modifiers:c,open:u,placement:d,popperOptions:p,popperRef:f,slotProps:h={},slots:m={},TransitionProps:g,ownerState:b,...A}=e,w=n.useRef(null),x=(0,y.A)(w,t),S=n.useRef(null),E=(0,y.A)(S,f),_=n.useRef(E);(0,v.A)(()=>{_.current=E},[E]),n.useImperativeHandle(f,()=>S.current,[]);let C=function(e,t){if("ltr"===t)return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}(d,a),[R,O]=n.useState(C),[k,I]=n.useState(eO(o));n.useEffect(()=>{S.current&&S.current.forceUpdate()}),n.useEffect(()=>{o&&I(eO(o))},[o]),(0,v.A)(()=>{if(!k||!u)return;let e=[{name:"preventOverflow",options:{altBoundary:l}},{name:"flip",options:{altBoundary:l}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:e=>{let{state:t}=e;O(t.placement)}}];null!=c&&(e=e.concat(c)),p&&null!=p.modifiers&&(e=e.concat(p.modifiers));let t=ew(k,w.current,{placement:C,...p,modifiers:e});return _.current(t),()=>{t.destroy(),_.current(null)}},[k,l,c,u,p,C]);let T={placement:R};null!==g&&(T.TransitionProps=g);let M=(e=>{let{classes:t}=e;return(0,s.A)({root:["root"]},eC,t)})(e),P=null!=(r=m.root)?r:"div",j=(0,ex.A)({elementType:P,externalSlotProps:h.root,externalForwardedProps:A,additionalProps:{role:"tooltip",ref:x},ownerState:e,className:M.root});return(0,eR.jsx)(P,{...j,children:"function"==typeof i?i(T):i})}),eT=n.forwardRef(function(e,t){let r,{anchorEl:o,children:i,container:s,direction:a="ltr",disablePortal:l=!1,keepMounted:c=!1,modifiers:u,open:d,placement:p="bottom",popperOptions:f=ek,popperRef:h,style:m,transition:g=!1,slotProps:y={},slots:v={},...A}=e,[w,x]=n.useState(!0);if(!c&&!d&&(!g||w))return null;if(s)r=s;else if(o){let e=eO(o);r=e&&void 0!==e.nodeType?(0,b.A)(e).body:(0,b.A)(null).body}let S=!d&&c&&(!g||w)?"none":void 0,E=g?{in:d,onEnter:()=>{x(!1)},onExited:()=>{x(!0)}}:void 0;return(0,eR.jsx)(eS.A,{disablePortal:l,container:r,children:(0,eR.jsx)(eI,{anchorEl:o,direction:a,disablePortal:l,modifiers:u,ref:t,open:g?!w:d,placement:p,popperOptions:f,popperRef:h,slotProps:y,slots:v,...A,style:{position:"fixed",top:0,left:0,display:S,...m},TransitionProps:E,children:i})})}),eM=(0,d.Ay)(eT,{name:"MuiPopper",slot:"Root",overridesResolver:(e,t)=>t.root})({}),eP=n.forwardRef(function(e,t){var r;let n=(0,l.I)(),{anchorEl:o,component:i,components:s,componentsProps:a,container:c,disablePortal:u,keepMounted:d,modifiers:p,open:f,placement:m,popperOptions:g,popperRef:y,transition:v,slots:b,slotProps:A,...w}=(0,h.b)({props:e,name:"MuiPopper"}),x=null!=(r=null==b?void 0:b.root)?r:null==s?void 0:s.Root,S={anchorEl:o,container:c,disablePortal:u,keepMounted:d,modifiers:p,open:f,placement:m,popperOptions:g,popperRef:y,transition:v,...w};return(0,eR.jsx)(eM,{as:i,direction:n?"rtl":"ltr",slots:{root:x},slotProps:null!=A?A:a,...S,ref:t})});var ej=r(97395),eN=r(66313),eL=r(27449),eB=r(53322),eF=r(67360);function eD(e){return(0,e_.Ay)("MuiTooltip",e)}let e$=(0,eE.A)("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]),ez=(0,d.Ay)(eP,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.popper,!r.disableInteractive&&t.popperInteractive,r.arrow&&t.popperArrow,!r.open&&t.popperClose]}})((0,f.A)(e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.tooltip,pointerEvents:"none",variants:[{props:e=>{let{ownerState:t}=e;return!t.disableInteractive},style:{pointerEvents:"auto"}},{props:e=>{let{open:t}=e;return!t},style:{pointerEvents:"none"}},{props:e=>{let{ownerState:t}=e;return t.arrow},style:{['&[data-popper-placement*="bottom"] .'.concat(e$.arrow)]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},['&[data-popper-placement*="top"] .'.concat(e$.arrow)]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},['&[data-popper-placement*="right"] .'.concat(e$.arrow)]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},['&[data-popper-placement*="left"] .'.concat(e$.arrow)]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!t.isRtl},style:{['&[data-popper-placement*="right"] .'.concat(e$.arrow)]:{left:0,marginLeft:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!!t.isRtl},style:{['&[data-popper-placement*="right"] .'.concat(e$.arrow)]:{right:0,marginRight:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!t.isRtl},style:{['&[data-popper-placement*="left"] .'.concat(e$.arrow)]:{right:0,marginRight:"-0.71em"}}},{props:e=>{let{ownerState:t}=e;return t.arrow&&!!t.isRtl},style:{['&[data-popper-placement*="left"] .'.concat(e$.arrow)]:{left:0,marginLeft:"-0.71em"}}}]}})),eW=(0,d.Ay)("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.tooltip,r.touch&&t.touch,r.arrow&&t.tooltipArrow,t["tooltipPlacement".concat((0,m.A)(r.placement.split("-")[0]))]]}})((0,f.A)(e=>{let{theme:t}=e;return{backgroundColor:t.vars?t.vars.palette.Tooltip.bg:(0,a.X4)(t.palette.grey[700],.92),borderRadius:(t.vars||t).shape.borderRadius,color:(t.vars||t).palette.common.white,fontFamily:t.typography.fontFamily,padding:"4px 8px",fontSize:t.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:t.typography.fontWeightMedium,[".".concat(e$.popper,'[data-popper-placement*="left"] &')]:{transformOrigin:"right center"},[".".concat(e$.popper,'[data-popper-placement*="right"] &')]:{transformOrigin:"left center"},[".".concat(e$.popper,'[data-popper-placement*="top"] &')]:{transformOrigin:"center bottom",marginBottom:"14px"},[".".concat(e$.popper,'[data-popper-placement*="bottom"] &')]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:e=>{let{ownerState:t}=e;return t.arrow},style:{position:"relative",margin:0}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{padding:"8px 16px",fontSize:t.typography.pxToRem(14),lineHeight:"".concat(Math.round(16/14*1e5)/1e5,"em"),fontWeight:t.typography.fontWeightRegular}},{props:e=>{let{ownerState:t}=e;return!t.isRtl},style:{[".".concat(e$.popper,'[data-popper-placement*="left"] &')]:{marginRight:"14px"},[".".concat(e$.popper,'[data-popper-placement*="right"] &')]:{marginLeft:"14px"}}},{props:e=>{let{ownerState:t}=e;return!t.isRtl&&t.touch},style:{[".".concat(e$.popper,'[data-popper-placement*="left"] &')]:{marginRight:"24px"},[".".concat(e$.popper,'[data-popper-placement*="right"] &')]:{marginLeft:"24px"}}},{props:e=>{let{ownerState:t}=e;return!!t.isRtl},style:{[".".concat(e$.popper,'[data-popper-placement*="left"] &')]:{marginLeft:"14px"},[".".concat(e$.popper,'[data-popper-placement*="right"] &')]:{marginRight:"14px"}}},{props:e=>{let{ownerState:t}=e;return!!t.isRtl&&t.touch},style:{[".".concat(e$.popper,'[data-popper-placement*="left"] &')]:{marginLeft:"24px"},[".".concat(e$.popper,'[data-popper-placement*="right"] &')]:{marginRight:"24px"}}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{[".".concat(e$.popper,'[data-popper-placement*="top"] &')]:{marginBottom:"24px"}}},{props:e=>{let{ownerState:t}=e;return t.touch},style:{[".".concat(e$.popper,'[data-popper-placement*="bottom"] &')]:{marginTop:"24px"}}}]}})),eU=(0,d.Ay)("span",{name:"MuiTooltip",slot:"Arrow",overridesResolver:(e,t)=>t.arrow})((0,f.A)(e=>{let{theme:t}=e;return{overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:t.vars?t.vars.palette.Tooltip.bg:(0,a.X4)(t.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}}})),eH=!1,eq=new i.E,eV={x:0,y:0};function eX(e,t){return function(r){for(var n=arguments.length,o=Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];t&&t(r,...o),e(r,...o)}}let eK=n.forwardRef(function(e,t){var r,a,d;let f=(0,h.b)({props:e,name:"MuiTooltip"}),{arrow:y=!1,children:v,classes:b,components:A={},componentsProps:w={},describeChild:x=!1,disableFocusListener:S=!1,disableHoverListener:E=!1,disableInteractive:_=!1,disableTouchListener:C=!1,enterDelay:R=100,enterNextDelay:O=0,enterTouchDelay:k=700,followCursor:I=!1,id:T,leaveDelay:M=0,leaveTouchDelay:P=1500,onClose:j,onOpen:N,open:L,placement:B="bottom",PopperComponent:F,PopperProps:D={},slotProps:$={},slots:z={},title:W,TransitionComponent:U,TransitionProps:H,...q}=f,V=n.isValidElement(v)?v:(0,eR.jsx)("span",{children:v}),X=(0,p.A)(),K=(0,l.I)(),[G,Y]=n.useState(),[J,Q]=n.useState(null),Z=n.useRef(!1),ee=_||I,et=(0,i.A)(),er=(0,i.A)(),en=(0,i.A)(),eo=(0,i.A)(),[ei,es]=(0,eB.A)({controlled:L,default:!1,name:"Tooltip",state:"open"}),ea=ei,el=(0,eL.A)(T),ec=n.useRef(),eu=(0,ej.A)(()=>{void 0!==ec.current&&(document.body.style.WebkitUserSelect=ec.current,ec.current=void 0),eo.clear()});n.useEffect(()=>eu,[eu]);let ed=e=>{eq.clear(),eH=!0,es(!0),N&&!ea&&N(e)},ep=(0,ej.A)(e=>{eq.start(800+M,()=>{eH=!1}),es(!1),j&&ea&&j(e),et.start(X.transitions.duration.shortest,()=>{Z.current=!1})}),ef=e=>{Z.current&&"touchstart"!==e.type||(G&&G.removeAttribute("title"),er.clear(),en.clear(),R||eH&&O?er.start(eH?O:R,()=>{ed(e)}):ed(e))},eh=e=>{er.clear(),en.start(M,()=>{ep(e)})},[,em]=n.useState(!1),eg=e=>{(0,c.A)(e.target)||(em(!1),eh(e))},ey=e=>{G||Y(e.currentTarget),(0,c.A)(e.target)&&(em(!0),ef(e))},ev=e=>{Z.current=!0;let t=V.props;t.onTouchStart&&t.onTouchStart(e)};n.useEffect(()=>{if(ea)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){"Escape"===e.key&&ep(e)}},[ep,ea]);let eb=(0,eN.A)((0,u.A)(V),Y,t);W||0===W||(ea=!1);let eA=n.useRef(),ew={},ex="string"==typeof W;x?(ew.title=ea||!ex||E?null:W,ew["aria-describedby"]=ea?el:null):(ew["aria-label"]=ex?W:null,ew["aria-labelledby"]=ea&&!ex?el:null);let eS={...ew,...q,...V.props,className:(0,o.A)(q.className,V.props.className),onTouchStart:ev,ref:eb,...I?{onMouseMove:e=>{let t=V.props;t.onMouseMove&&t.onMouseMove(e),eV={x:e.clientX,y:e.clientY},eA.current&&eA.current.update()}}:{}},eE={};C||(eS.onTouchStart=e=>{ev(e),en.clear(),et.clear(),eu(),ec.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",eo.start(k,()=>{document.body.style.WebkitUserSelect=ec.current,ef(e)})},eS.onTouchEnd=e=>{V.props.onTouchEnd&&V.props.onTouchEnd(e),eu(),en.start(P,()=>{ep(e)})}),!E&&(eS.onMouseOver=eX(ef,eS.onMouseOver),eS.onMouseLeave=eX(eh,eS.onMouseLeave),ee||(eE.onMouseOver=ef,eE.onMouseLeave=eh)),!S&&(eS.onFocus=eX(ey,eS.onFocus),eS.onBlur=eX(eg,eS.onBlur),ee||(eE.onFocus=ey,eE.onBlur=eg));let e_={...f,isRtl:K,arrow:y,disableInteractive:ee,placement:B,PopperComponentProp:F,touch:Z.current},eC="function"==typeof $.popper?$.popper(e_):$.popper,eO=n.useMemo(()=>{var e,t;let r=[{name:"arrow",enabled:!!J,options:{element:J,padding:4}}];return(null==(e=D.popperOptions)?void 0:e.modifiers)&&(r=r.concat(D.popperOptions.modifiers)),(null==eC||null==(t=eC.popperOptions)?void 0:t.modifiers)&&(r=r.concat(eC.popperOptions.modifiers)),{...D.popperOptions,...null==eC?void 0:eC.popperOptions,modifiers:r}},[J,D.popperOptions,null==eC?void 0:eC.popperOptions]),ek=(e=>{let{classes:t,disableInteractive:r,arrow:n,touch:o,placement:i}=e,a={popper:["popper",!r&&"popperInteractive",n&&"popperArrow"],tooltip:["tooltip",n&&"tooltipArrow",o&&"touch","tooltipPlacement".concat((0,m.A)(i.split("-")[0]))],arrow:["arrow"]};return(0,s.A)(a,eD,t)})(e_),eI="function"==typeof $.transition?$.transition(e_):$.transition,eT={slots:{popper:A.Popper,transition:null!=(r=A.Transition)?r:U,tooltip:A.Tooltip,arrow:A.Arrow,...z},slotProps:{arrow:null!=(a=$.arrow)?a:w.arrow,popper:{...D,...null!=eC?eC:w.popper},tooltip:null!=(d=$.tooltip)?d:w.tooltip,transition:{...H,...null!=eI?eI:w.transition}}},[eM,e$]=(0,eF.A)("popper",{elementType:ez,externalForwardedProps:eT,ownerState:e_,className:(0,o.A)(ek.popper,null==D?void 0:D.className)}),[eK,eG]=(0,eF.A)("transition",{elementType:g.A,externalForwardedProps:eT,ownerState:e_}),[eY,eJ]=(0,eF.A)("tooltip",{elementType:eW,className:ek.tooltip,externalForwardedProps:eT,ownerState:e_}),[eQ,eZ]=(0,eF.A)("arrow",{elementType:eU,className:ek.arrow,externalForwardedProps:eT,ownerState:e_,ref:Q});return(0,eR.jsxs)(n.Fragment,{children:[n.cloneElement(V,eS),(0,eR.jsx)(eM,{as:null!=F?F:eP,placement:B,anchorEl:I?{getBoundingClientRect:()=>({top:eV.y,left:eV.x,right:eV.x,bottom:eV.y,width:0,height:0})}:G,popperRef:eA,open:!!G&&ea,id:el,transition:!0,...eE,...e$,popperOptions:eO,children:e=>{let{TransitionProps:t}=e;return(0,eR.jsx)(eK,{timeout:X.transitions.duration.shorter,...t,...eG,children:(0,eR.jsxs)(eY,{...eJ,children:[W,y?(0,eR.jsx)(eQ,{...eZ}):null]})})}})]})})},81104:(e,t,r)=>{"use strict";r.d(t,{A:()=>S});var n=r(14232),o=r(69241),i=r(4697),s=r(126),a=r(80027),l=r(78457),c=r(26872),u=r(54773),d=r(82987),p=r(52196),f=r(47951),h=r(45879);function m(e){return(0,h.Ay)("MuiSwitch",e)}let g=(0,f.A)("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]);var y=r(67360),v=r(37876);let b=(0,u.Ay)("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.edge&&t["edge".concat((0,a.A)(r.edge))],t["size".concat((0,a.A)(r.size))]]}})({display:"inline-flex",width:58,height:38,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,["& .".concat(g.thumb)]:{width:16,height:16},["& .".concat(g.switchBase)]:{padding:4,["&.".concat(g.checked)]:{transform:"translateX(16px)"}}}}]}),A=(0,u.Ay)(c.A,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.switchBase,{["& .".concat(g.input)]:t.input},"default"!==r.color&&t["color".concat((0,a.A)(r.color))]]}})((0,d.A)(e=>{let{theme:t}=e;return{position:"absolute",top:0,left:0,zIndex:1,color:t.vars?t.vars.palette.Switch.defaultColor:"".concat("light"===t.palette.mode?t.palette.common.white:t.palette.grey[300]),transition:t.transitions.create(["left","transform"],{duration:t.transitions.duration.shortest}),["&.".concat(g.checked)]:{transform:"translateX(20px)"},["&.".concat(g.disabled)]:{color:t.vars?t.vars.palette.Switch.defaultDisabledColor:"".concat("light"===t.palette.mode?t.palette.grey[100]:t.palette.grey[600])},["&.".concat(g.checked," + .").concat(g.track)]:{opacity:.5},["&.".concat(g.disabled," + .").concat(g.track)]:{opacity:t.vars?t.vars.opacity.switchTrackDisabled:"".concat("light"===t.palette.mode?.12:.2)},["& .".concat(g.input)]:{left:"-100%",width:"300%"}}}),(0,d.A)(e=>{let{theme:t}=e;return{"&:hover":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.action.activeChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):(0,s.X4)(t.palette.action.active,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(t.palette).filter((0,l.A)(["light"])).map(e=>{let[r]=e;return{props:{color:r},style:{["&.".concat(g.checked)]:{color:(t.vars||t).palette[r].main,"&:hover":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette[r].mainChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):(0,s.X4)(t.palette[r].main,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},["&.".concat(g.disabled)]:{color:t.vars?t.vars.palette.Switch["".concat(r,"DisabledColor")]:"".concat("light"===t.palette.mode?(0,s.a)(t.palette[r].main,.62):(0,s.e$)(t.palette[r].main,.55))}},["&.".concat(g.checked," + .").concat(g.track)]:{backgroundColor:(t.vars||t).palette[r].main}}}})]}})),w=(0,u.Ay)("span",{name:"MuiSwitch",slot:"Track",overridesResolver:(e,t)=>t.track})((0,d.A)(e=>{let{theme:t}=e;return{height:"100%",width:"100%",borderRadius:7,zIndex:-1,transition:t.transitions.create(["opacity","background-color"],{duration:t.transitions.duration.shortest}),backgroundColor:t.vars?t.vars.palette.common.onBackground:"".concat("light"===t.palette.mode?t.palette.common.black:t.palette.common.white),opacity:t.vars?t.vars.opacity.switchTrack:"".concat("light"===t.palette.mode?.38:.3)}})),x=(0,u.Ay)("span",{name:"MuiSwitch",slot:"Thumb",overridesResolver:(e,t)=>t.thumb})((0,d.A)(e=>{let{theme:t}=e;return{boxShadow:(t.vars||t).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}})),S=n.forwardRef(function(e,t){let r=(0,p.b)({props:e,name:"MuiSwitch"}),{className:n,color:s="primary",edge:l=!1,size:c="medium",sx:u,slots:d={},slotProps:f={},...h}=r,g={...r,color:s,edge:l,size:c},S=(e=>{let{classes:t,edge:r,size:n,color:o,checked:s,disabled:l}=e,c={root:["root",r&&"edge".concat((0,a.A)(r)),"size".concat((0,a.A)(n))],switchBase:["switchBase","color".concat((0,a.A)(o)),s&&"checked",l&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},u=(0,i.A)(c,m,t);return{...t,...u}})(g),E={slots:d,slotProps:f},[_,C]=(0,y.A)("root",{className:(0,o.A)(S.root,n),elementType:b,externalForwardedProps:E,ownerState:g,additionalProps:{sx:u}}),[R,O]=(0,y.A)("thumb",{className:S.thumb,elementType:x,externalForwardedProps:E,ownerState:g}),k=(0,v.jsx)(R,{...O}),[I,T]=(0,y.A)("track",{className:S.track,elementType:w,externalForwardedProps:E,ownerState:g});return(0,v.jsxs)(_,{...C,children:[(0,v.jsx)(A,{type:"checkbox",icon:k,checkedIcon:k,ref:t,ownerState:g,...h,classes:{...S,root:S.switchBase},slots:{...d.switchBase&&{root:d.switchBase},...d.input&&{input:d.input}},slotProps:{...f.switchBase&&{root:"function"==typeof f.switchBase?f.switchBase(g):f.switchBase},...f.input&&{input:"function"==typeof f.input?f.input(g):f.input}}}),(0,v.jsx)(I,{...T})]})})},82260:(e,t,r)=>{"use strict";r.d(t,{A:()=>k});var n=r(14232),o=r(69241),i=r(4697),s=r(126),a=r(26872),l=r(31057),c=r(37876);let u=(0,l.A)((0,c.jsx)("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"}),"CheckBoxOutlineBlank"),d=(0,l.A)((0,c.jsx)("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}),"CheckBox"),p=(0,l.A)((0,c.jsx)("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}),"IndeterminateCheckBox");var f=r(80027),h=r(68275),m=r(47951),g=r(45879);function y(e){return(0,g.Ay)("MuiCheckbox",e)}let v=(0,m.A)("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]);var b=r(54773),A=r(82987),w=r(78457),x=r(52196),S=r(45867),E=r(67360);let _=(0,b.Ay)(a.A,{shouldForwardProp:e=>(0,h.A)(e)||"classes"===e,name:"MuiCheckbox",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,r.indeterminate&&t.indeterminate,t["size".concat((0,f.A)(r.size))],"default"!==r.color&&t["color".concat((0,f.A)(r.color))]]}})((0,A.A)(e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.action.activeChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):(0,s.X4)(t.palette.action.active,t.palette.action.hoverOpacity)}}},...Object.entries(t.palette).filter((0,w.A)()).map(e=>{let[r]=e;return{props:{color:r,disableRipple:!1},style:{"&:hover":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette[r].mainChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):(0,s.X4)(t.palette[r].main,t.palette.action.hoverOpacity)}}}}),...Object.entries(t.palette).filter((0,w.A)()).map(e=>{let[r]=e;return{props:{color:r},style:{["&.".concat(v.checked,", &.").concat(v.indeterminate)]:{color:(t.vars||t).palette[r].main},["&.".concat(v.disabled)]:{color:(t.vars||t).palette.action.disabled}}}}),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}})),C=(0,c.jsx)(d,{}),R=(0,c.jsx)(u,{}),O=(0,c.jsx)(p,{}),k=n.forwardRef(function(e,t){var r,s,a;let l=(0,x.b)({props:e,name:"MuiCheckbox"}),{checkedIcon:u=C,color:d="primary",icon:p=R,indeterminate:h=!1,indeterminateIcon:m=O,inputProps:g,size:v="medium",disableRipple:b=!1,className:A,slots:w={},slotProps:k={},...I}=l,T=h?m:p,M=h?m:u,P={...l,disableRipple:b,color:d,indeterminate:h,size:v},j=(e=>{let{classes:t,indeterminate:r,color:n,size:o}=e,s={root:["root",r&&"indeterminate","color".concat((0,f.A)(n)),"size".concat((0,f.A)(o))]},a=(0,i.A)(s,y,t);return{...t,...a}})(P),N=null!=(r=k.input)?r:g,[L,B]=(0,E.A)("root",{ref:t,elementType:_,className:(0,o.A)(j.root,A),shouldForwardComponentProp:!0,externalForwardedProps:{slots:w,slotProps:k,...I},ownerState:P,additionalProps:{type:"checkbox",icon:n.cloneElement(T,{fontSize:null!=(s=T.props.fontSize)?s:v}),checkedIcon:n.cloneElement(M,{fontSize:null!=(a=M.props.fontSize)?a:v}),disableRipple:b,slots:w,slotProps:{input:(0,S.A)("function"==typeof N?N(P):N,{"data-indeterminate":h})}}});return(0,c.jsx)(L,{...B,classes:j})})},84147:(e,t,r)=>{"use strict";r.d(t,{A:()=>R});var n=r(14232),o=r(69241),i=r(4697),s=r(53855),a=r(80027),l=r(9050),c=r(36540),u=r(11951),d=r(47951),p=r(45879);function f(e){return(0,p.Ay)("MuiDialog",e)}let h=(0,d.A)("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]);var m=r(199),g=r(30929),y=r(54773),v=r(30566),b=r(82987),A=r(52196),w=r(67360),x=r(37876);let S=(0,y.Ay)(g.A,{name:"MuiDialog",slot:"Backdrop",overrides:(e,t)=>t.backdrop})({zIndex:-1}),E=(0,y.Ay)(l.A,{name:"MuiDialog",slot:"Root",overridesResolver:(e,t)=>t.root})({"@media print":{position:"absolute !important"}}),_=(0,y.Ay)("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.container,t["scroll".concat((0,a.A)(r.scroll))]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),C=(0,y.Ay)(u.A,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.paper,t["scrollPaper".concat((0,a.A)(r.scroll))],t["paperWidth".concat((0,a.A)(String(r.maxWidth)))],r.fullWidth&&t.paperFullWidth,r.fullScreen&&t.paperFullScreen]}})((0,b.A)(e=>{let{theme:t}=e;return{margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:e=>{let{ownerState:t}=e;return!t.maxWidth},style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:"px"===t.breakpoints.unit?Math.max(t.breakpoints.values.xs,444):"max(".concat(t.breakpoints.values.xs).concat(t.breakpoints.unit,", 444px)"),["&.".concat(h.paperScrollBody)]:{[t.breakpoints.down(Math.max(t.breakpoints.values.xs,444)+64)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(t.breakpoints.values).filter(e=>"xs"!==e).map(e=>({props:{maxWidth:e},style:{maxWidth:"".concat(t.breakpoints.values[e]).concat(t.breakpoints.unit),["&.".concat(h.paperScrollBody)]:{[t.breakpoints.down(t.breakpoints.values[e]+64)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:e=>{let{ownerState:t}=e;return t.fullWidth},style:{width:"calc(100% - 64px)"}},{props:e=>{let{ownerState:t}=e;return t.fullScreen},style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,["&.".concat(h.paperScrollBody)]:{margin:0,maxWidth:"100%"}}}]}})),R=n.forwardRef(function(e,t){let r=(0,A.b)({props:e,name:"MuiDialog"}),l=(0,v.A)(),d={enter:l.transitions.duration.enteringScreen,exit:l.transitions.duration.leavingScreen},{"aria-describedby":p,"aria-labelledby":h,"aria-modal":g=!0,BackdropComponent:y,BackdropProps:b,children:R,className:O,disableEscapeKeyDown:k=!1,fullScreen:I=!1,fullWidth:T=!1,maxWidth:M="sm",onBackdropClick:P,onClick:j,onClose:N,open:L,PaperComponent:B=u.A,PaperProps:F={},scroll:D="paper",slots:$={},slotProps:z={},TransitionComponent:W=c.A,transitionDuration:U=d,TransitionProps:H,...q}=r,V={...r,disableEscapeKeyDown:k,fullScreen:I,fullWidth:T,maxWidth:M,scroll:D},X=(e=>{let{classes:t,scroll:r,maxWidth:n,fullWidth:o,fullScreen:s}=e,l={root:["root"],container:["container","scroll".concat((0,a.A)(r))],paper:["paper","paperScroll".concat((0,a.A)(r)),"paperWidth".concat((0,a.A)(String(n))),o&&"paperFullWidth",s&&"paperFullScreen"]};return(0,i.A)(l,f,t)})(V),K=n.useRef(),G=(0,s.A)(h),Y=n.useMemo(()=>({titleId:G}),[G]),J={slots:{transition:W,...$},slotProps:{transition:H,paper:F,backdrop:b,...z}},[Q,Z]=(0,w.A)("root",{elementType:E,shouldForwardComponentProp:!0,externalForwardedProps:J,ownerState:V,className:(0,o.A)(X.root,O),ref:t}),[ee,et]=(0,w.A)("backdrop",{elementType:S,shouldForwardComponentProp:!0,externalForwardedProps:J,ownerState:V}),[er,en]=(0,w.A)("paper",{elementType:C,shouldForwardComponentProp:!0,externalForwardedProps:J,ownerState:V,className:(0,o.A)(X.paper,F.className)}),[eo,ei]=(0,w.A)("container",{elementType:_,externalForwardedProps:J,ownerState:V,className:(0,o.A)(X.container)}),[es,ea]=(0,w.A)("transition",{elementType:c.A,externalForwardedProps:J,ownerState:V,additionalProps:{appear:!0,in:L,timeout:U,role:"presentation"}});return(0,x.jsx)(Q,{closeAfterTransition:!0,slots:{backdrop:ee},slotProps:{backdrop:{transitionDuration:U,as:y,...et}},disableEscapeKeyDown:k,onClose:N,open:L,onClick:e=>{j&&j(e),K.current&&(K.current=null,P&&P(e),N&&N(e,"backdropClick"))},...Z,...q,children:(0,x.jsx)(es,{...ea,children:(0,x.jsx)(eo,{onMouseDown:e=>{K.current=e.target===e.currentTarget},...ei,children:(0,x.jsx)(er,{as:B,elevation:24,role:"dialog","aria-describedby":p,"aria-labelledby":G,"aria-modal":g,...en,children:(0,x.jsx)(m.A.Provider,{value:Y,children:R})})})})})})},86863:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(61637),o=r(26008),i=r(43903),s=r(60255);let a=function(e){let{elementType:t,externalSlotProps:r,ownerState:a,skipResolvingSlotProps:l=!1,...c}=e,u=l?{}:(0,s.A)(r,a),{props:d,internalRef:p}=(0,i.A)({...c,externalSlotProps:u}),f=(0,n.A)(p,u?.ref,e.additionalProps?.ref);return(0,o.A)(t,{...d,ref:f},a)}},87919:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8"}),"AddCircleOutline")},88220:e=>{!function(){var t={675:function(e,t){"use strict";t.byteLength=function(e){var t=l(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,i=l(e),s=i[0],a=i[1],c=new o((s+a)*3/4-a),u=0,d=a>0?s-4:s;for(r=0;r<d;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],c[u++]=t>>16&255,c[u++]=t>>8&255,c[u++]=255&t;return 2===a&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,c[u++]=255&t),1===a&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,c[u++]=t>>8&255,c[u++]=255&t),c},t.fromByteArray=function(e){for(var t,n=e.length,o=n%3,i=[],s=0,a=n-o;s<a;s+=16383)i.push(function(e,t,n){for(var o,i=[],s=t;s<n;s+=3)o=(e[s]<<16&0xff0000)+(e[s+1]<<8&65280)+(255&e[s+2]),i.push(r[o>>18&63]+r[o>>12&63]+r[o>>6&63]+r[63&o]);return i.join("")}(e,s,s+16383>a?a:s+16383));return 1===o?i.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===o&&i.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),i.join("")};for(var r=[],n=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,a=i.length;s<a;++s)r[s]=i[s],n[i.charCodeAt(s)]=s;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(e,t,r){"use strict";var n=r(675),o=r(783),i="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,a.prototype),t}function a(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return u(e)}return l(e,t,r)}function l(e,t,r){if("string"==typeof e){var n=e,o=t;if(("string"!=typeof o||""===o)&&(o="utf8"),!a.isEncoding(o))throw TypeError("Unknown encoding: "+o);var i=0|f(n,o),l=s(i),c=l.write(n,o);return c!==i&&(l=l.slice(0,c)),l}if(ArrayBuffer.isView(e))return d(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(k(e,ArrayBuffer)||e&&k(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(k(e,SharedArrayBuffer)||e&&k(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),a.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var u=e.valueOf&&e.valueOf();if(null!=u&&u!==e)return a.from(u,t,r);var h=function(e){if(a.isBuffer(e)){var t=0|p(e.length),r=s(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?s(0):d(e):"Buffer"===e.type&&Array.isArray(e.data)?d(e.data):void 0}(e);if(h)return h;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return a.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function c(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function u(e){return c(e),s(e<0?0:0|p(e))}function d(e){for(var t=e.length<0?0:0|p(e.length),r=s(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}t.Buffer=a,t.SlowBuffer=function(e){return+e!=e&&(e=0),a.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=0x7fffffff,a.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(e,t,r){return l(e,t,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(e,t,r){return(c(e),e<=0)?s(e):void 0!==t?"string"==typeof r?s(e).fill(t,r):s(e).fill(t):s(e)},a.allocUnsafe=function(e){return u(e)},a.allocUnsafeSlow=function(e){return u(e)};function p(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function f(e,t){if(a.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||k(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var o=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return _(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return R(e).length;default:if(o)return n?-1:_(e).length;t=(""+t).toLowerCase(),o=!0}}function h(e,t,r){var o,i,s,a=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var o="",i=t;i<r;++i)o+=I[e[i]];return o}(this,t,r);case"utf8":case"utf-8":return v(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var o=t;o<r;++o)n+=String.fromCharCode(127&e[o]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var o=t;o<r;++o)n+=String.fromCharCode(e[o]);return n}(this,t,r);case"base64":return o=this,i=t,s=r,0===i&&s===o.length?n.fromByteArray(o):n.fromByteArray(o.slice(i,s));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),o="",i=0;i<n.length;i+=2)o+=String.fromCharCode(n[i]+256*n[i+1]);return o}(this,t,r);default:if(a)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),a=!0}}function m(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function g(e,t,r,n,o){var i;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(i=r*=1)!=i&&(r=o?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(o)return -1;else r=e.length-1;else if(r<0)if(!o)return -1;else r=0;if("string"==typeof t&&(t=a.from(t,n)),a.isBuffer(t))return 0===t.length?-1:y(e,t,r,n,o);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(o)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return y(e,[t],r,n,o)}throw TypeError("val must be string, number or Buffer")}function y(e,t,r,n,o){var i,s=1,a=e.length,l=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;s=2,a/=2,l/=2,r/=2}function c(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(o){var u=-1;for(i=r;i<a;i++)if(c(e,i)===c(t,-1===u?0:i-u)){if(-1===u&&(u=i),i-u+1===l)return u*s}else -1!==u&&(i-=i-u),u=-1}else for(r+l>a&&(r=a-l),i=r;i>=0;i--){for(var d=!0,p=0;p<l;p++)if(c(e,i+p)!==c(t,p)){d=!1;break}if(d)return i}return -1}a.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==a.prototype},a.compare=function(e,t){if(k(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),k(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(e)||!a.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,o=0,i=Math.min(r,n);o<i;++o)if(e[o]!==t[o]){r=e[o],n=t[o];break}return r<n?-1:+(n<r)},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=a.allocUnsafe(t),o=0;for(r=0;r<e.length;++r){var i=e[r];if(k(i,Uint8Array)&&(i=a.from(i)),!a.isBuffer(i))throw TypeError('"list" argument must be an Array of Buffers');i.copy(n,o),o+=i.length}return n},a.byteLength=f,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)m(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},a.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?v(this,0,e):h.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(e){if(!a.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},i&&(a.prototype[i]=a.prototype.inspect),a.prototype.compare=function(e,t,r,n,o){if(k(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===o&&(o=this.length),t<0||r>e.length||n<0||o>this.length)throw RangeError("out of range index");if(n>=o&&t>=r)return 0;if(n>=o)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,o>>>=0,this===e)return 0;for(var i=o-n,s=r-t,l=Math.min(i,s),c=this.slice(n,o),u=e.slice(t,r),d=0;d<l;++d)if(c[d]!==u[d]){i=c[d],s=u[d];break}return i<s?-1:+(s<i)},a.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},a.prototype.indexOf=function(e,t,r){return g(this,e,t,r,!0)},a.prototype.lastIndexOf=function(e,t,r){return g(this,e,t,r,!1)};function v(e,t,r){r=Math.min(e.length,r);for(var n=[],o=t;o<r;){var i,s,a,l,c=e[o],u=null,d=c>239?4:c>223?3:c>191?2:1;if(o+d<=r)switch(d){case 1:c<128&&(u=c);break;case 2:(192&(i=e[o+1]))==128&&(l=(31&c)<<6|63&i)>127&&(u=l);break;case 3:i=e[o+1],s=e[o+2],(192&i)==128&&(192&s)==128&&(l=(15&c)<<12|(63&i)<<6|63&s)>2047&&(l<55296||l>57343)&&(u=l);break;case 4:i=e[o+1],s=e[o+2],a=e[o+3],(192&i)==128&&(192&s)==128&&(192&a)==128&&(l=(15&c)<<18|(63&i)<<12|(63&s)<<6|63&a)>65535&&l<1114112&&(u=l)}null===u?(u=65533,d=1):u>65535&&(u-=65536,n.push(u>>>10&1023|55296),u=56320|1023&u),n.push(u),o+=d}var p=n,f=p.length;if(f<=4096)return String.fromCharCode.apply(String,p);for(var h="",m=0;m<f;)h+=String.fromCharCode.apply(String,p.slice(m,m+=4096));return h}function b(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function A(e,t,r,n,o,i){if(!a.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>o||t<i)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function w(e,t,r,n,o,i){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function x(e,t,r,n,i){return t*=1,r>>>=0,i||w(e,t,r,4,34028234663852886e22,-34028234663852886e22),o.write(e,t,r,n,23,4),r+4}function S(e,t,r,n,i){return t*=1,r>>>=0,i||w(e,t,r,8,17976931348623157e292,-17976931348623157e292),o.write(e,t,r,n,52,8),r+8}a.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var o,i,s,a,l,c,u,d,p=this.length-t;if((void 0===r||r>p)&&(r=p),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var f=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var o=e.length-r;n?(n=Number(n))>o&&(n=o):n=o;var i=t.length;n>i/2&&(n=i/2);for(var s=0;s<n;++s){var a,l=parseInt(t.substr(2*s,2),16);if((a=l)!=a)break;e[r+s]=l}return s}(this,e,t,r);case"utf8":case"utf-8":return o=t,i=r,O(_(e,this.length-o),this,o,i);case"ascii":return s=t,a=r,O(C(e),this,s,a);case"latin1":case"binary":return function(e,t,r,n){return O(C(t),e,r,n)}(this,e,t,r);case"base64":return l=t,c=r,O(R(e),this,l,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return u=t,d=r,O(function(e,t){for(var r,n,o=[],i=0;i<e.length&&!((t-=2)<0);++i)n=(r=e.charCodeAt(i))>>8,o.push(r%256),o.push(n);return o}(e,this.length-u),this,u,d);default:if(f)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),f=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],o=1,i=0;++i<t&&(o*=256);)n+=this[e+i]*o;return n},a.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e+--t],o=1;t>0&&(o*=256);)n+=this[e+--t]*o;return n},a.prototype.readUInt8=function(e,t){return e>>>=0,t||b(e,1,this.length),this[e]},a.prototype.readUInt16LE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUInt16BE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},a.prototype.readUInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],o=1,i=0;++i<t&&(o*=256);)n+=this[e+i]*o;return n>=(o*=128)&&(n-=Math.pow(2,8*t)),n},a.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=t,o=1,i=this[e+--n];n>0&&(o*=256);)i+=this[e+--n]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*t)),i},a.prototype.readInt8=function(e,t){return(e>>>=0,t||b(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},a.prototype.readInt16LE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return e>>>=0,t||b(e,4,this.length),o.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return e>>>=0,t||b(e,4,this.length),o.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return e>>>=0,t||b(e,8,this.length),o.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return e>>>=0,t||b(e,8,this.length),o.read(this,e,!1,52,8)},a.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var o=Math.pow(2,8*r)-1;A(this,e,t,r,o,0)}var i=1,s=0;for(this[t]=255&e;++s<r&&(i*=256);)this[t+s]=e/i&255;return t+r},a.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var o=Math.pow(2,8*r)-1;A(this,e,t,r,o,0)}var i=r-1,s=1;for(this[t+i]=255&e;--i>=0&&(s*=256);)this[t+i]=e/s&255;return t+r},a.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||A(this,e,t,1,255,0),this[t]=255&e,t+1},a.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||A(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||A(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||A(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},a.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||A(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var o=Math.pow(2,8*r-1);A(this,e,t,r,o-1,-o)}var i=0,s=1,a=0;for(this[t]=255&e;++i<r&&(s*=256);)e<0&&0===a&&0!==this[t+i-1]&&(a=1),this[t+i]=(e/s|0)-a&255;return t+r},a.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var o=Math.pow(2,8*r-1);A(this,e,t,r,o-1,-o)}var i=r-1,s=1,a=0;for(this[t+i]=255&e;--i>=0&&(s*=256);)e<0&&0===a&&0!==this[t+i+1]&&(a=1),this[t+i]=(e/s|0)-a&255;return t+r},a.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||A(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||A(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||A(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||A(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},a.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||A(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeFloatLE=function(e,t,r){return x(this,e,t,!0,r)},a.prototype.writeFloatBE=function(e,t,r){return x(this,e,t,!1,r)},a.prototype.writeDoubleLE=function(e,t,r){return S(this,e,t,!0,r)},a.prototype.writeDoubleBE=function(e,t,r){return S(this,e,t,!1,r)},a.prototype.copy=function(e,t,r,n){if(!a.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var o=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var i=o-1;i>=0;--i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return o},a.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var o,i=e.charCodeAt(0);("utf8"===n&&i<128||"latin1"===n)&&(e=i)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var s=a.isBuffer(e)?e:a.from(e,n),l=s.length;if(0===l)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(o=0;o<r-t;++o)this[o+t]=s[o%l]}return this};var E=/[^+/0-9A-Za-z-_]/g;function _(e,t){t=t||1/0;for(var r,n=e.length,o=null,i=[],s=0;s<n;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!o){if(r>56319||s+1===n){(t-=3)>-1&&i.push(239,191,189);continue}o=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),o=r;continue}r=(o-55296<<10|r-56320)+65536}else o&&(t-=3)>-1&&i.push(239,191,189);if(o=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return i}function C(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function R(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(E,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function O(e,t,r,n){for(var o=0;o<n&&!(o+r>=t.length)&&!(o>=e.length);++o)t[o+r]=e[o];return o}function k(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var I=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,o=0;o<16;++o)t[n+o]=e[r]+e[o];return t}()},783:function(e,t){t.read=function(e,t,r,n,o){var i,s,a=8*o-n-1,l=(1<<a)-1,c=l>>1,u=-7,d=r?o-1:0,p=r?-1:1,f=e[t+d];for(d+=p,i=f&(1<<-u)-1,f>>=-u,u+=a;u>0;i=256*i+e[t+d],d+=p,u-=8);for(s=i&(1<<-u)-1,i>>=-u,u+=n;u>0;s=256*s+e[t+d],d+=p,u-=8);if(0===i)i=1-c;else{if(i===l)return s?NaN:1/0*(f?-1:1);s+=Math.pow(2,n),i-=c}return(f?-1:1)*s*Math.pow(2,i-n)},t.write=function(e,t,r,n,o,i){var s,a,l,c=8*i-o-1,u=(1<<c)-1,d=u>>1,p=5960464477539062e-23*(23===o),f=n?0:i-1,h=n?1:-1,m=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(a=+!!isNaN(t),s=u):(s=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-s))<1&&(s--,l*=2),s+d>=1?t+=p/l:t+=p*Math.pow(2,1-d),t*l>=2&&(s++,l/=2),s+d>=u?(a=0,s=u):s+d>=1?(a=(t*l-1)*Math.pow(2,o),s+=d):(a=t*Math.pow(2,d-1)*Math.pow(2,o),s=0));o>=8;e[r+f]=255&a,f+=h,a/=256,o-=8);for(s=s<<o|a,c+=o;c>0;e[r+f]=255&s,f+=h,s/=256,c-=8);e[r+f-h]|=128*m}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var i=r[e]={exports:{}},s=!0;try{t[e](i,i.exports,n),s=!1}finally{s&&delete r[e]}return i.exports}n.ab="//",e.exports=n(72)}()},88713:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var n=r(14232),o=r(69241),i=r(4697),s=r(54773),a=r(52196),l=r(49790),c=r(80027),u=r(65740),d=r(55509),p=r(47951),f=r(45879);function h(e){return(0,f.Ay)("MuiFormControl",e)}(0,p.A)("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);var m=r(37876);let g=(0,s.Ay)("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t["margin".concat((0,c.A)(r.margin))],r.fullWidth&&t.fullWidth]}})({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top",variants:[{props:{margin:"normal"},style:{marginTop:16,marginBottom:8}},{props:{margin:"dense"},style:{marginTop:8,marginBottom:4}},{props:{fullWidth:!0},style:{width:"100%"}}]}),y=n.forwardRef(function(e,t){let r,s=(0,a.b)({props:e,name:"MuiFormControl"}),{children:p,className:f,color:y="primary",component:v="div",disabled:b=!1,error:A=!1,focused:w,fullWidth:x=!1,hiddenLabel:S=!1,margin:E="none",required:_=!1,size:C="medium",variant:R="outlined",...O}=s,k={...s,color:y,component:v,disabled:b,error:A,fullWidth:x,hiddenLabel:S,margin:E,required:_,size:C,variant:R},I=(e=>{let{classes:t,margin:r,fullWidth:n}=e,o={root:["root","none"!==r&&"margin".concat((0,c.A)(r)),n&&"fullWidth"]};return(0,i.A)(o,h,t)})(k),[T,M]=n.useState(()=>{let e=!1;return p&&n.Children.forEach(p,t=>{if(!(0,u.A)(t,["Input","Select"]))return;let r=(0,u.A)(t,["Select"])?t.props.input:t;r&&(0,l.gr)(r.props)&&(e=!0)}),e}),[P,j]=n.useState(()=>{let e=!1;return p&&n.Children.forEach(p,t=>{(0,u.A)(t,["Input","Select"])&&((0,l.lq)(t.props,!0)||(0,l.lq)(t.props.inputProps,!0))&&(e=!0)}),e}),[N,L]=n.useState(!1);b&&N&&L(!1);let B=void 0===w||b?N:w;n.useRef(!1);let F=n.useCallback(()=>{j(!0)},[]),D=n.useCallback(()=>{j(!1)},[]),$=n.useMemo(()=>({adornedStart:T,setAdornedStart:M,color:y,disabled:b,error:A,filled:P,focused:B,fullWidth:x,hiddenLabel:S,size:C,onBlur:()=>{L(!1)},onFocus:()=>{L(!0)},onEmpty:D,onFilled:F,registerEffect:r,required:_,variant:R}),[T,y,b,A,P,B,x,S,r,D,F,_,C,R]);return(0,m.jsx)(d.A.Provider,{value:$,children:(0,m.jsx)(g,{as:v,ownerState:k,className:(0,o.A)(I.root,f),ref:t,...O,children:p})})})},90405:(e,t,r)=>{"use strict";r.d(t,{A:()=>E});var n=r(14232),o=r(4697),i=r(69241),s=r(27367),a=r(74073),l=r(80027),c=r(54773),u=r(82987),d=r(78457),p=r(52196),f=r(47951),h=r(45879);function m(e){return(0,h.Ay)("MuiFormLabel",e)}let g=(0,f.A)("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]);var y=r(37876);let v=(0,c.Ay)("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,"secondary"===r.color&&t.colorSecondary,r.filled&&t.filled]}})((0,u.A)(e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,...t.typography.body1,lineHeight:"1.4375em",padding:0,position:"relative",variants:[...Object.entries(t.palette).filter((0,d.A)()).map(e=>{let[r]=e;return{props:{color:r},style:{["&.".concat(g.focused)]:{color:(t.vars||t).palette[r].main}}}}),{props:{},style:{["&.".concat(g.disabled)]:{color:(t.vars||t).palette.text.disabled},["&.".concat(g.error)]:{color:(t.vars||t).palette.error.main}}}]}})),b=(0,c.Ay)("span",{name:"MuiFormLabel",slot:"Asterisk",overridesResolver:(e,t)=>t.asterisk})((0,u.A)(e=>{let{theme:t}=e;return{["&.".concat(g.error)]:{color:(t.vars||t).palette.error.main}}})),A=n.forwardRef(function(e,t){let r=(0,p.b)({props:e,name:"MuiFormLabel"}),{children:n,className:c,color:u,component:d="label",disabled:f,error:h,filled:g,focused:A,required:w,...x}=r,S=(0,a.A)(),E=(0,s.A)({props:r,muiFormControl:S,states:["color","required","focused","disabled","error","filled"]}),_={...r,color:E.color||"primary",component:d,disabled:E.disabled,error:E.error,filled:E.filled,focused:E.focused,required:E.required},C=(e=>{let{classes:t,color:r,focused:n,disabled:i,error:s,filled:a,required:c}=e,u={root:["root","color".concat((0,l.A)(r)),i&&"disabled",s&&"error",a&&"filled",n&&"focused",c&&"required"],asterisk:["asterisk",s&&"error"]};return(0,o.A)(u,m,t)})(_);return(0,y.jsxs)(v,{as:d,ownerState:_,className:(0,i.A)(C.root,c),ref:t,...x,children:[n,E.required&&(0,y.jsxs)(b,{ownerState:_,"aria-hidden":!0,className:C.asterisk,children:[" ","*"]})]})});var w=r(68275);function x(e){return(0,h.Ay)("MuiInputLabel",e)}(0,f.A)("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);let S=(0,c.Ay)(A,{shouldForwardProp:e=>(0,w.A)(e)||"classes"===e,name:"MuiInputLabel",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{["& .".concat(g.asterisk)]:t.asterisk},t.root,r.formControl&&t.formControl,"small"===r.size&&t.sizeSmall,r.shrink&&t.shrink,!r.disableAnimation&&t.animated,r.focused&&t.focused,t[r.variant]]}})((0,u.A)(e=>{let{theme:t}=e;return{display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:e=>{let{ownerState:t}=e;return t.formControl},style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:e=>{let{ownerState:t}=e;return t.shrink},style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:e=>{let{ownerState:t}=e;return!t.disableAnimation},style:{transition:t.transitions.create(["color","transform","max-width"],{duration:t.transitions.duration.shorter,easing:t.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:e=>{let{variant:t,ownerState:r}=e;return"filled"===t&&r.shrink},style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:e=>{let{variant:t,ownerState:r,size:n}=e;return"filled"===t&&r.shrink&&"small"===n},style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:e=>{let{variant:t,ownerState:r}=e;return"outlined"===t&&r.shrink},style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}})),E=n.forwardRef(function(e,t){let r=(0,p.b)({name:"MuiInputLabel",props:e}),{disableAnimation:n=!1,margin:c,shrink:u,variant:d,className:f,...h}=r,m=(0,a.A)(),g=u;void 0===g&&m&&(g=m.filled||m.focused||m.adornedStart);let v=(0,s.A)({props:r,muiFormControl:m,states:["size","variant","required","focused"]}),b={...r,disableAnimation:n,formControl:m,shrink:g,size:v.size,variant:v.variant,required:v.required,focused:v.focused},A=(e=>{let{classes:t,formControl:r,size:n,shrink:i,disableAnimation:s,variant:a,required:c}=e,u={root:["root",r&&"formControl",!s&&"animated",i&&"shrink",n&&"normal"!==n&&"size".concat((0,l.A)(n)),a],asterisk:[c&&"asterisk"]},d=(0,o.A)(u,x,t);return{...t,...d}})(b);return(0,y.jsx)(S,{"data-shrink":g,ref:t,className:(0,i.A)(A.root,f),...h,ownerState:b,classes:A})})},90757:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M15 16h4v2h-4zm0-8h7v2h-7zm0 4h6v2h-6zM3 18c0 1.1.9 2 2 2h6c1.1 0 2-.9 2-2V8H3zM14 5h-3l-1-1H6L5 5H2v2h12z"}),"DeleteSweep")},91411:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=r(99659).A},93256:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var n=r(14232),o=r(69241),i=r(4697),s=r(54638),a=r(77018),l=r(99003),c=r(54773),u=r(52196),d=r(632),p=r(67360),f=r(37876);let h=(0,c.Ay)("div",{name:"MuiListItemText",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[{["& .".concat(d.A.primary)]:t.primary},{["& .".concat(d.A.secondary)]:t.secondary},t.root,r.inset&&t.inset,r.primary&&r.secondary&&t.multiline,r.dense&&t.dense]}})({flex:"1 1 auto",minWidth:0,marginTop:4,marginBottom:4,[".".concat(s.A.root,":where(& .").concat(d.A.primary,")")]:{display:"block"},[".".concat(s.A.root,":where(& .").concat(d.A.secondary,")")]:{display:"block"},variants:[{props:e=>{let{ownerState:t}=e;return t.primary&&t.secondary},style:{marginTop:6,marginBottom:6}},{props:e=>{let{ownerState:t}=e;return t.inset},style:{paddingLeft:56}}]}),m=n.forwardRef(function(e,t){let r=(0,u.b)({props:e,name:"MuiListItemText"}),{children:s,className:c,disableTypography:m=!1,inset:g=!1,primary:y,primaryTypographyProps:v,secondary:b,secondaryTypographyProps:A,slots:w={},slotProps:x={},...S}=r,{dense:E}=n.useContext(l.A),_=null!=y?y:s,C=b,R={...r,disableTypography:m,inset:g,primary:!!_,secondary:!!C,dense:E},O=(e=>{let{classes:t,inset:r,primary:n,secondary:o,dense:s}=e;return(0,i.A)({root:["root",r&&"inset",s&&"dense",n&&o&&"multiline"],primary:["primary"],secondary:["secondary"]},d.b,t)})(R),k={slots:w,slotProps:{primary:v,secondary:A,...x}},[I,T]=(0,p.A)("root",{className:(0,o.A)(O.root,c),elementType:h,externalForwardedProps:{...k,...S},ownerState:R,ref:t}),[M,P]=(0,p.A)("primary",{className:O.primary,elementType:a.A,externalForwardedProps:k,ownerState:R}),[j,N]=(0,p.A)("secondary",{className:O.secondary,elementType:a.A,externalForwardedProps:k,ownerState:R});return null==_||_.type===a.A||m||(_=(0,f.jsx)(M,{variant:E?"body2":"body1",component:(null==P?void 0:P.variant)?void 0:"span",...P,children:_})),null==C||C.type===a.A||m||(C=(0,f.jsx)(j,{variant:"body2",color:"textSecondary",...N,children:C})),(0,f.jsxs)(I,{...T,children:[_,C]})})},95062:(e,t,r)=>{e.exports=r(79706)()},95684:(e,t,r)=>{"use strict";r.d(t,{A:()=>j});var n=r(14232),o=r(4697),i=r(53880),s=r(7061),a=r(92233);let l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoHideDuration:t=null,disableWindowBlurListener:r=!1,onClose:o,open:l,resumeHideDuration:c}=e,u=(0,i.A)();n.useEffect(()=>{if(l)return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)};function e(e){e.defaultPrevented||"Escape"!==e.key||null==o||o(e,"escapeKeyDown")}},[l,o]);let d=(0,s.A)((e,t)=>{null==o||o(e,t)}),p=(0,s.A)(e=>{o&&null!=e&&u.start(e,()=>{d(null,"timeout")})});n.useEffect(()=>(l&&p(t),u.clear),[l,t,p,u]);let f=u.clear,h=n.useCallback(()=>{null!=t&&p(null!=c?c:.5*t)},[t,c,p]);return n.useEffect(()=>{if(!r&&l)return window.addEventListener("focus",h),window.addEventListener("blur",f),()=>{window.removeEventListener("focus",h),window.removeEventListener("blur",f)}},[r,l,h,f]),{getRootProps:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r={...(0,a.A)(e),...(0,a.A)(t)};return{role:"presentation",...t,...r,onBlur:e=>{let t=r.onBlur;null==t||t(e),h()},onFocus:e=>{let t=r.onFocus;null==t||t(e),f()},onMouseEnter:e=>{let t=r.onMouseEnter;null==t||t(e),f()},onMouseLeave:e=>{let t=r.onMouseLeave;null==t||t(e),h()}}},onClickAway:e=>{null==o||o(e,"clickaway")}}};var c=r(61637),u=r(44471),d=r(43165);function p(e){return e.substring(2).toLowerCase()}function f(e){let{children:t,disableReactTree:r=!1,mouseEvent:o="onClick",onClickAway:i,touchEvent:a="onTouchEnd"}=e,l=n.useRef(!1),f=n.useRef(null),h=n.useRef(!1),m=n.useRef(!1);n.useEffect(()=>(setTimeout(()=>{h.current=!0},0),()=>{h.current=!1}),[]);let g=(0,c.A)((0,d.A)(t),f),y=(0,s.A)(e=>{let t=m.current;m.current=!1;let n=(0,u.A)(f.current);if(!(!h.current||!f.current||"clientX"in e&&(n.documentElement.clientWidth<e.clientX||n.documentElement.clientHeight<e.clientY))){if(l.current){l.current=!1;return}(e.composedPath?e.composedPath().includes(f.current):!n.documentElement.contains(e.target)||f.current.contains(e.target))||!r&&t||i(e)}}),v=e=>r=>{m.current=!0;let n=t.props[e];n&&n(r)},b={ref:g};return!1!==a&&(b[a]=v(a)),n.useEffect(()=>{if(!1!==a){let e=p(a),t=(0,u.A)(f.current),r=()=>{l.current=!0};return t.addEventListener(e,y),t.addEventListener("touchmove",r),()=>{t.removeEventListener(e,y),t.removeEventListener("touchmove",r)}}},[y,a]),!1!==o&&(b[o]=v(o)),n.useEffect(()=>{if(!1!==o){let e=p(o),t=(0,u.A)(f.current);return t.addEventListener(e,y),()=>{t.removeEventListener(e,y)}}},[y,o]),n.cloneElement(t,b)}var h=r(54773),m=r(30566),g=r(82987),y=r(52196),v=r(80027),b=r(24766),A=r(69241),w=r(126),x=r(11951),S=r(47951),E=r(45879);function _(e){return(0,E.Ay)("MuiSnackbarContent",e)}(0,S.A)("MuiSnackbarContent",["root","message","action"]);var C=r(37876);let R=(0,h.Ay)(x.A,{name:"MuiSnackbarContent",slot:"Root",overridesResolver:(e,t)=>t.root})((0,g.A)(e=>{let{theme:t}=e,r="light"===t.palette.mode?.8:.98,n=(0,w.tL)(t.palette.background.default,r);return{...t.typography.body2,color:t.vars?t.vars.palette.SnackbarContent.color:t.palette.getContrastText(n),backgroundColor:t.vars?t.vars.palette.SnackbarContent.bg:n,display:"flex",alignItems:"center",flexWrap:"wrap",padding:"6px 16px",borderRadius:(t.vars||t).shape.borderRadius,flexGrow:1,[t.breakpoints.up("sm")]:{flexGrow:"initial",minWidth:288}}})),O=(0,h.Ay)("div",{name:"MuiSnackbarContent",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0"}),k=(0,h.Ay)("div",{name:"MuiSnackbarContent",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:16,marginRight:-8}),I=n.forwardRef(function(e,t){let r=(0,y.b)({props:e,name:"MuiSnackbarContent"}),{action:n,className:i,message:s,role:a="alert",...l}=r,c=(e=>{let{classes:t}=e;return(0,o.A)({root:["root"],action:["action"],message:["message"]},_,t)})(r);return(0,C.jsxs)(R,{role:a,square:!0,elevation:6,className:(0,A.A)(c.root,i),ownerState:r,ref:t,...l,children:[(0,C.jsx)(O,{className:c.message,ownerState:r,children:s}),n?(0,C.jsx)(k,{className:c.action,ownerState:r,children:n}):null]})});function T(e){return(0,E.Ay)("MuiSnackbar",e)}(0,S.A)("MuiSnackbar",["root","anchorOriginTopCenter","anchorOriginBottomCenter","anchorOriginTopRight","anchorOriginBottomRight","anchorOriginTopLeft","anchorOriginBottomLeft"]);var M=r(67360);let P=(0,h.Ay)("div",{name:"MuiSnackbar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,t["anchorOrigin".concat((0,v.A)(r.anchorOrigin.vertical)).concat((0,v.A)(r.anchorOrigin.horizontal))]]}})((0,g.A)(e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.snackbar,position:"fixed",display:"flex",left:8,right:8,justifyContent:"center",alignItems:"center",variants:[{props:e=>{let{ownerState:t}=e;return"top"===t.anchorOrigin.vertical},style:{top:8,[t.breakpoints.up("sm")]:{top:24}}},{props:e=>{let{ownerState:t}=e;return"top"!==t.anchorOrigin.vertical},style:{bottom:8,[t.breakpoints.up("sm")]:{bottom:24}}},{props:e=>{let{ownerState:t}=e;return"left"===t.anchorOrigin.horizontal},style:{justifyContent:"flex-start",[t.breakpoints.up("sm")]:{left:24,right:"auto"}}},{props:e=>{let{ownerState:t}=e;return"right"===t.anchorOrigin.horizontal},style:{justifyContent:"flex-end",[t.breakpoints.up("sm")]:{right:24,left:"auto"}}},{props:e=>{let{ownerState:t}=e;return"center"===t.anchorOrigin.horizontal},style:{[t.breakpoints.up("sm")]:{left:"50%",right:"auto",transform:"translateX(-50%)"}}}]}})),j=n.forwardRef(function(e,t){let r=(0,y.b)({props:e,name:"MuiSnackbar"}),i=(0,m.A)(),s={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{action:a,anchorOrigin:{vertical:c,horizontal:u}={vertical:"bottom",horizontal:"left"},autoHideDuration:d=null,children:p,className:h,ClickAwayListenerProps:g,ContentProps:A,disableWindowBlurListener:w=!1,message:x,onBlur:S,onClose:E,onFocus:_,onMouseEnter:R,onMouseLeave:O,open:k,resumeHideDuration:j,slots:N={},slotProps:L={},TransitionComponent:B,transitionDuration:F=s,TransitionProps:{onEnter:D,onExited:$,...z}={},...W}=r,U={...r,anchorOrigin:{vertical:c,horizontal:u},autoHideDuration:d,disableWindowBlurListener:w,TransitionComponent:B,transitionDuration:F},H=(e=>{let{classes:t,anchorOrigin:r}=e,n={root:["root","anchorOrigin".concat((0,v.A)(r.vertical)).concat((0,v.A)(r.horizontal))]};return(0,o.A)(n,T,t)})(U),{getRootProps:q,onClickAway:V}=l({...U}),[X,K]=n.useState(!0),G={slots:{transition:B,...N},slotProps:{content:A,clickAwayListener:g,transition:z,...L}},[Y,J]=(0,M.A)("root",{ref:t,className:[H.root,h],elementType:P,getSlotProps:q,externalForwardedProps:{...G,...W},ownerState:U}),[Q,{ownerState:Z,...ee}]=(0,M.A)("clickAwayListener",{elementType:f,externalForwardedProps:G,getSlotProps:e=>({onClickAway:function(){for(var t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];null==(t=e.onClickAway)||t.call(e,...n),V(...n)}}),ownerState:U}),[et,er]=(0,M.A)("content",{elementType:I,shouldForwardComponentProp:!0,externalForwardedProps:G,additionalProps:{message:x,action:a},ownerState:U}),[en,eo]=(0,M.A)("transition",{elementType:b.A,externalForwardedProps:G,getSlotProps:e=>({onEnter:function(){for(var t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];null==(t=e.onEnter)||t.call(e,...n),((e,t)=>{K(!1),D&&D(e,t)})(...n)},onExited:function(){for(var t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];null==(t=e.onExited)||t.call(e,...n),(e=>{K(!0),$&&$(e)})(...n)}}),additionalProps:{appear:!0,in:k,timeout:F,direction:"top"===c?"down":"up"},ownerState:U});return!k&&X?null:(0,C.jsx)(Q,{...ee,...N.clickAwayListener&&{ownerState:Z},children:(0,C.jsx)(Y,{...J,children:(0,C.jsx)(en,{...eo,children:p||(0,C.jsx)(et,{...er})})})})})},98780:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(31057),o=r(37876);let i=(0,n.A)((0,o.jsx)("path",{d:"M19 11h-8v6h8zm4 8V4.98C23 3.88 22.1 3 21 3H3c-1.1 0-2 .88-2 1.98V19c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2m-2 .02H3V4.97h18z"}),"PictureInPictureAlt")},99386:(e,t,r)=>{"use strict";r.d(t,{A:()=>P});var n=r(14232),o=r(69241),i=r(4697),s=r(126),a=r(26872),l=r(31057),c=r(37876);let u=(0,l.A)((0,c.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"RadioButtonUnchecked"),d=(0,l.A)((0,c.jsx)("path",{d:"M8.465 8.465C9.37 7.56 10.62 7 12 7C14.76 7 17 9.24 17 12C17 13.38 16.44 14.63 15.535 15.535C14.63 16.44 13.38 17 12 17C9.24 17 7 14.76 7 12C7 10.62 7.56 9.37 8.465 8.465Z"}),"RadioButtonChecked");var p=r(68275),f=r(54773),h=r(82987);let m=(0,f.Ay)("span",{shouldForwardProp:p.A})({position:"relative",display:"flex"}),g=(0,f.Ay)(u)({transform:"scale(1)"}),y=(0,f.Ay)(d)((0,h.A)(e=>{let{theme:t}=e;return{left:0,position:"absolute",transform:"scale(0)",transition:t.transitions.create("transform",{easing:t.transitions.easing.easeIn,duration:t.transitions.duration.shortest}),variants:[{props:{checked:!0},style:{transform:"scale(1)",transition:t.transitions.create("transform",{easing:t.transitions.easing.easeOut,duration:t.transitions.duration.shortest})}}]}})),v=function(e){let{checked:t=!1,classes:r={},fontSize:n}=e,o={...e,checked:t};return(0,c.jsxs)(m,{className:r.root,ownerState:o,children:[(0,c.jsx)(g,{fontSize:n,className:r.background,ownerState:o}),(0,c.jsx)(y,{fontSize:n,className:r.dot,ownerState:o})]})};var b=r(80027);let A=r(62435).A;var w=r(74073),x=r(71655),S=r(47951),E=r(45879);function _(e){return(0,E.Ay)("MuiRadio",e)}let C=(0,S.A)("MuiRadio",["root","checked","disabled","colorPrimary","colorSecondary","sizeSmall"]);var R=r(78457),O=r(67360),k=r(52196);let I=(0,f.Ay)(a.A,{shouldForwardProp:e=>(0,p.A)(e)||"classes"===e,name:"MuiRadio",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:r}=e;return[t.root,"medium"!==r.size&&t["size".concat((0,b.A)(r.size))],t["color".concat((0,b.A)(r.color))]]}})((0,h.A)(e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,["&.".concat(C.disabled)]:{color:(t.vars||t).palette.action.disabled},variants:[{props:{color:"default",disabled:!1,disableRipple:!1},style:{"&:hover":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette.action.activeChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):(0,s.X4)(t.palette.action.active,t.palette.action.hoverOpacity)}}},...Object.entries(t.palette).filter((0,R.A)()).map(e=>{let[r]=e;return{props:{color:r,disabled:!1,disableRipple:!1},style:{"&:hover":{backgroundColor:t.vars?"rgba(".concat(t.vars.palette[r].mainChannel," / ").concat(t.vars.palette.action.hoverOpacity,")"):(0,s.X4)(t.palette[r].main,t.palette.action.hoverOpacity)}}}}),...Object.entries(t.palette).filter((0,R.A)()).map(e=>{let[r]=e;return{props:{color:r,disabled:!1},style:{["&.".concat(C.checked)]:{color:(t.vars||t).palette[r].main}}}}),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}})),T=(0,c.jsx)(v,{checked:!0}),M=(0,c.jsx)(v,{}),P=n.forwardRef(function(e,t){var r,s,a,l,u;let d=(0,k.b)({props:e,name:"MuiRadio"}),{checked:p,checkedIcon:f=T,color:h="primary",icon:m=M,name:g,onChange:y,size:v="medium",className:S,disabled:E,disableRipple:C=!1,slots:R={},slotProps:P={},inputProps:j,...N}=d,L=(0,w.A)(),B=E;L&&void 0===B&&(B=L.disabled),null!=B||(B=!1);let F={...d,disabled:B,disableRipple:C,color:h,size:v},D=(e=>{let{classes:t,color:r,size:n}=e,o={root:["root","color".concat((0,b.A)(r)),"medium"!==n&&"size".concat((0,b.A)(n))]};return{...t,...(0,i.A)(o,_,t)}})(F),$=n.useContext(x.A),z=p,W=A(y,$&&$.onChange),U=g;$&&(void 0===z&&(l=$.value,z="object"==typeof(u=d.value)&&null!==u?l===u:String(l)===String(u)),void 0===U&&(U=$.name));let H=null!=(r=P.input)?r:j,[q,V]=(0,O.A)("root",{ref:t,elementType:I,className:(0,o.A)(D.root,S),shouldForwardComponentProp:!0,externalForwardedProps:{slots:R,slotProps:P,...N},getSlotProps:e=>({...e,onChange:function(t){for(var r,n=arguments.length,o=Array(n>1?n-1:0),i=1;i<n;i++)o[i-1]=arguments[i];null==(r=e.onChange)||r.call(e,t,...o),W(t,...o)}}),ownerState:F,additionalProps:{type:"radio",icon:n.cloneElement(m,{fontSize:null!=(s=m.props.fontSize)?s:v}),checkedIcon:n.cloneElement(f,{fontSize:null!=(a=f.props.fontSize)?a:v}),disabled:B,name:U,checked:z,slots:R,slotProps:{input:"function"==typeof H?H(F):H}}});return(0,c.jsx)(q,{...V,classes:D})})}}]);