"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./utils/speechServices.js":
/*!*********************************!*\
  !*** ./utils/speechServices.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AzureSpeechService: () => (/* binding */ AzureSpeechService),\n/* harmony export */   DeepgramSpeechService: () => (/* binding */ DeepgramSpeechService),\n/* harmony export */   createSpeechService: () => (/* binding */ createSpeechService)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet createClient = null;\nlet SpeechSDK = null;\n// Initialize client-side dependencies\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\")).then((module)=>{\n        createClient = module.createClient;\n    });\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23)).then((module)=>{\n        SpeechSDK = module;\n    });\n}\n/**\n * Abstract base class for speech services\n */ class SpeechService {\n    async start(mediaStream, source) {\n        throw new Error('start method must be implemented');\n    }\n    async stop() {\n        throw new Error('stop method must be implemented');\n    }\n    cleanup() {\n        // Default cleanup implementation\n        if (this.recognizer) {\n            this.recognizer = null;\n        }\n        this.isActive = false;\n    }\n    constructor(config, callbacks){\n        this.config = config;\n        this.callbacks = callbacks;\n        this.isActive = false;\n        this.recognizer = null;\n    }\n}\n/**\n * Deepgram Speech Service Implementation\n */ class DeepgramSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            if (!this.config.deepgramKey) {\n                throw new Error('Deepgram API key is required');\n            }\n            if (!createClient) {\n                const { createClient: dgCreateClient } = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\"));\n                createClient = dgCreateClient;\n            }\n            this.source = source;\n            this.deepgram = createClient(this.config.deepgramKey);\n            // Create WebSocket connection for real-time transcription\n            this.connection = this.deepgram.listen.live({\n                model: this.config.deepgramModel || 'nova-2',\n                language: this.config.deepgramLanguage || 'en-US',\n                smart_format: true,\n                interim_results: true,\n                endpointing: 300,\n                utterance_end_ms: 1000,\n                vad_events: true\n            });\n            // Set up event handlers\n            this.connection.on('open', ()=>{\n                var _this_callbacks_onStart, _this_callbacks;\n                console.log(\"Deepgram connection opened for \".concat(source));\n                this.isActive = true;\n                // Clear any connection timeout\n                if (this.connectionTimeout) {\n                    clearTimeout(this.connectionTimeout);\n                    this.connectionTimeout = null;\n                }\n                (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            });\n            this.connection.on('Results', (data)=>{\n                var _data_channel_alternatives_, _data_channel_alternatives, _data_channel;\n                console.log(\"Deepgram results for \".concat(source, \":\"), data);\n                const transcript = (_data_channel = data.channel) === null || _data_channel === void 0 ? void 0 : (_data_channel_alternatives = _data_channel.alternatives) === null || _data_channel_alternatives === void 0 ? void 0 : (_data_channel_alternatives_ = _data_channel_alternatives[0]) === null || _data_channel_alternatives_ === void 0 ? void 0 : _data_channel_alternatives_.transcript;\n                if (transcript && transcript.trim()) {\n                    if (data.is_final) {\n                        var _this_callbacks_onFinalResult, _this_callbacks;\n                        console.log(\"Final transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, transcript, source);\n                    } else {\n                        var _this_callbacks_onInterimResult, _this_callbacks1;\n                        console.log(\"Interim transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onInterimResult = (_this_callbacks1 = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks1, transcript, source);\n                    }\n                }\n            });\n            this.connection.on('Metadata', (data)=>{\n                console.log(\"Deepgram metadata for \".concat(source, \":\"), data);\n            });\n            this.connection.on('error', (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"Deepgram error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                this.stop();\n            });\n            this.connection.on('close', (closeEvent)=>{\n                console.log(\"Deepgram connection closed for \".concat(source, \":\"), closeEvent);\n                this.isActive = false;\n                // If connection closes immediately after opening, it might be a configuration issue\n                if (closeEvent && closeEvent.code && closeEvent.code !== 1000) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection closed with error code \".concat(closeEvent.code, \": \").concat(closeEvent.reason));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error(\"Connection closed: \".concat(closeEvent.reason || 'Unknown error')), source);\n                } else {\n                    var _this_callbacks_onStop, _this_callbacks1;\n                    (_this_callbacks_onStop = (_this_callbacks1 = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks1, source);\n                }\n            });\n            this.connection.on('warning', (warning)=>{\n                console.warn(\"Deepgram warning for \".concat(source, \":\"), warning);\n            });\n            // Set up MediaRecorder to send audio data to Deepgram\n            // Try different mimeTypes for better compatibility\n            let mimeType = 'audio/webm;codecs=opus';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n                mimeType = 'audio/webm';\n                if (!MediaRecorder.isTypeSupported(mimeType)) {\n                    mimeType = 'audio/mp4';\n                    if (!MediaRecorder.isTypeSupported(mimeType)) {\n                        mimeType = ''; // Use default\n                    }\n                }\n            }\n            console.log(\"Using MediaRecorder mimeType: \".concat(mimeType || 'default', \" for \").concat(source));\n            this.mediaRecorder = new MediaRecorder(mediaStream, mimeType ? {\n                mimeType\n            } : {});\n            this.mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0 && this.connection && this.isActive) {\n                    console.log(\"Sending audio data to Deepgram: \".concat(event.data.size, \" bytes for \").concat(source));\n                    this.connection.send(event.data);\n                }\n            };\n            this.mediaRecorder.onerror = (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"MediaRecorder error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            };\n            this.mediaRecorder.onstart = ()=>{\n                console.log(\"MediaRecorder started for \".concat(source));\n            };\n            this.mediaRecorder.onstop = ()=>{\n                console.log(\"MediaRecorder stopped for \".concat(source));\n            };\n            // Start recording and sending data\n            this.mediaRecorder.start(250); // Send data every 250ms for better stability\n            // Set up connection timeout to detect failed connections\n            this.connectionTimeout = setTimeout(()=>{\n                if (!this.isActive) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection timeout for \".concat(source));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error('Connection timeout - please check your API key and network connection'), source);\n                    this.stop();\n                }\n            }, 10000); // 10 second timeout\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks;\n            console.error(\"Failed to start Deepgram for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n                this.mediaRecorder.stop();\n            }\n            if (this.connection) {\n                this.connection.finish();\n                this.connection = null;\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Deepgram:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        this.mediaRecorder = null;\n        this.connection = null;\n        this.deepgram = null;\n        this.source = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.deepgram = null;\n        this.connection = null;\n        this.mediaRecorder = null;\n        this.source = null;\n        this.connectionTimeout = null;\n    }\n}\n/**\n * Azure Speech Service Implementation (Legacy)\n */ class AzureSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            var _this_callbacks_onStart, _this_callbacks;\n            if (!this.config.azureToken || !this.config.azureRegion) {\n                throw new Error('Azure Speech credentials are required');\n            }\n            if (!SpeechSDK) {\n                SpeechSDK = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23));\n            }\n            // Create audio config from media stream\n            this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n            // Create speech config\n            this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(this.config.azureToken, this.config.azureRegion);\n            this.speechConfig.speechRecognitionLanguage = this.config.azureLanguage || 'en-US';\n            // Create recognizer\n            this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);\n            // Set up event handlers\n            this.recognizer.recognizing = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                    var _this_callbacks_onInterimResult, _this_callbacks;\n                    (_this_callbacks_onInterimResult = (_this_callbacks = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.recognized = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                    var _this_callbacks_onFinalResult, _this_callbacks;\n                    (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.canceled = (s, e)=>{\n                console.log(\"Azure recognition canceled for \".concat(source, \": \").concat(e.reason));\n                if (e.reason === SpeechSDK.CancellationReason.Error) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    const error = new Error(\"Azure Speech error: \".concat(e.errorDetails));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                }\n                this.stop();\n            };\n            this.recognizer.sessionStopped = (s, e)=>{\n                var _this_callbacks_onStop, _this_callbacks;\n                console.log(\"Azure session stopped for \".concat(source));\n                (_this_callbacks_onStop = (_this_callbacks = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks, source);\n                this.stop();\n            };\n            // Start continuous recognition\n            await this.recognizer.startContinuousRecognitionAsync();\n            this.isActive = true;\n            (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks1;\n            console.error(\"Failed to start Azure Speech for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks1 = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks1, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            if (this.recognizer && typeof this.recognizer.stopContinuousRecognitionAsync === 'function') {\n                await this.recognizer.stopContinuousRecognitionAsync();\n            }\n            if (this.audioConfig && typeof this.audioConfig.close === 'function') {\n                this.audioConfig.close();\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Azure Speech:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n}\n/**\n * Factory function to create speech service instances\n */ function createSpeechService(config, callbacks) {\n    const serviceType = config.speechService || 'deepgram';\n    switch(serviceType){\n        case 'deepgram':\n            return new DeepgramSpeechService(config, callbacks);\n        case 'azure':\n            return new AzureSpeechService(config, callbacks);\n        default:\n            throw new Error(\"Unknown speech service: \".concat(serviceType));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3V0aWxzL3NwZWVjaFNlcnZpY2VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLHVDQUF1QztBQUN2QyxJQUFJQSxlQUFlO0FBQ25CLElBQUlDLFlBQVk7QUFFaEIsc0NBQXNDO0FBQ3RDLElBQUksSUFBNkIsRUFBRTtJQUNqQyw0UEFBdUIsQ0FBQ0MsSUFBSSxDQUFDQyxDQUFBQTtRQUMzQkgsZUFBZUcsT0FBT0gsWUFBWTtJQUNwQztJQUNBLHdYQUFnRCxDQUFDRSxJQUFJLENBQUNDLENBQUFBO1FBQ3BERixZQUFZRTtJQUNkO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNELE1BQU1DO0lBUUosTUFBTUMsTUFBTUMsV0FBVyxFQUFFQyxNQUFNLEVBQUU7UUFDL0IsTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0lBRUEsTUFBTUMsT0FBTztRQUNYLE1BQU0sSUFBSUQsTUFBTTtJQUNsQjtJQUVBRSxVQUFVO1FBQ1IsaUNBQWlDO1FBQ2pDLElBQUksSUFBSSxDQUFDQyxVQUFVLEVBQUU7WUFDbkIsSUFBSSxDQUFDQSxVQUFVLEdBQUc7UUFDcEI7UUFDQSxJQUFJLENBQUNDLFFBQVEsR0FBRztJQUNsQjtJQXJCQSxZQUFZQyxNQUFNLEVBQUVDLFNBQVMsQ0FBRTtRQUM3QixJQUFJLENBQUNELE1BQU0sR0FBR0E7UUFDZCxJQUFJLENBQUNDLFNBQVMsR0FBR0E7UUFDakIsSUFBSSxDQUFDRixRQUFRLEdBQUc7UUFDaEIsSUFBSSxDQUFDRCxVQUFVLEdBQUc7SUFDcEI7QUFpQkY7QUFFQTs7Q0FFQyxHQUNNLE1BQU1JLDhCQUE4Qlg7SUFVekMsTUFBTUMsTUFBTUMsV0FBVyxFQUFFQyxNQUFNLEVBQUU7UUFDL0IsSUFBSTtZQUNGLElBQUksQ0FBQyxJQUFJLENBQUNNLE1BQU0sQ0FBQ0csV0FBVyxFQUFFO2dCQUM1QixNQUFNLElBQUlSLE1BQU07WUFDbEI7WUFFQSxJQUFJLENBQUNSLGNBQWM7Z0JBQ2pCLE1BQU0sRUFBRUEsY0FBY2lCLGNBQWMsRUFBRSxHQUFHLE1BQU0sNFBBQXVCO2dCQUN0RWpCLGVBQWVpQjtZQUNqQjtZQUVBLElBQUksQ0FBQ1YsTUFBTSxHQUFHQTtZQUNkLElBQUksQ0FBQ1csUUFBUSxHQUFHbEIsYUFBYSxJQUFJLENBQUNhLE1BQU0sQ0FBQ0csV0FBVztZQUVwRCwwREFBMEQ7WUFDMUQsSUFBSSxDQUFDRyxVQUFVLEdBQUcsSUFBSSxDQUFDRCxRQUFRLENBQUNFLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDO2dCQUMxQ0MsT0FBTyxJQUFJLENBQUNULE1BQU0sQ0FBQ1UsYUFBYSxJQUFJO2dCQUNwQ0MsVUFBVSxJQUFJLENBQUNYLE1BQU0sQ0FBQ1ksZ0JBQWdCLElBQUk7Z0JBQzFDQyxjQUFjO2dCQUNkQyxpQkFBaUI7Z0JBQ2pCQyxhQUFhO2dCQUNiQyxrQkFBa0I7Z0JBQ2xCQyxZQUFZO1lBQ2Q7WUFFQSx3QkFBd0I7WUFDeEIsSUFBSSxDQUFDWCxVQUFVLENBQUNZLEVBQUUsQ0FBQyxRQUFRO29CQVV6QjtnQkFUQUMsUUFBUUMsR0FBRyxDQUFDLGtDQUF5QyxPQUFQMUI7Z0JBQzlDLElBQUksQ0FBQ0ssUUFBUSxHQUFHO2dCQUVoQiwrQkFBK0I7Z0JBQy9CLElBQUksSUFBSSxDQUFDc0IsaUJBQWlCLEVBQUU7b0JBQzFCQyxhQUFhLElBQUksQ0FBQ0QsaUJBQWlCO29CQUNuQyxJQUFJLENBQUNBLGlCQUFpQixHQUFHO2dCQUMzQjtpQkFFQSxpREFBSSxDQUFDcEIsU0FBUyxFQUFDc0IsT0FBTyxjQUF0Qiw0RkFBeUI3QjtZQUMzQjtZQUVBLElBQUksQ0FBQ1ksVUFBVSxDQUFDWSxFQUFFLENBQUMsV0FBVyxDQUFDTTtvQkFFVkEsNkJBQUFBLDRCQUFBQTtnQkFEbkJMLFFBQVFDLEdBQUcsQ0FBQyx3QkFBK0IsT0FBUDFCLFFBQU8sTUFBSThCO2dCQUMvQyxNQUFNQyxjQUFhRCxnQkFBQUEsS0FBS0UsT0FBTyxjQUFaRixxQ0FBQUEsNkJBQUFBLGNBQWNHLFlBQVksY0FBMUJILGtEQUFBQSw4QkFBQUEsMEJBQTRCLENBQUMsRUFBRSxjQUEvQkEsa0RBQUFBLDRCQUFpQ0MsVUFBVTtnQkFDOUQsSUFBSUEsY0FBY0EsV0FBV0csSUFBSSxJQUFJO29CQUNuQyxJQUFJSixLQUFLSyxRQUFRLEVBQUU7NEJBRWpCO3dCQURBVixRQUFRQyxHQUFHLENBQUMsd0JBQW9DSyxPQUFaL0IsUUFBTyxPQUFnQixPQUFYK0IsWUFBVzt5QkFDM0QsdURBQUksQ0FBQ3hCLFNBQVMsRUFBQzZCLGFBQWEsY0FBNUIsd0dBQStCTCxZQUFZL0I7b0JBQzdDLE9BQU87NEJBRUw7d0JBREF5QixRQUFRQyxHQUFHLENBQUMsMEJBQXNDSyxPQUFaL0IsUUFBTyxPQUFnQixPQUFYK0IsWUFBVzt5QkFDN0QsMERBQUksQ0FBQ3hCLFNBQVMsRUFBQzhCLGVBQWUsY0FBOUIsNkdBQWlDTixZQUFZL0I7b0JBQy9DO2dCQUNGO1lBQ0Y7WUFFQSxJQUFJLENBQUNZLFVBQVUsQ0FBQ1ksRUFBRSxDQUFDLFlBQVksQ0FBQ007Z0JBQzlCTCxRQUFRQyxHQUFHLENBQUMseUJBQWdDLE9BQVAxQixRQUFPLE1BQUk4QjtZQUNsRDtZQUVBLElBQUksQ0FBQ2xCLFVBQVUsQ0FBQ1ksRUFBRSxDQUFDLFNBQVMsQ0FBQ2M7b0JBRTNCO2dCQURBYixRQUFRYSxLQUFLLENBQUMsc0JBQTZCLE9BQVB0QyxRQUFPLE1BQUlzQztpQkFDL0MsaURBQUksQ0FBQy9CLFNBQVMsRUFBQ2dDLE9BQU8sY0FBdEIsNEZBQXlCRCxPQUFPdEM7Z0JBQ2hDLElBQUksQ0FBQ0UsSUFBSTtZQUNYO1lBRUEsSUFBSSxDQUFDVSxVQUFVLENBQUNZLEVBQUUsQ0FBQyxTQUFTLENBQUNnQjtnQkFDM0JmLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBeUMsT0FBUDFCLFFBQU8sTUFBSXdDO2dCQUN6RCxJQUFJLENBQUNuQyxRQUFRLEdBQUc7Z0JBRWhCLG9GQUFvRjtnQkFDcEYsSUFBSW1DLGNBQWNBLFdBQVdDLElBQUksSUFBSUQsV0FBV0MsSUFBSSxLQUFLLE1BQU07d0JBRTdEO29CQURBaEIsUUFBUWEsS0FBSyxDQUFDLDhDQUFrRUUsT0FBcEJBLFdBQVdDLElBQUksRUFBQyxNQUFzQixPQUFsQkQsV0FBV0UsTUFBTTtxQkFDakcsaURBQUksQ0FBQ25DLFNBQVMsRUFBQ2dDLE9BQU8sY0FBdEIsNEZBQXlCLElBQUl0QyxNQUFNLHNCQUEyRCxPQUFyQ3VDLFdBQVdFLE1BQU0sSUFBSSxtQkFBb0IxQztnQkFDcEcsT0FBTzt3QkFDTDtxQkFBQSxpREFBSSxDQUFDTyxTQUFTLEVBQUNvQyxNQUFNLGNBQXJCLDJGQUF3QjNDO2dCQUMxQjtZQUNGO1lBRUEsSUFBSSxDQUFDWSxVQUFVLENBQUNZLEVBQUUsQ0FBQyxXQUFXLENBQUNvQjtnQkFDN0JuQixRQUFRb0IsSUFBSSxDQUFDLHdCQUErQixPQUFQN0MsUUFBTyxNQUFJNEM7WUFDbEQ7WUFFQSxzREFBc0Q7WUFDdEQsbURBQW1EO1lBQ25ELElBQUlFLFdBQVc7WUFDZixJQUFJLENBQUNDLGNBQWNDLGVBQWUsQ0FBQ0YsV0FBVztnQkFDNUNBLFdBQVc7Z0JBQ1gsSUFBSSxDQUFDQyxjQUFjQyxlQUFlLENBQUNGLFdBQVc7b0JBQzVDQSxXQUFXO29CQUNYLElBQUksQ0FBQ0MsY0FBY0MsZUFBZSxDQUFDRixXQUFXO3dCQUM1Q0EsV0FBVyxJQUFJLGNBQWM7b0JBQy9CO2dCQUNGO1lBQ0Y7WUFFQXJCLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBOEQxQixPQUE3QjhDLFlBQVksV0FBVSxTQUFjLE9BQVA5QztZQUUxRSxJQUFJLENBQUNpRCxhQUFhLEdBQUcsSUFBSUYsY0FBY2hELGFBQWErQyxXQUFXO2dCQUFFQTtZQUFTLElBQUksQ0FBQztZQUUvRSxJQUFJLENBQUNHLGFBQWEsQ0FBQ0MsZUFBZSxHQUFHLENBQUNDO2dCQUNwQyxJQUFJQSxNQUFNckIsSUFBSSxDQUFDc0IsSUFBSSxHQUFHLEtBQUssSUFBSSxDQUFDeEMsVUFBVSxJQUFJLElBQUksQ0FBQ1AsUUFBUSxFQUFFO29CQUMzRG9CLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBZ0UxQixPQUE3Qm1ELE1BQU1yQixJQUFJLENBQUNzQixJQUFJLEVBQUMsZUFBb0IsT0FBUHBEO29CQUM1RSxJQUFJLENBQUNZLFVBQVUsQ0FBQ3lDLElBQUksQ0FBQ0YsTUFBTXJCLElBQUk7Z0JBQ2pDO1lBQ0Y7WUFFQSxJQUFJLENBQUNtQixhQUFhLENBQUNLLE9BQU8sR0FBRyxDQUFDaEI7b0JBRTVCO2dCQURBYixRQUFRYSxLQUFLLENBQUMsMkJBQWtDLE9BQVB0QyxRQUFPLE1BQUlzQztpQkFDcEQsaURBQUksQ0FBQy9CLFNBQVMsRUFBQ2dDLE9BQU8sY0FBdEIsNEZBQXlCRCxPQUFPdEM7WUFDbEM7WUFFQSxJQUFJLENBQUNpRCxhQUFhLENBQUNNLE9BQU8sR0FBRztnQkFDM0I5QixRQUFRQyxHQUFHLENBQUMsNkJBQW9DLE9BQVAxQjtZQUMzQztZQUVBLElBQUksQ0FBQ2lELGFBQWEsQ0FBQ08sTUFBTSxHQUFHO2dCQUMxQi9CLFFBQVFDLEdBQUcsQ0FBQyw2QkFBb0MsT0FBUDFCO1lBQzNDO1lBRUEsbUNBQW1DO1lBQ25DLElBQUksQ0FBQ2lELGFBQWEsQ0FBQ25ELEtBQUssQ0FBQyxNQUFNLDZDQUE2QztZQUU1RSx5REFBeUQ7WUFDekQsSUFBSSxDQUFDNkIsaUJBQWlCLEdBQUc4QixXQUFXO2dCQUNsQyxJQUFJLENBQUMsSUFBSSxDQUFDcEQsUUFBUSxFQUFFO3dCQUVsQjtvQkFEQW9CLFFBQVFhLEtBQUssQ0FBQyxtQ0FBMEMsT0FBUHRDO3FCQUNqRCxpREFBSSxDQUFDTyxTQUFTLEVBQUNnQyxPQUFPLGNBQXRCLDRGQUF5QixJQUFJdEMsTUFBTSwwRUFBMEVEO29CQUM3RyxJQUFJLENBQUNFLElBQUk7Z0JBQ1g7WUFDRixHQUFHLFFBQVEsb0JBQW9CO1lBRS9CLE9BQU8sSUFBSTtRQUNiLEVBQUUsT0FBT29DLE9BQU87Z0JBRWQ7WUFEQWIsUUFBUWEsS0FBSyxDQUFDLGdDQUF1QyxPQUFQdEMsUUFBTyxNQUFJc0M7YUFDekQsaURBQUksQ0FBQy9CLFNBQVMsRUFBQ2dDLE9BQU8sY0FBdEIsNEZBQXlCRCxPQUFPdEM7WUFDaEMsTUFBTXNDO1FBQ1I7SUFDRjtJQUVBLE1BQU1wQyxPQUFPO1FBQ1gsSUFBSTtZQUNGLElBQUksQ0FBQ0csUUFBUSxHQUFHO1lBRWhCLElBQUksSUFBSSxDQUFDNEMsYUFBYSxJQUFJLElBQUksQ0FBQ0EsYUFBYSxDQUFDUyxLQUFLLEtBQUssWUFBWTtnQkFDakUsSUFBSSxDQUFDVCxhQUFhLENBQUMvQyxJQUFJO1lBQ3pCO1lBRUEsSUFBSSxJQUFJLENBQUNVLFVBQVUsRUFBRTtnQkFDbkIsSUFBSSxDQUFDQSxVQUFVLENBQUMrQyxNQUFNO2dCQUN0QixJQUFJLENBQUMvQyxVQUFVLEdBQUc7WUFDcEI7WUFFQSxJQUFJLENBQUNULE9BQU87UUFDZCxFQUFFLE9BQU9tQyxPQUFPO1lBQ2RiLFFBQVFhLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBbkMsVUFBVTtRQUNSLEtBQUssQ0FBQ0E7UUFDTixJQUFJLENBQUM4QyxhQUFhLEdBQUc7UUFDckIsSUFBSSxDQUFDckMsVUFBVSxHQUFHO1FBQ2xCLElBQUksQ0FBQ0QsUUFBUSxHQUFHO1FBQ2hCLElBQUksQ0FBQ1gsTUFBTSxHQUFHO0lBQ2hCO0lBNUtBLFlBQVlNLE1BQU0sRUFBRUMsU0FBUyxDQUFFO1FBQzdCLEtBQUssQ0FBQ0QsUUFBUUM7UUFDZCxJQUFJLENBQUNJLFFBQVEsR0FBRztRQUNoQixJQUFJLENBQUNDLFVBQVUsR0FBRztRQUNsQixJQUFJLENBQUNxQyxhQUFhLEdBQUc7UUFDckIsSUFBSSxDQUFDakQsTUFBTSxHQUFHO1FBQ2QsSUFBSSxDQUFDMkIsaUJBQWlCLEdBQUc7SUFDM0I7QUFzS0Y7QUFFQTs7Q0FFQyxHQUNNLE1BQU1pQywyQkFBMkIvRDtJQU90QyxNQUFNQyxNQUFNQyxXQUFXLEVBQUVDLE1BQU0sRUFBRTtRQUMvQixJQUFJO2dCQXFERjtZQXBEQSxJQUFJLENBQUMsSUFBSSxDQUFDTSxNQUFNLENBQUN1RCxVQUFVLElBQUksQ0FBQyxJQUFJLENBQUN2RCxNQUFNLENBQUN3RCxXQUFXLEVBQUU7Z0JBQ3ZELE1BQU0sSUFBSTdELE1BQU07WUFDbEI7WUFFQSxJQUFJLENBQUNQLFdBQVc7Z0JBQ2RBLFlBQVksTUFBTSx3WEFBZ0Q7WUFDcEU7WUFFQSx3Q0FBd0M7WUFDeEMsSUFBSSxDQUFDcUUsV0FBVyxHQUFHckUsVUFBVXNFLFdBQVcsQ0FBQ0MsZUFBZSxDQUFDbEU7WUFFekQsdUJBQXVCO1lBQ3ZCLElBQUksQ0FBQ21FLFlBQVksR0FBR3hFLFVBQVV5RSxZQUFZLENBQUNDLGdCQUFnQixDQUN6RCxJQUFJLENBQUM5RCxNQUFNLENBQUN1RCxVQUFVLEVBQ3RCLElBQUksQ0FBQ3ZELE1BQU0sQ0FBQ3dELFdBQVc7WUFFekIsSUFBSSxDQUFDSSxZQUFZLENBQUNHLHlCQUF5QixHQUFHLElBQUksQ0FBQy9ELE1BQU0sQ0FBQ2dFLGFBQWEsSUFBSTtZQUUzRSxvQkFBb0I7WUFDcEIsSUFBSSxDQUFDbEUsVUFBVSxHQUFHLElBQUlWLFVBQVU2RSxnQkFBZ0IsQ0FBQyxJQUFJLENBQUNMLFlBQVksRUFBRSxJQUFJLENBQUNILFdBQVc7WUFFcEYsd0JBQXdCO1lBQ3hCLElBQUksQ0FBQzNELFVBQVUsQ0FBQ29FLFdBQVcsR0FBRyxDQUFDQyxHQUFHQztnQkFDaEMsSUFBSUEsRUFBRUMsTUFBTSxDQUFDakMsTUFBTSxLQUFLaEQsVUFBVWtGLFlBQVksQ0FBQ0MsaUJBQWlCLEVBQUU7d0JBQ2hFO3FCQUFBLHlEQUFJLENBQUN0RSxTQUFTLEVBQUM4QixlQUFlLGNBQTlCLDRHQUFpQ3FDLEVBQUVDLE1BQU0sQ0FBQ0csSUFBSSxFQUFFOUU7Z0JBQ2xEO1lBQ0Y7WUFFQSxJQUFJLENBQUNJLFVBQVUsQ0FBQzJFLFVBQVUsR0FBRyxDQUFDTixHQUFHQztnQkFDL0IsSUFBSUEsRUFBRUMsTUFBTSxDQUFDakMsTUFBTSxLQUFLaEQsVUFBVWtGLFlBQVksQ0FBQ0ksZ0JBQWdCLElBQUlOLEVBQUVDLE1BQU0sQ0FBQ0csSUFBSSxFQUFFO3dCQUNoRjtxQkFBQSx1REFBSSxDQUFDdkUsU0FBUyxFQUFDNkIsYUFBYSxjQUE1Qix3R0FBK0JzQyxFQUFFQyxNQUFNLENBQUNHLElBQUksRUFBRTlFO2dCQUNoRDtZQUNGO1lBRUEsSUFBSSxDQUFDSSxVQUFVLENBQUM2RSxRQUFRLEdBQUcsQ0FBQ1IsR0FBR0M7Z0JBQzdCakQsUUFBUUMsR0FBRyxDQUFDLGtDQUE2Q2dELE9BQVgxRSxRQUFPLE1BQWEsT0FBVDBFLEVBQUVoQyxNQUFNO2dCQUNqRSxJQUFJZ0MsRUFBRWhDLE1BQU0sS0FBS2hELFVBQVV3RixrQkFBa0IsQ0FBQ2pGLEtBQUssRUFBRTt3QkFFbkQ7b0JBREEsTUFBTXFDLFFBQVEsSUFBSXJDLE1BQU0sdUJBQXNDLE9BQWZ5RSxFQUFFUyxZQUFZO3FCQUM3RCxpREFBSSxDQUFDNUUsU0FBUyxFQUFDZ0MsT0FBTyxjQUF0Qiw0RkFBeUJELE9BQU90QztnQkFDbEM7Z0JBQ0EsSUFBSSxDQUFDRSxJQUFJO1lBQ1g7WUFFQSxJQUFJLENBQUNFLFVBQVUsQ0FBQ2dGLGNBQWMsR0FBRyxDQUFDWCxHQUFHQztvQkFFbkM7Z0JBREFqRCxRQUFRQyxHQUFHLENBQUMsNkJBQW9DLE9BQVAxQjtpQkFDekMsZ0RBQUksQ0FBQ08sU0FBUyxFQUFDb0MsTUFBTSxjQUFyQiwwRkFBd0IzQztnQkFDeEIsSUFBSSxDQUFDRSxJQUFJO1lBQ1g7WUFFQSwrQkFBK0I7WUFDL0IsTUFBTSxJQUFJLENBQUNFLFVBQVUsQ0FBQ2lGLCtCQUErQjtZQUNyRCxJQUFJLENBQUNoRixRQUFRLEdBQUc7YUFDaEIsaURBQUksQ0FBQ0UsU0FBUyxFQUFDc0IsT0FBTyxjQUF0Qiw0RkFBeUI3QjtZQUV6QixPQUFPLElBQUk7UUFDYixFQUFFLE9BQU9zQyxPQUFPO2dCQUVkO1lBREFiLFFBQVFhLEtBQUssQ0FBQyxvQ0FBMkMsT0FBUHRDLFFBQU8sTUFBSXNDO2FBQzdELGtEQUFJLENBQUMvQixTQUFTLEVBQUNnQyxPQUFPLGNBQXRCLDZGQUF5QkQsT0FBT3RDO1lBQ2hDLE1BQU1zQztRQUNSO0lBQ0Y7SUFFQSxNQUFNcEMsT0FBTztRQUNYLElBQUk7WUFDRixJQUFJLENBQUNHLFFBQVEsR0FBRztZQUVoQixJQUFJLElBQUksQ0FBQ0QsVUFBVSxJQUFJLE9BQU8sSUFBSSxDQUFDQSxVQUFVLENBQUNrRiw4QkFBOEIsS0FBSyxZQUFZO2dCQUMzRixNQUFNLElBQUksQ0FBQ2xGLFVBQVUsQ0FBQ2tGLDhCQUE4QjtZQUN0RDtZQUVBLElBQUksSUFBSSxDQUFDdkIsV0FBVyxJQUFJLE9BQU8sSUFBSSxDQUFDQSxXQUFXLENBQUN3QixLQUFLLEtBQUssWUFBWTtnQkFDcEUsSUFBSSxDQUFDeEIsV0FBVyxDQUFDd0IsS0FBSztZQUN4QjtZQUVBLElBQUksQ0FBQ3BGLE9BQU87UUFDZCxFQUFFLE9BQU9tQyxPQUFPO1lBQ2RiLFFBQVFhLEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBbkMsVUFBVTtRQUNSLEtBQUssQ0FBQ0E7UUFDTixJQUFJLENBQUM0RCxXQUFXLEdBQUc7UUFDbkIsSUFBSSxDQUFDRyxZQUFZLEdBQUc7SUFDdEI7SUE3RkEsWUFBWTVELE1BQU0sRUFBRUMsU0FBUyxDQUFFO1FBQzdCLEtBQUssQ0FBQ0QsUUFBUUM7UUFDZCxJQUFJLENBQUN3RCxXQUFXLEdBQUc7UUFDbkIsSUFBSSxDQUFDRyxZQUFZLEdBQUc7SUFDdEI7QUEwRkY7QUFFQTs7Q0FFQyxHQUNNLFNBQVNzQixvQkFBb0JsRixNQUFNLEVBQUVDLFNBQVM7SUFDbkQsTUFBTWtGLGNBQWNuRixPQUFPb0YsYUFBYSxJQUFJO0lBRTVDLE9BQVFEO1FBQ04sS0FBSztZQUNILE9BQU8sSUFBSWpGLHNCQUFzQkYsUUFBUUM7UUFDM0MsS0FBSztZQUNILE9BQU8sSUFBSXFELG1CQUFtQnRELFFBQVFDO1FBQ3hDO1lBQ0UsTUFBTSxJQUFJTixNQUFNLDJCQUF1QyxPQUFad0Y7SUFDL0M7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL3N1cmVuZHJhZ2FubmUvcmVwb3MvbXljb3BpbG90L3V0aWxzL3NwZWVjaFNlcnZpY2VzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIER5bmFtaWMgaW1wb3J0cyBmb3IgY2xpZW50LXNpZGUgb25seVxubGV0IGNyZWF0ZUNsaWVudCA9IG51bGw7XG5sZXQgU3BlZWNoU0RLID0gbnVsbDtcblxuLy8gSW5pdGlhbGl6ZSBjbGllbnQtc2lkZSBkZXBlbmRlbmNpZXNcbmlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICBpbXBvcnQoJ0BkZWVwZ3JhbS9zZGsnKS50aGVuKG1vZHVsZSA9PiB7XG4gICAgY3JlYXRlQ2xpZW50ID0gbW9kdWxlLmNyZWF0ZUNsaWVudDtcbiAgfSk7XG4gIGltcG9ydCgnbWljcm9zb2Z0LWNvZ25pdGl2ZXNlcnZpY2VzLXNwZWVjaC1zZGsnKS50aGVuKG1vZHVsZSA9PiB7XG4gICAgU3BlZWNoU0RLID0gbW9kdWxlO1xuICB9KTtcbn1cblxuLyoqXG4gKiBBYnN0cmFjdCBiYXNlIGNsYXNzIGZvciBzcGVlY2ggc2VydmljZXNcbiAqL1xuY2xhc3MgU3BlZWNoU2VydmljZSB7XG4gIGNvbnN0cnVjdG9yKGNvbmZpZywgY2FsbGJhY2tzKSB7XG4gICAgdGhpcy5jb25maWcgPSBjb25maWc7XG4gICAgdGhpcy5jYWxsYmFja3MgPSBjYWxsYmFja3M7XG4gICAgdGhpcy5pc0FjdGl2ZSA9IGZhbHNlO1xuICAgIHRoaXMucmVjb2duaXplciA9IG51bGw7XG4gIH1cblxuICBhc3luYyBzdGFydChtZWRpYVN0cmVhbSwgc291cmNlKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdzdGFydCBtZXRob2QgbXVzdCBiZSBpbXBsZW1lbnRlZCcpO1xuICB9XG5cbiAgYXN5bmMgc3RvcCgpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3N0b3AgbWV0aG9kIG11c3QgYmUgaW1wbGVtZW50ZWQnKTtcbiAgfVxuXG4gIGNsZWFudXAoKSB7XG4gICAgLy8gRGVmYXVsdCBjbGVhbnVwIGltcGxlbWVudGF0aW9uXG4gICAgaWYgKHRoaXMucmVjb2duaXplcikge1xuICAgICAgdGhpcy5yZWNvZ25pemVyID0gbnVsbDtcbiAgICB9XG4gICAgdGhpcy5pc0FjdGl2ZSA9IGZhbHNlO1xuICB9XG59XG5cbi8qKlxuICogRGVlcGdyYW0gU3BlZWNoIFNlcnZpY2UgSW1wbGVtZW50YXRpb25cbiAqL1xuZXhwb3J0IGNsYXNzIERlZXBncmFtU3BlZWNoU2VydmljZSBleHRlbmRzIFNwZWVjaFNlcnZpY2Uge1xuICBjb25zdHJ1Y3Rvcihjb25maWcsIGNhbGxiYWNrcykge1xuICAgIHN1cGVyKGNvbmZpZywgY2FsbGJhY2tzKTtcbiAgICB0aGlzLmRlZXBncmFtID0gbnVsbDtcbiAgICB0aGlzLmNvbm5lY3Rpb24gPSBudWxsO1xuICAgIHRoaXMubWVkaWFSZWNvcmRlciA9IG51bGw7XG4gICAgdGhpcy5zb3VyY2UgPSBudWxsO1xuICAgIHRoaXMuY29ubmVjdGlvblRpbWVvdXQgPSBudWxsO1xuICB9XG5cbiAgYXN5bmMgc3RhcnQobWVkaWFTdHJlYW0sIHNvdXJjZSkge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIXRoaXMuY29uZmlnLmRlZXBncmFtS2V5KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRGVlcGdyYW0gQVBJIGtleSBpcyByZXF1aXJlZCcpO1xuICAgICAgfVxuXG4gICAgICBpZiAoIWNyZWF0ZUNsaWVudCkge1xuICAgICAgICBjb25zdCB7IGNyZWF0ZUNsaWVudDogZGdDcmVhdGVDbGllbnQgfSA9IGF3YWl0IGltcG9ydCgnQGRlZXBncmFtL3NkaycpO1xuICAgICAgICBjcmVhdGVDbGllbnQgPSBkZ0NyZWF0ZUNsaWVudDtcbiAgICAgIH1cblxuICAgICAgdGhpcy5zb3VyY2UgPSBzb3VyY2U7XG4gICAgICB0aGlzLmRlZXBncmFtID0gY3JlYXRlQ2xpZW50KHRoaXMuY29uZmlnLmRlZXBncmFtS2V5KTtcblxuICAgICAgLy8gQ3JlYXRlIFdlYlNvY2tldCBjb25uZWN0aW9uIGZvciByZWFsLXRpbWUgdHJhbnNjcmlwdGlvblxuICAgICAgdGhpcy5jb25uZWN0aW9uID0gdGhpcy5kZWVwZ3JhbS5saXN0ZW4ubGl2ZSh7XG4gICAgICAgIG1vZGVsOiB0aGlzLmNvbmZpZy5kZWVwZ3JhbU1vZGVsIHx8ICdub3ZhLTInLFxuICAgICAgICBsYW5ndWFnZTogdGhpcy5jb25maWcuZGVlcGdyYW1MYW5ndWFnZSB8fCAnZW4tVVMnLFxuICAgICAgICBzbWFydF9mb3JtYXQ6IHRydWUsXG4gICAgICAgIGludGVyaW1fcmVzdWx0czogdHJ1ZSxcbiAgICAgICAgZW5kcG9pbnRpbmc6IDMwMCwgLy8gMzAwbXMgb2Ygc2lsZW5jZSBiZWZvcmUgZmluYWxpemluZ1xuICAgICAgICB1dHRlcmFuY2VfZW5kX21zOiAxMDAwLFxuICAgICAgICB2YWRfZXZlbnRzOiB0cnVlLFxuICAgICAgfSk7XG5cbiAgICAgIC8vIFNldCB1cCBldmVudCBoYW5kbGVyc1xuICAgICAgdGhpcy5jb25uZWN0aW9uLm9uKCdvcGVuJywgKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZyhgRGVlcGdyYW0gY29ubmVjdGlvbiBvcGVuZWQgZm9yICR7c291cmNlfWApO1xuICAgICAgICB0aGlzLmlzQWN0aXZlID0gdHJ1ZTtcblxuICAgICAgICAvLyBDbGVhciBhbnkgY29ubmVjdGlvbiB0aW1lb3V0XG4gICAgICAgIGlmICh0aGlzLmNvbm5lY3Rpb25UaW1lb3V0KSB7XG4gICAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuY29ubmVjdGlvblRpbWVvdXQpO1xuICAgICAgICAgIHRoaXMuY29ubmVjdGlvblRpbWVvdXQgPSBudWxsO1xuICAgICAgICB9XG5cbiAgICAgICAgdGhpcy5jYWxsYmFja3Mub25TdGFydD8uKHNvdXJjZSk7XG4gICAgICB9KTtcblxuICAgICAgdGhpcy5jb25uZWN0aW9uLm9uKCdSZXN1bHRzJywgKGRhdGEpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coYERlZXBncmFtIHJlc3VsdHMgZm9yICR7c291cmNlfTpgLCBkYXRhKTtcbiAgICAgICAgY29uc3QgdHJhbnNjcmlwdCA9IGRhdGEuY2hhbm5lbD8uYWx0ZXJuYXRpdmVzPy5bMF0/LnRyYW5zY3JpcHQ7XG4gICAgICAgIGlmICh0cmFuc2NyaXB0ICYmIHRyYW5zY3JpcHQudHJpbSgpKSB7XG4gICAgICAgICAgaWYgKGRhdGEuaXNfZmluYWwpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBGaW5hbCB0cmFuc2NyaXB0IGZvciAke3NvdXJjZX06IFwiJHt0cmFuc2NyaXB0fVwiYCk7XG4gICAgICAgICAgICB0aGlzLmNhbGxiYWNrcy5vbkZpbmFsUmVzdWx0Py4odHJhbnNjcmlwdCwgc291cmNlKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYEludGVyaW0gdHJhbnNjcmlwdCBmb3IgJHtzb3VyY2V9OiBcIiR7dHJhbnNjcmlwdH1cImApO1xuICAgICAgICAgICAgdGhpcy5jYWxsYmFja3Mub25JbnRlcmltUmVzdWx0Py4odHJhbnNjcmlwdCwgc291cmNlKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICB0aGlzLmNvbm5lY3Rpb24ub24oJ01ldGFkYXRhJywgKGRhdGEpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coYERlZXBncmFtIG1ldGFkYXRhIGZvciAke3NvdXJjZX06YCwgZGF0YSk7XG4gICAgICB9KTtcblxuICAgICAgdGhpcy5jb25uZWN0aW9uLm9uKCdlcnJvcicsIChlcnJvcikgPT4ge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBEZWVwZ3JhbSBlcnJvciBmb3IgJHtzb3VyY2V9OmAsIGVycm9yKTtcbiAgICAgICAgdGhpcy5jYWxsYmFja3Mub25FcnJvcj8uKGVycm9yLCBzb3VyY2UpO1xuICAgICAgICB0aGlzLnN0b3AoKTtcbiAgICAgIH0pO1xuXG4gICAgICB0aGlzLmNvbm5lY3Rpb24ub24oJ2Nsb3NlJywgKGNsb3NlRXZlbnQpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coYERlZXBncmFtIGNvbm5lY3Rpb24gY2xvc2VkIGZvciAke3NvdXJjZX06YCwgY2xvc2VFdmVudCk7XG4gICAgICAgIHRoaXMuaXNBY3RpdmUgPSBmYWxzZTtcblxuICAgICAgICAvLyBJZiBjb25uZWN0aW9uIGNsb3NlcyBpbW1lZGlhdGVseSBhZnRlciBvcGVuaW5nLCBpdCBtaWdodCBiZSBhIGNvbmZpZ3VyYXRpb24gaXNzdWVcbiAgICAgICAgaWYgKGNsb3NlRXZlbnQgJiYgY2xvc2VFdmVudC5jb2RlICYmIGNsb3NlRXZlbnQuY29kZSAhPT0gMTAwMCkge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYERlZXBncmFtIGNvbm5lY3Rpb24gY2xvc2VkIHdpdGggZXJyb3IgY29kZSAke2Nsb3NlRXZlbnQuY29kZX06ICR7Y2xvc2VFdmVudC5yZWFzb259YCk7XG4gICAgICAgICAgdGhpcy5jYWxsYmFja3Mub25FcnJvcj8uKG5ldyBFcnJvcihgQ29ubmVjdGlvbiBjbG9zZWQ6ICR7Y2xvc2VFdmVudC5yZWFzb24gfHwgJ1Vua25vd24gZXJyb3InfWApLCBzb3VyY2UpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRoaXMuY2FsbGJhY2tzLm9uU3RvcD8uKHNvdXJjZSk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICB0aGlzLmNvbm5lY3Rpb24ub24oJ3dhcm5pbmcnLCAod2FybmluZykgPT4ge1xuICAgICAgICBjb25zb2xlLndhcm4oYERlZXBncmFtIHdhcm5pbmcgZm9yICR7c291cmNlfTpgLCB3YXJuaW5nKTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBTZXQgdXAgTWVkaWFSZWNvcmRlciB0byBzZW5kIGF1ZGlvIGRhdGEgdG8gRGVlcGdyYW1cbiAgICAgIC8vIFRyeSBkaWZmZXJlbnQgbWltZVR5cGVzIGZvciBiZXR0ZXIgY29tcGF0aWJpbGl0eVxuICAgICAgbGV0IG1pbWVUeXBlID0gJ2F1ZGlvL3dlYm07Y29kZWNzPW9wdXMnO1xuICAgICAgaWYgKCFNZWRpYVJlY29yZGVyLmlzVHlwZVN1cHBvcnRlZChtaW1lVHlwZSkpIHtcbiAgICAgICAgbWltZVR5cGUgPSAnYXVkaW8vd2VibSc7XG4gICAgICAgIGlmICghTWVkaWFSZWNvcmRlci5pc1R5cGVTdXBwb3J0ZWQobWltZVR5cGUpKSB7XG4gICAgICAgICAgbWltZVR5cGUgPSAnYXVkaW8vbXA0JztcbiAgICAgICAgICBpZiAoIU1lZGlhUmVjb3JkZXIuaXNUeXBlU3VwcG9ydGVkKG1pbWVUeXBlKSkge1xuICAgICAgICAgICAgbWltZVR5cGUgPSAnJzsgLy8gVXNlIGRlZmF1bHRcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coYFVzaW5nIE1lZGlhUmVjb3JkZXIgbWltZVR5cGU6ICR7bWltZVR5cGUgfHwgJ2RlZmF1bHQnfSBmb3IgJHtzb3VyY2V9YCk7XG5cbiAgICAgIHRoaXMubWVkaWFSZWNvcmRlciA9IG5ldyBNZWRpYVJlY29yZGVyKG1lZGlhU3RyZWFtLCBtaW1lVHlwZSA/IHsgbWltZVR5cGUgfSA6IHt9KTtcblxuICAgICAgdGhpcy5tZWRpYVJlY29yZGVyLm9uZGF0YWF2YWlsYWJsZSA9IChldmVudCkgPT4ge1xuICAgICAgICBpZiAoZXZlbnQuZGF0YS5zaXplID4gMCAmJiB0aGlzLmNvbm5lY3Rpb24gJiYgdGhpcy5pc0FjdGl2ZSkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBTZW5kaW5nIGF1ZGlvIGRhdGEgdG8gRGVlcGdyYW06ICR7ZXZlbnQuZGF0YS5zaXplfSBieXRlcyBmb3IgJHtzb3VyY2V9YCk7XG4gICAgICAgICAgdGhpcy5jb25uZWN0aW9uLnNlbmQoZXZlbnQuZGF0YSk7XG4gICAgICAgIH1cbiAgICAgIH07XG5cbiAgICAgIHRoaXMubWVkaWFSZWNvcmRlci5vbmVycm9yID0gKGVycm9yKSA9PiB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYE1lZGlhUmVjb3JkZXIgZXJyb3IgZm9yICR7c291cmNlfTpgLCBlcnJvcik7XG4gICAgICAgIHRoaXMuY2FsbGJhY2tzLm9uRXJyb3I/LihlcnJvciwgc291cmNlKTtcbiAgICAgIH07XG5cbiAgICAgIHRoaXMubWVkaWFSZWNvcmRlci5vbnN0YXJ0ID0gKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZyhgTWVkaWFSZWNvcmRlciBzdGFydGVkIGZvciAke3NvdXJjZX1gKTtcbiAgICAgIH07XG5cbiAgICAgIHRoaXMubWVkaWFSZWNvcmRlci5vbnN0b3AgPSAoKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKGBNZWRpYVJlY29yZGVyIHN0b3BwZWQgZm9yICR7c291cmNlfWApO1xuICAgICAgfTtcblxuICAgICAgLy8gU3RhcnQgcmVjb3JkaW5nIGFuZCBzZW5kaW5nIGRhdGFcbiAgICAgIHRoaXMubWVkaWFSZWNvcmRlci5zdGFydCgyNTApOyAvLyBTZW5kIGRhdGEgZXZlcnkgMjUwbXMgZm9yIGJldHRlciBzdGFiaWxpdHlcblxuICAgICAgLy8gU2V0IHVwIGNvbm5lY3Rpb24gdGltZW91dCB0byBkZXRlY3QgZmFpbGVkIGNvbm5lY3Rpb25zXG4gICAgICB0aGlzLmNvbm5lY3Rpb25UaW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIGlmICghdGhpcy5pc0FjdGl2ZSkge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYERlZXBncmFtIGNvbm5lY3Rpb24gdGltZW91dCBmb3IgJHtzb3VyY2V9YCk7XG4gICAgICAgICAgdGhpcy5jYWxsYmFja3Mub25FcnJvcj8uKG5ldyBFcnJvcignQ29ubmVjdGlvbiB0aW1lb3V0IC0gcGxlYXNlIGNoZWNrIHlvdXIgQVBJIGtleSBhbmQgbmV0d29yayBjb25uZWN0aW9uJyksIHNvdXJjZSk7XG4gICAgICAgICAgdGhpcy5zdG9wKCk7XG4gICAgICAgIH1cbiAgICAgIH0sIDEwMDAwKTsgLy8gMTAgc2Vjb25kIHRpbWVvdXRcblxuICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byBzdGFydCBEZWVwZ3JhbSBmb3IgJHtzb3VyY2V9OmAsIGVycm9yKTtcbiAgICAgIHRoaXMuY2FsbGJhY2tzLm9uRXJyb3I/LihlcnJvciwgc291cmNlKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIHN0b3AoKSB7XG4gICAgdHJ5IHtcbiAgICAgIHRoaXMuaXNBY3RpdmUgPSBmYWxzZTtcblxuICAgICAgaWYgKHRoaXMubWVkaWFSZWNvcmRlciAmJiB0aGlzLm1lZGlhUmVjb3JkZXIuc3RhdGUgIT09ICdpbmFjdGl2ZScpIHtcbiAgICAgICAgdGhpcy5tZWRpYVJlY29yZGVyLnN0b3AoKTtcbiAgICAgIH1cblxuICAgICAgaWYgKHRoaXMuY29ubmVjdGlvbikge1xuICAgICAgICB0aGlzLmNvbm5lY3Rpb24uZmluaXNoKCk7XG4gICAgICAgIHRoaXMuY29ubmVjdGlvbiA9IG51bGw7XG4gICAgICB9XG5cbiAgICAgIHRoaXMuY2xlYW51cCgpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzdG9wcGluZyBEZWVwZ3JhbTonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICBjbGVhbnVwKCkge1xuICAgIHN1cGVyLmNsZWFudXAoKTtcbiAgICB0aGlzLm1lZGlhUmVjb3JkZXIgPSBudWxsO1xuICAgIHRoaXMuY29ubmVjdGlvbiA9IG51bGw7XG4gICAgdGhpcy5kZWVwZ3JhbSA9IG51bGw7XG4gICAgdGhpcy5zb3VyY2UgPSBudWxsO1xuICB9XG59XG5cbi8qKlxuICogQXp1cmUgU3BlZWNoIFNlcnZpY2UgSW1wbGVtZW50YXRpb24gKExlZ2FjeSlcbiAqL1xuZXhwb3J0IGNsYXNzIEF6dXJlU3BlZWNoU2VydmljZSBleHRlbmRzIFNwZWVjaFNlcnZpY2Uge1xuICBjb25zdHJ1Y3Rvcihjb25maWcsIGNhbGxiYWNrcykge1xuICAgIHN1cGVyKGNvbmZpZywgY2FsbGJhY2tzKTtcbiAgICB0aGlzLmF1ZGlvQ29uZmlnID0gbnVsbDtcbiAgICB0aGlzLnNwZWVjaENvbmZpZyA9IG51bGw7XG4gIH1cblxuICBhc3luYyBzdGFydChtZWRpYVN0cmVhbSwgc291cmNlKSB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICghdGhpcy5jb25maWcuYXp1cmVUb2tlbiB8fCAhdGhpcy5jb25maWcuYXp1cmVSZWdpb24pIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBenVyZSBTcGVlY2ggY3JlZGVudGlhbHMgYXJlIHJlcXVpcmVkJyk7XG4gICAgICB9XG5cbiAgICAgIGlmICghU3BlZWNoU0RLKSB7XG4gICAgICAgIFNwZWVjaFNESyA9IGF3YWl0IGltcG9ydCgnbWljcm9zb2Z0LWNvZ25pdGl2ZXNlcnZpY2VzLXNwZWVjaC1zZGsnKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIGF1ZGlvIGNvbmZpZyBmcm9tIG1lZGlhIHN0cmVhbVxuICAgICAgdGhpcy5hdWRpb0NvbmZpZyA9IFNwZWVjaFNESy5BdWRpb0NvbmZpZy5mcm9tU3RyZWFtSW5wdXQobWVkaWFTdHJlYW0pO1xuICAgICAgXG4gICAgICAvLyBDcmVhdGUgc3BlZWNoIGNvbmZpZ1xuICAgICAgdGhpcy5zcGVlY2hDb25maWcgPSBTcGVlY2hTREsuU3BlZWNoQ29uZmlnLmZyb21TdWJzY3JpcHRpb24oXG4gICAgICAgIHRoaXMuY29uZmlnLmF6dXJlVG9rZW4sIFxuICAgICAgICB0aGlzLmNvbmZpZy5henVyZVJlZ2lvblxuICAgICAgKTtcbiAgICAgIHRoaXMuc3BlZWNoQ29uZmlnLnNwZWVjaFJlY29nbml0aW9uTGFuZ3VhZ2UgPSB0aGlzLmNvbmZpZy5henVyZUxhbmd1YWdlIHx8ICdlbi1VUyc7XG5cbiAgICAgIC8vIENyZWF0ZSByZWNvZ25pemVyXG4gICAgICB0aGlzLnJlY29nbml6ZXIgPSBuZXcgU3BlZWNoU0RLLlNwZWVjaFJlY29nbml6ZXIodGhpcy5zcGVlY2hDb25maWcsIHRoaXMuYXVkaW9Db25maWcpO1xuXG4gICAgICAvLyBTZXQgdXAgZXZlbnQgaGFuZGxlcnNcbiAgICAgIHRoaXMucmVjb2duaXplci5yZWNvZ25pemluZyA9IChzLCBlKSA9PiB7XG4gICAgICAgIGlmIChlLnJlc3VsdC5yZWFzb24gPT09IFNwZWVjaFNESy5SZXN1bHRSZWFzb24uUmVjb2duaXppbmdTcGVlY2gpIHtcbiAgICAgICAgICB0aGlzLmNhbGxiYWNrcy5vbkludGVyaW1SZXN1bHQ/LihlLnJlc3VsdC50ZXh0LCBzb3VyY2UpO1xuICAgICAgICB9XG4gICAgICB9O1xuXG4gICAgICB0aGlzLnJlY29nbml6ZXIucmVjb2duaXplZCA9IChzLCBlKSA9PiB7XG4gICAgICAgIGlmIChlLnJlc3VsdC5yZWFzb24gPT09IFNwZWVjaFNESy5SZXN1bHRSZWFzb24uUmVjb2duaXplZFNwZWVjaCAmJiBlLnJlc3VsdC50ZXh0KSB7XG4gICAgICAgICAgdGhpcy5jYWxsYmFja3Mub25GaW5hbFJlc3VsdD8uKGUucmVzdWx0LnRleHQsIHNvdXJjZSk7XG4gICAgICAgIH1cbiAgICAgIH07XG5cbiAgICAgIHRoaXMucmVjb2duaXplci5jYW5jZWxlZCA9IChzLCBlKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKGBBenVyZSByZWNvZ25pdGlvbiBjYW5jZWxlZCBmb3IgJHtzb3VyY2V9OiAke2UucmVhc29ufWApO1xuICAgICAgICBpZiAoZS5yZWFzb24gPT09IFNwZWVjaFNESy5DYW5jZWxsYXRpb25SZWFzb24uRXJyb3IpIHtcbiAgICAgICAgICBjb25zdCBlcnJvciA9IG5ldyBFcnJvcihgQXp1cmUgU3BlZWNoIGVycm9yOiAke2UuZXJyb3JEZXRhaWxzfWApO1xuICAgICAgICAgIHRoaXMuY2FsbGJhY2tzLm9uRXJyb3I/LihlcnJvciwgc291cmNlKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnN0b3AoKTtcbiAgICAgIH07XG5cbiAgICAgIHRoaXMucmVjb2duaXplci5zZXNzaW9uU3RvcHBlZCA9IChzLCBlKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKGBBenVyZSBzZXNzaW9uIHN0b3BwZWQgZm9yICR7c291cmNlfWApO1xuICAgICAgICB0aGlzLmNhbGxiYWNrcy5vblN0b3A/Lihzb3VyY2UpO1xuICAgICAgICB0aGlzLnN0b3AoKTtcbiAgICAgIH07XG5cbiAgICAgIC8vIFN0YXJ0IGNvbnRpbnVvdXMgcmVjb2duaXRpb25cbiAgICAgIGF3YWl0IHRoaXMucmVjb2duaXplci5zdGFydENvbnRpbnVvdXNSZWNvZ25pdGlvbkFzeW5jKCk7XG4gICAgICB0aGlzLmlzQWN0aXZlID0gdHJ1ZTtcbiAgICAgIHRoaXMuY2FsbGJhY2tzLm9uU3RhcnQ/Lihzb3VyY2UpO1xuXG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihgRmFpbGVkIHRvIHN0YXJ0IEF6dXJlIFNwZWVjaCBmb3IgJHtzb3VyY2V9OmAsIGVycm9yKTtcbiAgICAgIHRoaXMuY2FsbGJhY2tzLm9uRXJyb3I/LihlcnJvciwgc291cmNlKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIHN0b3AoKSB7XG4gICAgdHJ5IHtcbiAgICAgIHRoaXMuaXNBY3RpdmUgPSBmYWxzZTtcblxuICAgICAgaWYgKHRoaXMucmVjb2duaXplciAmJiB0eXBlb2YgdGhpcy5yZWNvZ25pemVyLnN0b3BDb250aW51b3VzUmVjb2duaXRpb25Bc3luYyA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICBhd2FpdCB0aGlzLnJlY29nbml6ZXIuc3RvcENvbnRpbnVvdXNSZWNvZ25pdGlvbkFzeW5jKCk7XG4gICAgICB9XG5cbiAgICAgIGlmICh0aGlzLmF1ZGlvQ29uZmlnICYmIHR5cGVvZiB0aGlzLmF1ZGlvQ29uZmlnLmNsb3NlID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHRoaXMuYXVkaW9Db25maWcuY2xvc2UoKTtcbiAgICAgIH1cblxuICAgICAgdGhpcy5jbGVhbnVwKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHN0b3BwaW5nIEF6dXJlIFNwZWVjaDonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICBjbGVhbnVwKCkge1xuICAgIHN1cGVyLmNsZWFudXAoKTtcbiAgICB0aGlzLmF1ZGlvQ29uZmlnID0gbnVsbDtcbiAgICB0aGlzLnNwZWVjaENvbmZpZyA9IG51bGw7XG4gIH1cbn1cblxuLyoqXG4gKiBGYWN0b3J5IGZ1bmN0aW9uIHRvIGNyZWF0ZSBzcGVlY2ggc2VydmljZSBpbnN0YW5jZXNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVNwZWVjaFNlcnZpY2UoY29uZmlnLCBjYWxsYmFja3MpIHtcbiAgY29uc3Qgc2VydmljZVR5cGUgPSBjb25maWcuc3BlZWNoU2VydmljZSB8fCAnZGVlcGdyYW0nO1xuICBcbiAgc3dpdGNoIChzZXJ2aWNlVHlwZSkge1xuICAgIGNhc2UgJ2RlZXBncmFtJzpcbiAgICAgIHJldHVybiBuZXcgRGVlcGdyYW1TcGVlY2hTZXJ2aWNlKGNvbmZpZywgY2FsbGJhY2tzKTtcbiAgICBjYXNlICdhenVyZSc6XG4gICAgICByZXR1cm4gbmV3IEF6dXJlU3BlZWNoU2VydmljZShjb25maWcsIGNhbGxiYWNrcyk7XG4gICAgZGVmYXVsdDpcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVW5rbm93biBzcGVlY2ggc2VydmljZTogJHtzZXJ2aWNlVHlwZX1gKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsIlNwZWVjaFNESyIsInRoZW4iLCJtb2R1bGUiLCJTcGVlY2hTZXJ2aWNlIiwic3RhcnQiLCJtZWRpYVN0cmVhbSIsInNvdXJjZSIsIkVycm9yIiwic3RvcCIsImNsZWFudXAiLCJyZWNvZ25pemVyIiwiaXNBY3RpdmUiLCJjb25maWciLCJjYWxsYmFja3MiLCJEZWVwZ3JhbVNwZWVjaFNlcnZpY2UiLCJkZWVwZ3JhbUtleSIsImRnQ3JlYXRlQ2xpZW50IiwiZGVlcGdyYW0iLCJjb25uZWN0aW9uIiwibGlzdGVuIiwibGl2ZSIsIm1vZGVsIiwiZGVlcGdyYW1Nb2RlbCIsImxhbmd1YWdlIiwiZGVlcGdyYW1MYW5ndWFnZSIsInNtYXJ0X2Zvcm1hdCIsImludGVyaW1fcmVzdWx0cyIsImVuZHBvaW50aW5nIiwidXR0ZXJhbmNlX2VuZF9tcyIsInZhZF9ldmVudHMiLCJvbiIsImNvbnNvbGUiLCJsb2ciLCJjb25uZWN0aW9uVGltZW91dCIsImNsZWFyVGltZW91dCIsIm9uU3RhcnQiLCJkYXRhIiwidHJhbnNjcmlwdCIsImNoYW5uZWwiLCJhbHRlcm5hdGl2ZXMiLCJ0cmltIiwiaXNfZmluYWwiLCJvbkZpbmFsUmVzdWx0Iiwib25JbnRlcmltUmVzdWx0IiwiZXJyb3IiLCJvbkVycm9yIiwiY2xvc2VFdmVudCIsImNvZGUiLCJyZWFzb24iLCJvblN0b3AiLCJ3YXJuaW5nIiwid2FybiIsIm1pbWVUeXBlIiwiTWVkaWFSZWNvcmRlciIsImlzVHlwZVN1cHBvcnRlZCIsIm1lZGlhUmVjb3JkZXIiLCJvbmRhdGFhdmFpbGFibGUiLCJldmVudCIsInNpemUiLCJzZW5kIiwib25lcnJvciIsIm9uc3RhcnQiLCJvbnN0b3AiLCJzZXRUaW1lb3V0Iiwic3RhdGUiLCJmaW5pc2giLCJBenVyZVNwZWVjaFNlcnZpY2UiLCJhenVyZVRva2VuIiwiYXp1cmVSZWdpb24iLCJhdWRpb0NvbmZpZyIsIkF1ZGlvQ29uZmlnIiwiZnJvbVN0cmVhbUlucHV0Iiwic3BlZWNoQ29uZmlnIiwiU3BlZWNoQ29uZmlnIiwiZnJvbVN1YnNjcmlwdGlvbiIsInNwZWVjaFJlY29nbml0aW9uTGFuZ3VhZ2UiLCJhenVyZUxhbmd1YWdlIiwiU3BlZWNoUmVjb2duaXplciIsInJlY29nbml6aW5nIiwicyIsImUiLCJyZXN1bHQiLCJSZXN1bHRSZWFzb24iLCJSZWNvZ25pemluZ1NwZWVjaCIsInRleHQiLCJyZWNvZ25pemVkIiwiUmVjb2duaXplZFNwZWVjaCIsImNhbmNlbGVkIiwiQ2FuY2VsbGF0aW9uUmVhc29uIiwiZXJyb3JEZXRhaWxzIiwic2Vzc2lvblN0b3BwZWQiLCJzdGFydENvbnRpbnVvdXNSZWNvZ25pdGlvbkFzeW5jIiwic3RvcENvbnRpbnVvdXNSZWNvZ25pdGlvbkFzeW5jIiwiY2xvc2UiLCJjcmVhdGVTcGVlY2hTZXJ2aWNlIiwic2VydmljZVR5cGUiLCJzcGVlY2hTZXJ2aWNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./utils/speechServices.js\n"));

/***/ })

});