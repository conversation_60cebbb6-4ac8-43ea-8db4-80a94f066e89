"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./pages/interview.js":
/*!****************************!*\
  !*** ./pages/interview.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InterviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(pages-dir-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AppBar,Avatar,Box,Button,Card,CardContent,CardHeader,Checkbox,Chip,CircularProgress,Container,FormControlLabel,Grid,IconButton,List,ListItem,ListItemText,Paper,Snackbar,Switch,TextField,Toolbar,Tooltip,Typography,useTheme!=!@mui/material */ \"(pages-dir-browser)/__barrel_optimize__?names=Alert,AppBar,Avatar,Box,Button,Card,CardContent,CardHeader,Checkbox,Chip,CircularProgress,Container,FormControlLabel,Grid,IconButton,List,ListItem,ListItemText,Paper,Snackbar,Switch,TextField,Toolbar,Tooltip,Typography,useTheme!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/ArrowDownward */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ArrowDownward.js\");\n/* harmony import */ var _mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/icons-material/ArrowUpward */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ArrowUpward.js\");\n/* harmony import */ var _mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/icons-material/DeleteSweep */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/DeleteSweep.js\");\n/* harmony import */ var _mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/Hearing */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Hearing.js\");\n/* harmony import */ var _mui_icons_material_Mic__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/Mic */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Mic.js\");\n/* harmony import */ var _mui_icons_material_MicOff__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/icons-material/MicOff */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/MicOff.js\");\n/* harmony import */ var _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/Person */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _mui_icons_material_PictureInPictureAlt__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/PictureInPictureAlt */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/PictureInPictureAlt.js\");\n/* harmony import */ var _mui_icons_material_PlaylistAddCheck__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/icons-material/PlaylistAddCheck */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/PlaylistAddCheck.js\");\n/* harmony import */ var _mui_icons_material_ScreenShare__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/ScreenShare */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ScreenShare.js\");\n/* harmony import */ var _mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/icons-material/Send */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Send.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/icons-material/SmartToy */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/SmartToy.js\");\n/* harmony import */ var _mui_icons_material_StopScreenShare__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/StopScreenShare */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/StopScreenShare.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @google/generative-ai */ \"(pages-dir-browser)/./node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var highlight_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! highlight.js */ \"(pages-dir-browser)/./node_modules/highlight.js/es/index.js\");\n/* harmony import */ var highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! highlight.js/styles/atom-one-dark.css */ \"(pages-dir-browser)/./node_modules/highlight.js/styles/atom-one-dark.css\");\n/* harmony import */ var highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash.throttle */ \"(pages-dir-browser)/./node_modules/lodash.throttle/index.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! openai */ \"(pages-dir-browser)/./node_modules/openai/index.mjs\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-markdown */ \"(pages-dir-browser)/./node_modules/react-markdown/index.js\");\n/* harmony import */ var react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-scroll-to-bottom */ \"(pages-dir-browser)/./node_modules/react-scroll-to-bottom/lib/esm/index.js\");\n/* harmony import */ var _components_SettingsDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/SettingsDialog */ \"(pages-dir-browser)/./components/SettingsDialog.js\");\n/* harmony import */ var _redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../redux/aiResponseSlice */ \"(pages-dir-browser)/./redux/aiResponseSlice.js\");\n/* harmony import */ var _redux_historySlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../redux/historySlice */ \"(pages-dir-browser)/./redux/historySlice.js\");\n/* harmony import */ var _redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../redux/transcriptionSlice */ \"(pages-dir-browser)/./redux/transcriptionSlice.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../utils/config */ \"(pages-dir-browser)/./utils/config.js\");\n/* harmony import */ var _utils_speechServices__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../utils/speechServices */ \"(pages-dir-browser)/./utils/speechServices.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n// MUI Components\n\n// MUI Icons\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Third-party Libraries\n\n\n\n\n\n\n\n// Speech Services\n// Local Imports\n\n\n\n\n\n\nfunction debounce(func) {\n    let timeout = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;\n    var _this = this;\n    let timer;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        clearTimeout(timer);\n        timer = setTimeout(()=>{\n            func.apply(_this, args);\n        }, timeout);\n    };\n}\nfunction InterviewPage() {\n    _s();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useDispatch)();\n    const transcriptionFromStore = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[transcriptionFromStore]\": (state)=>state.transcription\n    }[\"InterviewPage.useSelector[transcriptionFromStore]\"]);\n    const aiResponseFromStore = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[aiResponseFromStore]\": (state)=>state.aiResponse\n    }[\"InterviewPage.useSelector[aiResponseFromStore]\"]);\n    const history = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[history]\": (state)=>state.history\n    }[\"InterviewPage.useSelector[history]\"]);\n    const theme = (0,_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    const [appConfig, setAppConfig] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)((0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)());\n    const [systemRecognizer, setSystemRecognizer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [micRecognizer, setMicRecognizer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [systemSpeechService, setSystemSpeechService] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [micSpeechService, setMicSpeechService] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [activeSpeechService, setActiveSpeechService] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null); // Track which service is active\n    const [systemAutoMode, setSystemAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(appConfig.systemAutoMode !== undefined ? appConfig.systemAutoMode : true);\n    const [openAI, setOpenAI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [settingsOpen, setSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isMicrophoneActive, setIsMicrophoneActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isSystemAudioActive, setIsSystemAudioActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('info');\n    const [selectedQuestions, setSelectedQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isManualMode, setIsManualMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(appConfig.isManualMode !== undefined ? appConfig.isManualMode : false);\n    const [micTranscription, setMicTranscription] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isAILoading, setIsAILoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [autoScroll, setAutoScroll] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [aiResponseSortOrder, setAiResponseSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('newestAtTop');\n    const [isPipWindowActive, setIsPipWindowActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const pipWindowRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const documentPipWindowRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const documentPipIframeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const systemInterimTranscription = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)('');\n    const micInterimTranscription = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)('');\n    const silenceTimer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const finalTranscript = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)({\n        system: '',\n        microphone: ''\n    });\n    const isManualModeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(isManualMode);\n    const systemAutoModeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(systemAutoMode);\n    const throttledDispatchSetAIResponseRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const showSnackbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"InterviewPage.useCallback[showSnackbar]\": function(message) {\n            let severity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n            setSnackbarMessage(message);\n            setSnackbarSeverity(severity);\n            setSnackbarOpen(true);\n        }\n    }[\"InterviewPage.useCallback[showSnackbar]\"], []);\n    const handleSettingsSaved = ()=>{\n        const newConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        setAppConfig(newConfig);\n        setIsAILoading(true);\n        setSystemAutoMode(newConfig.systemAutoMode !== undefined ? newConfig.systemAutoMode : true);\n        setIsManualMode(newConfig.isManualMode !== undefined ? newConfig.isManualMode : false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            const currentConfig = appConfig;\n            const initializeAI = {\n                \"InterviewPage.useEffect.initializeAI\": ()=>{\n                    try {\n                        if (currentConfig.aiModel.startsWith('gemini')) {\n                            if (!currentConfig.geminiKey) {\n                                showSnackbar('Gemini API key required. Please set it in Settings.', 'error');\n                                setOpenAI(null);\n                                return;\n                            }\n                            const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_4__.GoogleGenerativeAI(currentConfig.geminiKey);\n                            setOpenAI(genAI);\n                        } else {\n                            if (!currentConfig.openaiKey) {\n                                showSnackbar('OpenAI API key required. Please set it in Settings.', 'error');\n                                setOpenAI(null);\n                                return;\n                            }\n                            const openaiClient = new openai__WEBPACK_IMPORTED_MODULE_17__[\"default\"]({\n                                apiKey: currentConfig.openaiKey,\n                                dangerouslyAllowBrowser: true\n                            });\n                            setOpenAI(openaiClient);\n                        }\n                    } catch (error) {\n                        console.error('Error initializing AI client:', error);\n                        showSnackbar('Error initializing AI client: ' + error.message, 'error');\n                        setOpenAI(null);\n                    } finally{\n                        setIsAILoading(false);\n                    }\n                }\n            }[\"InterviewPage.useEffect.initializeAI\"];\n            if (isAILoading) initializeAI();\n        }\n    }[\"InterviewPage.useEffect\"], [\n        appConfig,\n        isAILoading,\n        showSnackbar\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            isManualModeRef.current = isManualMode;\n        }\n    }[\"InterviewPage.useEffect\"], [\n        isManualMode\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            systemAutoModeRef.current = systemAutoMode;\n        }\n    }[\"InterviewPage.useEffect\"], [\n        systemAutoMode\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            throttledDispatchSetAIResponseRef.current = lodash_throttle__WEBPACK_IMPORTED_MODULE_7___default()({\n                \"InterviewPage.useEffect\": (payload)=>{\n                    dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(payload));\n                }\n            }[\"InterviewPage.useEffect\"], 250, {\n                leading: true,\n                trailing: true\n            });\n            return ({\n                \"InterviewPage.useEffect\": ()=>{\n                    if (throttledDispatchSetAIResponseRef.current && typeof throttledDispatchSetAIResponseRef.current.cancel === 'function') {\n                        throttledDispatchSetAIResponseRef.current.cancel();\n                    }\n                }\n            })[\"InterviewPage.useEffect\"];\n        }\n    }[\"InterviewPage.useEffect\"], [\n        dispatch\n    ]);\n    const handleSnackbarClose = ()=>setSnackbarOpen(false);\n    const stopRecording = async (source)=>{\n        const speechService = source === 'system' ? systemSpeechService : micSpeechService;\n        const recognizer = source === 'system' ? systemRecognizer : micRecognizer;\n        try {\n            // Stop new speech service if available\n            if (speechService) {\n                await speechService.stop();\n            }\n            // Fallback to legacy Azure recognizer if still in use\n            if (recognizer && typeof recognizer.stopContinuousRecognitionAsync === 'function') {\n                await recognizer.stopContinuousRecognitionAsync();\n                if (recognizer.audioConfig && recognizer.audioConfig.privSource && recognizer.audioConfig.privSource.privStream) {\n                    const stream = recognizer.audioConfig.privSource.privStream;\n                    if (stream instanceof MediaStream) {\n                        stream.getTracks().forEach((track)=>{\n                            track.stop();\n                        });\n                    }\n                }\n                if (recognizer.audioConfig && typeof recognizer.audioConfig.close === 'function') {\n                    recognizer.audioConfig.close();\n                }\n            }\n        } catch (error) {\n            console.error(\"Error stopping \".concat(source, \" recognition:\"), error);\n            showSnackbar(\"Error stopping \".concat(source, \" audio: \").concat(error.message), 'error');\n        } finally{\n            if (source === 'system') {\n                setIsSystemAudioActive(false);\n                setSystemRecognizer(null);\n                setSystemSpeechService(null);\n            } else {\n                setIsMicrophoneActive(false);\n                setMicRecognizer(null);\n                setMicSpeechService(null);\n                setActiveSpeechService(null); // Clear active service when stopping microphone\n            }\n        }\n    };\n    const handleClearSystemTranscription = ()=>{\n        finalTranscript.current.system = '';\n        systemInterimTranscription.current = '';\n        dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.clearTranscription)());\n    };\n    const handleClearMicTranscription = ()=>{\n        finalTranscript.current.microphone = '';\n        micInterimTranscription.current = '';\n        setMicTranscription('');\n    };\n    const handleTranscriptionEvent = (text, source)=>{\n        const cleanText = text.replace(/\\s+/g, ' ').trim();\n        if (!cleanText) return;\n        finalTranscript.current[source] += cleanText + ' ';\n        if (source === 'system') {\n            dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + systemInterimTranscription.current));\n        } else {\n            setMicTranscription(finalTranscript.current.microphone + micInterimTranscription.current);\n        }\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        const currentSilenceTimerDuration = currentConfig.silenceTimerDuration;\n        if (source === 'system' && systemAutoModeRef.current || source === 'microphone' && !isManualModeRef.current) {\n            clearTimeout(silenceTimer.current);\n            silenceTimer.current = setTimeout(()=>{\n                askOpenAI(finalTranscript.current[source].trim(), source);\n            }, currentSilenceTimerDuration * 1000);\n        }\n    };\n    const handleManualInputChange = (value, source)=>{\n        if (source === 'system') {\n            dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(value));\n            finalTranscript.current.system = value;\n        } else {\n            setMicTranscription(value);\n            finalTranscript.current.microphone = value;\n        }\n    };\n    const handleManualSubmit = (source)=>{\n        const textToSubmit = source === 'system' ? transcriptionFromStore : micTranscription;\n        if (textToSubmit.trim()) {\n            askOpenAI(textToSubmit.trim(), source);\n        } else {\n            showSnackbar('Input is empty.', 'warning');\n        }\n    };\n    const handleKeyPress = (e, source)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleManualSubmit(source);\n        }\n    };\n    const handleCombineAndSubmit = ()=>{\n        if (selectedQuestions.length === 0) {\n            showSnackbar('No questions selected to combine.', 'warning');\n            return;\n        }\n        const questionHistory = history.filter((e)=>e.type === 'question').slice().reverse();\n        const questionTexts = selectedQuestions.map((selectedIndexInReversedArray)=>{\n            var _questionHistory_selectedIndexInReversedArray;\n            return (_questionHistory_selectedIndexInReversedArray = questionHistory[selectedIndexInReversedArray]) === null || _questionHistory_selectedIndexInReversedArray === void 0 ? void 0 : _questionHistory_selectedIndexInReversedArray.text;\n        }).filter((text)=>text);\n        if (questionTexts.length === 0) {\n            showSnackbar('Could not retrieve selected question texts.', 'warning');\n            return;\n        }\n        const combinedText = questionTexts.join('\\n\\n---\\n\\n');\n        askOpenAI(combinedText, 'combined');\n        setSelectedQuestions([]);\n    };\n    // Legacy Azure recognizer function for fallback\n    const createLegacyAzureRecognizer = async (mediaStream, source)=>{\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        if (!currentConfig.azureToken || !currentConfig.azureRegion) {\n            throw new Error('Azure Speech credentials missing');\n        }\n        let audioConfig;\n        try {\n            audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n        } catch (configError) {\n            console.error(\"Error creating AudioConfig for \".concat(source, \":\"), configError);\n            throw new Error(\"Error setting up audio for \".concat(source, \": \").concat(configError.message));\n        }\n        const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(currentConfig.azureToken, currentConfig.azureRegion);\n        speechConfig.speechRecognitionLanguage = currentConfig.azureLanguage;\n        const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);\n        recognizer.recognizing = (s, e)=>{\n            if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                const interimText = e.result.text;\n                if (source === 'system') {\n                    systemInterimTranscription.current = interimText;\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + interimText));\n                } else {\n                    micInterimTranscription.current = interimText;\n                    setMicTranscription(finalTranscript.current.microphone + interimText);\n                }\n            }\n        };\n        recognizer.recognized = (s, e)=>{\n            if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                if (source === 'system') systemInterimTranscription.current = '';\n                else micInterimTranscription.current = '';\n                handleTranscriptionEvent(e.result.text, source);\n            }\n        };\n        recognizer.canceled = (s, e)=>{\n            console.log(\"CANCELED: Reason=\".concat(e.reason, \" for \").concat(source));\n            if (e.reason === SpeechSDK.CancellationReason.Error) {\n                console.error(\"CANCELED: ErrorCode=\".concat(e.errorCode));\n                console.error(\"CANCELED: ErrorDetails=\".concat(e.errorDetails));\n                showSnackbar(\"Speech recognition error for \".concat(source, \": \").concat(e.errorDetails), 'error');\n            }\n            stopRecording(source);\n        };\n        recognizer.sessionStopped = (s, e)=>{\n            console.log(\"Session stopped event for \".concat(source, \".\"));\n            stopRecording(source);\n        };\n        try {\n            await recognizer.startContinuousRecognitionAsync();\n            showSnackbar(\"\".concat(source === 'system' ? 'System audio' : 'Microphone', \" recording started (Azure Speech).\"), 'success');\n            return recognizer;\n        } catch (error) {\n            console.error(\"Error starting \".concat(source, \" continuous recognition:\"), error);\n            if (audioConfig && typeof audioConfig.close === 'function') audioConfig.close();\n            throw error;\n        }\n    };\n    const createSpeechRecognizer = async (mediaStream, source)=>{\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        // Create speech service callbacks\n        const callbacks = {\n            onStart: (source)=>{\n                console.log(\"Speech recognition started for \".concat(source));\n                showSnackbar(\"\".concat(source === 'system' ? 'System audio' : 'Microphone', \" recording started.\"), 'success');\n            },\n            onInterimResult: (text, source)=>{\n                console.log(\"Interim result for \".concat(source, \":\"), text);\n                if (source === 'system') {\n                    systemInterimTranscription.current = text;\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + text));\n                } else {\n                    micInterimTranscription.current = text;\n                    setMicTranscription(finalTranscript.current.microphone + text);\n                }\n            },\n            onFinalResult: (text, source)=>{\n                console.log(\"Final result for \".concat(source, \":\"), text);\n                // Clear interim transcription\n                if (source === 'system') systemInterimTranscription.current = '';\n                else micInterimTranscription.current = '';\n                // Handle the final transcription\n                handleTranscriptionEvent(text, source);\n            },\n            onError: (error, source)=>{\n                console.error(\"Speech recognition error for \".concat(source, \":\"), error);\n                showSnackbar(\"Speech recognition error for \".concat(source, \": \").concat(error.message), 'error');\n                stopRecording(source);\n            },\n            onStop: (source)=>{\n                console.log(\"Speech recognition stopped for \".concat(source));\n            }\n        };\n        try {\n            // Check if we have the required credentials for the selected service\n            const selectedService = currentConfig.speechService || 'deepgram';\n            let configToUse1 = currentConfig;\n            if (selectedService === 'deepgram' && !currentConfig.deepgramKey) {\n                // If Deepgram is selected but no key is provided, check if Azure is available\n                if (currentConfig.azureToken && currentConfig.azureRegion) {\n                    console.log('Deepgram key missing, falling back to Azure Speech Services');\n                    configToUse1 = {\n                        ...currentConfig,\n                        speechService: 'azure'\n                    };\n                    showSnackbar('Using Azure Speech Services (Deepgram key not configured)', 'info');\n                } else {\n                    throw new Error('Deepgram API key is required. Get a free API key from deepgram.com and configure it in Settings (⚙️ icon).');\n                }\n            } else if (selectedService === 'azure' && (!currentConfig.azureToken || !currentConfig.azureRegion)) {\n                // If Azure is selected but credentials are missing, check if Deepgram is available\n                if (currentConfig.deepgramKey) {\n                    console.log('Azure credentials missing, falling back to Deepgram');\n                    configToUse1 = {\n                        ...currentConfig,\n                        speechService: 'deepgram'\n                    };\n                    showSnackbar('Using Deepgram (Azure credentials not configured)', 'info');\n                } else {\n                    throw new Error('Azure Speech credentials are required. Please configure them in Settings, or provide Deepgram API key for fallback.');\n                }\n            }\n            // Create the appropriate speech service\n            const speechService = (0,_utils_speechServices__WEBPACK_IMPORTED_MODULE_14__.createSpeechService)(configToUse1, callbacks);\n            // Track which service is being used\n            setActiveSpeechService(configToUse1.speechService);\n            // Start the speech service\n            await speechService.start(mediaStream, source);\n            // Show which service is active\n            const serviceName = configToUse1.speechService === 'deepgram' ? 'Deepgram' : 'Azure Speech';\n            showSnackbar(\"Using \".concat(serviceName, \" for \").concat(source === 'system' ? 'system audio' : 'microphone'), 'info');\n            return speechService;\n        } catch (error) {\n            console.error(\"Error creating speech service for \".concat(source, \":\"), error);\n            // If Deepgram fails and we have Azure credentials, try Azure Speech Service first\n            if (configToUse.speechService === 'deepgram' && currentConfig.azureToken && currentConfig.azureRegion) {\n                try {\n                    console.log(\"Deepgram failed, trying Azure Speech Service for \".concat(source));\n                    // Create Azure service configuration\n                    const azureConfig = {\n                        ...currentConfig,\n                        speechService: 'azure'\n                    };\n                    const azureSpeechService = (0,_utils_speechServices__WEBPACK_IMPORTED_MODULE_14__.createSpeechService)(azureConfig, callbacks);\n                    // Track which service is being used\n                    setActiveSpeechService('azure');\n                    // Start the Azure speech service\n                    await azureSpeechService.start(mediaStream, source);\n                    showSnackbar(\"Switched to Azure Speech Services (Deepgram connection failed)\", 'warning');\n                    return azureSpeechService;\n                } catch (azureError) {\n                    console.error(\"Azure Speech Service also failed for \".concat(source, \":\"), azureError);\n                }\n            }\n            // Try legacy Azure recognizer as final fallback\n            if (currentConfig.azureToken && currentConfig.azureRegion) {\n                try {\n                    console.log(\"Attempting legacy Azure fallback for \".concat(source));\n                    const legacyRecognizer = await createLegacyAzureRecognizer(mediaStream, source);\n                    showSnackbar(\"Using legacy Azure Speech Services\", 'warning');\n                    return legacyRecognizer;\n                } catch (legacyError) {\n                    console.error(\"Legacy Azure fallback also failed for \".concat(source, \":\"), legacyError);\n                }\n            }\n            // Provide helpful error messages\n            if (error.message.includes('API key') || error.message.includes('credentials')) {\n                showSnackbar(\"\".concat(error.message), 'error');\n            } else if (error.message.includes('Deepgram')) {\n                showSnackbar(\"\".concat(error.message), 'error');\n            } else {\n                showSnackbar(\"Failed to start \".concat(source, \" recognition: \").concat(error.message), 'error');\n            }\n            mediaStream.getTracks().forEach((track)=>track.stop());\n            return null;\n        }\n    };\n    const startSystemAudioRecognition = async ()=>{\n        if (isSystemAudioActive) {\n            await stopRecording('system');\n            return;\n        }\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {\n            showSnackbar('Screen sharing is not supported by your browser.', 'error');\n            setIsSystemAudioActive(false);\n            return;\n        }\n        try {\n            const mediaStream = await navigator.mediaDevices.getDisplayMedia({\n                audio: true,\n                video: {\n                    displaySurface: 'browser',\n                    logicalSurface: true\n                }\n            });\n            const audioTracks = mediaStream.getAudioTracks();\n            if (audioTracks.length === 0) {\n                showSnackbar('No audio track detected. Please ensure you share a tab with audio.', 'warning');\n                mediaStream.getTracks().forEach((track)=>track.stop());\n                return;\n            }\n            if (systemRecognizer) {\n                await stopRecording('system');\n            }\n            const speechServiceInstance = await createSpeechRecognizer(mediaStream, 'system');\n            if (speechServiceInstance) {\n                // Check if it's a new speech service or legacy recognizer\n                if (speechServiceInstance.start && speechServiceInstance.stop) {\n                    // New speech service\n                    setSystemSpeechService(speechServiceInstance);\n                } else {\n                    // Legacy Azure recognizer\n                    setSystemRecognizer(speechServiceInstance);\n                }\n                setIsSystemAudioActive(true);\n                mediaStream.getTracks().forEach((track)=>{\n                    track.onended = ()=>{\n                        showSnackbar('Tab sharing ended.', 'info');\n                        stopRecording('system');\n                    };\n                });\n            } else {\n                mediaStream.getTracks().forEach((track)=>track.stop());\n            }\n        } catch (error) {\n            console.error('System audio capture error:', error);\n            if (error.name === \"NotAllowedError\") {\n                showSnackbar('Permission denied for screen recording. Please allow access.', 'error');\n            } else if (error.name === \"NotFoundError\") {\n                showSnackbar('No suitable tab/window found to share.', 'error');\n            } else if (error.name === \"NotSupportedError\") {\n                showSnackbar('System audio capture not supported by your browser.', 'error');\n            } else {\n                showSnackbar(\"Failed to start system audio capture: \".concat(error.message || 'Unknown error'), 'error');\n            }\n            setIsSystemAudioActive(false);\n        }\n    };\n    const startMicrophoneRecognition = async ()=>{\n        if (isMicrophoneActive) {\n            await stopRecording('microphone');\n            return;\n        }\n        try {\n            const mediaStream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            if (micSpeechService) await stopRecording('microphone');\n            const speechServiceInstance = await createSpeechRecognizer(mediaStream, 'microphone');\n            if (speechServiceInstance) {\n                // Check if it's a new speech service or legacy recognizer\n                if (speechServiceInstance.start && speechServiceInstance.stop) {\n                    // New speech service\n                    setMicSpeechService(speechServiceInstance);\n                } else {\n                    // Legacy Azure recognizer\n                    setMicRecognizer(speechServiceInstance);\n                }\n                setIsMicrophoneActive(true);\n            } else {\n                mediaStream.getTracks().forEach((track)=>track.stop());\n            }\n        } catch (error) {\n            console.error('Microphone capture error:', error);\n            if (error.name === \"NotAllowedError\" || error.name === \"NotFoundError\") {\n                showSnackbar('Permission denied for microphone. Please allow access.', 'error');\n            } else {\n                showSnackbar(\"Failed to access microphone: \".concat(error.message || 'Unknown error'), 'error');\n            }\n            setIsMicrophoneActive(false);\n        }\n    };\n    // Helper function to determine reasoning effort for future advanced models\n    const getReasoningEffort = (text, source)=>{\n        const textLength = text.length;\n        const isComplexQuestion = text.includes('?') && textLength > 100;\n        const isCombined = source === 'combined';\n        if (isCombined || isComplexQuestion || textLength > 500) return 'high';\n        if (textLength > 200) return 'medium';\n        return 'low';\n    };\n    const askOpenAI = async (text, source)=>{\n        if (!text.trim()) {\n            showSnackbar('No input text to process.', 'warning');\n            return;\n        }\n        if (!openAI || isAILoading) {\n            showSnackbar('AI client is not ready. Please wait or check settings.', 'warning');\n            return;\n        }\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        const lengthSettings = {\n            concise: {\n                temperature: 0.4,\n                maxTokens: 250\n            },\n            medium: {\n                temperature: 0.6,\n                maxTokens: 500\n            },\n            lengthy: {\n                temperature: 0.8,\n                maxTokens: 1000\n            }\n        };\n        const { temperature, maxTokens } = lengthSettings[currentConfig.responseLength || 'medium'];\n        setIsProcessing(true);\n        const timestamp = new Date().toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n        let streamedResponse = '';\n        dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n            type: 'question',\n            text,\n            timestamp,\n            source,\n            status: 'pending'\n        }));\n        dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(''));\n        try {\n            const conversationHistoryForAPI = history.filter((e)=>e.text && (e.type === 'question' || e.type === 'response') && e.status !== 'pending').slice(-6).map((event)=>({\n                    role: event.type === 'question' ? 'user' : 'assistant',\n                    content: event.text\n                }));\n            if (currentConfig.aiModel.startsWith('gemini')) {\n                // Enhanced Gemini API configuration for 2.5 models\n                const modelConfig = {\n                    model: currentConfig.aiModel,\n                    generationConfig: {\n                        temperature,\n                        maxOutputTokens: maxTokens\n                    },\n                    systemInstruction: {\n                        parts: [\n                            {\n                                text: currentConfig.gptSystemPrompt\n                            }\n                        ]\n                    }\n                };\n                // Add thinking configuration for Gemini 2.5 models\n                if (currentConfig.aiModel.includes('2.5') && currentConfig.thinkingBudget !== undefined) {\n                    if (currentConfig.thinkingBudget === 0) {\n                        // Disable thinking\n                        modelConfig.generationConfig.thinkingConfig = {\n                            thinkingBudget: 0\n                        };\n                    } else if (currentConfig.thinkingBudget > 0) {\n                        // Custom thinking budget\n                        modelConfig.generationConfig.thinkingConfig = {\n                            thinkingBudget: currentConfig.thinkingBudget\n                        };\n                    }\n                // If thinkingBudget is null, use default (thinking enabled)\n                }\n                const model = openAI.getGenerativeModel(modelConfig);\n                const chat = model.startChat({\n                    history: conversationHistoryForAPI.map((msg)=>({\n                            role: msg.role === 'user' ? 'user' : 'model',\n                            parts: [\n                                {\n                                    text: msg.content\n                                }\n                            ]\n                        }))\n                });\n                const result = await chat.sendMessageStream(text);\n                for await (const chunk of result.stream){\n                    if (chunk && typeof chunk.text === 'function') {\n                        const chunkText = chunk.text();\n                        streamedResponse += chunkText;\n                        if (throttledDispatchSetAIResponseRef.current) {\n                            throttledDispatchSetAIResponseRef.current(streamedResponse);\n                        }\n                    }\n                }\n            } else {\n                // Enhanced OpenAI API usage with future-ready parameters\n                const messages = [\n                    {\n                        role: 'system',\n                        content: currentConfig.gptSystemPrompt\n                    },\n                    ...conversationHistoryForAPI,\n                    {\n                        role: 'user',\n                        content: text\n                    }\n                ];\n                const requestParams = {\n                    model: currentConfig.aiModel,\n                    messages,\n                    stream: true\n                };\n                // Set temperature based on model capabilities\n                if (currentConfig.aiModel.startsWith('o1')) {\n                // o1 models don't support temperature parameter at all\n                // Don't set temperature for o1 models\n                } else if (currentConfig.aiModel.startsWith('gpt-5')) {\n                    // GPT-5 models may have temperature restrictions, use default value\n                    requestParams.temperature = 1;\n                } else {\n                    // Standard models support configurable temperature\n                    requestParams.temperature = temperature;\n                }\n                // Use the correct token parameter based on model\n                if (currentConfig.aiModel.startsWith('gpt-5') || currentConfig.aiModel.startsWith('o1')) {\n                    requestParams.max_completion_tokens = maxTokens;\n                } else {\n                    requestParams.max_tokens = maxTokens;\n                }\n                // Add model-specific parameters for different model types\n                if (currentConfig.aiModel.startsWith('gpt-5')) {\n                    // GPT-5 models support new parameters\n                    if (currentConfig.reasoningEffort) {\n                        requestParams.reasoning_effort = currentConfig.reasoningEffort;\n                    }\n                    if (currentConfig.verbosity !== undefined) {\n                        requestParams.verbosity = currentConfig.verbosity === 0 ? 'low' : currentConfig.verbosity === 1 ? 'medium' : 'high';\n                    }\n                } else if (currentConfig.aiModel.startsWith('o1')) {\n                    // o1 models use different parameters and don't support streaming\n                    // Remove streaming for o1 models\n                    requestParams.stream = false;\n                    // o1 models don't use system messages in the same way\n                    // Move system prompt to the first user message\n                    requestParams.messages = [\n                        {\n                            role: 'user',\n                            content: \"\".concat(currentConfig.gptSystemPrompt, \"\\n\\n\").concat(text)\n                        },\n                        ...conversationHistoryForAPI.slice(1) // Skip the system message\n                    ];\n                // o1 models require temperature = 1 (already set above)\n                // No additional temperature modification needed\n                }\n                if (currentConfig.aiModel.startsWith('o1')) {\n                    var _response_choices__message, _response_choices_;\n                    // o1 models don't support streaming, handle as single response\n                    const response = await openAI.chat.completions.create(requestParams);\n                    streamedResponse = ((_response_choices_ = response.choices[0]) === null || _response_choices_ === void 0 ? void 0 : (_response_choices__message = _response_choices_.message) === null || _response_choices__message === void 0 ? void 0 : _response_choices__message.content) || '';\n                    if (throttledDispatchSetAIResponseRef.current) {\n                        throttledDispatchSetAIResponseRef.current(streamedResponse);\n                    }\n                } else {\n                    // Standard streaming for GPT-5, GPT-4o, and other models\n                    const stream = await openAI.chat.completions.create(requestParams);\n                    for await (const chunk of stream){\n                        var _chunk_choices__delta, _chunk_choices_;\n                        const chunkText = ((_chunk_choices_ = chunk.choices[0]) === null || _chunk_choices_ === void 0 ? void 0 : (_chunk_choices__delta = _chunk_choices_.delta) === null || _chunk_choices__delta === void 0 ? void 0 : _chunk_choices__delta.content) || '';\n                        streamedResponse += chunkText;\n                        if (throttledDispatchSetAIResponseRef.current) {\n                            throttledDispatchSetAIResponseRef.current(streamedResponse);\n                        }\n                    }\n                }\n            }\n            if (throttledDispatchSetAIResponseRef.current && typeof throttledDispatchSetAIResponseRef.current.cancel === 'function') {\n                throttledDispatchSetAIResponseRef.current.cancel();\n            }\n            dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(streamedResponse));\n            const finalTimestamp = new Date().toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n            });\n            dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n                type: 'response',\n                text: streamedResponse,\n                timestamp: finalTimestamp,\n                status: 'completed'\n            }));\n        } catch (error) {\n            console.error(\"AI request error:\", error);\n            const errorMessage = \"AI request failed: \".concat(error.message || 'Unknown error');\n            showSnackbar(errorMessage, 'error');\n            dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(\"Error: \".concat(errorMessage)));\n            dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n                type: 'response',\n                text: \"Error: \".concat(errorMessage),\n                timestamp: new Date().toLocaleTimeString([], {\n                    hour: '2-digit',\n                    minute: '2-digit'\n                }),\n                status: 'error'\n            }));\n        } finally{\n            if (source === 'system' && systemAutoModeRef.current || source === 'microphone' && !isManualModeRef.current) {\n                finalTranscript.current[source] = '';\n                if (source === 'system') {\n                    systemInterimTranscription.current = '';\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(''));\n                } else {\n                    micInterimTranscription.current = '';\n                    setMicTranscription('');\n                }\n            }\n            setIsProcessing(false);\n        }\n    };\n    const formatAndDisplayResponse = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (response)=>{\n            if (!response) return null;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                components: {\n                    code (param) {\n                        let { node, inline, className, children, ...props } = param;\n                        const match = /language-(\\w+)/.exec(className || '');\n                        return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                            sx: {\n                                my: 1,\n                                position: 'relative',\n                                '& pre': {\n                                    borderRadius: '4px',\n                                    padding: '12px !important',\n                                    fontSize: '0.875rem',\n                                    overflowX: 'auto',\n                                    whiteSpace: 'pre-wrap',\n                                    wordBreak: 'break-all'\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: className,\n                                    ...props,\n                                    dangerouslySetInnerHTML: {\n                                        __html: highlight_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"].highlight(String(children).replace(/\\n$/, ''), {\n                                            language: match[1],\n                                            ignoreIllegals: true\n                                        }).value\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 828,\n                                    columnNumber: 22\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 828,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 816,\n                            columnNumber: 15\n                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: className,\n                            ...props,\n                            style: {\n                                backgroundColor: 'rgba(0,0,0,0.05)',\n                                padding: '2px 4px',\n                                borderRadius: '4px',\n                                fontFamily: 'monospace',\n                                fontSize: '0.875rem',\n                                wordBreak: 'break-all'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 831,\n                            columnNumber: 15\n                        }, void 0);\n                    },\n                    p: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                paragraph: true,\n                                ...props,\n                                sx: {\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 847,\n                                columnNumber: 38\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    strong: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"strong\",\n                                fontWeight: \"bold\",\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 848,\n                                columnNumber: 43\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    em: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"em\",\n                                fontStyle: \"italic\",\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 849,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    ul: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"ul\",\n                                sx: {\n                                    pl: 2.5,\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 850,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    ol: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"ol\",\n                                sx: {\n                                    pl: 2.5,\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 851,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    li: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"li\",\n                                sx: {\n                                    mb: 0.25,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 852,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"]\n                },\n                children: response\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 811,\n                columnNumber: 7\n            }, this);\n        }\n    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"], []);\n    const renderHistoryItem = (item, index)=>{\n        if (item.type !== 'response') return null;\n        const Icon = _mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n        const title = 'AI Assistant';\n        const avatarBgColor = theme.palette.secondary.light;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n            sx: {\n                alignItems: 'flex-start',\n                px: 0,\n                py: 1.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                    sx: {\n                        bgcolor: avatarBgColor,\n                        mr: 2,\n                        mt: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        sx: {\n                            color: theme.palette.getContrastText(avatarBgColor)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 869,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 868,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Paper, {\n                    variant: \"outlined\",\n                    sx: {\n                        p: 1.5,\n                        flexGrow: 1,\n                        bgcolor: theme.palette.background.default,\n                        borderColor: theme.palette.divider,\n                        overflowX: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                            sx: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                mb: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"subtitle2\",\n                                    fontWeight: \"bold\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 873,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"caption\",\n                                    color: \"text.secondary\",\n                                    children: item.timestamp\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 874,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 872,\n                            columnNumber: 11\n                        }, this),\n                        formatAndDisplayResponse(item.text)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 871,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, \"response-\".concat(index), true, {\n            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n            lineNumber: 867,\n            columnNumber: 7\n        }, this);\n    };\n    const renderQuestionHistoryItem = (item, index)=>{\n        const Icon = item.source === 'system' ? _mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__[\"default\"] : _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__[\"default\"];\n        const title = item.source === 'system' ? 'Interviewer' : 'Candidate';\n        const avatarBgColor = item.source === 'system' ? theme.palette.info.light : theme.palette.success.light;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n            secondaryAction: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Checkbox, {\n                edge: \"end\",\n                checked: selectedQuestions.includes(index),\n                onChange: ()=>{\n                    setSelectedQuestions((prev)=>prev.includes(index) ? prev.filter((x)=>x !== index) : [\n                            ...prev,\n                            index\n                        ]);\n                },\n                color: \"secondary\",\n                size: \"small\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 891,\n                columnNumber: 11\n            }, void 0),\n            disablePadding: true,\n            sx: {\n                py: 0.5,\n                display: 'flex',\n                alignItems: 'center'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                    sx: {\n                        bgcolor: avatarBgColor,\n                        mr: 1.5,\n                        width: 32,\n                        height: 32,\n                        fontSize: '1rem'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        fontSize: \"small\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 907,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 906,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItemText, {\n                    primary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                        variant: \"body2\",\n                        noWrap: true,\n                        sx: {\n                            fontWeight: selectedQuestions.includes(index) ? 'bold' : 'normal',\n                            display: '-webkit-box',\n                            WebkitLineClamp: 2,\n                            WebkitBoxOrient: 'vertical',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis'\n                        },\n                        children: item.text\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 911,\n                        columnNumber: 13\n                    }, void 0),\n                    secondary: \"\".concat(title, \" - \").concat(item.timestamp)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 909,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, \"question-hist-\".concat(index), true, {\n            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n            lineNumber: 888,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSortOrderToggle = ()=>{\n        setAiResponseSortOrder((prev)=>prev === 'newestAtBottom' ? 'newestAtTop' : 'newestAtBottom');\n    };\n    const getAiResponsesToDisplay = ()=>{\n        let responses = history.filter((item)=>item.type === 'response').slice();\n        const currentStreamingText = aiResponseFromStore;\n        if (isProcessing && currentStreamingText && currentStreamingText.trim() !== '') {\n            responses.push({\n                text: currentStreamingText,\n                timestamp: 'Streaming...',\n                type: 'current_streaming'\n            });\n        }\n        if (aiResponseSortOrder === 'newestAtTop') {\n            return responses.reverse();\n        }\n        return responses;\n    };\n    const togglePipWindow = async ()=>{\n        if (isPipWindowActive) {\n            if (documentPipWindowRef.current && typeof documentPipWindowRef.current.close === 'function') {\n                try {\n                    await documentPipWindowRef.current.close();\n                } catch (e) {\n                    console.error(\"Error closing document PiP window:\", e);\n                }\n            } else if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                pipWindowRef.current.close();\n            }\n            return; // State update will be handled by pagehide/interval listeners\n        }\n        const addResizeListener = (pipWindow)=>{\n            const handlePipResize = debounce(()=>{\n                if (!pipWindow || pipWindow.closed) return;\n                const target = documentPipIframeRef.current ? documentPipIframeRef.current.contentWindow : pipWindow;\n                if (target) {\n                    target.postMessage({\n                        type: 'PIP_RESIZE',\n                        payload: {\n                            width: pipWindow.innerWidth,\n                            height: pipWindow.innerHeight\n                        }\n                    }, '*');\n                }\n            }, 50);\n            pipWindow.addEventListener('resize', handlePipResize);\n            return ()=>pipWindow.removeEventListener('resize', handlePipResize); // Return a cleanup function\n        };\n        if (window.documentPictureInPicture && typeof window.documentPictureInPicture.requestWindow === 'function') {\n            try {\n                const pipOptions = {\n                    width: 400,\n                    height: 300\n                };\n                const requestedPipWindow = await window.documentPictureInPicture.requestWindow(pipOptions);\n                documentPipWindowRef.current = requestedPipWindow;\n                setIsPipWindowActive(true);\n                const iframe = documentPipWindowRef.current.document.createElement('iframe');\n                iframe.src = '/pip-log';\n                iframe.style.width = '100%';\n                iframe.style.height = '100%';\n                iframe.style.border = 'none';\n                documentPipWindowRef.current.document.body.style.margin = '0';\n                documentPipWindowRef.current.document.body.style.overflow = 'hidden';\n                documentPipWindowRef.current.document.body.append(iframe);\n                documentPipIframeRef.current = iframe;\n                const removeResizeListener = addResizeListener(documentPipWindowRef.current);\n                iframe.onload = ()=>{\n                    if (documentPipIframeRef.current && documentPipIframeRef.current.contentWindow) {\n                        documentPipIframeRef.current.contentWindow.postMessage({\n                            type: 'AI_LOG_DATA',\n                            payload: {\n                                historicalResponses: history.filter((item)=>item.type === 'response'),\n                                currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                                isProcessing: isProcessing,\n                                sortOrder: aiResponseSortOrder\n                            }\n                        }, '*');\n                    }\n                };\n                documentPipWindowRef.current.addEventListener('pagehide', ()=>{\n                    removeResizeListener();\n                    setIsPipWindowActive(false);\n                    documentPipWindowRef.current = null;\n                    documentPipIframeRef.current = null;\n                });\n                showSnackbar('Native PiP window opened.', 'success');\n                return;\n            } catch (err) {\n                console.error('Document Picture-in-Picture API error:', err);\n                showSnackbar(\"Native PiP not available or failed. Trying popup. (\".concat(err.message, \")\"), 'warning');\n            }\n        }\n        pipWindowRef.current = window.open('/pip-log', 'AIResponsePiP', 'width=400,height=550,resizable=yes,scrollbars=yes,status=no,toolbar=no,menubar=no,location=no,noopener,noreferrer,popup=yes');\n        if (pipWindowRef.current) {\n            setIsPipWindowActive(true);\n            const removeResizeListener = addResizeListener(pipWindowRef.current);\n            pipWindowRef.current.onload = ()=>{\n                if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                    pipWindowRef.current.postMessage({\n                        type: 'AI_LOG_DATA',\n                        payload: {\n                            historicalResponses: history.filter((item)=>item.type === 'response'),\n                            currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                            isProcessing: isProcessing,\n                            sortOrder: aiResponseSortOrder\n                        }\n                    }, '*');\n                }\n            };\n            const pipCheckInterval = setInterval(()=>{\n                if (pipWindowRef.current && pipWindowRef.current.closed) {\n                    clearInterval(pipCheckInterval);\n                    removeResizeListener();\n                    setIsPipWindowActive(false);\n                    pipWindowRef.current = null;\n                }\n            }, 500);\n            if (pipWindowRef.current) pipWindowRef.current._pipIntervalId = pipCheckInterval;\n        } else {\n            showSnackbar('Failed to open PiP window. Please check popup blocker settings.', 'error');\n            setIsPipWindowActive(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            return ({\n                \"InterviewPage.useEffect\": ()=>{\n                    if (pipWindowRef.current && pipWindowRef.current._pipIntervalId) {\n                        clearInterval(pipWindowRef.current._pipIntervalId);\n                    }\n                    if (documentPipWindowRef.current && typeof documentPipWindowRef.current.close === 'function') {\n                        try {\n                            documentPipWindowRef.current.close();\n                        } catch (e) {}\n                    }\n                }\n            })[\"InterviewPage.useEffect\"];\n        }\n    }[\"InterviewPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            let targetWindowForMessage = null;\n            if (documentPipWindowRef.current && documentPipIframeRef.current && documentPipIframeRef.current.contentWindow) {\n                targetWindowForMessage = documentPipIframeRef.current.contentWindow;\n            } else if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                targetWindowForMessage = pipWindowRef.current;\n            }\n            if (isPipWindowActive && targetWindowForMessage) {\n                try {\n                    targetWindowForMessage.postMessage({\n                        type: 'AI_LOG_DATA',\n                        payload: {\n                            historicalResponses: history.filter({\n                                \"InterviewPage.useEffect\": (item)=>item.type === 'response'\n                            }[\"InterviewPage.useEffect\"]),\n                            currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                            isProcessing: isProcessing,\n                            sortOrder: aiResponseSortOrder\n                        }\n                    }, '*');\n                } catch (e) {\n                    console.warn(\"Could not post message to PiP window:\", e);\n                }\n            }\n        }\n    }[\"InterviewPage.useEffect\"], [\n        history,\n        aiResponseFromStore,\n        isPipWindowActive,\n        aiResponseSortOrder,\n        isProcessing\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"88c8eba4614332d8\",\n                            [\n                                theme.palette.background.paper,\n                                theme.palette.grey[400],\n                                theme.palette.background.paper,\n                                theme.palette.grey[500],\n                                theme.palette.grey[400],\n                                theme.palette.background.paper\n                            ]\n                        ]\n                    ]),\n                    children: \"Interview Copilot - Active Session\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 1093,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 1092,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                sx: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    height: '100vh'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.AppBar, {\n                        position: \"static\",\n                        color: \"default\",\n                        elevation: 1,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Toolbar, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    sx: {\n                                        mr: 2,\n                                        color: 'primary.main'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1098,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"h6\",\n                                    component: \"div\",\n                                    sx: {\n                                        flexGrow: 1,\n                                        color: 'text.primary'\n                                    },\n                                    children: \"Interview Copilot\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1099,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                    title: \"Settings\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                        color: \"primary\",\n                                        onClick: ()=>setSettingsOpen(true),\n                                        \"aria-label\": \"settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1104,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1103,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1097,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1096,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Container, {\n                        maxWidth: \"xl\",\n                        sx: {\n                            flexGrow: 1,\n                            py: 2,\n                            display: 'flex',\n                            flexDirection: 'column'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 3,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column',\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                    title: \"System Audio (Interviewer)\",\n                                                    avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1115,\n                                                        columnNumber: 72\n                                                    }, void 0),\n                                                    sx: {\n                                                        pb: 1\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1115,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                            control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                                checked: systemAutoMode,\n                                                                onChange: (e)=>setSystemAutoMode(e.target.checked),\n                                                                color: \"primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1118,\n                                                                columnNumber: 30\n                                                            }, void 0),\n                                                            label: \"Auto-Submit Question\",\n                                                            sx: {\n                                                                mb: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.TextField, {\n                                                            fullWidth: true,\n                                                            multiline: true,\n                                                            rows: 3,\n                                                            variant: \"outlined\",\n                                                            value: transcriptionFromStore,\n                                                            onChange: (e)=>handleManualInputChange(e.target.value, 'system'),\n                                                            onKeyDown: (e)=>handleKeyPress(e, 'system'),\n                                                            placeholder: \"Interviewer's speech...\",\n                                                            sx: {\n                                                                mb: 2\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1122,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                            sx: {\n                                                                display: 'flex',\n                                                                gap: 1,\n                                                                flexWrap: 'wrap'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                    onClick: startSystemAudioRecognition,\n                                                                    variant: \"contained\",\n                                                                    color: isSystemAudioActive ? 'error' : 'primary',\n                                                                    startIcon: isSystemAudioActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_StopScreenShare__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1138,\n                                                                        columnNumber: 56\n                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ScreenShare__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1138,\n                                                                        columnNumber: 82\n                                                                    }, void 0),\n                                                                    sx: {\n                                                                        flexGrow: 1\n                                                                    },\n                                                                    children: isSystemAudioActive ? 'Stop System Audio' : 'Record System Audio'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1134,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                                    variant: \"caption\",\n                                                                    sx: {\n                                                                        mt: 1,\n                                                                        display: 'block',\n                                                                        width: '100%'\n                                                                    },\n                                                                    children: isSystemAudioActive ? 'Recording system audio...' : 'Select \"Chrome Tab\" and check \"Share audio\" when prompted.'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1143,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                                    title: \"Clear System Transcription\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                        onClick: handleClearSystemTranscription,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                            lineNumber: 1147,\n                                                                            columnNumber: 76\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1147,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1146,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                !systemAutoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                    onClick: ()=>handleManualSubmit('system'),\n                                                                    variant: \"outlined\",\n                                                                    color: \"primary\",\n                                                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1154,\n                                                                        columnNumber: 36\n                                                                    }, void 0),\n                                                                    disabled: isProcessing || !transcriptionFromStore.trim(),\n                                                                    children: \"Submit\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1150,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1133,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1116,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                            sx: {\n                                                flexGrow: 1,\n                                                display: 'flex',\n                                                flexDirection: 'column'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                    title: \"Question History\",\n                                                    avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_PlaylistAddCheck__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1166,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                        variant: \"contained\",\n                                                        size: \"small\",\n                                                        onClick: handleCombineAndSubmit,\n                                                        disabled: selectedQuestions.length === 0 || isProcessing,\n                                                        startIcon: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CircularProgress, {\n                                                            size: 16,\n                                                            color: \"inherit\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 49\n                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 98\n                                                        }, void 0),\n                                                        children: \"Ask Combined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1168,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    sx: {\n                                                        pb: 1,\n                                                        borderBottom: \"1px solid \".concat(theme.palette.divider)\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                    sx: {\n                                                        flexGrow: 1,\n                                                        overflow: 'hidden',\n                                                        p: 0\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"scroll-to-bottom\",\n                                                        followButtonClassName: \"hidden-follow-button\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.List, {\n                                                            dense: true,\n                                                            sx: {\n                                                                pt: 0,\n                                                                px: 1\n                                                            },\n                                                            children: history.filter((e)=>e.type === 'question').slice().reverse().map(renderQuestionHistoryItem)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1182,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1180,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        sx: {\n                                            flexGrow: 1,\n                                            display: 'flex',\n                                            flexDirection: 'column'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"AI Assistant Log\",\n                                                avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1195,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                            title: isPipWindowActive ? \"Close PiP Log\" : \"Open PiP Log\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                onClick: togglePipWindow,\n                                                                size: \"small\",\n                                                                color: isPipWindowActive ? \"secondary\" : \"default\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_PictureInPictureAlt__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1200,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1199,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1198,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                            title: aiResponseSortOrder === 'newestAtTop' ? \"Sort: Newest at Bottom\" : \"Sort: Newest on Top\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                onClick: handleSortOrderToggle,\n                                                                size: \"small\",\n                                                                children: aiResponseSortOrder === 'newestAtTop' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1205,\n                                                                    columnNumber: 68\n                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1205,\n                                                                    columnNumber: 92\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1204,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1203,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                            variant: \"caption\",\n                                                            sx: {\n                                                                mr: 1,\n                                                                fontStyle: 'italic'\n                                                            },\n                                                            children: aiResponseSortOrder === 'newestAtTop' ? \"Newest First\" : \"Oldest First\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1208,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                            control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                                checked: autoScroll,\n                                                                onChange: (e)=>setAutoScroll(e.target.checked),\n                                                                color: \"primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1212,\n                                                                columnNumber: 34\n                                                            }, void 0),\n                                                            label: \"Auto Scroll\",\n                                                            sx: {\n                                                                ml: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1211,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true),\n                                                sx: {\n                                                    borderBottom: \"1px solid \".concat(theme.palette.divider)\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                sx: {\n                                                    flexGrow: 1,\n                                                    overflow: 'hidden',\n                                                    p: 0\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"scroll-to-bottom\",\n                                                    mode: autoScroll ? aiResponseSortOrder === 'newestAtTop' ? \"top\" : \"bottom\" : undefined,\n                                                    followButtonClassName: \"hidden-follow-button\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.List, {\n                                                        sx: {\n                                                            px: 2,\n                                                            py: 1\n                                                        },\n                                                        children: [\n                                                            getAiResponsesToDisplay().map(renderHistoryItem),\n                                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n                                                                sx: {\n                                                                    justifyContent: 'center',\n                                                                    py: 2\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CircularProgress, {\n                                                                        size: 24\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1230,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                                        variant: \"caption\",\n                                                                        sx: {\n                                                                            ml: 1\n                                                                        },\n                                                                        children: \"AI is thinking...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1231,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1229,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1226,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1221,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1220,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1192,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 3,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        sx: {\n                                            flexGrow: 1,\n                                            display: 'flex',\n                                            flexDirection: 'column'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"Your Mic (Candidate)\",\n                                                avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1245,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                action: activeSpeechService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Chip, {\n                                                    label: activeSpeechService === 'deepgram' ? 'Deepgram' : 'Azure',\n                                                    size: \"small\",\n                                                    color: activeSpeechService === 'deepgram' ? 'primary' : 'secondary',\n                                                    variant: \"outlined\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1248,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                sx: {\n                                                    pb: 1\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1243,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                sx: {\n                                                    flexGrow: 1,\n                                                    display: 'flex',\n                                                    flexDirection: 'column'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                            checked: isManualMode,\n                                                            onChange: (e)=>setIsManualMode(e.target.checked),\n                                                            color: \"primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1260,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        label: \"Manual Input Mode\",\n                                                        sx: {\n                                                            mb: 1\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.TextField, {\n                                                        fullWidth: true,\n                                                        multiline: true,\n                                                        rows: 8,\n                                                        variant: \"outlined\",\n                                                        value: micTranscription,\n                                                        onChange: (e)=>handleManualInputChange(e.target.value, 'microphone'),\n                                                        onKeyDown: (e)=>handleKeyPress(e, 'microphone'),\n                                                        placeholder: \"Your speech or manual input...\",\n                                                        sx: {\n                                                            mb: 2,\n                                                            flexGrow: 1\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                        sx: {\n                                                            display: 'flex',\n                                                            gap: 1,\n                                                            flexWrap: 'wrap',\n                                                            mt: 'auto'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                onClick: startMicrophoneRecognition,\n                                                                variant: \"contained\",\n                                                                color: isMicrophoneActive ? 'error' : 'primary',\n                                                                startIcon: isMicrophoneActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_MicOff__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1280,\n                                                                    columnNumber: 55\n                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Mic__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1280,\n                                                                    columnNumber: 72\n                                                                }, void 0),\n                                                                sx: {\n                                                                    flexGrow: 1\n                                                                },\n                                                                children: isMicrophoneActive ? 'Stop Mic' : 'Start Mic'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1276,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                                title: \"Clear Your Transcription\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                    onClick: handleClearMicTranscription,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1286,\n                                                                        columnNumber: 73\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1286,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1285,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isManualMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                onClick: ()=>handleManualSubmit('microphone'),\n                                                                variant: \"outlined\",\n                                                                color: \"primary\",\n                                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1293,\n                                                                    columnNumber: 36\n                                                                }, void 0),\n                                                                disabled: isProcessing || !micTranscription.trim(),\n                                                                children: \"Submit\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1289,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1275,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1241,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1111,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SettingsDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        open: settingsOpen,\n                        onClose: ()=>setSettingsOpen(false),\n                        onSave: handleSettingsSaved\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Snackbar, {\n                        open: snackbarOpen,\n                        autoHideDuration: 4000,\n                        onClose: handleSnackbarClose,\n                        anchorOrigin: {\n                            vertical: 'bottom',\n                            horizontal: 'center'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Alert, {\n                            onClose: handleSnackbarClose,\n                            severity: snackbarSeverity,\n                            sx: {\n                                width: '100%',\n                                boxShadow: theme.shadows[6]\n                            },\n                            children: snackbarMessage\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1317,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1311,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 1095,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"88c8eba4614332d8\",\n                dynamic: [\n                    theme.palette.background.paper,\n                    theme.palette.grey[400],\n                    theme.palette.background.paper,\n                    theme.palette.grey[500],\n                    theme.palette.grey[400],\n                    theme.palette.background.paper\n                ],\n                children: \".scroll-to-bottom{height:100%;width:100%;overflow-y:auto}.hidden-follow-button{display:none}.scroll-to-bottom::-webkit-scrollbar{width:8px;height:8px}.scroll-to-bottom::-webkit-scrollbar-track{background:\".concat(theme.palette.background.paper, \";border-radius:10px}.scroll-to-bottom::-webkit-scrollbar-thumb{background-color:\").concat(theme.palette.grey[400], \";border-radius:10px;border:2px solid \").concat(theme.palette.background.paper, \"}.scroll-to-bottom::-webkit-scrollbar-thumb:hover{background-color:\").concat(theme.palette.grey[500], \"}.scroll-to-bottom{scrollbar-width:thin;scrollbar-color:\").concat(theme.palette.grey[400], \" \").concat(theme.palette.background.paper, \"}\")\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(InterviewPage, \"B0k6/B2kH+enNb98YMyuDAPpCss=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useDispatch,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        _barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.useTheme\n    ];\n});\n_c = InterviewPage;\nvar _c;\n$RefreshReg$(_c, \"InterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL2ludGVydmlldy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWlFO0FBRXBDO0FBQzBCO0FBRXZELGlCQUFpQjtBQTRCTTtBQUV2QixZQUFZO0FBQ3NEO0FBQ0o7QUFDQTtBQUNSO0FBQ1I7QUFDTTtBQUNBO0FBQzBCO0FBQ047QUFDVjtBQUNkO0FBQ1E7QUFDQTtBQUNjO0FBRXRFLHdCQUF3QjtBQUNtQztBQUMzQjtBQUNlO0FBQ1I7QUFDWDtBQUNlO0FBQ1M7QUFFcEQsa0JBQWtCO0FBRWxCLGdCQUFnQjtBQUMwQztBQUNEO0FBQ0o7QUFDOEI7QUFDdkM7QUFDa0I7QUFJOUQsU0FBUzRELFNBQVNDLElBQUk7UUFBRUMsVUFBQUEsaUVBQVU7O0lBQ2hDLElBQUlDO0lBQ0osT0FBTzt5Q0FBSUM7WUFBQUE7O1FBQ1RDLGFBQWFGO1FBQ2JBLFFBQVFHLFdBQVc7WUFDakJMLEtBQUtNLEtBQUssUUFBT0g7UUFDbkIsR0FBR0Y7SUFDTDtBQUNGO0FBR2UsU0FBU007O0lBQ3RCLE1BQU1DLFdBQVdoRSx5REFBV0E7SUFDNUIsTUFBTWlFLHlCQUF5QmhFLHlEQUFXQTs2REFBQ2lFLENBQUFBLFFBQVNBLE1BQU1DLGFBQWE7O0lBQ3ZFLE1BQU1DLHNCQUFzQm5FLHlEQUFXQTswREFBQ2lFLENBQUFBLFFBQVNBLE1BQU1HLFVBQVU7O0lBQ2pFLE1BQU1DLFVBQVVyRSx5REFBV0E7OENBQUNpRSxDQUFBQSxRQUFTQSxNQUFNSSxPQUFPOztJQUNsRCxNQUFNQyxRQUFRNUMsa1RBQVFBO0lBRXRCLE1BQU0sQ0FBQzZDLFdBQVdDLGFBQWEsR0FBRzNFLCtDQUFRQSxDQUFDdUQseURBQVNBO0lBRXBELE1BQU0sQ0FBQ3FCLGtCQUFrQkMsb0JBQW9CLEdBQUc3RSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM4RSxlQUFlQyxpQkFBaUIsR0FBRy9FLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2dGLHFCQUFxQkMsdUJBQXVCLEdBQUdqRiwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNrRixrQkFBa0JDLG9CQUFvQixHQUFHbkYsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDb0YscUJBQXFCQyx1QkFBdUIsR0FBR3JGLCtDQUFRQSxDQUFDLE9BQU8sZ0NBQWdDO0lBQ3RHLE1BQU0sQ0FBQ3NGLGdCQUFnQkMsa0JBQWtCLEdBQUd2RiwrQ0FBUUEsQ0FBQzBFLFVBQVVZLGNBQWMsS0FBS0UsWUFBWWQsVUFBVVksY0FBYyxHQUFHO0lBQ3pILE1BQU0sQ0FBQ0csUUFBUUMsVUFBVSxHQUFHMUYsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDMkYsY0FBY0MsZ0JBQWdCLEdBQUc1RiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUM2RixvQkFBb0JDLHNCQUFzQixHQUFHOUYsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDK0YscUJBQXFCQyx1QkFBdUIsR0FBR2hHLCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ2lHLGNBQWNDLGdCQUFnQixHQUFHbEcsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDbUcsaUJBQWlCQyxtQkFBbUIsR0FBR3BHLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ3FHLGtCQUFrQkMsb0JBQW9CLEdBQUd0RywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUN1RyxtQkFBbUJDLHFCQUFxQixHQUFHeEcsK0NBQVFBLENBQUMsRUFBRTtJQUM3RCxNQUFNLENBQUN5RyxjQUFjQyxnQkFBZ0IsR0FBRzFHLCtDQUFRQSxDQUFDMEUsVUFBVStCLFlBQVksS0FBS2pCLFlBQVlkLFVBQVUrQixZQUFZLEdBQUc7SUFDakgsTUFBTSxDQUFDRSxrQkFBa0JDLG9CQUFvQixHQUFHNUcsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDNkcsY0FBY0MsZ0JBQWdCLEdBQUc5RywrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMrRyxhQUFhQyxlQUFlLEdBQUdoSCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNpSCxZQUFZQyxjQUFjLEdBQUdsSCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNtSCxxQkFBcUJDLHVCQUF1QixHQUFHcEgsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDcUgsbUJBQW1CQyxxQkFBcUIsR0FBR3RILCtDQUFRQSxDQUFDO0lBRTNELE1BQU11SCxlQUFleEgsNkNBQU1BLENBQUM7SUFDNUIsTUFBTXlILHVCQUF1QnpILDZDQUFNQSxDQUFDO0lBQ3BDLE1BQU0wSCx1QkFBdUIxSCw2Q0FBTUEsQ0FBQztJQUNwQyxNQUFNMkgsNkJBQTZCM0gsNkNBQU1BLENBQUM7SUFDMUMsTUFBTTRILDBCQUEwQjVILDZDQUFNQSxDQUFDO0lBQ3ZDLE1BQU02SCxlQUFlN0gsNkNBQU1BLENBQUM7SUFDNUIsTUFBTThILGtCQUFrQjlILDZDQUFNQSxDQUFDO1FBQUUrSCxRQUFRO1FBQUlDLFlBQVk7SUFBRztJQUM1RCxNQUFNQyxrQkFBa0JqSSw2Q0FBTUEsQ0FBQzBHO0lBQy9CLE1BQU13QixvQkFBb0JsSSw2Q0FBTUEsQ0FBQ3VGO0lBQ2pDLE1BQU00QyxvQ0FBb0NuSSw2Q0FBTUEsQ0FBQztJQUVqRCxNQUFNb0ksZUFBZXRJLGtEQUFXQTttREFBQyxTQUFDdUk7Z0JBQVNDLDRFQUFXO1lBQ3BEakMsbUJBQW1CZ0M7WUFDbkI5QixvQkFBb0IrQjtZQUNwQm5DLGdCQUFnQjtRQUNsQjtrREFBRyxFQUFFO0lBRUwsTUFBTW9DLHNCQUFzQjtRQUMxQixNQUFNQyxZQUFZaEYseURBQVNBO1FBQzNCb0IsYUFBYTREO1FBQ2J2QixlQUFlO1FBQ2Z6QixrQkFBa0JnRCxVQUFVakQsY0FBYyxLQUFLRSxZQUFZK0MsVUFBVWpELGNBQWMsR0FBRztRQUN0Rm9CLGdCQUFnQjZCLFVBQVU5QixZQUFZLEtBQUtqQixZQUFZK0MsVUFBVTlCLFlBQVksR0FBRztJQUNsRjtJQUVBM0csZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTTBJLGdCQUFnQjlEO1lBQ3RCLE1BQU0rRDt3REFBZTtvQkFDbkIsSUFBSTt3QkFDRixJQUFJRCxjQUFjRSxPQUFPLENBQUNDLFVBQVUsQ0FBQyxXQUFXOzRCQUM5QyxJQUFJLENBQUNILGNBQWNJLFNBQVMsRUFBRTtnQ0FDNUJULGFBQWEsdURBQXVEO2dDQUNwRXpDLFVBQVU7Z0NBQ1Y7NEJBQ0Y7NEJBQ0EsTUFBTW1ELFFBQVEsSUFBSWpHLHFFQUFrQkEsQ0FBQzRGLGNBQWNJLFNBQVM7NEJBQzVEbEQsVUFBVW1EO3dCQUNaLE9BQU87NEJBQ0wsSUFBSSxDQUFDTCxjQUFjTSxTQUFTLEVBQUU7Z0NBQzVCWCxhQUFhLHVEQUF1RDtnQ0FDcEV6QyxVQUFVO2dDQUNWOzRCQUNGOzRCQUNBLE1BQU1xRCxlQUFlLElBQUloRywrQ0FBTUEsQ0FBQztnQ0FDOUJpRyxRQUFRUixjQUFjTSxTQUFTO2dDQUMvQkcseUJBQXlCOzRCQUMzQjs0QkFDQXZELFVBQVVxRDt3QkFDWjtvQkFDRixFQUFFLE9BQU9HLE9BQU87d0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO3dCQUMvQ2YsYUFBYSxtQ0FBbUNlLE1BQU1kLE9BQU8sRUFBRTt3QkFDL0QxQyxVQUFVO29CQUNaLFNBQVU7d0JBQ1JzQixlQUFlO29CQUNqQjtnQkFDRjs7WUFDQSxJQUFJRCxhQUFhMEI7UUFDbkI7a0NBQUc7UUFBQy9EO1FBQVdxQztRQUFhb0I7S0FBYTtJQUV6Q3JJLGdEQUFTQTttQ0FBQztZQUFRa0ksZ0JBQWdCb0IsT0FBTyxHQUFHM0M7UUFBYztrQ0FBRztRQUFDQTtLQUFhO0lBQzNFM0csZ0RBQVNBO21DQUFDO1lBQVFtSSxrQkFBa0JtQixPQUFPLEdBQUc5RDtRQUFnQjtrQ0FBRztRQUFDQTtLQUFlO0lBRWpGeEYsZ0RBQVNBO21DQUFDO1lBQ1JvSSxrQ0FBa0NrQixPQUFPLEdBQUd0RyxzREFBUUE7MkNBQUMsQ0FBQ3VHO29CQUNwRG5GLFNBQVNmLHNFQUFhQSxDQUFDa0c7Z0JBQ3pCOzBDQUFHLEtBQUs7Z0JBQUVDLFNBQVM7Z0JBQU1DLFVBQVU7WUFBSztZQUV4QzsyQ0FBTztvQkFDTCxJQUFJckIsa0NBQWtDa0IsT0FBTyxJQUFJLE9BQU9sQixrQ0FBa0NrQixPQUFPLENBQUNJLE1BQU0sS0FBSyxZQUFZO3dCQUN2SHRCLGtDQUFrQ2tCLE9BQU8sQ0FBQ0ksTUFBTTtvQkFDbEQ7Z0JBQ0Y7O1FBQ0Y7a0NBQUc7UUFBQ3RGO0tBQVM7SUFFYixNQUFNdUYsc0JBQXNCLElBQU12RCxnQkFBZ0I7SUFFbEQsTUFBTXdELGdCQUFnQixPQUFPQztRQUMzQixNQUFNQyxnQkFBZ0JELFdBQVcsV0FBVzNFLHNCQUFzQkU7UUFDbEUsTUFBTTJFLGFBQWFGLFdBQVcsV0FBVy9FLG1CQUFtQkU7UUFFNUQsSUFBSTtZQUNGLHVDQUF1QztZQUN2QyxJQUFJOEUsZUFBZTtnQkFDakIsTUFBTUEsY0FBY0UsSUFBSTtZQUMxQjtZQUVBLHNEQUFzRDtZQUN0RCxJQUFJRCxjQUFjLE9BQU9BLFdBQVdFLDhCQUE4QixLQUFLLFlBQVk7Z0JBQ2pGLE1BQU1GLFdBQVdFLDhCQUE4QjtnQkFDL0MsSUFBSUYsV0FBV0csV0FBVyxJQUFJSCxXQUFXRyxXQUFXLENBQUNDLFVBQVUsSUFBSUosV0FBV0csV0FBVyxDQUFDQyxVQUFVLENBQUNDLFVBQVUsRUFBRTtvQkFDL0csTUFBTUMsU0FBU04sV0FBV0csV0FBVyxDQUFDQyxVQUFVLENBQUNDLFVBQVU7b0JBQzNELElBQUlDLGtCQUFrQkMsYUFBYTt3QkFDakNELE9BQU9FLFNBQVMsR0FBR0MsT0FBTyxDQUFDQyxDQUFBQTs0QkFDekJBLE1BQU1ULElBQUk7d0JBQ1o7b0JBQ0Y7Z0JBQ0Y7Z0JBQ0EsSUFBSUQsV0FBV0csV0FBVyxJQUFJLE9BQU9ILFdBQVdHLFdBQVcsQ0FBQ1EsS0FBSyxLQUFLLFlBQVk7b0JBQ2hGWCxXQUFXRyxXQUFXLENBQUNRLEtBQUs7Z0JBQzlCO1lBQ0Y7UUFDRixFQUFFLE9BQU90QixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQkFBeUIsT0FBUFMsUUFBTyxrQkFBZ0JUO1lBQ3ZEZixhQUFhLGtCQUFtQ2UsT0FBakJTLFFBQU8sWUFBd0IsT0FBZFQsTUFBTWQsT0FBTyxHQUFJO1FBQ25FLFNBQVU7WUFDUixJQUFJdUIsV0FBVyxVQUFVO2dCQUN2QjNELHVCQUF1QjtnQkFDdkJuQixvQkFBb0I7Z0JBQ3BCSSx1QkFBdUI7WUFDekIsT0FBTztnQkFDTGEsc0JBQXNCO2dCQUN0QmYsaUJBQWlCO2dCQUNqQkksb0JBQW9CO2dCQUNwQkUsdUJBQXVCLE9BQU8sZ0RBQWdEO1lBQ2hGO1FBQ0Y7SUFDRjtJQUVBLE1BQU1vRixpQ0FBaUM7UUFDckM1QyxnQkFBZ0J1QixPQUFPLENBQUN0QixNQUFNLEdBQUc7UUFDakNKLDJCQUEyQjBCLE9BQU8sR0FBRztRQUNyQ2xGLFNBQVNiLDhFQUFrQkE7SUFDN0I7SUFFQSxNQUFNcUgsOEJBQThCO1FBQ2xDN0MsZ0JBQWdCdUIsT0FBTyxDQUFDckIsVUFBVSxHQUFHO1FBQ3JDSix3QkFBd0J5QixPQUFPLEdBQUc7UUFDbEN4QyxvQkFBb0I7SUFDdEI7SUFFQSxNQUFNK0QsMkJBQTJCLENBQUNDLE1BQU1qQjtRQUN0QyxNQUFNa0IsWUFBWUQsS0FBS0UsT0FBTyxDQUFDLFFBQVEsS0FBS0MsSUFBSTtRQUNoRCxJQUFJLENBQUNGLFdBQVc7UUFFaEJoRCxnQkFBZ0J1QixPQUFPLENBQUNPLE9BQU8sSUFBSWtCLFlBQVk7UUFFL0MsSUFBSWxCLFdBQVcsVUFBVTtZQUN2QnpGLFNBQVNaLDRFQUFnQkEsQ0FBQ3VFLGdCQUFnQnVCLE9BQU8sQ0FBQ3RCLE1BQU0sR0FBR0osMkJBQTJCMEIsT0FBTztRQUMvRixPQUFPO1lBQ0x4QyxvQkFBb0JpQixnQkFBZ0J1QixPQUFPLENBQUNyQixVQUFVLEdBQUdKLHdCQUF3QnlCLE9BQU87UUFDMUY7UUFFQSxNQUFNWixnQkFBZ0JqRix5REFBU0E7UUFDL0IsTUFBTXlILDhCQUE4QnhDLGNBQWN5QyxvQkFBb0I7UUFFdEUsSUFBSSxXQUFZLFlBQVloRCxrQkFBa0JtQixPQUFPLElBQU1PLFdBQVcsZ0JBQWdCLENBQUMzQixnQkFBZ0JvQixPQUFPLEVBQUc7WUFDL0d0RixhQUFhOEQsYUFBYXdCLE9BQU87WUFDakN4QixhQUFhd0IsT0FBTyxHQUFHckYsV0FBVztnQkFDaENtSCxVQUFVckQsZ0JBQWdCdUIsT0FBTyxDQUFDTyxPQUFPLENBQUNvQixJQUFJLElBQUlwQjtZQUNwRCxHQUFHcUIsOEJBQThCO1FBQ25DO0lBQ0Y7SUFFQSxNQUFNRywwQkFBMEIsQ0FBQ0MsT0FBT3pCO1FBQ3RDLElBQUlBLFdBQVcsVUFBVTtZQUN2QnpGLFNBQVNaLDRFQUFnQkEsQ0FBQzhIO1lBQzFCdkQsZ0JBQWdCdUIsT0FBTyxDQUFDdEIsTUFBTSxHQUFHc0Q7UUFDbkMsT0FBTztZQUNMeEUsb0JBQW9Cd0U7WUFDcEJ2RCxnQkFBZ0J1QixPQUFPLENBQUNyQixVQUFVLEdBQUdxRDtRQUN2QztJQUNGO0lBRUEsTUFBTUMscUJBQXFCLENBQUMxQjtRQUMxQixNQUFNMkIsZUFBZTNCLFdBQVcsV0FBV3hGLHlCQUF5QndDO1FBQ3BFLElBQUkyRSxhQUFhUCxJQUFJLElBQUk7WUFDdkJHLFVBQVVJLGFBQWFQLElBQUksSUFBSXBCO1FBQ2pDLE9BQU87WUFDTHhCLGFBQWEsbUJBQW1CO1FBQ2xDO0lBQ0Y7SUFFQSxNQUFNb0QsaUJBQWlCLENBQUNDLEdBQUc3QjtRQUN6QixJQUFJNkIsRUFBRUMsR0FBRyxLQUFLLFdBQVcsQ0FBQ0QsRUFBRUUsUUFBUSxFQUFFO1lBQ3BDRixFQUFFRyxjQUFjO1lBQ2hCTixtQkFBbUIxQjtRQUNyQjtJQUNGO0lBRUEsTUFBTWlDLHlCQUF5QjtRQUM3QixJQUFJckYsa0JBQWtCc0YsTUFBTSxLQUFLLEdBQUc7WUFDbEMxRCxhQUFhLHFDQUFxQztZQUNsRDtRQUNGO1FBQ0EsTUFBTTJELGtCQUFrQnRILFFBQVF1SCxNQUFNLENBQUNQLENBQUFBLElBQUtBLEVBQUVRLElBQUksS0FBSyxZQUFZQyxLQUFLLEdBQUdDLE9BQU87UUFDbEYsTUFBTUMsZ0JBQWdCNUYsa0JBQWtCNkYsR0FBRyxDQUFDQyxDQUFBQTtnQkFDbkNQO1lBQVAsUUFBT0EsZ0RBQUFBLGVBQWUsQ0FBQ08sNkJBQTZCLGNBQTdDUCxvRUFBQUEsOENBQStDbEIsSUFBSTtRQUM1RCxHQUFHbUIsTUFBTSxDQUFDbkIsQ0FBQUEsT0FBUUE7UUFFbEIsSUFBSXVCLGNBQWNOLE1BQU0sS0FBSyxHQUFHO1lBQzlCMUQsYUFBYSwrQ0FBK0M7WUFDNUQ7UUFDRjtRQUVBLE1BQU1tRSxlQUFlSCxjQUFjSSxJQUFJLENBQUM7UUFDeENyQixVQUFVb0IsY0FBYztRQUN4QjlGLHFCQUFxQixFQUFFO0lBQ3pCO0lBRUEsZ0RBQWdEO0lBQ2hELE1BQU1nRyw4QkFBOEIsT0FBT0MsYUFBYTlDO1FBQ3RELE1BQU1uQixnQkFBZ0JqRix5REFBU0E7UUFDL0IsSUFBSSxDQUFDaUYsY0FBY2tFLFVBQVUsSUFBSSxDQUFDbEUsY0FBY21FLFdBQVcsRUFBRTtZQUMzRCxNQUFNLElBQUlDLE1BQU07UUFDbEI7UUFFQSxJQUFJNUM7UUFDSixJQUFJO1lBQ0ZBLGNBQWM2QyxVQUFVQyxXQUFXLENBQUNDLGVBQWUsQ0FBQ047UUFDdEQsRUFBRSxPQUFPTyxhQUFhO1lBQ3BCN0QsUUFBUUQsS0FBSyxDQUFDLGtDQUF5QyxPQUFQUyxRQUFPLE1BQUlxRDtZQUMzRCxNQUFNLElBQUlKLE1BQU0sOEJBQXlDSSxPQUFYckQsUUFBTyxNQUF3QixPQUFwQnFELFlBQVk1RSxPQUFPO1FBQzlFO1FBRUEsTUFBTTZFLGVBQWVKLFVBQVVLLFlBQVksQ0FBQ0MsZ0JBQWdCLENBQUMzRSxjQUFja0UsVUFBVSxFQUFFbEUsY0FBY21FLFdBQVc7UUFDaEhNLGFBQWFHLHlCQUF5QixHQUFHNUUsY0FBYzZFLGFBQWE7UUFFcEUsTUFBTXhELGFBQWEsSUFBSWdELFVBQVVTLGdCQUFnQixDQUFDTCxjQUFjakQ7UUFFaEVILFdBQVcwRCxXQUFXLEdBQUcsQ0FBQ0MsR0FBR2hDO1lBQzNCLElBQUlBLEVBQUVpQyxNQUFNLENBQUNDLE1BQU0sS0FBS2IsVUFBVWMsWUFBWSxDQUFDQyxpQkFBaUIsRUFBRTtnQkFDaEUsTUFBTUMsY0FBY3JDLEVBQUVpQyxNQUFNLENBQUM3QyxJQUFJO2dCQUNqQyxJQUFJakIsV0FBVyxVQUFVO29CQUN2QmpDLDJCQUEyQjBCLE9BQU8sR0FBR3lFO29CQUNyQzNKLFNBQVNaLDRFQUFnQkEsQ0FBQ3VFLGdCQUFnQnVCLE9BQU8sQ0FBQ3RCLE1BQU0sR0FBRytGO2dCQUM3RCxPQUFPO29CQUNMbEcsd0JBQXdCeUIsT0FBTyxHQUFHeUU7b0JBQ2xDakgsb0JBQW9CaUIsZ0JBQWdCdUIsT0FBTyxDQUFDckIsVUFBVSxHQUFHOEY7Z0JBQzNEO1lBQ0Y7UUFDRjtRQUVBaEUsV0FBV2lFLFVBQVUsR0FBRyxDQUFDTixHQUFHaEM7WUFDMUIsSUFBSUEsRUFBRWlDLE1BQU0sQ0FBQ0MsTUFBTSxLQUFLYixVQUFVYyxZQUFZLENBQUNJLGdCQUFnQixJQUFJdkMsRUFBRWlDLE1BQU0sQ0FBQzdDLElBQUksRUFBRTtnQkFDaEYsSUFBSWpCLFdBQVcsVUFBVWpDLDJCQUEyQjBCLE9BQU8sR0FBRztxQkFDekR6Qix3QkFBd0J5QixPQUFPLEdBQUc7Z0JBQ3ZDdUIseUJBQXlCYSxFQUFFaUMsTUFBTSxDQUFDN0MsSUFBSSxFQUFFakI7WUFDMUM7UUFDRjtRQUVBRSxXQUFXbUUsUUFBUSxHQUFHLENBQUNSLEdBQUdoQztZQUN4QnJDLFFBQVE4RSxHQUFHLENBQUMsb0JBQW9DdEUsT0FBaEI2QixFQUFFa0MsTUFBTSxFQUFDLFNBQWMsT0FBUC9EO1lBQ2hELElBQUk2QixFQUFFa0MsTUFBTSxLQUFLYixVQUFVcUIsa0JBQWtCLENBQUN0QixLQUFLLEVBQUU7Z0JBQ25EekQsUUFBUUQsS0FBSyxDQUFDLHVCQUFtQyxPQUFac0MsRUFBRTJDLFNBQVM7Z0JBQ2hEaEYsUUFBUUQsS0FBSyxDQUFDLDBCQUF5QyxPQUFmc0MsRUFBRTRDLFlBQVk7Z0JBQ3REakcsYUFBYSxnQ0FBMkNxRCxPQUFYN0IsUUFBTyxNQUFtQixPQUFmNkIsRUFBRTRDLFlBQVksR0FBSTtZQUM1RTtZQUNBMUUsY0FBY0M7UUFDaEI7UUFFQUUsV0FBV3dFLGNBQWMsR0FBRyxDQUFDYixHQUFHaEM7WUFDOUJyQyxRQUFROEUsR0FBRyxDQUFDLDZCQUFvQyxPQUFQdEUsUUFBTztZQUNoREQsY0FBY0M7UUFDaEI7UUFFQSxJQUFJO1lBQ0YsTUFBTUUsV0FBV3lFLCtCQUErQjtZQUNoRG5HLGFBQWEsR0FBdUQsT0FBcER3QixXQUFXLFdBQVcsaUJBQWlCLGNBQWEsdUNBQXFDO1lBQ3pHLE9BQU9FO1FBQ1QsRUFBRSxPQUFPWCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQkFBeUIsT0FBUFMsUUFBTyw2QkFBMkJUO1lBQ2xFLElBQUljLGVBQWUsT0FBT0EsWUFBWVEsS0FBSyxLQUFLLFlBQVlSLFlBQVlRLEtBQUs7WUFDN0UsTUFBTXRCO1FBQ1I7SUFDRjtJQUVBLE1BQU1xRix5QkFBeUIsT0FBTzlCLGFBQWE5QztRQUNqRCxNQUFNbkIsZ0JBQWdCakYseURBQVNBO1FBRS9CLGtDQUFrQztRQUNsQyxNQUFNaUwsWUFBWTtZQUNoQkMsU0FBUyxDQUFDOUU7Z0JBQ1JSLFFBQVE4RSxHQUFHLENBQUMsa0NBQXlDLE9BQVB0RTtnQkFDOUN4QixhQUFhLEdBQXVELE9BQXBEd0IsV0FBVyxXQUFXLGlCQUFpQixjQUFhLHdCQUFzQjtZQUM1RjtZQUVBK0UsaUJBQWlCLENBQUM5RCxNQUFNakI7Z0JBQ3RCUixRQUFROEUsR0FBRyxDQUFDLHNCQUE2QixPQUFQdEUsUUFBTyxNQUFJaUI7Z0JBQzdDLElBQUlqQixXQUFXLFVBQVU7b0JBQ3ZCakMsMkJBQTJCMEIsT0FBTyxHQUFHd0I7b0JBQ3JDMUcsU0FBU1osNEVBQWdCQSxDQUFDdUUsZ0JBQWdCdUIsT0FBTyxDQUFDdEIsTUFBTSxHQUFHOEM7Z0JBQzdELE9BQU87b0JBQ0xqRCx3QkFBd0J5QixPQUFPLEdBQUd3QjtvQkFDbENoRSxvQkFBb0JpQixnQkFBZ0J1QixPQUFPLENBQUNyQixVQUFVLEdBQUc2QztnQkFDM0Q7WUFDRjtZQUVBK0QsZUFBZSxDQUFDL0QsTUFBTWpCO2dCQUNwQlIsUUFBUThFLEdBQUcsQ0FBQyxvQkFBMkIsT0FBUHRFLFFBQU8sTUFBSWlCO2dCQUMzQyw4QkFBOEI7Z0JBQzlCLElBQUlqQixXQUFXLFVBQVVqQywyQkFBMkIwQixPQUFPLEdBQUc7cUJBQ3pEekIsd0JBQXdCeUIsT0FBTyxHQUFHO2dCQUV2QyxpQ0FBaUM7Z0JBQ2pDdUIseUJBQXlCQyxNQUFNakI7WUFDakM7WUFFQWlGLFNBQVMsQ0FBQzFGLE9BQU9TO2dCQUNmUixRQUFRRCxLQUFLLENBQUMsZ0NBQXVDLE9BQVBTLFFBQU8sTUFBSVQ7Z0JBQ3pEZixhQUFhLGdDQUEyQ2UsT0FBWFMsUUFBTyxNQUFrQixPQUFkVCxNQUFNZCxPQUFPLEdBQUk7Z0JBQ3pFc0IsY0FBY0M7WUFDaEI7WUFFQWtGLFFBQVEsQ0FBQ2xGO2dCQUNQUixRQUFROEUsR0FBRyxDQUFDLGtDQUF5QyxPQUFQdEU7WUFDaEQ7UUFDRjtRQUVBLElBQUk7WUFDRixxRUFBcUU7WUFDckUsTUFBTW1GLGtCQUFrQnRHLGNBQWNvQixhQUFhLElBQUk7WUFDdkQsSUFBSW1GLGVBQWN2RztZQUVsQixJQUFJc0csb0JBQW9CLGNBQWMsQ0FBQ3RHLGNBQWN3RyxXQUFXLEVBQUU7Z0JBQ2hFLDhFQUE4RTtnQkFDOUUsSUFBSXhHLGNBQWNrRSxVQUFVLElBQUlsRSxjQUFjbUUsV0FBVyxFQUFFO29CQUN6RHhELFFBQVE4RSxHQUFHLENBQUM7b0JBQ1pjLGVBQWM7d0JBQUUsR0FBR3ZHLGFBQWE7d0JBQUVvQixlQUFlO29CQUFRO29CQUN6RHpCLGFBQWEsNkRBQTZEO2dCQUM1RSxPQUFPO29CQUNMLE1BQU0sSUFBSXlFLE1BQU07Z0JBQ2xCO1lBQ0YsT0FBTyxJQUFJa0Msb0JBQW9CLFdBQVksRUFBQ3RHLGNBQWNrRSxVQUFVLElBQUksQ0FBQ2xFLGNBQWNtRSxXQUFXLEdBQUc7Z0JBQ25HLG1GQUFtRjtnQkFDbkYsSUFBSW5FLGNBQWN3RyxXQUFXLEVBQUU7b0JBQzdCN0YsUUFBUThFLEdBQUcsQ0FBQztvQkFDWmMsZUFBYzt3QkFBRSxHQUFHdkcsYUFBYTt3QkFBRW9CLGVBQWU7b0JBQVc7b0JBQzVEekIsYUFBYSxxREFBcUQ7Z0JBQ3BFLE9BQU87b0JBQ0wsTUFBTSxJQUFJeUUsTUFBTTtnQkFDbEI7WUFDRjtZQUVBLHdDQUF3QztZQUN4QyxNQUFNaEQsZ0JBQWdCcEcsMkVBQW1CQSxDQUFDdUwsY0FBYVA7WUFFdkQsb0NBQW9DO1lBQ3BDbkosdUJBQXVCMEosYUFBWW5GLGFBQWE7WUFFaEQsMkJBQTJCO1lBQzNCLE1BQU1BLGNBQWNxRixLQUFLLENBQUN4QyxhQUFhOUM7WUFFdkMsK0JBQStCO1lBQy9CLE1BQU11RixjQUFjSCxhQUFZbkYsYUFBYSxLQUFLLGFBQWEsYUFBYTtZQUM1RXpCLGFBQWEsU0FBNEJ3QixPQUFuQnVGLGFBQVksU0FBMkQsT0FBcER2RixXQUFXLFdBQVcsaUJBQWlCLGVBQWdCO1lBRWhHLE9BQU9DO1FBQ1QsRUFBRSxPQUFPVixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxxQ0FBNEMsT0FBUFMsUUFBTyxNQUFJVDtZQUU5RCxrRkFBa0Y7WUFDbEYsSUFBSTZGLFlBQVluRixhQUFhLEtBQUssY0FBY3BCLGNBQWNrRSxVQUFVLElBQUlsRSxjQUFjbUUsV0FBVyxFQUFFO2dCQUNyRyxJQUFJO29CQUNGeEQsUUFBUThFLEdBQUcsQ0FBQyxvREFBMkQsT0FBUHRFO29CQUVoRSxxQ0FBcUM7b0JBQ3JDLE1BQU13RixjQUFjO3dCQUFFLEdBQUczRyxhQUFhO3dCQUFFb0IsZUFBZTtvQkFBUTtvQkFDL0QsTUFBTXdGLHFCQUFxQjVMLDJFQUFtQkEsQ0FBQzJMLGFBQWFYO29CQUU1RCxvQ0FBb0M7b0JBQ3BDbkosdUJBQXVCO29CQUV2QixpQ0FBaUM7b0JBQ2pDLE1BQU0rSixtQkFBbUJILEtBQUssQ0FBQ3hDLGFBQWE5QztvQkFFNUN4QixhQUFjLGtFQUFpRTtvQkFDL0UsT0FBT2lIO2dCQUNULEVBQUUsT0FBT0MsWUFBWTtvQkFDbkJsRyxRQUFRRCxLQUFLLENBQUMsd0NBQStDLE9BQVBTLFFBQU8sTUFBSTBGO2dCQUNuRTtZQUNGO1lBRUEsZ0RBQWdEO1lBQ2hELElBQUk3RyxjQUFja0UsVUFBVSxJQUFJbEUsY0FBY21FLFdBQVcsRUFBRTtnQkFDekQsSUFBSTtvQkFDRnhELFFBQVE4RSxHQUFHLENBQUMsd0NBQStDLE9BQVB0RTtvQkFDcEQsTUFBTTJGLG1CQUFtQixNQUFNOUMsNEJBQTRCQyxhQUFhOUM7b0JBQ3hFeEIsYUFBYyxzQ0FBcUM7b0JBQ25ELE9BQU9tSDtnQkFDVCxFQUFFLE9BQU9DLGFBQWE7b0JBQ3BCcEcsUUFBUUQsS0FBSyxDQUFDLHlDQUFnRCxPQUFQUyxRQUFPLE1BQUk0RjtnQkFDcEU7WUFDRjtZQUVBLGlDQUFpQztZQUNqQyxJQUFJckcsTUFBTWQsT0FBTyxDQUFDb0gsUUFBUSxDQUFDLGNBQWN0RyxNQUFNZCxPQUFPLENBQUNvSCxRQUFRLENBQUMsZ0JBQWdCO2dCQUM5RXJILGFBQWEsR0FBaUIsT0FBZGUsTUFBTWQsT0FBTyxHQUFJO1lBQ25DLE9BQU8sSUFBSWMsTUFBTWQsT0FBTyxDQUFDb0gsUUFBUSxDQUFDLGFBQWE7Z0JBQzdDckgsYUFBYSxHQUFpQixPQUFkZSxNQUFNZCxPQUFPLEdBQUk7WUFDbkMsT0FBTztnQkFDTEQsYUFBYSxtQkFBMENlLE9BQXZCUyxRQUFPLGtCQUE4QixPQUFkVCxNQUFNZCxPQUFPLEdBQUk7WUFDMUU7WUFFQXFFLFlBQVlwQyxTQUFTLEdBQUdDLE9BQU8sQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTVQsSUFBSTtZQUNuRCxPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU0yRiw4QkFBOEI7UUFDbEMsSUFBSTFKLHFCQUFxQjtZQUN2QixNQUFNMkQsY0FBYztZQUNwQjtRQUNGO1FBRUEsSUFBSSxDQUFDZ0csVUFBVUMsWUFBWSxJQUFJLENBQUNELFVBQVVDLFlBQVksQ0FBQ0MsZUFBZSxFQUFFO1lBQ3RFekgsYUFBYSxvREFBb0Q7WUFDakVuQyx1QkFBdUI7WUFDdkI7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNeUcsY0FBYyxNQUFNaUQsVUFBVUMsWUFBWSxDQUFDQyxlQUFlLENBQUM7Z0JBQy9EQyxPQUFPO2dCQUNQQyxPQUFPO29CQUNMQyxnQkFBZ0I7b0JBQ2hCQyxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxNQUFNQyxjQUFjeEQsWUFBWXlELGNBQWM7WUFDOUMsSUFBSUQsWUFBWXBFLE1BQU0sS0FBSyxHQUFHO2dCQUM1QjFELGFBQWEsc0VBQXNFO2dCQUNuRnNFLFlBQVlwQyxTQUFTLEdBQUdDLE9BQU8sQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTVQsSUFBSTtnQkFDbkQ7WUFDRjtZQUVBLElBQUlsRixrQkFBa0I7Z0JBQ3BCLE1BQU04RSxjQUFjO1lBQ3RCO1lBRUEsTUFBTXlHLHdCQUF3QixNQUFNNUIsdUJBQXVCOUIsYUFBYTtZQUN4RSxJQUFJMEQsdUJBQXVCO2dCQUN6QiwwREFBMEQ7Z0JBQzFELElBQUlBLHNCQUFzQmxCLEtBQUssSUFBSWtCLHNCQUFzQnJHLElBQUksRUFBRTtvQkFDN0QscUJBQXFCO29CQUNyQjdFLHVCQUF1QmtMO2dCQUN6QixPQUFPO29CQUNMLDBCQUEwQjtvQkFDMUJ0TCxvQkFBb0JzTDtnQkFDdEI7Z0JBQ0FuSyx1QkFBdUI7Z0JBQ3ZCeUcsWUFBWXBDLFNBQVMsR0FBR0MsT0FBTyxDQUFDQyxDQUFBQTtvQkFDOUJBLE1BQU02RixPQUFPLEdBQUc7d0JBQ2RqSSxhQUFhLHNCQUFzQjt3QkFDbkN1QixjQUFjO29CQUNoQjtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wrQyxZQUFZcEMsU0FBUyxHQUFHQyxPQUFPLENBQUNDLENBQUFBLFFBQVNBLE1BQU1ULElBQUk7WUFDckQ7UUFDRixFQUFFLE9BQU9aLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0MsSUFBSUEsTUFBTW1ILElBQUksS0FBSyxtQkFBbUI7Z0JBQ3BDbEksYUFBYSxnRUFBZ0U7WUFDL0UsT0FBTyxJQUFJZSxNQUFNbUgsSUFBSSxLQUFLLGlCQUFpQjtnQkFDekNsSSxhQUFhLDBDQUEwQztZQUN6RCxPQUFPLElBQUllLE1BQU1tSCxJQUFJLEtBQUsscUJBQXFCO2dCQUM3Q2xJLGFBQWEsdURBQXVEO1lBQ3RFLE9BQU87Z0JBQ0xBLGFBQWEseUNBQTBFLE9BQWpDZSxNQUFNZCxPQUFPLElBQUksa0JBQW1CO1lBQzVGO1lBQ0FwQyx1QkFBdUI7UUFDekI7SUFDRjtJQUVBLE1BQU1zSyw2QkFBNkI7UUFDakMsSUFBSXpLLG9CQUFvQjtZQUN0QixNQUFNNkQsY0FBYztZQUNwQjtRQUNGO1FBQ0EsSUFBSTtZQUNGLE1BQU0rQyxjQUFjLE1BQU1pRCxVQUFVQyxZQUFZLENBQUNZLFlBQVksQ0FBQztnQkFBRVYsT0FBTztZQUFLO1lBQzVFLElBQUkzSyxrQkFBa0IsTUFBTXdFLGNBQWM7WUFFMUMsTUFBTXlHLHdCQUF3QixNQUFNNUIsdUJBQXVCOUIsYUFBYTtZQUN4RSxJQUFJMEQsdUJBQXVCO2dCQUN6QiwwREFBMEQ7Z0JBQzFELElBQUlBLHNCQUFzQmxCLEtBQUssSUFBSWtCLHNCQUFzQnJHLElBQUksRUFBRTtvQkFDN0QscUJBQXFCO29CQUNyQjNFLG9CQUFvQmdMO2dCQUN0QixPQUFPO29CQUNMLDBCQUEwQjtvQkFDMUJwTCxpQkFBaUJvTDtnQkFDbkI7Z0JBQ0FySyxzQkFBc0I7WUFDeEIsT0FBTztnQkFDTDJHLFlBQVlwQyxTQUFTLEdBQUdDLE9BQU8sQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTVQsSUFBSTtZQUNyRDtRQUNGLEVBQUUsT0FBT1osT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxJQUFJQSxNQUFNbUgsSUFBSSxLQUFLLHFCQUFxQm5ILE1BQU1tSCxJQUFJLEtBQUssaUJBQWlCO2dCQUN0RWxJLGFBQWEsMERBQTBEO1lBQ3pFLE9BQU87Z0JBQ0xBLGFBQWEsZ0NBQWlFLE9BQWpDZSxNQUFNZCxPQUFPLElBQUksa0JBQW1CO1lBQ25GO1lBQ0F0QyxzQkFBc0I7UUFDeEI7SUFDRjtJQUVBLDJFQUEyRTtJQUMzRSxNQUFNMEsscUJBQXFCLENBQUM1RixNQUFNakI7UUFDaEMsTUFBTThHLGFBQWE3RixLQUFLaUIsTUFBTTtRQUM5QixNQUFNNkUsb0JBQW9COUYsS0FBSzRFLFFBQVEsQ0FBQyxRQUFRaUIsYUFBYTtRQUM3RCxNQUFNRSxhQUFhaEgsV0FBVztRQUU5QixJQUFJZ0gsY0FBY0QscUJBQXFCRCxhQUFhLEtBQUssT0FBTztRQUNoRSxJQUFJQSxhQUFhLEtBQUssT0FBTztRQUM3QixPQUFPO0lBQ1Q7SUFFQSxNQUFNdkYsWUFBWSxPQUFPTixNQUFNakI7UUFDN0IsSUFBSSxDQUFDaUIsS0FBS0csSUFBSSxJQUFJO1lBQ2hCNUMsYUFBYSw2QkFBNkI7WUFDMUM7UUFDRjtRQUNBLElBQUksQ0FBQzFDLFVBQVVzQixhQUFhO1lBQzFCb0IsYUFBYSwwREFBMEQ7WUFDdkU7UUFDRjtRQUVBLE1BQU1LLGdCQUFnQmpGLHlEQUFTQTtRQUMvQixNQUFNcU4saUJBQWlCO1lBQ3JCQyxTQUFTO2dCQUFFQyxhQUFhO2dCQUFLQyxXQUFXO1lBQUk7WUFDNUNDLFFBQVE7Z0JBQUVGLGFBQWE7Z0JBQUtDLFdBQVc7WUFBSTtZQUMzQ0UsU0FBUztnQkFBRUgsYUFBYTtnQkFBS0MsV0FBVztZQUFLO1FBQy9DO1FBQ0EsTUFBTSxFQUFFRCxXQUFXLEVBQUVDLFNBQVMsRUFBRSxHQUFHSCxjQUFjLENBQUNwSSxjQUFjMEksY0FBYyxJQUFJLFNBQVM7UUFFM0ZwSyxnQkFBZ0I7UUFDaEIsTUFBTXFLLFlBQVksSUFBSUMsT0FBT0Msa0JBQWtCLENBQUMsRUFBRSxFQUFFO1lBQUVDLE1BQU07WUFBV0MsUUFBUTtRQUFVO1FBQ3pGLElBQUlDLG1CQUFtQjtRQUV2QnROLFNBQVNkLGtFQUFZQSxDQUFDO1lBQUU0SSxNQUFNO1lBQVlwQjtZQUFNdUc7WUFBV3hIO1lBQVE4SCxRQUFRO1FBQVU7UUFDckZ2TixTQUFTZixzRUFBYUEsQ0FBQztRQUV2QixJQUFJO1lBQ0YsTUFBTXVPLDRCQUE0QmxOLFFBQy9CdUgsTUFBTSxDQUFDUCxDQUFBQSxJQUFLQSxFQUFFWixJQUFJLElBQUtZLENBQUFBLEVBQUVRLElBQUksS0FBSyxjQUFjUixFQUFFUSxJQUFJLEtBQUssVUFBUyxLQUFNUixFQUFFaUcsTUFBTSxLQUFLLFdBQ3ZGeEYsS0FBSyxDQUFDLENBQUMsR0FDUEcsR0FBRyxDQUFDdUYsQ0FBQUEsUUFBVTtvQkFDYkMsTUFBTUQsTUFBTTNGLElBQUksS0FBSyxhQUFhLFNBQVM7b0JBQzNDNkYsU0FBU0YsTUFBTS9HLElBQUk7Z0JBQ3JCO1lBRUYsSUFBSXBDLGNBQWNFLE9BQU8sQ0FBQ0MsVUFBVSxDQUFDLFdBQVc7Z0JBQzlDLG1EQUFtRDtnQkFDbkQsTUFBTW1KLGNBQWM7b0JBQ2xCQyxPQUFPdkosY0FBY0UsT0FBTztvQkFDNUJzSixrQkFBa0I7d0JBQ2hCbEI7d0JBQ0FtQixpQkFBaUJsQjtvQkFDbkI7b0JBQ0FtQixtQkFBbUI7d0JBQUVDLE9BQU87NEJBQUM7Z0NBQUV2SCxNQUFNcEMsY0FBYzRKLGVBQWU7NEJBQUM7eUJBQUU7b0JBQUM7Z0JBQ3hFO2dCQUVBLG1EQUFtRDtnQkFDbkQsSUFBSTVKLGNBQWNFLE9BQU8sQ0FBQzhHLFFBQVEsQ0FBQyxVQUFVaEgsY0FBYzZKLGNBQWMsS0FBSzdNLFdBQVc7b0JBQ3ZGLElBQUlnRCxjQUFjNkosY0FBYyxLQUFLLEdBQUc7d0JBQ3RDLG1CQUFtQjt3QkFDbkJQLFlBQVlFLGdCQUFnQixDQUFDTSxjQUFjLEdBQUc7NEJBQUVELGdCQUFnQjt3QkFBRTtvQkFDcEUsT0FBTyxJQUFJN0osY0FBYzZKLGNBQWMsR0FBRyxHQUFHO3dCQUMzQyx5QkFBeUI7d0JBQ3pCUCxZQUFZRSxnQkFBZ0IsQ0FBQ00sY0FBYyxHQUFHOzRCQUFFRCxnQkFBZ0I3SixjQUFjNkosY0FBYzt3QkFBQztvQkFDL0Y7Z0JBQ0EsNERBQTREO2dCQUM5RDtnQkFFQSxNQUFNTixRQUFRdE0sT0FBTzhNLGtCQUFrQixDQUFDVDtnQkFDeEMsTUFBTVUsT0FBT1QsTUFBTVUsU0FBUyxDQUFDO29CQUMzQmpPLFNBQVNrTiwwQkFBMEJ0RixHQUFHLENBQUNzRyxDQUFBQSxNQUFROzRCQUM3Q2QsTUFBTWMsSUFBSWQsSUFBSSxLQUFLLFNBQVMsU0FBUzs0QkFDckNPLE9BQU87Z0NBQUM7b0NBQUV2SCxNQUFNOEgsSUFBSWIsT0FBTztnQ0FBQzs2QkFBRTt3QkFDaEM7Z0JBQ0Y7Z0JBQ0EsTUFBTXBFLFNBQVMsTUFBTStFLEtBQUtHLGlCQUFpQixDQUFDL0g7Z0JBQzVDLFdBQVcsTUFBTWdJLFNBQVNuRixPQUFPdEQsTUFBTSxDQUFFO29CQUN2QyxJQUFJeUksU0FBUyxPQUFPQSxNQUFNaEksSUFBSSxLQUFLLFlBQVk7d0JBQzdDLE1BQU1pSSxZQUFZRCxNQUFNaEksSUFBSTt3QkFDNUI0RyxvQkFBb0JxQjt3QkFDcEIsSUFBSTNLLGtDQUFrQ2tCLE9BQU8sRUFBRTs0QkFDN0NsQixrQ0FBa0NrQixPQUFPLENBQUNvSTt3QkFDNUM7b0JBQ0Y7Z0JBQ0Y7WUFDRixPQUFPO2dCQUNMLHlEQUF5RDtnQkFDekQsTUFBTXNCLFdBQVc7b0JBQ2Y7d0JBQUVsQixNQUFNO3dCQUFVQyxTQUFTckosY0FBYzRKLGVBQWU7b0JBQUM7dUJBQ3REVjtvQkFDSDt3QkFBRUUsTUFBTTt3QkFBUUMsU0FBU2pIO29CQUFLO2lCQUMvQjtnQkFFRCxNQUFNbUksZ0JBQWdCO29CQUNwQmhCLE9BQU92SixjQUFjRSxPQUFPO29CQUM1Qm9LO29CQUNBM0ksUUFBUTtnQkFDVjtnQkFFQSw4Q0FBOEM7Z0JBQzlDLElBQUkzQixjQUFjRSxPQUFPLENBQUNDLFVBQVUsQ0FBQyxPQUFPO2dCQUMxQyx1REFBdUQ7Z0JBQ3ZELHNDQUFzQztnQkFDeEMsT0FBTyxJQUFJSCxjQUFjRSxPQUFPLENBQUNDLFVBQVUsQ0FBQyxVQUFVO29CQUNwRCxvRUFBb0U7b0JBQ3BFb0ssY0FBY2pDLFdBQVcsR0FBRztnQkFDOUIsT0FBTztvQkFDTCxtREFBbUQ7b0JBQ25EaUMsY0FBY2pDLFdBQVcsR0FBR0E7Z0JBQzlCO2dCQUVBLGlEQUFpRDtnQkFDakQsSUFBSXRJLGNBQWNFLE9BQU8sQ0FBQ0MsVUFBVSxDQUFDLFlBQVlILGNBQWNFLE9BQU8sQ0FBQ0MsVUFBVSxDQUFDLE9BQU87b0JBQ3ZGb0ssY0FBY0MscUJBQXFCLEdBQUdqQztnQkFDeEMsT0FBTztvQkFDTGdDLGNBQWNFLFVBQVUsR0FBR2xDO2dCQUM3QjtnQkFFQSwwREFBMEQ7Z0JBQzFELElBQUl2SSxjQUFjRSxPQUFPLENBQUNDLFVBQVUsQ0FBQyxVQUFVO29CQUM3QyxzQ0FBc0M7b0JBQ3RDLElBQUlILGNBQWMwSyxlQUFlLEVBQUU7d0JBQ2pDSCxjQUFjSSxnQkFBZ0IsR0FBRzNLLGNBQWMwSyxlQUFlO29CQUNoRTtvQkFDQSxJQUFJMUssY0FBYzRLLFNBQVMsS0FBSzVOLFdBQVc7d0JBQ3pDdU4sY0FBY0ssU0FBUyxHQUFHNUssY0FBYzRLLFNBQVMsS0FBSyxJQUFJLFFBQ2pDNUssY0FBYzRLLFNBQVMsS0FBSyxJQUFJLFdBQVc7b0JBQ3RFO2dCQUNGLE9BQU8sSUFBSTVLLGNBQWNFLE9BQU8sQ0FBQ0MsVUFBVSxDQUFDLE9BQU87b0JBQ2pELGlFQUFpRTtvQkFDakUsaUNBQWlDO29CQUNqQ29LLGNBQWM1SSxNQUFNLEdBQUc7b0JBRXZCLHNEQUFzRDtvQkFDdEQsK0NBQStDO29CQUMvQzRJLGNBQWNELFFBQVEsR0FBRzt3QkFDdkI7NEJBQUVsQixNQUFNOzRCQUFRQyxTQUFTLEdBQXVDakgsT0FBcENwQyxjQUFjNEosZUFBZSxFQUFDLFFBQVcsT0FBTHhIO3dCQUFPOzJCQUNwRThHLDBCQUEwQnpGLEtBQUssQ0FBQyxHQUFHLDBCQUEwQjtxQkFDakU7Z0JBRUQsd0RBQXdEO2dCQUN4RCxnREFBZ0Q7Z0JBQ2xEO2dCQUVBLElBQUl6RCxjQUFjRSxPQUFPLENBQUNDLFVBQVUsQ0FBQyxPQUFPO3dCQUd2QjBLLDRCQUFBQTtvQkFGbkIsK0RBQStEO29CQUMvRCxNQUFNQSxXQUFXLE1BQU01TixPQUFPK00sSUFBSSxDQUFDYyxXQUFXLENBQUNDLE1BQU0sQ0FBQ1I7b0JBQ3REdkIsbUJBQW1CNkIsRUFBQUEscUJBQUFBLFNBQVNHLE9BQU8sQ0FBQyxFQUFFLGNBQW5CSCwwQ0FBQUEsNkJBQUFBLG1CQUFxQmpMLE9BQU8sY0FBNUJpTCxpREFBQUEsMkJBQThCeEIsT0FBTyxLQUFJO29CQUM1RCxJQUFJM0osa0NBQWtDa0IsT0FBTyxFQUFFO3dCQUM3Q2xCLGtDQUFrQ2tCLE9BQU8sQ0FBQ29JO29CQUM1QztnQkFDRixPQUFPO29CQUNMLHlEQUF5RDtvQkFDekQsTUFBTXJILFNBQVMsTUFBTTFFLE9BQU8rTSxJQUFJLENBQUNjLFdBQVcsQ0FBQ0MsTUFBTSxDQUFDUjtvQkFFcEQsV0FBVyxNQUFNSCxTQUFTekksT0FBUTs0QkFDZHlJLHVCQUFBQTt3QkFBbEIsTUFBTUMsWUFBWUQsRUFBQUEsa0JBQUFBLE1BQU1ZLE9BQU8sQ0FBQyxFQUFFLGNBQWhCWix1Q0FBQUEsd0JBQUFBLGdCQUFrQmEsS0FBSyxjQUF2QmIsNENBQUFBLHNCQUF5QmYsT0FBTyxLQUFJO3dCQUN0REwsb0JBQW9CcUI7d0JBQ3BCLElBQUkzSyxrQ0FBa0NrQixPQUFPLEVBQUU7NEJBQzdDbEIsa0NBQWtDa0IsT0FBTyxDQUFDb0k7d0JBQzVDO29CQUNGO2dCQUNGO1lBQ0Y7WUFDQSxJQUFJdEosa0NBQWtDa0IsT0FBTyxJQUFJLE9BQU9sQixrQ0FBa0NrQixPQUFPLENBQUNJLE1BQU0sS0FBSyxZQUFZO2dCQUN2SHRCLGtDQUFrQ2tCLE9BQU8sQ0FBQ0ksTUFBTTtZQUNsRDtZQUNBdEYsU0FBU2Ysc0VBQWFBLENBQUNxTztZQUV2QixNQUFNa0MsaUJBQWlCLElBQUl0QyxPQUFPQyxrQkFBa0IsQ0FBQyxFQUFFLEVBQUU7Z0JBQUVDLE1BQU07Z0JBQVdDLFFBQVE7WUFBVTtZQUM5RnJOLFNBQVNkLGtFQUFZQSxDQUFDO2dCQUFFNEksTUFBTTtnQkFBWXBCLE1BQU00RztnQkFBa0JMLFdBQVd1QztnQkFBZ0JqQyxRQUFRO1lBQVk7UUFFbkgsRUFBRSxPQUFPdkksT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMscUJBQXFCQTtZQUNuQyxNQUFNeUssZUFBZSxzQkFBdUQsT0FBakN6SyxNQUFNZCxPQUFPLElBQUk7WUFDNURELGFBQWF3TCxjQUFjO1lBQzNCelAsU0FBU2Ysc0VBQWFBLENBQUMsVUFBdUIsT0FBYndRO1lBQ2pDelAsU0FBU2Qsa0VBQVlBLENBQUM7Z0JBQUU0SSxNQUFNO2dCQUFZcEIsTUFBTSxVQUF1QixPQUFiK0k7Z0JBQWdCeEMsV0FBVyxJQUFJQyxPQUFPQyxrQkFBa0IsQ0FBQyxFQUFFLEVBQUU7b0JBQUVDLE1BQU07b0JBQVdDLFFBQVE7Z0JBQVU7Z0JBQUlFLFFBQVE7WUFBUTtRQUNsTCxTQUFVO1lBQ1IsSUFBSSxXQUFZLFlBQVl4SixrQkFBa0JtQixPQUFPLElBQU1PLFdBQVcsZ0JBQWdCLENBQUMzQixnQkFBZ0JvQixPQUFPLEVBQUc7Z0JBQy9HdkIsZ0JBQWdCdUIsT0FBTyxDQUFDTyxPQUFPLEdBQUc7Z0JBQ2xDLElBQUlBLFdBQVcsVUFBVTtvQkFDdkJqQywyQkFBMkIwQixPQUFPLEdBQUc7b0JBQ3JDbEYsU0FBU1osNEVBQWdCQSxDQUFDO2dCQUM1QixPQUFPO29CQUNMcUUsd0JBQXdCeUIsT0FBTyxHQUFHO29CQUNsQ3hDLG9CQUFvQjtnQkFDdEI7WUFDRjtZQUNBRSxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLE1BQU04TSwyQkFBMkIvVCxrREFBV0E7K0RBQUMsQ0FBQ3dUO1lBQzVDLElBQUksQ0FBQ0EsVUFBVSxPQUFPO1lBQ3RCLHFCQUNFLDhEQUFDclEsdURBQWFBO2dCQUNaNlEsWUFBWTtvQkFDVkMsTUFBSyxLQUErQzs0QkFBL0MsRUFBRUMsSUFBSSxFQUFFQyxNQUFNLEVBQUVDLFNBQVMsRUFBRUMsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBL0M7d0JBQ0gsTUFBTUMsUUFBUSxpQkFBaUJDLElBQUksQ0FBQ0osYUFBYTt3QkFDakQsT0FBTyxDQUFDRCxVQUFVSSxzQkFDaEIsOERBQUM3VCx5U0FBR0E7NEJBQUMrVCxJQUFJO2dDQUNQQyxJQUFJO2dDQUNKQyxVQUFVO2dDQUNWLFNBQVM7b0NBQ1BDLGNBQWM7b0NBQ2RDLFNBQVM7b0NBQ1RDLFVBQVU7b0NBQ1ZDLFdBQVc7b0NBQ1hDLFlBQVk7b0NBQ1pDLFdBQVc7Z0NBQ2I7NEJBQ0Y7c0NBQ0UsNEVBQUNDOzBDQUFJLDRFQUFDakI7b0NBQUtHLFdBQVdBO29DQUFZLEdBQUdFLEtBQUs7b0NBQUVhLHlCQUF5Qjt3Q0FBRUMsUUFBUXBTLDhEQUFjLENBQUNzUyxPQUFPakIsVUFBVXBKLE9BQU8sQ0FBQyxPQUFPLEtBQUs7NENBQUVzSyxVQUFVaEIsS0FBSyxDQUFDLEVBQUU7NENBQUVpQixnQkFBZ0I7d0NBQUssR0FBR2pLLEtBQUs7b0NBQUM7Ozs7Ozs7Ozs7Ozs7OzttREFHekwsOERBQUMwSTs0QkFDQ0csV0FBV0E7NEJBQ1YsR0FBR0UsS0FBSzs0QkFDVG1CLE9BQU87Z0NBQ0xDLGlCQUFpQjtnQ0FDakJiLFNBQVM7Z0NBQ1RELGNBQWM7Z0NBQ2RlLFlBQVk7Z0NBQ1piLFVBQVU7Z0NBQ1ZHLFdBQVc7NEJBQ2I7c0NBRUNaOzs7Ozs7b0JBR1A7b0JBQ0F1QixDQUFDOytFQUFFO2dDQUFDLEVBQUUxQixJQUFJLEVBQUUsR0FBR0ksT0FBTztpREFBSyw4REFBQ3ZTLGdUQUFVQTtnQ0FBQzhULFNBQVM7Z0NBQUUsR0FBR3ZCLEtBQUs7Z0NBQUVHLElBQUk7b0NBQUVxQixJQUFJO29DQUFHaEIsVUFBVTtvQ0FBV0csV0FBVztnQ0FBYTs7Ozs7Ozs7b0JBQ3RIYyxNQUFNOytFQUFFO2dDQUFDLEVBQUU3QixJQUFJLEVBQUUsR0FBR0ksT0FBTztpREFBSyw4REFBQ3ZTLGdUQUFVQTtnQ0FBQ2lVLFdBQVU7Z0NBQVNDLFlBQVc7Z0NBQVEsR0FBRzNCLEtBQUs7Ozs7Ozs7O29CQUMxRjRCLEVBQUU7K0VBQUU7Z0NBQUMsRUFBRWhDLElBQUksRUFBRSxHQUFHSSxPQUFPO2lEQUFLLDhEQUFDdlMsZ1RBQVVBO2dDQUFDaVUsV0FBVTtnQ0FBS0csV0FBVTtnQ0FBVSxHQUFHN0IsS0FBSzs7Ozs7Ozs7b0JBQ25GOEIsRUFBRTsrRUFBRTtnQ0FBQyxFQUFFbEMsSUFBSSxFQUFFLEdBQUdJLE9BQU87aURBQUssOERBQUN2UyxnVEFBVUE7Z0NBQUNpVSxXQUFVO2dDQUFLdkIsSUFBSTtvQ0FBRTRCLElBQUk7b0NBQUtQLElBQUk7b0NBQUdoQixVQUFVO29DQUFXRyxXQUFXO2dDQUFhO2dDQUFJLEdBQUdYLEtBQUs7Ozs7Ozs7O29CQUN0SWdDLEVBQUU7K0VBQUU7Z0NBQUMsRUFBRXBDLElBQUksRUFBRSxHQUFHSSxPQUFPO2lEQUFLLDhEQUFDdlMsZ1RBQVVBO2dDQUFDaVUsV0FBVTtnQ0FBS3ZCLElBQUk7b0NBQUU0QixJQUFJO29DQUFLUCxJQUFJO29DQUFHaEIsVUFBVTtvQ0FBV0csV0FBVztnQ0FBYTtnQ0FBSSxHQUFHWCxLQUFLOzs7Ozs7OztvQkFDdElpQyxFQUFFOytFQUFFO2dDQUFDLEVBQUVyQyxJQUFJLEVBQUUsR0FBR0ksT0FBTztpREFBSyw4REFBQ3ZTLGdUQUFVQTtnQ0FBQ2lVLFdBQVU7Z0NBQUt2QixJQUFJO29DQUFFcUIsSUFBSTtvQ0FBTWhCLFVBQVU7b0NBQVdHLFdBQVc7Z0NBQWE7Z0NBQUksR0FBR1gsS0FBSzs7Ozs7Ozs7Z0JBQ2xJOzBCQUVDZDs7Ozs7O1FBR1A7OERBQUcsRUFBRTtJQUVMLE1BQU1nRCxvQkFBb0IsQ0FBQ0MsTUFBTUM7UUFDL0IsSUFBSUQsS0FBS3RLLElBQUksS0FBSyxZQUFZLE9BQU87UUFDckMsTUFBTXdLLE9BQU85VCxxRUFBWUE7UUFDekIsTUFBTStULFFBQVE7UUFDZCxNQUFNQyxnQkFBZ0JqUyxNQUFNa1MsT0FBTyxDQUFDQyxTQUFTLENBQUNDLEtBQUs7UUFFbkQscUJBQ0UsOERBQUN6Viw4U0FBUUE7WUFBMkJrVCxJQUFJO2dCQUFFd0MsWUFBWTtnQkFBY0MsSUFBSTtnQkFBR0MsSUFBSTtZQUFJOzs4QkFDakYsOERBQUMxVyw0U0FBTUE7b0JBQUNnVSxJQUFJO3dCQUFFMkMsU0FBU1A7d0JBQWVRLElBQUk7d0JBQUdDLElBQUk7b0JBQUk7OEJBQ25ELDRFQUFDWDt3QkFBS2xDLElBQUk7NEJBQUU4QyxPQUFPM1MsTUFBTWtTLE9BQU8sQ0FBQ1UsZUFBZSxDQUFDWDt3QkFBZTs7Ozs7Ozs7Ozs7OEJBRWxFLDhEQUFDcFYsMlNBQUtBO29CQUFDZ1csU0FBUTtvQkFBV2hELElBQUk7d0JBQUVtQixHQUFHO3dCQUFLOEIsVUFBVTt3QkFBR04sU0FBU3hTLE1BQU1rUyxPQUFPLENBQUNhLFVBQVUsQ0FBQ0MsT0FBTzt3QkFBRUMsYUFBYWpULE1BQU1rUyxPQUFPLENBQUNnQixPQUFPO3dCQUFFL0MsV0FBVztvQkFBTzs7c0NBQ3BKLDhEQUFDclUseVNBQUdBOzRCQUFDK1QsSUFBSTtnQ0FBRXNELFNBQVM7Z0NBQVFDLGdCQUFnQjtnQ0FBaUJmLFlBQVk7Z0NBQVVuQixJQUFJOzRCQUFJOzs4Q0FDekYsOERBQUMvVCxnVEFBVUE7b0NBQUMwVixTQUFRO29DQUFZeEIsWUFBVzs4Q0FBUVc7Ozs7Ozs4Q0FDbkQsOERBQUM3VSxnVEFBVUE7b0NBQUMwVixTQUFRO29DQUFVRixPQUFNOzhDQUFrQmQsS0FBS25GLFNBQVM7Ozs7Ozs7Ozs7Ozt3QkFFckV5Qyx5QkFBeUIwQyxLQUFLMUwsSUFBSTs7Ozs7Ozs7V0FUeEIsWUFBa0IsT0FBTjJMOzs7OztJQWEvQjtJQUVBLE1BQU11Qiw0QkFBNEIsQ0FBQ3hCLE1BQU1DO1FBQ3ZDLE1BQU1DLE9BQU9GLEtBQUszTSxNQUFNLEtBQUssV0FBVzFILG9FQUFXQSxHQUFHRyxtRUFBVUE7UUFDaEUsTUFBTXFVLFFBQVFILEtBQUszTSxNQUFNLEtBQUssV0FBVyxnQkFBZ0I7UUFDekQsTUFBTStNLGdCQUFnQkosS0FBSzNNLE1BQU0sS0FBSyxXQUFXbEYsTUFBTWtTLE9BQU8sQ0FBQ29CLElBQUksQ0FBQ2xCLEtBQUssR0FBR3BTLE1BQU1rUyxPQUFPLENBQUNxQixPQUFPLENBQUNuQixLQUFLO1FBRXZHLHFCQUNFLDhEQUFDelYsOFNBQVFBO1lBRVA2VywrQkFDRSw4REFBQ3JYLDhTQUFRQTtnQkFDUHNYLE1BQUs7Z0JBQ0xDLFNBQVM1UixrQkFBa0JpSixRQUFRLENBQUMrRztnQkFDcEM2QixVQUFVO29CQUNSNVIscUJBQXFCNlIsQ0FBQUEsT0FDbkJBLEtBQUs3SSxRQUFRLENBQUMrRyxTQUFTOEIsS0FBS3RNLE1BQU0sQ0FBQ3VNLENBQUFBLElBQUtBLE1BQU0vQixTQUFTOytCQUFJOEI7NEJBQU05Qjt5QkFBTTtnQkFFM0U7Z0JBQ0FhLE9BQU07Z0JBQ05tQixNQUFLOzs7Ozs7WUFHVEMsY0FBYztZQUNkbEUsSUFBSTtnQkFBRTBDLElBQUk7Z0JBQUtZLFNBQVM7Z0JBQVFkLFlBQVk7WUFBUzs7OEJBRXJELDhEQUFDeFcsNFNBQU1BO29CQUFDZ1UsSUFBSTt3QkFBRTJDLFNBQVNQO3dCQUFlUSxJQUFJO3dCQUFLdUIsT0FBTzt3QkFBSUMsUUFBUTt3QkFBSS9ELFVBQVU7b0JBQU87OEJBQ3JGLDRFQUFDNkI7d0JBQUs3QixVQUFTOzs7Ozs7Ozs7Ozs4QkFFakIsOERBQUN0VCxrVEFBWUE7b0JBQ1hzWCx1QkFDRSw4REFBQy9XLGdUQUFVQTt3QkFBQzBWLFNBQVE7d0JBQVFzQixNQUFNO3dCQUFDdEUsSUFBSTs0QkFBRXdCLFlBQVl2UCxrQkFBa0JpSixRQUFRLENBQUMrRyxTQUFTLFNBQVM7NEJBQVVxQixTQUFTOzRCQUFlaUIsaUJBQWlCOzRCQUFHQyxpQkFBaUI7NEJBQVlDLFVBQVU7NEJBQVVDLGNBQWM7d0JBQVc7a0NBQy9OMUMsS0FBSzFMLElBQUk7Ozs7OztvQkFHZGdNLFdBQVcsR0FBY04sT0FBWEcsT0FBTSxPQUFvQixPQUFmSCxLQUFLbkYsU0FBUzs7Ozs7OztXQTFCcEMsaUJBQXVCLE9BQU5vRjs7Ozs7SUE4QjVCO0lBRUEsTUFBTTBDLHdCQUF3QjtRQUM1QjdSLHVCQUF1QmlSLENBQUFBLE9BQVFBLFNBQVMsbUJBQW1CLGdCQUFnQjtJQUM3RTtJQUVBLE1BQU1hLDBCQUEwQjtRQUM5QixJQUFJQyxZQUFZM1UsUUFBUXVILE1BQU0sQ0FBQ3VLLENBQUFBLE9BQVFBLEtBQUt0SyxJQUFJLEtBQUssWUFBWUMsS0FBSztRQUN0RSxNQUFNbU4sdUJBQXVCOVU7UUFFN0IsSUFBSXVDLGdCQUFnQnVTLHdCQUF3QkEscUJBQXFCck8sSUFBSSxPQUFPLElBQUk7WUFDOUVvTyxVQUFVRSxJQUFJLENBQUM7Z0JBQUV6TyxNQUFNd087Z0JBQXNCakksV0FBVztnQkFBZ0JuRixNQUFNO1lBQW9CO1FBQ3BHO1FBRUEsSUFBSTdFLHdCQUF3QixlQUFlO1lBQ3pDLE9BQU9nUyxVQUFVak4sT0FBTztRQUMxQjtRQUNBLE9BQU9pTjtJQUNUO0lBRUEsTUFBTUcsa0JBQWtCO1FBQ3RCLElBQUlqUyxtQkFBbUI7WUFDckIsSUFBSUcscUJBQXFCNEIsT0FBTyxJQUFJLE9BQU81QixxQkFBcUI0QixPQUFPLENBQUNvQixLQUFLLEtBQUssWUFBWTtnQkFDNUYsSUFBSTtvQkFDRixNQUFNaEQscUJBQXFCNEIsT0FBTyxDQUFDb0IsS0FBSztnQkFDMUMsRUFBRSxPQUFPZ0IsR0FBRztvQkFBRXJDLFFBQVFELEtBQUssQ0FBQyxzQ0FBc0NzQztnQkFBSTtZQUN4RSxPQUFPLElBQUlqRSxhQUFhNkIsT0FBTyxJQUFJLENBQUM3QixhQUFhNkIsT0FBTyxDQUFDbVEsTUFBTSxFQUFFO2dCQUMvRGhTLGFBQWE2QixPQUFPLENBQUNvQixLQUFLO1lBQzVCO1lBQ0EsUUFBUSw4REFBOEQ7UUFDeEU7UUFFQSxNQUFNZ1Asb0JBQW9CLENBQUNDO1lBQ3pCLE1BQU1DLGtCQUFrQmpXLFNBQVM7Z0JBQy9CLElBQUksQ0FBQ2dXLGFBQWNBLFVBQVVGLE1BQU0sRUFBRztnQkFDdEMsTUFBTUksU0FBU2xTLHFCQUFxQjJCLE9BQU8sR0FBRzNCLHFCQUFxQjJCLE9BQU8sQ0FBQ3dRLGFBQWEsR0FBR0g7Z0JBQzNGLElBQUlFLFFBQVE7b0JBQ1ZBLE9BQU9FLFdBQVcsQ0FBQzt3QkFDakI3TixNQUFNO3dCQUNOM0MsU0FBUzs0QkFDUG9QLE9BQU9nQixVQUFVSyxVQUFVOzRCQUMzQnBCLFFBQVFlLFVBQVVNLFdBQVc7d0JBQy9CO29CQUNGLEdBQUc7Z0JBQ0w7WUFDRixHQUFHO1lBRUhOLFVBQVVPLGdCQUFnQixDQUFDLFVBQVVOO1lBQ3JDLE9BQU8sSUFBTUQsVUFBVVEsbUJBQW1CLENBQUMsVUFBVVAsa0JBQWtCLDRCQUE0QjtRQUNyRztRQUVBLElBQUlRLE9BQU9DLHdCQUF3QixJQUFJLE9BQU9ELE9BQU9DLHdCQUF3QixDQUFDQyxhQUFhLEtBQUssWUFBWTtZQUMxRyxJQUFJO2dCQUNGLE1BQU1DLGFBQWE7b0JBQUU1QixPQUFPO29CQUFLQyxRQUFRO2dCQUFJO2dCQUM3QyxNQUFNNEIscUJBQXFCLE1BQU1KLE9BQU9DLHdCQUF3QixDQUFDQyxhQUFhLENBQUNDO2dCQUMvRTdTLHFCQUFxQjRCLE9BQU8sR0FBR2tSO2dCQUMvQmhULHFCQUFxQjtnQkFFckIsTUFBTWlULFNBQVMvUyxxQkFBcUI0QixPQUFPLENBQUNvUixRQUFRLENBQUNDLGFBQWEsQ0FBQztnQkFDbkVGLE9BQU9HLEdBQUcsR0FBRztnQkFDYkgsT0FBT2pGLEtBQUssQ0FBQ21ELEtBQUssR0FBRztnQkFDckI4QixPQUFPakYsS0FBSyxDQUFDb0QsTUFBTSxHQUFHO2dCQUN0QjZCLE9BQU9qRixLQUFLLENBQUNxRixNQUFNLEdBQUc7Z0JBQ3RCblQscUJBQXFCNEIsT0FBTyxDQUFDb1IsUUFBUSxDQUFDSSxJQUFJLENBQUN0RixLQUFLLENBQUN1RixNQUFNLEdBQUc7Z0JBQzFEclQscUJBQXFCNEIsT0FBTyxDQUFDb1IsUUFBUSxDQUFDSSxJQUFJLENBQUN0RixLQUFLLENBQUN5RCxRQUFRLEdBQUc7Z0JBQzVEdlIscUJBQXFCNEIsT0FBTyxDQUFDb1IsUUFBUSxDQUFDSSxJQUFJLENBQUNFLE1BQU0sQ0FBQ1A7Z0JBQ2xEOVMscUJBQXFCMkIsT0FBTyxHQUFHbVI7Z0JBRS9CLE1BQU1RLHVCQUF1QnZCLGtCQUFrQmhTLHFCQUFxQjRCLE9BQU87Z0JBRTNFbVIsT0FBT1MsTUFBTSxHQUFHO29CQUNkLElBQUl2VCxxQkFBcUIyQixPQUFPLElBQUkzQixxQkFBcUIyQixPQUFPLENBQUN3USxhQUFhLEVBQUU7d0JBQzlFblMscUJBQXFCMkIsT0FBTyxDQUFDd1EsYUFBYSxDQUFDQyxXQUFXLENBQUM7NEJBQ3JEN04sTUFBTTs0QkFDTjNDLFNBQVM7Z0NBQ1A0UixxQkFBcUJ6VyxRQUFRdUgsTUFBTSxDQUFDdUssQ0FBQUEsT0FBUUEsS0FBS3RLLElBQUksS0FBSztnQ0FDMURvTixzQkFBc0J2UyxlQUFldkMsc0JBQXNCO2dDQUMzRHVDLGNBQWNBO2dDQUNkcVUsV0FBVy9UOzRCQUNiO3dCQUNGLEdBQUc7b0JBQ0w7Z0JBQ0Y7Z0JBRUFLLHFCQUFxQjRCLE9BQU8sQ0FBQzRRLGdCQUFnQixDQUFDLFlBQVk7b0JBQ3hEZTtvQkFDQXpULHFCQUFxQjtvQkFDckJFLHFCQUFxQjRCLE9BQU8sR0FBRztvQkFDL0IzQixxQkFBcUIyQixPQUFPLEdBQUc7Z0JBQ2pDO2dCQUVBakIsYUFBYSw2QkFBNkI7Z0JBQzFDO1lBRUYsRUFBRSxPQUFPZ1QsS0FBSztnQkFDWmhTLFFBQVFELEtBQUssQ0FBQywwQ0FBMENpUztnQkFDeERoVCxhQUFhLHNEQUFrRSxPQUFaZ1QsSUFBSS9TLE9BQU8sRUFBQyxNQUFJO1lBQ3JGO1FBQ0Y7UUFFQWIsYUFBYTZCLE9BQU8sR0FBRzhRLE9BQU9rQixJQUFJLENBQUMsWUFBWSxpQkFBaUI7UUFFaEUsSUFBSTdULGFBQWE2QixPQUFPLEVBQUU7WUFDeEI5QixxQkFBcUI7WUFDckIsTUFBTXlULHVCQUF1QnZCLGtCQUFrQmpTLGFBQWE2QixPQUFPO1lBRW5FN0IsYUFBYTZCLE9BQU8sQ0FBQzRSLE1BQU0sR0FBRztnQkFDNUIsSUFBSXpULGFBQWE2QixPQUFPLElBQUksQ0FBQzdCLGFBQWE2QixPQUFPLENBQUNtUSxNQUFNLEVBQUU7b0JBQ3hEaFMsYUFBYTZCLE9BQU8sQ0FBQ3lRLFdBQVcsQ0FBQzt3QkFDL0I3TixNQUFNO3dCQUNOM0MsU0FBUzs0QkFDUDRSLHFCQUFxQnpXLFFBQVF1SCxNQUFNLENBQUN1SyxDQUFBQSxPQUFRQSxLQUFLdEssSUFBSSxLQUFLOzRCQUMxRG9OLHNCQUFzQnZTLGVBQWV2QyxzQkFBc0I7NEJBQzNEdUMsY0FBY0E7NEJBQ2RxVSxXQUFXL1Q7d0JBQ2I7b0JBQ0YsR0FBRztnQkFDTDtZQUNGO1lBQ0EsTUFBTWtVLG1CQUFtQkMsWUFBWTtnQkFDbkMsSUFBSS9ULGFBQWE2QixPQUFPLElBQUk3QixhQUFhNkIsT0FBTyxDQUFDbVEsTUFBTSxFQUFFO29CQUN2RGdDLGNBQWNGO29CQUNkTjtvQkFDQXpULHFCQUFxQjtvQkFDckJDLGFBQWE2QixPQUFPLEdBQUc7Z0JBQ3pCO1lBQ0YsR0FBRztZQUNILElBQUk3QixhQUFhNkIsT0FBTyxFQUFFN0IsYUFBYTZCLE9BQU8sQ0FBQ29TLGNBQWMsR0FBR0g7UUFDbEUsT0FBTztZQUNMbFQsYUFBYSxtRUFBbUU7WUFDaEZiLHFCQUFxQjtRQUN2QjtJQUNGO0lBRUF4SCxnREFBU0E7bUNBQUM7WUFDUjsyQ0FBTztvQkFDTCxJQUFJeUgsYUFBYTZCLE9BQU8sSUFBSTdCLGFBQWE2QixPQUFPLENBQUNvUyxjQUFjLEVBQUU7d0JBQy9ERCxjQUFjaFUsYUFBYTZCLE9BQU8sQ0FBQ29TLGNBQWM7b0JBQ25EO29CQUNBLElBQUloVSxxQkFBcUI0QixPQUFPLElBQUksT0FBTzVCLHFCQUFxQjRCLE9BQU8sQ0FBQ29CLEtBQUssS0FBSyxZQUFZO3dCQUM1RixJQUFJOzRCQUFFaEQscUJBQXFCNEIsT0FBTyxDQUFDb0IsS0FBSzt3QkFBSSxFQUFFLE9BQU9nQixHQUFHLENBQWE7b0JBQ3ZFO2dCQUNGOztRQUNGO2tDQUFHLEVBQUU7SUFFTDFMLGdEQUFTQTttQ0FBQztZQUNSLElBQUkyYix5QkFBeUI7WUFFN0IsSUFBSWpVLHFCQUFxQjRCLE9BQU8sSUFBSTNCLHFCQUFxQjJCLE9BQU8sSUFBSTNCLHFCQUFxQjJCLE9BQU8sQ0FBQ3dRLGFBQWEsRUFBRTtnQkFDOUc2Qix5QkFBeUJoVSxxQkFBcUIyQixPQUFPLENBQUN3USxhQUFhO1lBQ3JFLE9BQU8sSUFBSXJTLGFBQWE2QixPQUFPLElBQUksQ0FBQzdCLGFBQWE2QixPQUFPLENBQUNtUSxNQUFNLEVBQUU7Z0JBQy9Ea0MseUJBQXlCbFUsYUFBYTZCLE9BQU87WUFDL0M7WUFFQSxJQUFJL0IscUJBQXFCb1Usd0JBQXdCO2dCQUMvQyxJQUFJO29CQUNGQSx1QkFBdUI1QixXQUFXLENBQUM7d0JBQ2pDN04sTUFBTTt3QkFDTjNDLFNBQVM7NEJBQ1A0UixxQkFBcUJ6VyxRQUFRdUgsTUFBTTsyREFBQ3VLLENBQUFBLE9BQVFBLEtBQUt0SyxJQUFJLEtBQUs7OzRCQUMxRG9OLHNCQUFzQnZTLGVBQWV2QyxzQkFBc0I7NEJBQzNEdUMsY0FBY0E7NEJBQ2RxVSxXQUFXL1Q7d0JBQ2I7b0JBQ0YsR0FBRztnQkFDTCxFQUFFLE9BQU9xRSxHQUFHO29CQUNWckMsUUFBUXVTLElBQUksQ0FBQyx5Q0FBeUNsUTtnQkFDeEQ7WUFDRjtRQUNGO2tDQUFHO1FBQUNoSDtRQUFTRjtRQUFxQitDO1FBQW1CRjtRQUFxQk47S0FBYTtJQUV2RixxQkFDRTs7MEJBQ0UsOERBQUM1RyxrREFBSUE7MEJBQ0gsNEVBQUN3Vzs7Ozs7Z0NBbVBlaFMsTUFBTWtTLE9BQU8sQ0FBQ2EsVUFBVSxDQUFDbUUsS0FBSztnQ0FJeEJsWCxNQUFNa1MsT0FBTyxDQUFDaUYsSUFBSSxDQUFDLElBQUk7Z0NBRXZCblgsTUFBTWtTLE9BQU8sQ0FBQ2EsVUFBVSxDQUFDbUUsS0FBSztnQ0FHOUJsWCxNQUFNa1MsT0FBTyxDQUFDaUYsSUFBSSxDQUFDLElBQUk7Z0NBSXhCblgsTUFBTWtTLE9BQU8sQ0FBQ2lGLElBQUksQ0FBQyxJQUFJO2dDQUFJblgsTUFBTWtTLE9BQU8sQ0FBQ2EsVUFBVSxDQUFDbUUsS0FBSzs7Ozs4QkFoUXZFOzs7Ozs7Ozs7OzswQkFFVCw4REFBQ3BiLHlTQUFHQTtnQkFBQytULElBQUk7b0JBQUVzRCxTQUFTO29CQUFRaUUsZUFBZTtvQkFBVW5ELFFBQVE7Z0JBQVE7O2tDQUNuRSw4REFBQ3JZLDRTQUFNQTt3QkFBQ21VLFVBQVM7d0JBQVM0QyxPQUFNO3dCQUFVMEUsV0FBVztrQ0FDbkQsNEVBQUNwYSw2U0FBT0E7OzhDQUNOLDhEQUFDZ0IscUVBQVlBO29DQUFDNFIsSUFBSTt3Q0FBRTRDLElBQUk7d0NBQUdFLE9BQU87b0NBQWU7Ozs7Ozs4Q0FDakQsOERBQUN4VixnVEFBVUE7b0NBQUMwVixTQUFRO29DQUFLekIsV0FBVTtvQ0FBTXZCLElBQUk7d0NBQUVpRCxVQUFVO3dDQUFHSCxPQUFPO29DQUFlOzhDQUFHOzs7Ozs7OENBR3JGLDhEQUFDelYsNlNBQU9BO29DQUFDOFUsT0FBTTs4Q0FDYiw0RUFBQ3ZWLGdUQUFVQTt3Q0FBQ2tXLE9BQU07d0NBQVUyRSxTQUFTLElBQU1uVyxnQkFBZ0I7d0NBQU9vVyxjQUFXO2tEQUMzRSw0RUFBQ3ZaLHFFQUFZQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTXJCLDhEQUFDMUIsK1NBQVNBO3dCQUFDa2IsVUFBUzt3QkFBSzNILElBQUk7NEJBQUVpRCxVQUFVOzRCQUFHUCxJQUFJOzRCQUFHWSxTQUFTOzRCQUFRaUUsZUFBZTt3QkFBUztrQ0FDMUYsNEVBQUM1YSwwU0FBSUE7NEJBQUNpYixTQUFTOzRCQUFDQyxTQUFTOzRCQUFHN0gsSUFBSTtnQ0FBRWlELFVBQVU7NEJBQUU7OzhDQUU1Qyw4REFBQ3RXLDBTQUFJQTtvQ0FBQ3FWLElBQUk7b0NBQUM4RixJQUFJO29DQUFJQyxJQUFJO29DQUFHL0gsSUFBSTt3Q0FBRXNELFNBQVM7d0NBQVFpRSxlQUFlO3dDQUFVUyxLQUFLO29DQUFFOztzREFDL0UsOERBQUM3YiwwU0FBSUE7OzhEQUNILDhEQUFDRSxnVEFBVUE7b0RBQUM4VixPQUFNO29EQUE2QjhGLHNCQUFRLDhEQUFDdGEsb0VBQVdBOzs7OztvREFBS3FTLElBQUk7d0RBQUVrSSxJQUFJO29EQUFFOzs7Ozs7OERBQ3BGLDhEQUFDOWIsaVRBQVdBOztzRUFDViw4REFBQ00sc1RBQWdCQTs0REFDZnliLHVCQUFTLDhEQUFDamIsNFNBQU1BO2dFQUFDMlcsU0FBUzdTO2dFQUFnQjhTLFVBQVU1TSxDQUFBQSxJQUFLakcsa0JBQWtCaUcsRUFBRW1PLE1BQU0sQ0FBQ3hCLE9BQU87Z0VBQUdmLE9BQU07Ozs7Ozs0REFDcEdzRixPQUFNOzREQUNOcEksSUFBSTtnRUFBRXFCLElBQUk7NERBQUU7Ozs7OztzRUFFZCw4REFBQ2xVLCtTQUFTQTs0REFDUmtiLFNBQVM7NERBQ1RDLFNBQVM7NERBQ1RDLE1BQU07NERBQ052RixTQUFROzREQUNSbE0sT0FBT2pIOzREQUNQaVUsVUFBVSxDQUFDNU0sSUFBTUwsd0JBQXdCSyxFQUFFbU8sTUFBTSxDQUFDdk8sS0FBSyxFQUFFOzREQUN6RDBSLFdBQVcsQ0FBQ3RSLElBQU1ELGVBQWVDLEdBQUc7NERBQ3BDdVIsYUFBWTs0REFDWnpJLElBQUk7Z0VBQUVxQixJQUFJOzREQUFFOzs7Ozs7c0VBRWQsOERBQUNwVix5U0FBR0E7NERBQUMrVCxJQUFJO2dFQUFFc0QsU0FBUztnRUFBUTBFLEtBQUs7Z0VBQUdVLFVBQVU7NERBQU87OzhFQUNuRCw4REFBQ3hjLDRTQUFNQTtvRUFDTHViLFNBQVN0TTtvRUFDVDZILFNBQVE7b0VBQ1JGLE9BQU9yUixzQkFBc0IsVUFBVTtvRUFDdkNrWCxXQUFXbFgsb0NBQXNCLDhEQUFDcEQsNEVBQW1CQTs7OzsrRkFBTSw4REFBQ0osd0VBQWVBOzs7OztvRUFDM0UrUixJQUFJO3dFQUFFaUQsVUFBVTtvRUFBRTs4RUFFakJ4UixzQkFBc0Isc0JBQXNCOzs7Ozs7OEVBRS9DLDhEQUFDbkUsZ1RBQVVBO29FQUFDMFYsU0FBUTtvRUFBVWhELElBQUk7d0VBQUU2QyxJQUFJO3dFQUFHUyxTQUFTO3dFQUFTYSxPQUFPO29FQUFPOzhFQUN4RTFTLHNCQUFzQiw4QkFBOEI7Ozs7Ozs4RUFFdkQsOERBQUNwRSw2U0FBT0E7b0VBQUM4VSxPQUFNOzhFQUNiLDRFQUFDdlYsZ1RBQVVBO3dFQUFDNmEsU0FBU3RSO2tGQUFnQyw0RUFBQ3pJLHdFQUFlQTs7Ozs7Ozs7Ozs7Ozs7O2dFQUV0RSxDQUFDc0QsZ0NBQ0EsOERBQUM5RSw0U0FBTUE7b0VBQ0x1YixTQUFTLElBQU0xUSxtQkFBbUI7b0VBQ2xDaU0sU0FBUTtvRUFDUkYsT0FBTTtvRUFDTjZGLHlCQUFXLDhEQUFDemEsaUVBQVFBOzs7OztvRUFDcEIwYSxVQUFVclcsZ0JBQWdCLENBQUMxQyx1QkFBdUI0RyxJQUFJOzhFQUN2RDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU9ULDhEQUFDdEssMFNBQUlBOzRDQUFDNlQsSUFBSTtnREFBRWlELFVBQVU7Z0RBQUdLLFNBQVM7Z0RBQVFpRSxlQUFlOzRDQUFTOzs4REFDaEUsOERBQUNsYixnVEFBVUE7b0RBQ1Q4VixPQUFNO29EQUNOOEYsc0JBQVEsOERBQUNqYSw2RUFBb0JBOzs7OztvREFDN0I2YSxzQkFDRSw4REFBQzNjLDRTQUFNQTt3REFDTDhXLFNBQVE7d0RBQ1JpQixNQUFLO3dEQUNMd0QsU0FBU25RO3dEQUNUc1IsVUFBVTNXLGtCQUFrQnNGLE1BQU0sS0FBSyxLQUFLaEY7d0RBQzVDb1csV0FBV3BXLDZCQUFlLDhEQUFDL0Ysc1RBQWdCQTs0REFBQ3lYLE1BQU07NERBQUluQixPQUFNOzs7OzttRkFBZSw4REFBQzVVLGlFQUFRQTs7Ozs7a0VBQ3JGOzs7Ozs7b0RBSUg4UixJQUFJO3dEQUFFa0ksSUFBSTt3REFBR1ksY0FBYyxhQUFtQyxPQUF0QjNZLE1BQU1rUyxPQUFPLENBQUNnQixPQUFPO29EQUFHOzs7Ozs7OERBRWxFLDhEQUFDalgsaVRBQVdBO29EQUFDNFQsSUFBSTt3REFBRWlELFVBQVU7d0RBQUd3QixVQUFVO3dEQUFVdEQsR0FBRztvREFBRTs4REFDdkQsNEVBQUN4Uyw4REFBY0E7d0RBQUNnUixXQUFVO3dEQUFtQm9KLHVCQUFzQjtrRUFDakUsNEVBQUNsYywwU0FBSUE7NERBQUNtYyxLQUFLOzREQUFDaEosSUFBSTtnRUFBRWlKLElBQUk7Z0VBQUd4RyxJQUFJOzREQUFFO3NFQUM1QnZTLFFBQVF1SCxNQUFNLENBQUNQLENBQUFBLElBQUtBLEVBQUVRLElBQUksS0FBSyxZQUFZQyxLQUFLLEdBQUdDLE9BQU8sR0FBR0UsR0FBRyxDQUFDMEw7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBUTVFLDhEQUFDN1csMFNBQUlBO29DQUFDcVYsSUFBSTtvQ0FBQzhGLElBQUk7b0NBQUlDLElBQUk7b0NBQUcvSCxJQUFJO3dDQUFFc0QsU0FBUzt3Q0FBUWlFLGVBQWU7b0NBQVM7OENBQ3ZFLDRFQUFDcGIsMFNBQUlBO3dDQUFDNlQsSUFBSTs0Q0FBRWlELFVBQVU7NENBQUdLLFNBQVM7NENBQVFpRSxlQUFlO3dDQUFTOzswREFDaEUsOERBQUNsYixnVEFBVUE7Z0RBQ1Q4VixPQUFNO2dEQUNOOEYsc0JBQVEsOERBQUM3WixxRUFBWUE7Ozs7O2dEQUNyQnlhLHNCQUNFOztzRUFDRSw4REFBQ3hiLDZTQUFPQTs0REFBQzhVLE9BQU9wUCxvQkFBb0Isa0JBQWtCO3NFQUNwRCw0RUFBQ25HLGdUQUFVQTtnRUFBQzZhLFNBQVN6QztnRUFBaUJmLE1BQUs7Z0VBQVFuQixPQUFPL1Asb0JBQW9CLGNBQWM7MEVBQzFGLDRFQUFDaEYsZ0ZBQXVCQTs7Ozs7Ozs7Ozs7Ozs7O3NFQUc1Qiw4REFBQ1YsNlNBQU9BOzREQUFDOFUsT0FBT3RQLHdCQUF3QixnQkFBZ0IsMkJBQTJCO3NFQUNqRiw0RUFBQ2pHLGdUQUFVQTtnRUFBQzZhLFNBQVM5QztnRUFBdUJWLE1BQUs7MEVBQzlDcFIsd0JBQXdCLDhCQUFnQiw4REFBQ3JGLDBFQUFpQkE7Ozs7MkZBQU0sOERBQUNDLHdFQUFlQTs7Ozs7Ozs7Ozs7Ozs7O3NFQUdyRiw4REFBQ0gsZ1RBQVVBOzREQUFDMFYsU0FBUTs0REFBVWhELElBQUk7Z0VBQUU0QyxJQUFJO2dFQUFHbEIsV0FBVzs0REFBUztzRUFDNUQ3Tyx3QkFBd0IsZ0JBQWdCLGlCQUFpQjs7Ozs7O3NFQUU1RCw4REFBQ25HLHNUQUFnQkE7NERBQ2Z5Yix1QkFBUyw4REFBQ2piLDRTQUFNQTtnRUFBQzJXLFNBQVNsUjtnRUFBWW1SLFVBQVUsQ0FBQzVNLElBQU10RSxjQUFjc0UsRUFBRW1PLE1BQU0sQ0FBQ3hCLE9BQU87Z0VBQUdmLE9BQU07Ozs7Ozs0REFDOUZzRixPQUFNOzREQUNOcEksSUFBSTtnRUFBRWtKLElBQUk7NERBQUU7Ozs7Ozs7O2dEQUlsQmxKLElBQUk7b0RBQUU4SSxjQUFjLGFBQW1DLE9BQXRCM1ksTUFBTWtTLE9BQU8sQ0FBQ2dCLE9BQU87Z0RBQUc7Ozs7OzswREFFM0QsOERBQUNqWCxpVEFBV0E7Z0RBQUM0VCxJQUFJO29EQUFFaUQsVUFBVTtvREFBR3dCLFVBQVU7b0RBQVV0RCxHQUFHO2dEQUFFOzBEQUN2RCw0RUFBQ3hTLDhEQUFjQTtvREFDYmdSLFdBQVU7b0RBQ1Z3SixNQUFNeFcsYUFBY0Usd0JBQXdCLGdCQUFnQixRQUFRLFdBQVkzQjtvREFDaEY2WCx1QkFBc0I7OERBRXRCLDRFQUFDbGMsMFNBQUlBO3dEQUFDbVQsSUFBSTs0REFBRXlDLElBQUk7NERBQUdDLElBQUk7d0RBQUU7OzREQUN0QmtDLDBCQUEwQjlNLEdBQUcsQ0FBQ2lLOzREQUM5QnhQLDhCQUNDLDhEQUFDekYsOFNBQVFBO2dFQUFDa1QsSUFBSTtvRUFBRXVELGdCQUFnQjtvRUFBVWIsSUFBSTtnRUFBRTs7a0ZBQzlDLDhEQUFDbFcsc1RBQWdCQTt3RUFBQ3lYLE1BQU07Ozs7OztrRkFDeEIsOERBQUMzVyxnVEFBVUE7d0VBQUMwVixTQUFRO3dFQUFVaEQsSUFBSTs0RUFBRWtKLElBQUk7d0VBQUU7a0ZBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FVM0QsOERBQUN2YywwU0FBSUE7b0NBQUNxVixJQUFJO29DQUFDOEYsSUFBSTtvQ0FBSUMsSUFBSTtvQ0FBRy9ILElBQUk7d0NBQUVzRCxTQUFTO3dDQUFRaUUsZUFBZTtvQ0FBUzs4Q0FDdkUsNEVBQUNwYiwwU0FBSUE7d0NBQUM2VCxJQUFJOzRDQUFFaUQsVUFBVTs0Q0FBR0ssU0FBUzs0Q0FBUWlFLGVBQWU7d0NBQVM7OzBEQUNoRSw4REFBQ2xiLGdUQUFVQTtnREFDVDhWLE9BQU07Z0RBQ044RixzQkFBUSw4REFBQ25hLG1FQUFVQTs7Ozs7Z0RBQ25CK2EsUUFDRS9YLHFDQUNFLDhEQUFDdkUsMFNBQUlBO29EQUNINmIsT0FBT3RYLHdCQUF3QixhQUFhLGFBQWE7b0RBQ3pEbVQsTUFBSztvREFDTG5CLE9BQU9oUyx3QkFBd0IsYUFBYSxZQUFZO29EQUN4RGtTLFNBQVE7Ozs7OztnREFJZGhELElBQUk7b0RBQUVrSSxJQUFJO2dEQUFFOzs7Ozs7MERBRWQsOERBQUM5YixpVEFBV0E7Z0RBQUM0VCxJQUFJO29EQUFFaUQsVUFBVTtvREFBR0ssU0FBUztvREFBUWlFLGVBQWU7Z0RBQVM7O2tFQUN2RSw4REFBQzdhLHNUQUFnQkE7d0RBQ2Z5Yix1QkFBUyw4REFBQ2piLDRTQUFNQTs0REFBQzJXLFNBQVMxUjs0REFBYzJSLFVBQVU1TSxDQUFBQSxJQUFLOUUsZ0JBQWdCOEUsRUFBRW1PLE1BQU0sQ0FBQ3hCLE9BQU87NERBQUdmLE9BQU07Ozs7Ozt3REFDaEdzRixPQUFNO3dEQUNOcEksSUFBSTs0REFBRXFCLElBQUk7d0RBQUU7Ozs7OztrRUFFZCw4REFBQ2xVLCtTQUFTQTt3REFDUmtiLFNBQVM7d0RBQ1RDLFNBQVM7d0RBQ1RDLE1BQU07d0RBQ052RixTQUFRO3dEQUNSbE0sT0FBT3pFO3dEQUNQeVIsVUFBVSxDQUFDNU0sSUFBTUwsd0JBQXdCSyxFQUFFbU8sTUFBTSxDQUFDdk8sS0FBSyxFQUFFO3dEQUN6RDBSLFdBQVcsQ0FBQ3RSLElBQU1ELGVBQWVDLEdBQUc7d0RBQ3BDdVIsYUFBWTt3REFDWnpJLElBQUk7NERBQUVxQixJQUFJOzREQUFHNEIsVUFBVTt3REFBRTs7Ozs7O2tFQUUzQiw4REFBQ2hYLHlTQUFHQTt3REFBQytULElBQUk7NERBQUVzRCxTQUFTOzREQUFRMEUsS0FBSzs0REFBR1UsVUFBVTs0REFBUTdGLElBQUk7d0RBQU87OzBFQUMvRCw4REFBQzNXLDRTQUFNQTtnRUFDTHViLFNBQVN6TDtnRUFDVGdILFNBQVE7Z0VBQ1JGLE9BQU92UixxQkFBcUIsVUFBVTtnRUFDdENvWCxXQUFXcFgsbUNBQXFCLDhEQUFDMUQsbUVBQVVBOzs7OzJGQUFNLDhEQUFDRCxnRUFBT0E7Ozs7O2dFQUN6RG9TLElBQUk7b0VBQUVpRCxVQUFVO2dFQUFFOzBFQUVqQjFSLHFCQUFxQixhQUFhOzs7Ozs7MEVBRXJDLDhEQUFDbEUsNlNBQU9BO2dFQUFDOFUsT0FBTTswRUFDYiw0RUFBQ3ZWLGdUQUFVQTtvRUFBQzZhLFNBQVNyUjs4RUFBNkIsNEVBQUMxSSx3RUFBZUE7Ozs7Ozs7Ozs7Ozs7Ozs0REFFbkV5RSw4QkFDQyw4REFBQ2pHLDRTQUFNQTtnRUFDTHViLFNBQVMsSUFBTTFRLG1CQUFtQjtnRUFDbENpTSxTQUFRO2dFQUNSRixPQUFNO2dFQUNONkYseUJBQVcsOERBQUN6YSxpRUFBUUE7Ozs7O2dFQUNwQjBhLFVBQVVyVyxnQkFBZ0IsQ0FBQ0YsaUJBQWlCb0UsSUFBSTswRUFDakQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBV2YsOERBQUM3SCxrRUFBY0E7d0JBQ2JrWSxNQUFNelY7d0JBQ04rWCxTQUFTLElBQU05WCxnQkFBZ0I7d0JBQy9CK1gsUUFBUXJWOzs7Ozs7a0NBRVYsOERBQUMvRyw4U0FBUUE7d0JBQ1A2WixNQUFNblY7d0JBQ04yWCxrQkFBa0I7d0JBQ2xCRixTQUFTalU7d0JBQ1RvVSxjQUFjOzRCQUFFQyxVQUFVOzRCQUFVQyxZQUFZO3dCQUFTO2tDQUV6RCw0RUFBQzNkLDJTQUFLQTs0QkFBQ3NkLFNBQVNqVTs0QkFBcUJwQixVQUFVaEM7NEJBQWtCaU8sSUFBSTtnQ0FBRW1FLE9BQU87Z0NBQVF1RixXQUFXdlosTUFBTXdaLE9BQU8sQ0FBQyxFQUFFOzRCQUFDO3NDQUMvRzlYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFrQlcxQixNQUFNa1MsT0FBTyxDQUFDYSxVQUFVLENBQUNtRSxLQUFLO29CQUl4QmxYLE1BQU1rUyxPQUFPLENBQUNpRixJQUFJLENBQUMsSUFBSTtvQkFFdkJuWCxNQUFNa1MsT0FBTyxDQUFDYSxVQUFVLENBQUNtRSxLQUFLO29CQUc5QmxYLE1BQU1rUyxPQUFPLENBQUNpRixJQUFJLENBQUMsSUFBSTtvQkFJeEJuWCxNQUFNa1MsT0FBTyxDQUFDaUYsSUFBSSxDQUFDLElBQUk7b0JBQUluWCxNQUFNa1MsT0FBTyxDQUFDYSxVQUFVLENBQUNtRSxLQUFLOzt5T0FUeERsWCxPQUpOQSxNQUFNa1MsT0FBTyxDQUFDYSxVQUFVLENBQUNtRSxLQUFLLHNGQU14QmxYLE9BRkFBLE1BQU1rUyxPQUFPLENBQUNpRixJQUFJLENBQUMsSUFBSSwyQ0FLdkJuWCxPQUhBQSxNQUFNa1MsT0FBTyxDQUFDYSxVQUFVLENBQUNtRSxLQUFLLHlFQU8vQmxYLE9BSkNBLE1BQU1rUyxPQUFPLENBQUNpRixJQUFJLENBQUMsSUFBSSw4REFJR25YLE9BQTNCQSxNQUFNa1MsT0FBTyxDQUFDaUYsSUFBSSxDQUFDLElBQUksY0FBSW5YLE1BQU1rUyxPQUFPLENBQUNhLFVBQVUsQ0FBQ21FLEtBQUs7Ozs7QUFLdEY7R0F0dkN3QjFYOztRQUNML0QscURBQVdBO1FBQ0dDLHFEQUFXQTtRQUNkQSxxREFBV0E7UUFDdkJBLHFEQUFXQTtRQUNiMEIsOFNBQVFBOzs7S0FMQW9DIiwic291cmNlcyI6WyIvVXNlcnMvc3VyZW5kcmFnYW5uZS9yZXBvcy9teWNvcGlsb3QvcGFnZXMvaW50ZXJ2aWV3LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbmltcG9ydCBIZWFkIGZyb20gJ25leHQvaGVhZCc7XG5pbXBvcnQgeyB1c2VEaXNwYXRjaCwgdXNlU2VsZWN0b3IgfSBmcm9tICdyZWFjdC1yZWR1eCc7XG5cbi8vIE1VSSBDb21wb25lbnRzXG5pbXBvcnQge1xuICAgIEFsZXJ0LFxuICAgIEFwcEJhcixcbiAgICBBdmF0YXIsXG4gICAgQm94LFxuICAgIEJ1dHRvbixcbiAgICBDYXJkLFxuICAgIENhcmRDb250ZW50LFxuICAgIENhcmRIZWFkZXIsXG4gICAgQ2hlY2tib3gsXG4gICAgQ2hpcCxcbiAgICBDaXJjdWxhclByb2dyZXNzLFxuICAgIENvbnRhaW5lcixcbiAgICBGb3JtQ29udHJvbExhYmVsLFxuICAgIEdyaWQsXG4gICAgSWNvbkJ1dHRvbixcbiAgICBMaXN0LFxuICAgIExpc3RJdGVtLFxuICAgIExpc3RJdGVtVGV4dCxcbiAgICBQYXBlcixcbiAgICBTbmFja2JhcixcbiAgICBTd2l0Y2gsXG4gICAgVGV4dEZpZWxkLFxuICAgIFRvb2xiYXIsXG4gICAgVG9vbHRpcCxcbiAgICBUeXBvZ3JhcGh5LFxuICAgIHVzZVRoZW1lXG59IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xuXG4vLyBNVUkgSWNvbnNcbmltcG9ydCBBcnJvd0Rvd253YXJkSWNvbiBmcm9tICdAbXVpL2ljb25zLW1hdGVyaWFsL0Fycm93RG93bndhcmQnO1xuaW1wb3J0IEFycm93VXB3YXJkSWNvbiBmcm9tICdAbXVpL2ljb25zLW1hdGVyaWFsL0Fycm93VXB3YXJkJztcbmltcG9ydCBEZWxldGVTd2VlcEljb24gZnJvbSAnQG11aS9pY29ucy1tYXRlcmlhbC9EZWxldGVTd2VlcCc7XG5pbXBvcnQgSGVhcmluZ0ljb24gZnJvbSAnQG11aS9pY29ucy1tYXRlcmlhbC9IZWFyaW5nJztcbmltcG9ydCBNaWNJY29uIGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwvTWljJztcbmltcG9ydCBNaWNPZmZJY29uIGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwvTWljT2ZmJztcbmltcG9ydCBQZXJzb25JY29uIGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwvUGVyc29uJztcbmltcG9ydCBQaWN0dXJlSW5QaWN0dXJlQWx0SWNvbiBmcm9tICdAbXVpL2ljb25zLW1hdGVyaWFsL1BpY3R1cmVJblBpY3R1cmVBbHQnO1xuaW1wb3J0IFBsYXlsaXN0QWRkQ2hlY2tJY29uIGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwvUGxheWxpc3RBZGRDaGVjayc7XG5pbXBvcnQgU2NyZWVuU2hhcmVJY29uIGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwvU2NyZWVuU2hhcmUnO1xuaW1wb3J0IFNlbmRJY29uIGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwvU2VuZCc7XG5pbXBvcnQgU2V0dGluZ3NJY29uIGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwvU2V0dGluZ3MnO1xuaW1wb3J0IFNtYXJ0VG95SWNvbiBmcm9tICdAbXVpL2ljb25zLW1hdGVyaWFsL1NtYXJ0VG95JztcbmltcG9ydCBTdG9wU2NyZWVuU2hhcmVJY29uIGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwvU3RvcFNjcmVlblNoYXJlJztcblxuLy8gVGhpcmQtcGFydHkgTGlicmFyaWVzXG5pbXBvcnQgeyBHb29nbGVHZW5lcmF0aXZlQUkgfSBmcm9tICdAZ29vZ2xlL2dlbmVyYXRpdmUtYWknO1xuaW1wb3J0IGhsanMgZnJvbSAnaGlnaGxpZ2h0LmpzJztcbmltcG9ydCAnaGlnaGxpZ2h0LmpzL3N0eWxlcy9hdG9tLW9uZS1kYXJrLmNzcyc7XG5pbXBvcnQgdGhyb3R0bGUgZnJvbSAnbG9kYXNoLnRocm90dGxlJztcbmltcG9ydCBPcGVuQUkgZnJvbSAnb3BlbmFpJztcbmltcG9ydCBSZWFjdE1hcmtkb3duIGZyb20gJ3JlYWN0LW1hcmtkb3duJztcbmltcG9ydCBTY3JvbGxUb0JvdHRvbSBmcm9tICdyZWFjdC1zY3JvbGwtdG8tYm90dG9tJztcblxuLy8gU3BlZWNoIFNlcnZpY2VzXG5cbi8vIExvY2FsIEltcG9ydHNcbmltcG9ydCBTZXR0aW5nc0RpYWxvZyBmcm9tICcuLi9jb21wb25lbnRzL1NldHRpbmdzRGlhbG9nJztcbmltcG9ydCB7IHNldEFJUmVzcG9uc2UgfSBmcm9tICcuLi9yZWR1eC9haVJlc3BvbnNlU2xpY2UnO1xuaW1wb3J0IHsgYWRkVG9IaXN0b3J5IH0gZnJvbSAnLi4vcmVkdXgvaGlzdG9yeVNsaWNlJztcbmltcG9ydCB7IGNsZWFyVHJhbnNjcmlwdGlvbiwgc2V0VHJhbnNjcmlwdGlvbiB9IGZyb20gJy4uL3JlZHV4L3RyYW5zY3JpcHRpb25TbGljZSc7XG5pbXBvcnQgeyBnZXRDb25maWcgfSBmcm9tICcuLi91dGlscy9jb25maWcnO1xuaW1wb3J0IHsgY3JlYXRlU3BlZWNoU2VydmljZSB9IGZyb20gJy4uL3V0aWxzL3NwZWVjaFNlcnZpY2VzJztcblxuXG5cbmZ1bmN0aW9uIGRlYm91bmNlKGZ1bmMsIHRpbWVvdXQgPSAxMDApIHtcbiAgbGV0IHRpbWVyO1xuICByZXR1cm4gKC4uLmFyZ3MpID0+IHtcbiAgICBjbGVhclRpbWVvdXQodGltZXIpO1xuICAgIHRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBmdW5jLmFwcGx5KHRoaXMsIGFyZ3MpO1xuICAgIH0sIHRpbWVvdXQpO1xuICB9O1xufVxuXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEludGVydmlld1BhZ2UoKSB7XG4gIGNvbnN0IGRpc3BhdGNoID0gdXNlRGlzcGF0Y2goKTtcbiAgY29uc3QgdHJhbnNjcmlwdGlvbkZyb21TdG9yZSA9IHVzZVNlbGVjdG9yKHN0YXRlID0+IHN0YXRlLnRyYW5zY3JpcHRpb24pO1xuICBjb25zdCBhaVJlc3BvbnNlRnJvbVN0b3JlID0gdXNlU2VsZWN0b3Ioc3RhdGUgPT4gc3RhdGUuYWlSZXNwb25zZSk7XG4gIGNvbnN0IGhpc3RvcnkgPSB1c2VTZWxlY3RvcihzdGF0ZSA9PiBzdGF0ZS5oaXN0b3J5KTtcbiAgY29uc3QgdGhlbWUgPSB1c2VUaGVtZSgpO1xuXG4gIGNvbnN0IFthcHBDb25maWcsIHNldEFwcENvbmZpZ10gPSB1c2VTdGF0ZShnZXRDb25maWcoKSk7XG5cbiAgY29uc3QgW3N5c3RlbVJlY29nbml6ZXIsIHNldFN5c3RlbVJlY29nbml6ZXJdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFttaWNSZWNvZ25pemVyLCBzZXRNaWNSZWNvZ25pemVyXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbc3lzdGVtU3BlZWNoU2VydmljZSwgc2V0U3lzdGVtU3BlZWNoU2VydmljZV0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW21pY1NwZWVjaFNlcnZpY2UsIHNldE1pY1NwZWVjaFNlcnZpY2VdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFthY3RpdmVTcGVlY2hTZXJ2aWNlLCBzZXRBY3RpdmVTcGVlY2hTZXJ2aWNlXSA9IHVzZVN0YXRlKG51bGwpOyAvLyBUcmFjayB3aGljaCBzZXJ2aWNlIGlzIGFjdGl2ZVxuICBjb25zdCBbc3lzdGVtQXV0b01vZGUsIHNldFN5c3RlbUF1dG9Nb2RlXSA9IHVzZVN0YXRlKGFwcENvbmZpZy5zeXN0ZW1BdXRvTW9kZSAhPT0gdW5kZWZpbmVkID8gYXBwQ29uZmlnLnN5c3RlbUF1dG9Nb2RlIDogdHJ1ZSk7XG4gIGNvbnN0IFtvcGVuQUksIHNldE9wZW5BSV0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3NldHRpbmdzT3Blbiwgc2V0U2V0dGluZ3NPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzTWljcm9waG9uZUFjdGl2ZSwgc2V0SXNNaWNyb3Bob25lQWN0aXZlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzU3lzdGVtQXVkaW9BY3RpdmUsIHNldElzU3lzdGVtQXVkaW9BY3RpdmVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc25hY2tiYXJPcGVuLCBzZXRTbmFja2Jhck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc25hY2tiYXJNZXNzYWdlLCBzZXRTbmFja2Jhck1lc3NhZ2VdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbc25hY2tiYXJTZXZlcml0eSwgc2V0U25hY2tiYXJTZXZlcml0eV0gPSB1c2VTdGF0ZSgnaW5mbycpO1xuICBjb25zdCBbc2VsZWN0ZWRRdWVzdGlvbnMsIHNldFNlbGVjdGVkUXVlc3Rpb25zXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2lzTWFudWFsTW9kZSwgc2V0SXNNYW51YWxNb2RlXSA9IHVzZVN0YXRlKGFwcENvbmZpZy5pc01hbnVhbE1vZGUgIT09IHVuZGVmaW5lZCA/IGFwcENvbmZpZy5pc01hbnVhbE1vZGUgOiBmYWxzZSk7XG4gIGNvbnN0IFttaWNUcmFuc2NyaXB0aW9uLCBzZXRNaWNUcmFuc2NyaXB0aW9uXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzUHJvY2Vzc2luZywgc2V0SXNQcm9jZXNzaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQUlMb2FkaW5nLCBzZXRJc0FJTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2F1dG9TY3JvbGwsIHNldEF1dG9TY3JvbGxdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFthaVJlc3BvbnNlU29ydE9yZGVyLCBzZXRBaVJlc3BvbnNlU29ydE9yZGVyXSA9IHVzZVN0YXRlKCduZXdlc3RBdFRvcCcpO1xuICBjb25zdCBbaXNQaXBXaW5kb3dBY3RpdmUsIHNldElzUGlwV2luZG93QWN0aXZlXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBwaXBXaW5kb3dSZWYgPSB1c2VSZWYobnVsbCk7XG4gIGNvbnN0IGRvY3VtZW50UGlwV2luZG93UmVmID0gdXNlUmVmKG51bGwpO1xuICBjb25zdCBkb2N1bWVudFBpcElmcmFtZVJlZiA9IHVzZVJlZihudWxsKTtcbiAgY29uc3Qgc3lzdGVtSW50ZXJpbVRyYW5zY3JpcHRpb24gPSB1c2VSZWYoJycpO1xuICBjb25zdCBtaWNJbnRlcmltVHJhbnNjcmlwdGlvbiA9IHVzZVJlZignJyk7XG4gIGNvbnN0IHNpbGVuY2VUaW1lciA9IHVzZVJlZihudWxsKTtcbiAgY29uc3QgZmluYWxUcmFuc2NyaXB0ID0gdXNlUmVmKHsgc3lzdGVtOiAnJywgbWljcm9waG9uZTogJycgfSk7XG4gIGNvbnN0IGlzTWFudWFsTW9kZVJlZiA9IHVzZVJlZihpc01hbnVhbE1vZGUpO1xuICBjb25zdCBzeXN0ZW1BdXRvTW9kZVJlZiA9IHVzZVJlZihzeXN0ZW1BdXRvTW9kZSk7XG4gIGNvbnN0IHRocm90dGxlZERpc3BhdGNoU2V0QUlSZXNwb25zZVJlZiA9IHVzZVJlZihudWxsKTtcblxuICBjb25zdCBzaG93U25hY2tiYXIgPSB1c2VDYWxsYmFjaygobWVzc2FnZSwgc2V2ZXJpdHkgPSAnaW5mbycpID0+IHtcbiAgICBzZXRTbmFja2Jhck1lc3NhZ2UobWVzc2FnZSk7XG4gICAgc2V0U25hY2tiYXJTZXZlcml0eShzZXZlcml0eSk7XG4gICAgc2V0U25hY2tiYXJPcGVuKHRydWUpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgaGFuZGxlU2V0dGluZ3NTYXZlZCA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdDb25maWcgPSBnZXRDb25maWcoKTtcbiAgICBzZXRBcHBDb25maWcobmV3Q29uZmlnKTtcbiAgICBzZXRJc0FJTG9hZGluZyh0cnVlKTtcbiAgICBzZXRTeXN0ZW1BdXRvTW9kZShuZXdDb25maWcuc3lzdGVtQXV0b01vZGUgIT09IHVuZGVmaW5lZCA/IG5ld0NvbmZpZy5zeXN0ZW1BdXRvTW9kZSA6IHRydWUpO1xuICAgIHNldElzTWFudWFsTW9kZShuZXdDb25maWcuaXNNYW51YWxNb2RlICE9PSB1bmRlZmluZWQgPyBuZXdDb25maWcuaXNNYW51YWxNb2RlIDogZmFsc2UpO1xuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgY3VycmVudENvbmZpZyA9IGFwcENvbmZpZztcbiAgICBjb25zdCBpbml0aWFsaXplQUkgPSAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBpZiAoY3VycmVudENvbmZpZy5haU1vZGVsLnN0YXJ0c1dpdGgoJ2dlbWluaScpKSB7XG4gICAgICAgICAgaWYgKCFjdXJyZW50Q29uZmlnLmdlbWluaUtleSkge1xuICAgICAgICAgICAgc2hvd1NuYWNrYmFyKCdHZW1pbmkgQVBJIGtleSByZXF1aXJlZC4gUGxlYXNlIHNldCBpdCBpbiBTZXR0aW5ncy4nLCAnZXJyb3InKTtcbiAgICAgICAgICAgIHNldE9wZW5BSShudWxsKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICB9XG4gICAgICAgICAgY29uc3QgZ2VuQUkgPSBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJKGN1cnJlbnRDb25maWcuZ2VtaW5pS2V5KTtcbiAgICAgICAgICBzZXRPcGVuQUkoZ2VuQUkpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGlmICghY3VycmVudENvbmZpZy5vcGVuYWlLZXkpIHtcbiAgICAgICAgICAgIHNob3dTbmFja2JhcignT3BlbkFJIEFQSSBrZXkgcmVxdWlyZWQuIFBsZWFzZSBzZXQgaXQgaW4gU2V0dGluZ3MuJywgJ2Vycm9yJyk7XG4gICAgICAgICAgICBzZXRPcGVuQUkobnVsbCk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICAgIGNvbnN0IG9wZW5haUNsaWVudCA9IG5ldyBPcGVuQUkoe1xuICAgICAgICAgICAgYXBpS2V5OiBjdXJyZW50Q29uZmlnLm9wZW5haUtleSxcbiAgICAgICAgICAgIGRhbmdlcm91c2x5QWxsb3dCcm93c2VyOiB0cnVlXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgc2V0T3BlbkFJKG9wZW5haUNsaWVudCk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluaXRpYWxpemluZyBBSSBjbGllbnQ6JywgZXJyb3IpO1xuICAgICAgICBzaG93U25hY2tiYXIoJ0Vycm9yIGluaXRpYWxpemluZyBBSSBjbGllbnQ6ICcgKyBlcnJvci5tZXNzYWdlLCAnZXJyb3InKTtcbiAgICAgICAgc2V0T3BlbkFJKG51bGwpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgc2V0SXNBSUxvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG4gICAgaWYgKGlzQUlMb2FkaW5nKSBpbml0aWFsaXplQUkoKTtcbiAgfSwgW2FwcENvbmZpZywgaXNBSUxvYWRpbmcsIHNob3dTbmFja2Jhcl0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7IGlzTWFudWFsTW9kZVJlZi5jdXJyZW50ID0gaXNNYW51YWxNb2RlOyB9LCBbaXNNYW51YWxNb2RlXSk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7IHN5c3RlbUF1dG9Nb2RlUmVmLmN1cnJlbnQgPSBzeXN0ZW1BdXRvTW9kZTsgfSwgW3N5c3RlbUF1dG9Nb2RlXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICB0aHJvdHRsZWREaXNwYXRjaFNldEFJUmVzcG9uc2VSZWYuY3VycmVudCA9IHRocm90dGxlKChwYXlsb2FkKSA9PiB7XG4gICAgICBkaXNwYXRjaChzZXRBSVJlc3BvbnNlKHBheWxvYWQpKTtcbiAgICB9LCAyNTAsIHsgbGVhZGluZzogdHJ1ZSwgdHJhaWxpbmc6IHRydWUgfSk7XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKHRocm90dGxlZERpc3BhdGNoU2V0QUlSZXNwb25zZVJlZi5jdXJyZW50ICYmIHR5cGVvZiB0aHJvdHRsZWREaXNwYXRjaFNldEFJUmVzcG9uc2VSZWYuY3VycmVudC5jYW5jZWwgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgdGhyb3R0bGVkRGlzcGF0Y2hTZXRBSVJlc3BvbnNlUmVmLmN1cnJlbnQuY2FuY2VsKCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW2Rpc3BhdGNoXSk7XG5cbiAgY29uc3QgaGFuZGxlU25hY2tiYXJDbG9zZSA9ICgpID0+IHNldFNuYWNrYmFyT3BlbihmYWxzZSk7XG5cbiAgY29uc3Qgc3RvcFJlY29yZGluZyA9IGFzeW5jIChzb3VyY2UpID0+IHtcbiAgICBjb25zdCBzcGVlY2hTZXJ2aWNlID0gc291cmNlID09PSAnc3lzdGVtJyA/IHN5c3RlbVNwZWVjaFNlcnZpY2UgOiBtaWNTcGVlY2hTZXJ2aWNlO1xuICAgIGNvbnN0IHJlY29nbml6ZXIgPSBzb3VyY2UgPT09ICdzeXN0ZW0nID8gc3lzdGVtUmVjb2duaXplciA6IG1pY1JlY29nbml6ZXI7XG5cbiAgICB0cnkge1xuICAgICAgLy8gU3RvcCBuZXcgc3BlZWNoIHNlcnZpY2UgaWYgYXZhaWxhYmxlXG4gICAgICBpZiAoc3BlZWNoU2VydmljZSkge1xuICAgICAgICBhd2FpdCBzcGVlY2hTZXJ2aWNlLnN0b3AoKTtcbiAgICAgIH1cblxuICAgICAgLy8gRmFsbGJhY2sgdG8gbGVnYWN5IEF6dXJlIHJlY29nbml6ZXIgaWYgc3RpbGwgaW4gdXNlXG4gICAgICBpZiAocmVjb2duaXplciAmJiB0eXBlb2YgcmVjb2duaXplci5zdG9wQ29udGludW91c1JlY29nbml0aW9uQXN5bmMgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgYXdhaXQgcmVjb2duaXplci5zdG9wQ29udGludW91c1JlY29nbml0aW9uQXN5bmMoKTtcbiAgICAgICAgaWYgKHJlY29nbml6ZXIuYXVkaW9Db25maWcgJiYgcmVjb2duaXplci5hdWRpb0NvbmZpZy5wcml2U291cmNlICYmIHJlY29nbml6ZXIuYXVkaW9Db25maWcucHJpdlNvdXJjZS5wcml2U3RyZWFtKSB7XG4gICAgICAgICAgY29uc3Qgc3RyZWFtID0gcmVjb2duaXplci5hdWRpb0NvbmZpZy5wcml2U291cmNlLnByaXZTdHJlYW07XG4gICAgICAgICAgaWYgKHN0cmVhbSBpbnN0YW5jZW9mIE1lZGlhU3RyZWFtKSB7XG4gICAgICAgICAgICBzdHJlYW0uZ2V0VHJhY2tzKCkuZm9yRWFjaCh0cmFjayA9PiB7XG4gICAgICAgICAgICAgIHRyYWNrLnN0b3AoKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAocmVjb2duaXplci5hdWRpb0NvbmZpZyAmJiB0eXBlb2YgcmVjb2duaXplci5hdWRpb0NvbmZpZy5jbG9zZSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgIHJlY29nbml6ZXIuYXVkaW9Db25maWcuY2xvc2UoKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBFcnJvciBzdG9wcGluZyAke3NvdXJjZX0gcmVjb2duaXRpb246YCwgZXJyb3IpO1xuICAgICAgc2hvd1NuYWNrYmFyKGBFcnJvciBzdG9wcGluZyAke3NvdXJjZX0gYXVkaW86ICR7ZXJyb3IubWVzc2FnZX1gLCAnZXJyb3InKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgaWYgKHNvdXJjZSA9PT0gJ3N5c3RlbScpIHtcbiAgICAgICAgc2V0SXNTeXN0ZW1BdWRpb0FjdGl2ZShmYWxzZSk7XG4gICAgICAgIHNldFN5c3RlbVJlY29nbml6ZXIobnVsbCk7XG4gICAgICAgIHNldFN5c3RlbVNwZWVjaFNlcnZpY2UobnVsbCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRJc01pY3JvcGhvbmVBY3RpdmUoZmFsc2UpO1xuICAgICAgICBzZXRNaWNSZWNvZ25pemVyKG51bGwpO1xuICAgICAgICBzZXRNaWNTcGVlY2hTZXJ2aWNlKG51bGwpO1xuICAgICAgICBzZXRBY3RpdmVTcGVlY2hTZXJ2aWNlKG51bGwpOyAvLyBDbGVhciBhY3RpdmUgc2VydmljZSB3aGVuIHN0b3BwaW5nIG1pY3JvcGhvbmVcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2xlYXJTeXN0ZW1UcmFuc2NyaXB0aW9uID0gKCkgPT4ge1xuICAgIGZpbmFsVHJhbnNjcmlwdC5jdXJyZW50LnN5c3RlbSA9ICcnO1xuICAgIHN5c3RlbUludGVyaW1UcmFuc2NyaXB0aW9uLmN1cnJlbnQgPSAnJztcbiAgICBkaXNwYXRjaChjbGVhclRyYW5zY3JpcHRpb24oKSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2xlYXJNaWNUcmFuc2NyaXB0aW9uID0gKCkgPT4ge1xuICAgIGZpbmFsVHJhbnNjcmlwdC5jdXJyZW50Lm1pY3JvcGhvbmUgPSAnJztcbiAgICBtaWNJbnRlcmltVHJhbnNjcmlwdGlvbi5jdXJyZW50ID0gJyc7XG4gICAgc2V0TWljVHJhbnNjcmlwdGlvbignJyk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlVHJhbnNjcmlwdGlvbkV2ZW50ID0gKHRleHQsIHNvdXJjZSkgPT4ge1xuICAgIGNvbnN0IGNsZWFuVGV4dCA9IHRleHQucmVwbGFjZSgvXFxzKy9nLCAnICcpLnRyaW0oKTtcbiAgICBpZiAoIWNsZWFuVGV4dCkgcmV0dXJuO1xuXG4gICAgZmluYWxUcmFuc2NyaXB0LmN1cnJlbnRbc291cmNlXSArPSBjbGVhblRleHQgKyAnICc7XG5cbiAgICBpZiAoc291cmNlID09PSAnc3lzdGVtJykge1xuICAgICAgZGlzcGF0Y2goc2V0VHJhbnNjcmlwdGlvbihmaW5hbFRyYW5zY3JpcHQuY3VycmVudC5zeXN0ZW0gKyBzeXN0ZW1JbnRlcmltVHJhbnNjcmlwdGlvbi5jdXJyZW50KSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldE1pY1RyYW5zY3JpcHRpb24oZmluYWxUcmFuc2NyaXB0LmN1cnJlbnQubWljcm9waG9uZSArIG1pY0ludGVyaW1UcmFuc2NyaXB0aW9uLmN1cnJlbnQpO1xuICAgIH1cblxuICAgIGNvbnN0IGN1cnJlbnRDb25maWcgPSBnZXRDb25maWcoKTtcbiAgICBjb25zdCBjdXJyZW50U2lsZW5jZVRpbWVyRHVyYXRpb24gPSBjdXJyZW50Q29uZmlnLnNpbGVuY2VUaW1lckR1cmF0aW9uO1xuXG4gICAgaWYgKChzb3VyY2UgPT09ICdzeXN0ZW0nICYmIHN5c3RlbUF1dG9Nb2RlUmVmLmN1cnJlbnQpIHx8IChzb3VyY2UgPT09ICdtaWNyb3Bob25lJyAmJiAhaXNNYW51YWxNb2RlUmVmLmN1cnJlbnQpKSB7XG4gICAgICBjbGVhclRpbWVvdXQoc2lsZW5jZVRpbWVyLmN1cnJlbnQpO1xuICAgICAgc2lsZW5jZVRpbWVyLmN1cnJlbnQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgYXNrT3BlbkFJKGZpbmFsVHJhbnNjcmlwdC5jdXJyZW50W3NvdXJjZV0udHJpbSgpLCBzb3VyY2UpO1xuICAgICAgfSwgY3VycmVudFNpbGVuY2VUaW1lckR1cmF0aW9uICogMTAwMCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU1hbnVhbElucHV0Q2hhbmdlID0gKHZhbHVlLCBzb3VyY2UpID0+IHtcbiAgICBpZiAoc291cmNlID09PSAnc3lzdGVtJykge1xuICAgICAgZGlzcGF0Y2goc2V0VHJhbnNjcmlwdGlvbih2YWx1ZSkpO1xuICAgICAgZmluYWxUcmFuc2NyaXB0LmN1cnJlbnQuc3lzdGVtID0gdmFsdWU7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldE1pY1RyYW5zY3JpcHRpb24odmFsdWUpO1xuICAgICAgZmluYWxUcmFuc2NyaXB0LmN1cnJlbnQubWljcm9waG9uZSA9IHZhbHVlO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVNYW51YWxTdWJtaXQgPSAoc291cmNlKSA9PiB7XG4gICAgY29uc3QgdGV4dFRvU3VibWl0ID0gc291cmNlID09PSAnc3lzdGVtJyA/IHRyYW5zY3JpcHRpb25Gcm9tU3RvcmUgOiBtaWNUcmFuc2NyaXB0aW9uO1xuICAgIGlmICh0ZXh0VG9TdWJtaXQudHJpbSgpKSB7XG4gICAgICBhc2tPcGVuQUkodGV4dFRvU3VibWl0LnRyaW0oKSwgc291cmNlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2hvd1NuYWNrYmFyKCdJbnB1dCBpcyBlbXB0eS4nLCAnd2FybmluZycpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVLZXlQcmVzcyA9IChlLCBzb3VyY2UpID0+IHtcbiAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicgJiYgIWUuc2hpZnRLZXkpIHtcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGhhbmRsZU1hbnVhbFN1Ym1pdChzb3VyY2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDb21iaW5lQW5kU3VibWl0ID0gKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHtcbiAgICAgIHNob3dTbmFja2JhcignTm8gcXVlc3Rpb25zIHNlbGVjdGVkIHRvIGNvbWJpbmUuJywgJ3dhcm5pbmcnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgY29uc3QgcXVlc3Rpb25IaXN0b3J5ID0gaGlzdG9yeS5maWx0ZXIoZSA9PiBlLnR5cGUgPT09ICdxdWVzdGlvbicpLnNsaWNlKCkucmV2ZXJzZSgpO1xuICAgIGNvbnN0IHF1ZXN0aW9uVGV4dHMgPSBzZWxlY3RlZFF1ZXN0aW9ucy5tYXAoc2VsZWN0ZWRJbmRleEluUmV2ZXJzZWRBcnJheSA9PiB7XG4gICAgICByZXR1cm4gcXVlc3Rpb25IaXN0b3J5W3NlbGVjdGVkSW5kZXhJblJldmVyc2VkQXJyYXldPy50ZXh0O1xuICAgIH0pLmZpbHRlcih0ZXh0ID0+IHRleHQpO1xuXG4gICAgaWYgKHF1ZXN0aW9uVGV4dHMubGVuZ3RoID09PSAwKSB7XG4gICAgICBzaG93U25hY2tiYXIoJ0NvdWxkIG5vdCByZXRyaWV2ZSBzZWxlY3RlZCBxdWVzdGlvbiB0ZXh0cy4nLCAnd2FybmluZycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnN0IGNvbWJpbmVkVGV4dCA9IHF1ZXN0aW9uVGV4dHMuam9pbignXFxuXFxuLS0tXFxuXFxuJyk7XG4gICAgYXNrT3BlbkFJKGNvbWJpbmVkVGV4dCwgJ2NvbWJpbmVkJyk7XG4gICAgc2V0U2VsZWN0ZWRRdWVzdGlvbnMoW10pO1xuICB9O1xuXG4gIC8vIExlZ2FjeSBBenVyZSByZWNvZ25pemVyIGZ1bmN0aW9uIGZvciBmYWxsYmFja1xuICBjb25zdCBjcmVhdGVMZWdhY3lBenVyZVJlY29nbml6ZXIgPSBhc3luYyAobWVkaWFTdHJlYW0sIHNvdXJjZSkgPT4ge1xuICAgIGNvbnN0IGN1cnJlbnRDb25maWcgPSBnZXRDb25maWcoKTtcbiAgICBpZiAoIWN1cnJlbnRDb25maWcuYXp1cmVUb2tlbiB8fCAhY3VycmVudENvbmZpZy5henVyZVJlZ2lvbikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdBenVyZSBTcGVlY2ggY3JlZGVudGlhbHMgbWlzc2luZycpO1xuICAgIH1cblxuICAgIGxldCBhdWRpb0NvbmZpZztcbiAgICB0cnkge1xuICAgICAgYXVkaW9Db25maWcgPSBTcGVlY2hTREsuQXVkaW9Db25maWcuZnJvbVN0cmVhbUlucHV0KG1lZGlhU3RyZWFtKTtcbiAgICB9IGNhdGNoIChjb25maWdFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgY3JlYXRpbmcgQXVkaW9Db25maWcgZm9yICR7c291cmNlfTpgLCBjb25maWdFcnJvcik7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm9yIHNldHRpbmcgdXAgYXVkaW8gZm9yICR7c291cmNlfTogJHtjb25maWdFcnJvci5tZXNzYWdlfWApO1xuICAgIH1cblxuICAgIGNvbnN0IHNwZWVjaENvbmZpZyA9IFNwZWVjaFNESy5TcGVlY2hDb25maWcuZnJvbVN1YnNjcmlwdGlvbihjdXJyZW50Q29uZmlnLmF6dXJlVG9rZW4sIGN1cnJlbnRDb25maWcuYXp1cmVSZWdpb24pO1xuICAgIHNwZWVjaENvbmZpZy5zcGVlY2hSZWNvZ25pdGlvbkxhbmd1YWdlID0gY3VycmVudENvbmZpZy5henVyZUxhbmd1YWdlO1xuXG4gICAgY29uc3QgcmVjb2duaXplciA9IG5ldyBTcGVlY2hTREsuU3BlZWNoUmVjb2duaXplcihzcGVlY2hDb25maWcsIGF1ZGlvQ29uZmlnKTtcblxuICAgIHJlY29nbml6ZXIucmVjb2duaXppbmcgPSAocywgZSkgPT4ge1xuICAgICAgaWYgKGUucmVzdWx0LnJlYXNvbiA9PT0gU3BlZWNoU0RLLlJlc3VsdFJlYXNvbi5SZWNvZ25pemluZ1NwZWVjaCkge1xuICAgICAgICBjb25zdCBpbnRlcmltVGV4dCA9IGUucmVzdWx0LnRleHQ7XG4gICAgICAgIGlmIChzb3VyY2UgPT09ICdzeXN0ZW0nKSB7XG4gICAgICAgICAgc3lzdGVtSW50ZXJpbVRyYW5zY3JpcHRpb24uY3VycmVudCA9IGludGVyaW1UZXh0O1xuICAgICAgICAgIGRpc3BhdGNoKHNldFRyYW5zY3JpcHRpb24oZmluYWxUcmFuc2NyaXB0LmN1cnJlbnQuc3lzdGVtICsgaW50ZXJpbVRleHQpKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBtaWNJbnRlcmltVHJhbnNjcmlwdGlvbi5jdXJyZW50ID0gaW50ZXJpbVRleHQ7XG4gICAgICAgICAgc2V0TWljVHJhbnNjcmlwdGlvbihmaW5hbFRyYW5zY3JpcHQuY3VycmVudC5taWNyb3Bob25lICsgaW50ZXJpbVRleHQpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfTtcblxuICAgIHJlY29nbml6ZXIucmVjb2duaXplZCA9IChzLCBlKSA9PiB7XG4gICAgICBpZiAoZS5yZXN1bHQucmVhc29uID09PSBTcGVlY2hTREsuUmVzdWx0UmVhc29uLlJlY29nbml6ZWRTcGVlY2ggJiYgZS5yZXN1bHQudGV4dCkge1xuICAgICAgICBpZiAoc291cmNlID09PSAnc3lzdGVtJykgc3lzdGVtSW50ZXJpbVRyYW5zY3JpcHRpb24uY3VycmVudCA9ICcnO1xuICAgICAgICBlbHNlIG1pY0ludGVyaW1UcmFuc2NyaXB0aW9uLmN1cnJlbnQgPSAnJztcbiAgICAgICAgaGFuZGxlVHJhbnNjcmlwdGlvbkV2ZW50KGUucmVzdWx0LnRleHQsIHNvdXJjZSk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIHJlY29nbml6ZXIuY2FuY2VsZWQgPSAocywgZSkgPT4ge1xuICAgICAgY29uc29sZS5sb2coYENBTkNFTEVEOiBSZWFzb249JHtlLnJlYXNvbn0gZm9yICR7c291cmNlfWApO1xuICAgICAgaWYgKGUucmVhc29uID09PSBTcGVlY2hTREsuQ2FuY2VsbGF0aW9uUmVhc29uLkVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYENBTkNFTEVEOiBFcnJvckNvZGU9JHtlLmVycm9yQ29kZX1gKTtcbiAgICAgICAgY29uc29sZS5lcnJvcihgQ0FOQ0VMRUQ6IEVycm9yRGV0YWlscz0ke2UuZXJyb3JEZXRhaWxzfWApO1xuICAgICAgICBzaG93U25hY2tiYXIoYFNwZWVjaCByZWNvZ25pdGlvbiBlcnJvciBmb3IgJHtzb3VyY2V9OiAke2UuZXJyb3JEZXRhaWxzfWAsICdlcnJvcicpO1xuICAgICAgfVxuICAgICAgc3RvcFJlY29yZGluZyhzb3VyY2UpO1xuICAgIH07XG5cbiAgICByZWNvZ25pemVyLnNlc3Npb25TdG9wcGVkID0gKHMsIGUpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKGBTZXNzaW9uIHN0b3BwZWQgZXZlbnQgZm9yICR7c291cmNlfS5gKTtcbiAgICAgIHN0b3BSZWNvcmRpbmcoc291cmNlKTtcbiAgICB9O1xuXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHJlY29nbml6ZXIuc3RhcnRDb250aW51b3VzUmVjb2duaXRpb25Bc3luYygpO1xuICAgICAgc2hvd1NuYWNrYmFyKGAke3NvdXJjZSA9PT0gJ3N5c3RlbScgPyAnU3lzdGVtIGF1ZGlvJyA6ICdNaWNyb3Bob25lJ30gcmVjb3JkaW5nIHN0YXJ0ZWQgKEF6dXJlIFNwZWVjaCkuYCwgJ3N1Y2Nlc3MnKTtcbiAgICAgIHJldHVybiByZWNvZ25pemVyO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBFcnJvciBzdGFydGluZyAke3NvdXJjZX0gY29udGludW91cyByZWNvZ25pdGlvbjpgLCBlcnJvcik7XG4gICAgICBpZiAoYXVkaW9Db25maWcgJiYgdHlwZW9mIGF1ZGlvQ29uZmlnLmNsb3NlID09PSAnZnVuY3Rpb24nKSBhdWRpb0NvbmZpZy5jbG9zZSgpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGNyZWF0ZVNwZWVjaFJlY29nbml6ZXIgPSBhc3luYyAobWVkaWFTdHJlYW0sIHNvdXJjZSkgPT4ge1xuICAgIGNvbnN0IGN1cnJlbnRDb25maWcgPSBnZXRDb25maWcoKTtcblxuICAgIC8vIENyZWF0ZSBzcGVlY2ggc2VydmljZSBjYWxsYmFja3NcbiAgICBjb25zdCBjYWxsYmFja3MgPSB7XG4gICAgICBvblN0YXJ0OiAoc291cmNlKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKGBTcGVlY2ggcmVjb2duaXRpb24gc3RhcnRlZCBmb3IgJHtzb3VyY2V9YCk7XG4gICAgICAgIHNob3dTbmFja2JhcihgJHtzb3VyY2UgPT09ICdzeXN0ZW0nID8gJ1N5c3RlbSBhdWRpbycgOiAnTWljcm9waG9uZSd9IHJlY29yZGluZyBzdGFydGVkLmAsICdzdWNjZXNzJyk7XG4gICAgICB9LFxuXG4gICAgICBvbkludGVyaW1SZXN1bHQ6ICh0ZXh0LCBzb3VyY2UpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coYEludGVyaW0gcmVzdWx0IGZvciAke3NvdXJjZX06YCwgdGV4dCk7XG4gICAgICAgIGlmIChzb3VyY2UgPT09ICdzeXN0ZW0nKSB7XG4gICAgICAgICAgc3lzdGVtSW50ZXJpbVRyYW5zY3JpcHRpb24uY3VycmVudCA9IHRleHQ7XG4gICAgICAgICAgZGlzcGF0Y2goc2V0VHJhbnNjcmlwdGlvbihmaW5hbFRyYW5zY3JpcHQuY3VycmVudC5zeXN0ZW0gKyB0ZXh0KSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgbWljSW50ZXJpbVRyYW5zY3JpcHRpb24uY3VycmVudCA9IHRleHQ7XG4gICAgICAgICAgc2V0TWljVHJhbnNjcmlwdGlvbihmaW5hbFRyYW5zY3JpcHQuY3VycmVudC5taWNyb3Bob25lICsgdGV4dCk7XG4gICAgICAgIH1cbiAgICAgIH0sXG5cbiAgICAgIG9uRmluYWxSZXN1bHQ6ICh0ZXh0LCBzb3VyY2UpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coYEZpbmFsIHJlc3VsdCBmb3IgJHtzb3VyY2V9OmAsIHRleHQpO1xuICAgICAgICAvLyBDbGVhciBpbnRlcmltIHRyYW5zY3JpcHRpb25cbiAgICAgICAgaWYgKHNvdXJjZSA9PT0gJ3N5c3RlbScpIHN5c3RlbUludGVyaW1UcmFuc2NyaXB0aW9uLmN1cnJlbnQgPSAnJztcbiAgICAgICAgZWxzZSBtaWNJbnRlcmltVHJhbnNjcmlwdGlvbi5jdXJyZW50ID0gJyc7XG5cbiAgICAgICAgLy8gSGFuZGxlIHRoZSBmaW5hbCB0cmFuc2NyaXB0aW9uXG4gICAgICAgIGhhbmRsZVRyYW5zY3JpcHRpb25FdmVudCh0ZXh0LCBzb3VyY2UpO1xuICAgICAgfSxcblxuICAgICAgb25FcnJvcjogKGVycm9yLCBzb3VyY2UpID0+IHtcbiAgICAgICAgY29uc29sZS5lcnJvcihgU3BlZWNoIHJlY29nbml0aW9uIGVycm9yIGZvciAke3NvdXJjZX06YCwgZXJyb3IpO1xuICAgICAgICBzaG93U25hY2tiYXIoYFNwZWVjaCByZWNvZ25pdGlvbiBlcnJvciBmb3IgJHtzb3VyY2V9OiAke2Vycm9yLm1lc3NhZ2V9YCwgJ2Vycm9yJyk7XG4gICAgICAgIHN0b3BSZWNvcmRpbmcoc291cmNlKTtcbiAgICAgIH0sXG5cbiAgICAgIG9uU3RvcDogKHNvdXJjZSkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZyhgU3BlZWNoIHJlY29nbml0aW9uIHN0b3BwZWQgZm9yICR7c291cmNlfWApO1xuICAgICAgfVxuICAgIH07XG5cbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgaWYgd2UgaGF2ZSB0aGUgcmVxdWlyZWQgY3JlZGVudGlhbHMgZm9yIHRoZSBzZWxlY3RlZCBzZXJ2aWNlXG4gICAgICBjb25zdCBzZWxlY3RlZFNlcnZpY2UgPSBjdXJyZW50Q29uZmlnLnNwZWVjaFNlcnZpY2UgfHwgJ2RlZXBncmFtJztcbiAgICAgIGxldCBjb25maWdUb1VzZSA9IGN1cnJlbnRDb25maWc7XG5cbiAgICAgIGlmIChzZWxlY3RlZFNlcnZpY2UgPT09ICdkZWVwZ3JhbScgJiYgIWN1cnJlbnRDb25maWcuZGVlcGdyYW1LZXkpIHtcbiAgICAgICAgLy8gSWYgRGVlcGdyYW0gaXMgc2VsZWN0ZWQgYnV0IG5vIGtleSBpcyBwcm92aWRlZCwgY2hlY2sgaWYgQXp1cmUgaXMgYXZhaWxhYmxlXG4gICAgICAgIGlmIChjdXJyZW50Q29uZmlnLmF6dXJlVG9rZW4gJiYgY3VycmVudENvbmZpZy5henVyZVJlZ2lvbikge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdEZWVwZ3JhbSBrZXkgbWlzc2luZywgZmFsbGluZyBiYWNrIHRvIEF6dXJlIFNwZWVjaCBTZXJ2aWNlcycpO1xuICAgICAgICAgIGNvbmZpZ1RvVXNlID0geyAuLi5jdXJyZW50Q29uZmlnLCBzcGVlY2hTZXJ2aWNlOiAnYXp1cmUnIH07XG4gICAgICAgICAgc2hvd1NuYWNrYmFyKCdVc2luZyBBenVyZSBTcGVlY2ggU2VydmljZXMgKERlZXBncmFtIGtleSBub3QgY29uZmlndXJlZCknLCAnaW5mbycpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRGVlcGdyYW0gQVBJIGtleSBpcyByZXF1aXJlZC4gR2V0IGEgZnJlZSBBUEkga2V5IGZyb20gZGVlcGdyYW0uY29tIGFuZCBjb25maWd1cmUgaXQgaW4gU2V0dGluZ3MgKOKame+4jyBpY29uKS4nKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChzZWxlY3RlZFNlcnZpY2UgPT09ICdhenVyZScgJiYgKCFjdXJyZW50Q29uZmlnLmF6dXJlVG9rZW4gfHwgIWN1cnJlbnRDb25maWcuYXp1cmVSZWdpb24pKSB7XG4gICAgICAgIC8vIElmIEF6dXJlIGlzIHNlbGVjdGVkIGJ1dCBjcmVkZW50aWFscyBhcmUgbWlzc2luZywgY2hlY2sgaWYgRGVlcGdyYW0gaXMgYXZhaWxhYmxlXG4gICAgICAgIGlmIChjdXJyZW50Q29uZmlnLmRlZXBncmFtS2V5KSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0F6dXJlIGNyZWRlbnRpYWxzIG1pc3NpbmcsIGZhbGxpbmcgYmFjayB0byBEZWVwZ3JhbScpO1xuICAgICAgICAgIGNvbmZpZ1RvVXNlID0geyAuLi5jdXJyZW50Q29uZmlnLCBzcGVlY2hTZXJ2aWNlOiAnZGVlcGdyYW0nIH07XG4gICAgICAgICAgc2hvd1NuYWNrYmFyKCdVc2luZyBEZWVwZ3JhbSAoQXp1cmUgY3JlZGVudGlhbHMgbm90IGNvbmZpZ3VyZWQpJywgJ2luZm8nKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F6dXJlIFNwZWVjaCBjcmVkZW50aWFscyBhcmUgcmVxdWlyZWQuIFBsZWFzZSBjb25maWd1cmUgdGhlbSBpbiBTZXR0aW5ncywgb3IgcHJvdmlkZSBEZWVwZ3JhbSBBUEkga2V5IGZvciBmYWxsYmFjay4nKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBDcmVhdGUgdGhlIGFwcHJvcHJpYXRlIHNwZWVjaCBzZXJ2aWNlXG4gICAgICBjb25zdCBzcGVlY2hTZXJ2aWNlID0gY3JlYXRlU3BlZWNoU2VydmljZShjb25maWdUb1VzZSwgY2FsbGJhY2tzKTtcblxuICAgICAgLy8gVHJhY2sgd2hpY2ggc2VydmljZSBpcyBiZWluZyB1c2VkXG4gICAgICBzZXRBY3RpdmVTcGVlY2hTZXJ2aWNlKGNvbmZpZ1RvVXNlLnNwZWVjaFNlcnZpY2UpO1xuXG4gICAgICAvLyBTdGFydCB0aGUgc3BlZWNoIHNlcnZpY2VcbiAgICAgIGF3YWl0IHNwZWVjaFNlcnZpY2Uuc3RhcnQobWVkaWFTdHJlYW0sIHNvdXJjZSk7XG5cbiAgICAgIC8vIFNob3cgd2hpY2ggc2VydmljZSBpcyBhY3RpdmVcbiAgICAgIGNvbnN0IHNlcnZpY2VOYW1lID0gY29uZmlnVG9Vc2Uuc3BlZWNoU2VydmljZSA9PT0gJ2RlZXBncmFtJyA/ICdEZWVwZ3JhbScgOiAnQXp1cmUgU3BlZWNoJztcbiAgICAgIHNob3dTbmFja2JhcihgVXNpbmcgJHtzZXJ2aWNlTmFtZX0gZm9yICR7c291cmNlID09PSAnc3lzdGVtJyA/ICdzeXN0ZW0gYXVkaW8nIDogJ21pY3JvcGhvbmUnfWAsICdpbmZvJyk7XG5cbiAgICAgIHJldHVybiBzcGVlY2hTZXJ2aWNlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBFcnJvciBjcmVhdGluZyBzcGVlY2ggc2VydmljZSBmb3IgJHtzb3VyY2V9OmAsIGVycm9yKTtcblxuICAgICAgLy8gSWYgRGVlcGdyYW0gZmFpbHMgYW5kIHdlIGhhdmUgQXp1cmUgY3JlZGVudGlhbHMsIHRyeSBBenVyZSBTcGVlY2ggU2VydmljZSBmaXJzdFxuICAgICAgaWYgKGNvbmZpZ1RvVXNlLnNwZWVjaFNlcnZpY2UgPT09ICdkZWVwZ3JhbScgJiYgY3VycmVudENvbmZpZy5henVyZVRva2VuICYmIGN1cnJlbnRDb25maWcuYXp1cmVSZWdpb24pIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgRGVlcGdyYW0gZmFpbGVkLCB0cnlpbmcgQXp1cmUgU3BlZWNoIFNlcnZpY2UgZm9yICR7c291cmNlfWApO1xuXG4gICAgICAgICAgLy8gQ3JlYXRlIEF6dXJlIHNlcnZpY2UgY29uZmlndXJhdGlvblxuICAgICAgICAgIGNvbnN0IGF6dXJlQ29uZmlnID0geyAuLi5jdXJyZW50Q29uZmlnLCBzcGVlY2hTZXJ2aWNlOiAnYXp1cmUnIH07XG4gICAgICAgICAgY29uc3QgYXp1cmVTcGVlY2hTZXJ2aWNlID0gY3JlYXRlU3BlZWNoU2VydmljZShhenVyZUNvbmZpZywgY2FsbGJhY2tzKTtcblxuICAgICAgICAgIC8vIFRyYWNrIHdoaWNoIHNlcnZpY2UgaXMgYmVpbmcgdXNlZFxuICAgICAgICAgIHNldEFjdGl2ZVNwZWVjaFNlcnZpY2UoJ2F6dXJlJyk7XG5cbiAgICAgICAgICAvLyBTdGFydCB0aGUgQXp1cmUgc3BlZWNoIHNlcnZpY2VcbiAgICAgICAgICBhd2FpdCBhenVyZVNwZWVjaFNlcnZpY2Uuc3RhcnQobWVkaWFTdHJlYW0sIHNvdXJjZSk7XG5cbiAgICAgICAgICBzaG93U25hY2tiYXIoYFN3aXRjaGVkIHRvIEF6dXJlIFNwZWVjaCBTZXJ2aWNlcyAoRGVlcGdyYW0gY29ubmVjdGlvbiBmYWlsZWQpYCwgJ3dhcm5pbmcnKTtcbiAgICAgICAgICByZXR1cm4gYXp1cmVTcGVlY2hTZXJ2aWNlO1xuICAgICAgICB9IGNhdGNoIChhenVyZUVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihgQXp1cmUgU3BlZWNoIFNlcnZpY2UgYWxzbyBmYWlsZWQgZm9yICR7c291cmNlfTpgLCBhenVyZUVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBUcnkgbGVnYWN5IEF6dXJlIHJlY29nbml6ZXIgYXMgZmluYWwgZmFsbGJhY2tcbiAgICAgIGlmIChjdXJyZW50Q29uZmlnLmF6dXJlVG9rZW4gJiYgY3VycmVudENvbmZpZy5henVyZVJlZ2lvbikge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBBdHRlbXB0aW5nIGxlZ2FjeSBBenVyZSBmYWxsYmFjayBmb3IgJHtzb3VyY2V9YCk7XG4gICAgICAgICAgY29uc3QgbGVnYWN5UmVjb2duaXplciA9IGF3YWl0IGNyZWF0ZUxlZ2FjeUF6dXJlUmVjb2duaXplcihtZWRpYVN0cmVhbSwgc291cmNlKTtcbiAgICAgICAgICBzaG93U25hY2tiYXIoYFVzaW5nIGxlZ2FjeSBBenVyZSBTcGVlY2ggU2VydmljZXNgLCAnd2FybmluZycpO1xuICAgICAgICAgIHJldHVybiBsZWdhY3lSZWNvZ25pemVyO1xuICAgICAgICB9IGNhdGNoIChsZWdhY3lFcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYExlZ2FjeSBBenVyZSBmYWxsYmFjayBhbHNvIGZhaWxlZCBmb3IgJHtzb3VyY2V9OmAsIGxlZ2FjeUVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBQcm92aWRlIGhlbHBmdWwgZXJyb3IgbWVzc2FnZXNcbiAgICAgIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdBUEkga2V5JykgfHwgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnY3JlZGVudGlhbHMnKSkge1xuICAgICAgICBzaG93U25hY2tiYXIoYCR7ZXJyb3IubWVzc2FnZX1gLCAnZXJyb3InKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnRGVlcGdyYW0nKSkge1xuICAgICAgICBzaG93U25hY2tiYXIoYCR7ZXJyb3IubWVzc2FnZX1gLCAnZXJyb3InKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNob3dTbmFja2JhcihgRmFpbGVkIHRvIHN0YXJ0ICR7c291cmNlfSByZWNvZ25pdGlvbjogJHtlcnJvci5tZXNzYWdlfWAsICdlcnJvcicpO1xuICAgICAgfVxuXG4gICAgICBtZWRpYVN0cmVhbS5nZXRUcmFja3MoKS5mb3JFYWNoKHRyYWNrID0+IHRyYWNrLnN0b3AoKSk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH07XG5cbiAgY29uc3Qgc3RhcnRTeXN0ZW1BdWRpb1JlY29nbml0aW9uID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmIChpc1N5c3RlbUF1ZGlvQWN0aXZlKSB7XG4gICAgICBhd2FpdCBzdG9wUmVjb3JkaW5nKCdzeXN0ZW0nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoIW5hdmlnYXRvci5tZWRpYURldmljZXMgfHwgIW5hdmlnYXRvci5tZWRpYURldmljZXMuZ2V0RGlzcGxheU1lZGlhKSB7XG4gICAgICBzaG93U25hY2tiYXIoJ1NjcmVlbiBzaGFyaW5nIGlzIG5vdCBzdXBwb3J0ZWQgYnkgeW91ciBicm93c2VyLicsICdlcnJvcicpO1xuICAgICAgc2V0SXNTeXN0ZW1BdWRpb0FjdGl2ZShmYWxzZSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IG1lZGlhU3RyZWFtID0gYXdhaXQgbmF2aWdhdG9yLm1lZGlhRGV2aWNlcy5nZXREaXNwbGF5TWVkaWEoe1xuICAgICAgICBhdWRpbzogdHJ1ZSxcbiAgICAgICAgdmlkZW86IHtcbiAgICAgICAgICBkaXNwbGF5U3VyZmFjZTogJ2Jyb3dzZXInLFxuICAgICAgICAgIGxvZ2ljYWxTdXJmYWNlOiB0cnVlXG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBhdWRpb1RyYWNrcyA9IG1lZGlhU3RyZWFtLmdldEF1ZGlvVHJhY2tzKCk7XG4gICAgICBpZiAoYXVkaW9UcmFja3MubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHNob3dTbmFja2JhcignTm8gYXVkaW8gdHJhY2sgZGV0ZWN0ZWQuIFBsZWFzZSBlbnN1cmUgeW91IHNoYXJlIGEgdGFiIHdpdGggYXVkaW8uJywgJ3dhcm5pbmcnKTtcbiAgICAgICAgbWVkaWFTdHJlYW0uZ2V0VHJhY2tzKCkuZm9yRWFjaCh0cmFjayA9PiB0cmFjay5zdG9wKCkpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGlmIChzeXN0ZW1SZWNvZ25pemVyKSB7XG4gICAgICAgIGF3YWl0IHN0b3BSZWNvcmRpbmcoJ3N5c3RlbScpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBzcGVlY2hTZXJ2aWNlSW5zdGFuY2UgPSBhd2FpdCBjcmVhdGVTcGVlY2hSZWNvZ25pemVyKG1lZGlhU3RyZWFtLCAnc3lzdGVtJyk7XG4gICAgICBpZiAoc3BlZWNoU2VydmljZUluc3RhbmNlKSB7XG4gICAgICAgIC8vIENoZWNrIGlmIGl0J3MgYSBuZXcgc3BlZWNoIHNlcnZpY2Ugb3IgbGVnYWN5IHJlY29nbml6ZXJcbiAgICAgICAgaWYgKHNwZWVjaFNlcnZpY2VJbnN0YW5jZS5zdGFydCAmJiBzcGVlY2hTZXJ2aWNlSW5zdGFuY2Uuc3RvcCkge1xuICAgICAgICAgIC8vIE5ldyBzcGVlY2ggc2VydmljZVxuICAgICAgICAgIHNldFN5c3RlbVNwZWVjaFNlcnZpY2Uoc3BlZWNoU2VydmljZUluc3RhbmNlKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBMZWdhY3kgQXp1cmUgcmVjb2duaXplclxuICAgICAgICAgIHNldFN5c3RlbVJlY29nbml6ZXIoc3BlZWNoU2VydmljZUluc3RhbmNlKTtcbiAgICAgICAgfVxuICAgICAgICBzZXRJc1N5c3RlbUF1ZGlvQWN0aXZlKHRydWUpO1xuICAgICAgICBtZWRpYVN0cmVhbS5nZXRUcmFja3MoKS5mb3JFYWNoKHRyYWNrID0+IHtcbiAgICAgICAgICB0cmFjay5vbmVuZGVkID0gKCkgPT4ge1xuICAgICAgICAgICAgc2hvd1NuYWNrYmFyKCdUYWIgc2hhcmluZyBlbmRlZC4nLCAnaW5mbycpO1xuICAgICAgICAgICAgc3RvcFJlY29yZGluZygnc3lzdGVtJyk7XG4gICAgICAgICAgfTtcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBtZWRpYVN0cmVhbS5nZXRUcmFja3MoKS5mb3JFYWNoKHRyYWNrID0+IHRyYWNrLnN0b3AoKSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1N5c3RlbSBhdWRpbyBjYXB0dXJlIGVycm9yOicsIGVycm9yKTtcbiAgICAgIGlmIChlcnJvci5uYW1lID09PSBcIk5vdEFsbG93ZWRFcnJvclwiKSB7XG4gICAgICAgIHNob3dTbmFja2JhcignUGVybWlzc2lvbiBkZW5pZWQgZm9yIHNjcmVlbiByZWNvcmRpbmcuIFBsZWFzZSBhbGxvdyBhY2Nlc3MuJywgJ2Vycm9yJyk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLm5hbWUgPT09IFwiTm90Rm91bmRFcnJvclwiKSB7XG4gICAgICAgIHNob3dTbmFja2JhcignTm8gc3VpdGFibGUgdGFiL3dpbmRvdyBmb3VuZCB0byBzaGFyZS4nLCAnZXJyb3InKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IubmFtZSA9PT0gXCJOb3RTdXBwb3J0ZWRFcnJvclwiKSB7XG4gICAgICAgIHNob3dTbmFja2JhcignU3lzdGVtIGF1ZGlvIGNhcHR1cmUgbm90IHN1cHBvcnRlZCBieSB5b3VyIGJyb3dzZXIuJywgJ2Vycm9yJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzaG93U25hY2tiYXIoYEZhaWxlZCB0byBzdGFydCBzeXN0ZW0gYXVkaW8gY2FwdHVyZTogJHtlcnJvci5tZXNzYWdlIHx8ICdVbmtub3duIGVycm9yJ31gLCAnZXJyb3InKTtcbiAgICAgIH1cbiAgICAgIHNldElzU3lzdGVtQXVkaW9BY3RpdmUoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBzdGFydE1pY3JvcGhvbmVSZWNvZ25pdGlvbiA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoaXNNaWNyb3Bob25lQWN0aXZlKSB7XG4gICAgICBhd2FpdCBzdG9wUmVjb3JkaW5nKCdtaWNyb3Bob25lJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICBjb25zdCBtZWRpYVN0cmVhbSA9IGF3YWl0IG5hdmlnYXRvci5tZWRpYURldmljZXMuZ2V0VXNlck1lZGlhKHsgYXVkaW86IHRydWUgfSk7XG4gICAgICBpZiAobWljU3BlZWNoU2VydmljZSkgYXdhaXQgc3RvcFJlY29yZGluZygnbWljcm9waG9uZScpO1xuXG4gICAgICBjb25zdCBzcGVlY2hTZXJ2aWNlSW5zdGFuY2UgPSBhd2FpdCBjcmVhdGVTcGVlY2hSZWNvZ25pemVyKG1lZGlhU3RyZWFtLCAnbWljcm9waG9uZScpO1xuICAgICAgaWYgKHNwZWVjaFNlcnZpY2VJbnN0YW5jZSkge1xuICAgICAgICAvLyBDaGVjayBpZiBpdCdzIGEgbmV3IHNwZWVjaCBzZXJ2aWNlIG9yIGxlZ2FjeSByZWNvZ25pemVyXG4gICAgICAgIGlmIChzcGVlY2hTZXJ2aWNlSW5zdGFuY2Uuc3RhcnQgJiYgc3BlZWNoU2VydmljZUluc3RhbmNlLnN0b3ApIHtcbiAgICAgICAgICAvLyBOZXcgc3BlZWNoIHNlcnZpY2VcbiAgICAgICAgICBzZXRNaWNTcGVlY2hTZXJ2aWNlKHNwZWVjaFNlcnZpY2VJbnN0YW5jZSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gTGVnYWN5IEF6dXJlIHJlY29nbml6ZXJcbiAgICAgICAgICBzZXRNaWNSZWNvZ25pemVyKHNwZWVjaFNlcnZpY2VJbnN0YW5jZSk7XG4gICAgICAgIH1cbiAgICAgICAgc2V0SXNNaWNyb3Bob25lQWN0aXZlKHRydWUpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbWVkaWFTdHJlYW0uZ2V0VHJhY2tzKCkuZm9yRWFjaCh0cmFjayA9PiB0cmFjay5zdG9wKCkpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdNaWNyb3Bob25lIGNhcHR1cmUgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgaWYgKGVycm9yLm5hbWUgPT09IFwiTm90QWxsb3dlZEVycm9yXCIgfHwgZXJyb3IubmFtZSA9PT0gXCJOb3RGb3VuZEVycm9yXCIpIHtcbiAgICAgICAgc2hvd1NuYWNrYmFyKCdQZXJtaXNzaW9uIGRlbmllZCBmb3IgbWljcm9waG9uZS4gUGxlYXNlIGFsbG93IGFjY2Vzcy4nLCAnZXJyb3InKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNob3dTbmFja2JhcihgRmFpbGVkIHRvIGFjY2VzcyBtaWNyb3Bob25lOiAke2Vycm9yLm1lc3NhZ2UgfHwgJ1Vua25vd24gZXJyb3InfWAsICdlcnJvcicpO1xuICAgICAgfVxuICAgICAgc2V0SXNNaWNyb3Bob25lQWN0aXZlKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGRldGVybWluZSByZWFzb25pbmcgZWZmb3J0IGZvciBmdXR1cmUgYWR2YW5jZWQgbW9kZWxzXG4gIGNvbnN0IGdldFJlYXNvbmluZ0VmZm9ydCA9ICh0ZXh0LCBzb3VyY2UpID0+IHtcbiAgICBjb25zdCB0ZXh0TGVuZ3RoID0gdGV4dC5sZW5ndGg7XG4gICAgY29uc3QgaXNDb21wbGV4UXVlc3Rpb24gPSB0ZXh0LmluY2x1ZGVzKCc/JykgJiYgdGV4dExlbmd0aCA+IDEwMDtcbiAgICBjb25zdCBpc0NvbWJpbmVkID0gc291cmNlID09PSAnY29tYmluZWQnO1xuXG4gICAgaWYgKGlzQ29tYmluZWQgfHwgaXNDb21wbGV4UXVlc3Rpb24gfHwgdGV4dExlbmd0aCA+IDUwMCkgcmV0dXJuICdoaWdoJztcbiAgICBpZiAodGV4dExlbmd0aCA+IDIwMCkgcmV0dXJuICdtZWRpdW0nO1xuICAgIHJldHVybiAnbG93JztcbiAgfTtcblxuICBjb25zdCBhc2tPcGVuQUkgPSBhc3luYyAodGV4dCwgc291cmNlKSA9PiB7XG4gICAgaWYgKCF0ZXh0LnRyaW0oKSkge1xuICAgICAgc2hvd1NuYWNrYmFyKCdObyBpbnB1dCB0ZXh0IHRvIHByb2Nlc3MuJywgJ3dhcm5pbmcnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgaWYgKCFvcGVuQUkgfHwgaXNBSUxvYWRpbmcpIHtcbiAgICAgIHNob3dTbmFja2JhcignQUkgY2xpZW50IGlzIG5vdCByZWFkeS4gUGxlYXNlIHdhaXQgb3IgY2hlY2sgc2V0dGluZ3MuJywgJ3dhcm5pbmcnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCBjdXJyZW50Q29uZmlnID0gZ2V0Q29uZmlnKCk7XG4gICAgY29uc3QgbGVuZ3RoU2V0dGluZ3MgPSB7XG4gICAgICBjb25jaXNlOiB7IHRlbXBlcmF0dXJlOiAwLjQsIG1heFRva2VuczogMjUwIH0sXG4gICAgICBtZWRpdW06IHsgdGVtcGVyYXR1cmU6IDAuNiwgbWF4VG9rZW5zOiA1MDAgfSxcbiAgICAgIGxlbmd0aHk6IHsgdGVtcGVyYXR1cmU6IDAuOCwgbWF4VG9rZW5zOiAxMDAwIH1cbiAgICB9O1xuICAgIGNvbnN0IHsgdGVtcGVyYXR1cmUsIG1heFRva2VucyB9ID0gbGVuZ3RoU2V0dGluZ3NbY3VycmVudENvbmZpZy5yZXNwb25zZUxlbmd0aCB8fCAnbWVkaXVtJ107XG5cbiAgICBzZXRJc1Byb2Nlc3NpbmcodHJ1ZSk7XG4gICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoW10sIHsgaG91cjogJzItZGlnaXQnLCBtaW51dGU6ICcyLWRpZ2l0JyB9KTtcbiAgICBsZXQgc3RyZWFtZWRSZXNwb25zZSA9ICcnO1xuXG4gICAgZGlzcGF0Y2goYWRkVG9IaXN0b3J5KHsgdHlwZTogJ3F1ZXN0aW9uJywgdGV4dCwgdGltZXN0YW1wLCBzb3VyY2UsIHN0YXR1czogJ3BlbmRpbmcnIH0pKTtcbiAgICBkaXNwYXRjaChzZXRBSVJlc3BvbnNlKCcnKSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgY29udmVyc2F0aW9uSGlzdG9yeUZvckFQSSA9IGhpc3RvcnlcbiAgICAgICAgLmZpbHRlcihlID0+IGUudGV4dCAmJiAoZS50eXBlID09PSAncXVlc3Rpb24nIHx8IGUudHlwZSA9PT0gJ3Jlc3BvbnNlJykgJiYgZS5zdGF0dXMgIT09ICdwZW5kaW5nJylcbiAgICAgICAgLnNsaWNlKC02KVxuICAgICAgICAubWFwKGV2ZW50ID0+ICh7XG4gICAgICAgICAgcm9sZTogZXZlbnQudHlwZSA9PT0gJ3F1ZXN0aW9uJyA/ICd1c2VyJyA6ICdhc3Npc3RhbnQnLFxuICAgICAgICAgIGNvbnRlbnQ6IGV2ZW50LnRleHQsXG4gICAgICAgIH0pKTtcblxuICAgICAgaWYgKGN1cnJlbnRDb25maWcuYWlNb2RlbC5zdGFydHNXaXRoKCdnZW1pbmknKSkge1xuICAgICAgICAvLyBFbmhhbmNlZCBHZW1pbmkgQVBJIGNvbmZpZ3VyYXRpb24gZm9yIDIuNSBtb2RlbHNcbiAgICAgICAgY29uc3QgbW9kZWxDb25maWcgPSB7XG4gICAgICAgICAgbW9kZWw6IGN1cnJlbnRDb25maWcuYWlNb2RlbCxcbiAgICAgICAgICBnZW5lcmF0aW9uQ29uZmlnOiB7XG4gICAgICAgICAgICB0ZW1wZXJhdHVyZSxcbiAgICAgICAgICAgIG1heE91dHB1dFRva2VuczogbWF4VG9rZW5zXG4gICAgICAgICAgfSxcbiAgICAgICAgICBzeXN0ZW1JbnN0cnVjdGlvbjogeyBwYXJ0czogW3sgdGV4dDogY3VycmVudENvbmZpZy5ncHRTeXN0ZW1Qcm9tcHQgfV0gfVxuICAgICAgICB9O1xuXG4gICAgICAgIC8vIEFkZCB0aGlua2luZyBjb25maWd1cmF0aW9uIGZvciBHZW1pbmkgMi41IG1vZGVsc1xuICAgICAgICBpZiAoY3VycmVudENvbmZpZy5haU1vZGVsLmluY2x1ZGVzKCcyLjUnKSAmJiBjdXJyZW50Q29uZmlnLnRoaW5raW5nQnVkZ2V0ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICBpZiAoY3VycmVudENvbmZpZy50aGlua2luZ0J1ZGdldCA9PT0gMCkge1xuICAgICAgICAgICAgLy8gRGlzYWJsZSB0aGlua2luZ1xuICAgICAgICAgICAgbW9kZWxDb25maWcuZ2VuZXJhdGlvbkNvbmZpZy50aGlua2luZ0NvbmZpZyA9IHsgdGhpbmtpbmdCdWRnZXQ6IDAgfTtcbiAgICAgICAgICB9IGVsc2UgaWYgKGN1cnJlbnRDb25maWcudGhpbmtpbmdCdWRnZXQgPiAwKSB7XG4gICAgICAgICAgICAvLyBDdXN0b20gdGhpbmtpbmcgYnVkZ2V0XG4gICAgICAgICAgICBtb2RlbENvbmZpZy5nZW5lcmF0aW9uQ29uZmlnLnRoaW5raW5nQ29uZmlnID0geyB0aGlua2luZ0J1ZGdldDogY3VycmVudENvbmZpZy50aGlua2luZ0J1ZGdldCB9O1xuICAgICAgICAgIH1cbiAgICAgICAgICAvLyBJZiB0aGlua2luZ0J1ZGdldCBpcyBudWxsLCB1c2UgZGVmYXVsdCAodGhpbmtpbmcgZW5hYmxlZClcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IG1vZGVsID0gb3BlbkFJLmdldEdlbmVyYXRpdmVNb2RlbChtb2RlbENvbmZpZyk7XG4gICAgICAgIGNvbnN0IGNoYXQgPSBtb2RlbC5zdGFydENoYXQoe1xuICAgICAgICAgIGhpc3Rvcnk6IGNvbnZlcnNhdGlvbkhpc3RvcnlGb3JBUEkubWFwKG1zZyA9PiAoe1xuICAgICAgICAgICAgcm9sZTogbXNnLnJvbGUgPT09ICd1c2VyJyA/ICd1c2VyJyA6ICdtb2RlbCcsXG4gICAgICAgICAgICBwYXJ0czogW3sgdGV4dDogbXNnLmNvbnRlbnQgfV1cbiAgICAgICAgICB9KSksXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjaGF0LnNlbmRNZXNzYWdlU3RyZWFtKHRleHQpO1xuICAgICAgICBmb3IgYXdhaXQgKGNvbnN0IGNodW5rIG9mIHJlc3VsdC5zdHJlYW0pIHtcbiAgICAgICAgICBpZiAoY2h1bmsgJiYgdHlwZW9mIGNodW5rLnRleHQgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgIGNvbnN0IGNodW5rVGV4dCA9IGNodW5rLnRleHQoKTtcbiAgICAgICAgICAgIHN0cmVhbWVkUmVzcG9uc2UgKz0gY2h1bmtUZXh0O1xuICAgICAgICAgICAgaWYgKHRocm90dGxlZERpc3BhdGNoU2V0QUlSZXNwb25zZVJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAgIHRocm90dGxlZERpc3BhdGNoU2V0QUlSZXNwb25zZVJlZi5jdXJyZW50KHN0cmVhbWVkUmVzcG9uc2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gRW5oYW5jZWQgT3BlbkFJIEFQSSB1c2FnZSB3aXRoIGZ1dHVyZS1yZWFkeSBwYXJhbWV0ZXJzXG4gICAgICAgIGNvbnN0IG1lc3NhZ2VzID0gW1xuICAgICAgICAgIHsgcm9sZTogJ3N5c3RlbScsIGNvbnRlbnQ6IGN1cnJlbnRDb25maWcuZ3B0U3lzdGVtUHJvbXB0IH0sXG4gICAgICAgICAgLi4uY29udmVyc2F0aW9uSGlzdG9yeUZvckFQSSxcbiAgICAgICAgICB7IHJvbGU6ICd1c2VyJywgY29udGVudDogdGV4dCB9XG4gICAgICAgIF07XG5cbiAgICAgICAgY29uc3QgcmVxdWVzdFBhcmFtcyA9IHtcbiAgICAgICAgICBtb2RlbDogY3VycmVudENvbmZpZy5haU1vZGVsLFxuICAgICAgICAgIG1lc3NhZ2VzLFxuICAgICAgICAgIHN0cmVhbTogdHJ1ZSxcbiAgICAgICAgfTtcblxuICAgICAgICAvLyBTZXQgdGVtcGVyYXR1cmUgYmFzZWQgb24gbW9kZWwgY2FwYWJpbGl0aWVzXG4gICAgICAgIGlmIChjdXJyZW50Q29uZmlnLmFpTW9kZWwuc3RhcnRzV2l0aCgnbzEnKSkge1xuICAgICAgICAgIC8vIG8xIG1vZGVscyBkb24ndCBzdXBwb3J0IHRlbXBlcmF0dXJlIHBhcmFtZXRlciBhdCBhbGxcbiAgICAgICAgICAvLyBEb24ndCBzZXQgdGVtcGVyYXR1cmUgZm9yIG8xIG1vZGVsc1xuICAgICAgICB9IGVsc2UgaWYgKGN1cnJlbnRDb25maWcuYWlNb2RlbC5zdGFydHNXaXRoKCdncHQtNScpKSB7XG4gICAgICAgICAgLy8gR1BULTUgbW9kZWxzIG1heSBoYXZlIHRlbXBlcmF0dXJlIHJlc3RyaWN0aW9ucywgdXNlIGRlZmF1bHQgdmFsdWVcbiAgICAgICAgICByZXF1ZXN0UGFyYW1zLnRlbXBlcmF0dXJlID0gMTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBTdGFuZGFyZCBtb2RlbHMgc3VwcG9ydCBjb25maWd1cmFibGUgdGVtcGVyYXR1cmVcbiAgICAgICAgICByZXF1ZXN0UGFyYW1zLnRlbXBlcmF0dXJlID0gdGVtcGVyYXR1cmU7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBVc2UgdGhlIGNvcnJlY3QgdG9rZW4gcGFyYW1ldGVyIGJhc2VkIG9uIG1vZGVsXG4gICAgICAgIGlmIChjdXJyZW50Q29uZmlnLmFpTW9kZWwuc3RhcnRzV2l0aCgnZ3B0LTUnKSB8fCBjdXJyZW50Q29uZmlnLmFpTW9kZWwuc3RhcnRzV2l0aCgnbzEnKSkge1xuICAgICAgICAgIHJlcXVlc3RQYXJhbXMubWF4X2NvbXBsZXRpb25fdG9rZW5zID0gbWF4VG9rZW5zO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJlcXVlc3RQYXJhbXMubWF4X3Rva2VucyA9IG1heFRva2VucztcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEFkZCBtb2RlbC1zcGVjaWZpYyBwYXJhbWV0ZXJzIGZvciBkaWZmZXJlbnQgbW9kZWwgdHlwZXNcbiAgICAgICAgaWYgKGN1cnJlbnRDb25maWcuYWlNb2RlbC5zdGFydHNXaXRoKCdncHQtNScpKSB7XG4gICAgICAgICAgLy8gR1BULTUgbW9kZWxzIHN1cHBvcnQgbmV3IHBhcmFtZXRlcnNcbiAgICAgICAgICBpZiAoY3VycmVudENvbmZpZy5yZWFzb25pbmdFZmZvcnQpIHtcbiAgICAgICAgICAgIHJlcXVlc3RQYXJhbXMucmVhc29uaW5nX2VmZm9ydCA9IGN1cnJlbnRDb25maWcucmVhc29uaW5nRWZmb3J0O1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAoY3VycmVudENvbmZpZy52ZXJib3NpdHkgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgcmVxdWVzdFBhcmFtcy52ZXJib3NpdHkgPSBjdXJyZW50Q29uZmlnLnZlcmJvc2l0eSA9PT0gMCA/ICdsb3cnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50Q29uZmlnLnZlcmJvc2l0eSA9PT0gMSA/ICdtZWRpdW0nIDogJ2hpZ2gnO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmIChjdXJyZW50Q29uZmlnLmFpTW9kZWwuc3RhcnRzV2l0aCgnbzEnKSkge1xuICAgICAgICAgIC8vIG8xIG1vZGVscyB1c2UgZGlmZmVyZW50IHBhcmFtZXRlcnMgYW5kIGRvbid0IHN1cHBvcnQgc3RyZWFtaW5nXG4gICAgICAgICAgLy8gUmVtb3ZlIHN0cmVhbWluZyBmb3IgbzEgbW9kZWxzXG4gICAgICAgICAgcmVxdWVzdFBhcmFtcy5zdHJlYW0gPSBmYWxzZTtcblxuICAgICAgICAgIC8vIG8xIG1vZGVscyBkb24ndCB1c2Ugc3lzdGVtIG1lc3NhZ2VzIGluIHRoZSBzYW1lIHdheVxuICAgICAgICAgIC8vIE1vdmUgc3lzdGVtIHByb21wdCB0byB0aGUgZmlyc3QgdXNlciBtZXNzYWdlXG4gICAgICAgICAgcmVxdWVzdFBhcmFtcy5tZXNzYWdlcyA9IFtcbiAgICAgICAgICAgIHsgcm9sZTogJ3VzZXInLCBjb250ZW50OiBgJHtjdXJyZW50Q29uZmlnLmdwdFN5c3RlbVByb21wdH1cXG5cXG4ke3RleHR9YCB9LFxuICAgICAgICAgICAgLi4uY29udmVyc2F0aW9uSGlzdG9yeUZvckFQSS5zbGljZSgxKSAvLyBTa2lwIHRoZSBzeXN0ZW0gbWVzc2FnZVxuICAgICAgICAgIF07XG5cbiAgICAgICAgICAvLyBvMSBtb2RlbHMgcmVxdWlyZSB0ZW1wZXJhdHVyZSA9IDEgKGFscmVhZHkgc2V0IGFib3ZlKVxuICAgICAgICAgIC8vIE5vIGFkZGl0aW9uYWwgdGVtcGVyYXR1cmUgbW9kaWZpY2F0aW9uIG5lZWRlZFxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGN1cnJlbnRDb25maWcuYWlNb2RlbC5zdGFydHNXaXRoKCdvMScpKSB7XG4gICAgICAgICAgLy8gbzEgbW9kZWxzIGRvbid0IHN1cHBvcnQgc3RyZWFtaW5nLCBoYW5kbGUgYXMgc2luZ2xlIHJlc3BvbnNlXG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBvcGVuQUkuY2hhdC5jb21wbGV0aW9ucy5jcmVhdGUocmVxdWVzdFBhcmFtcyk7XG4gICAgICAgICAgc3RyZWFtZWRSZXNwb25zZSA9IHJlc3BvbnNlLmNob2ljZXNbMF0/Lm1lc3NhZ2U/LmNvbnRlbnQgfHwgJyc7XG4gICAgICAgICAgaWYgKHRocm90dGxlZERpc3BhdGNoU2V0QUlSZXNwb25zZVJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICB0aHJvdHRsZWREaXNwYXRjaFNldEFJUmVzcG9uc2VSZWYuY3VycmVudChzdHJlYW1lZFJlc3BvbnNlKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gU3RhbmRhcmQgc3RyZWFtaW5nIGZvciBHUFQtNSwgR1BULTRvLCBhbmQgb3RoZXIgbW9kZWxzXG4gICAgICAgICAgY29uc3Qgc3RyZWFtID0gYXdhaXQgb3BlbkFJLmNoYXQuY29tcGxldGlvbnMuY3JlYXRlKHJlcXVlc3RQYXJhbXMpO1xuXG4gICAgICAgICAgZm9yIGF3YWl0IChjb25zdCBjaHVuayBvZiBzdHJlYW0pIHtcbiAgICAgICAgICAgIGNvbnN0IGNodW5rVGV4dCA9IGNodW5rLmNob2ljZXNbMF0/LmRlbHRhPy5jb250ZW50IHx8ICcnO1xuICAgICAgICAgICAgc3RyZWFtZWRSZXNwb25zZSArPSBjaHVua1RleHQ7XG4gICAgICAgICAgICBpZiAodGhyb3R0bGVkRGlzcGF0Y2hTZXRBSVJlc3BvbnNlUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgICAgdGhyb3R0bGVkRGlzcGF0Y2hTZXRBSVJlc3BvbnNlUmVmLmN1cnJlbnQoc3RyZWFtZWRSZXNwb25zZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgICBpZiAodGhyb3R0bGVkRGlzcGF0Y2hTZXRBSVJlc3BvbnNlUmVmLmN1cnJlbnQgJiYgdHlwZW9mIHRocm90dGxlZERpc3BhdGNoU2V0QUlSZXNwb25zZVJlZi5jdXJyZW50LmNhbmNlbCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICB0aHJvdHRsZWREaXNwYXRjaFNldEFJUmVzcG9uc2VSZWYuY3VycmVudC5jYW5jZWwoKTtcbiAgICAgIH1cbiAgICAgIGRpc3BhdGNoKHNldEFJUmVzcG9uc2Uoc3RyZWFtZWRSZXNwb25zZSkpO1xuXG4gICAgICBjb25zdCBmaW5hbFRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9Mb2NhbGVUaW1lU3RyaW5nKFtdLCB7IGhvdXI6ICcyLWRpZ2l0JywgbWludXRlOiAnMi1kaWdpdCcgfSk7XG4gICAgICBkaXNwYXRjaChhZGRUb0hpc3RvcnkoeyB0eXBlOiAncmVzcG9uc2UnLCB0ZXh0OiBzdHJlYW1lZFJlc3BvbnNlLCB0aW1lc3RhbXA6IGZpbmFsVGltZXN0YW1wLCBzdGF0dXM6ICdjb21wbGV0ZWQnIH0pKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiQUkgcmVxdWVzdCBlcnJvcjpcIiwgZXJyb3IpO1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gYEFJIHJlcXVlc3QgZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2UgfHwgJ1Vua25vd24gZXJyb3InfWA7XG4gICAgICBzaG93U25hY2tiYXIoZXJyb3JNZXNzYWdlLCAnZXJyb3InKTtcbiAgICAgIGRpc3BhdGNoKHNldEFJUmVzcG9uc2UoYEVycm9yOiAke2Vycm9yTWVzc2FnZX1gKSk7XG4gICAgICBkaXNwYXRjaChhZGRUb0hpc3RvcnkoeyB0eXBlOiAncmVzcG9uc2UnLCB0ZXh0OiBgRXJyb3I6ICR7ZXJyb3JNZXNzYWdlfWAsIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0xvY2FsZVRpbWVTdHJpbmcoW10sIHsgaG91cjogJzItZGlnaXQnLCBtaW51dGU6ICcyLWRpZ2l0JyB9KSwgc3RhdHVzOiAnZXJyb3InIH0pKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgaWYgKChzb3VyY2UgPT09ICdzeXN0ZW0nICYmIHN5c3RlbUF1dG9Nb2RlUmVmLmN1cnJlbnQpIHx8IChzb3VyY2UgPT09ICdtaWNyb3Bob25lJyAmJiAhaXNNYW51YWxNb2RlUmVmLmN1cnJlbnQpKSB7XG4gICAgICAgIGZpbmFsVHJhbnNjcmlwdC5jdXJyZW50W3NvdXJjZV0gPSAnJztcbiAgICAgICAgaWYgKHNvdXJjZSA9PT0gJ3N5c3RlbScpIHtcbiAgICAgICAgICBzeXN0ZW1JbnRlcmltVHJhbnNjcmlwdGlvbi5jdXJyZW50ID0gJyc7XG4gICAgICAgICAgZGlzcGF0Y2goc2V0VHJhbnNjcmlwdGlvbignJykpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIG1pY0ludGVyaW1UcmFuc2NyaXB0aW9uLmN1cnJlbnQgPSAnJztcbiAgICAgICAgICBzZXRNaWNUcmFuc2NyaXB0aW9uKCcnKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgc2V0SXNQcm9jZXNzaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZm9ybWF0QW5kRGlzcGxheVJlc3BvbnNlID0gdXNlQ2FsbGJhY2soKHJlc3BvbnNlKSA9PiB7XG4gICAgaWYgKCFyZXNwb25zZSkgcmV0dXJuIG51bGw7XG4gICAgcmV0dXJuIChcbiAgICAgIDxSZWFjdE1hcmtkb3duXG4gICAgICAgIGNvbXBvbmVudHM9e3tcbiAgICAgICAgICBjb2RlKHsgbm9kZSwgaW5saW5lLCBjbGFzc05hbWUsIGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgICBjb25zdCBtYXRjaCA9IC9sYW5ndWFnZS0oXFx3KykvLmV4ZWMoY2xhc3NOYW1lIHx8ICcnKTtcbiAgICAgICAgICAgIHJldHVybiAhaW5saW5lICYmIG1hdGNoID8gKFxuICAgICAgICAgICAgICA8Qm94IHN4PXt7XG4gICAgICAgICAgICAgICAgbXk6IDEsXG4gICAgICAgICAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXG4gICAgICAgICAgICAgICAgJyYgcHJlJzoge1xuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNHB4JyxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcxMnB4ICFpbXBvcnRhbnQnLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICBvdmVyZmxvd1g6ICdhdXRvJyxcbiAgICAgICAgICAgICAgICAgIHdoaXRlU3BhY2U6ICdwcmUtd3JhcCcsXG4gICAgICAgICAgICAgICAgICB3b3JkQnJlYWs6ICdicmVhay1hbGwnLFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgPHByZT48Y29kZSBjbGFzc05hbWU9e2NsYXNzTmFtZX0gey4uLnByb3BzfSBkYW5nZXJvdXNseVNldElubmVySFRNTD17eyBfX2h0bWw6IGhsanMuaGlnaGxpZ2h0KFN0cmluZyhjaGlsZHJlbikucmVwbGFjZSgvXFxuJC8sICcnKSwgeyBsYW5ndWFnZTogbWF0Y2hbMV0sIGlnbm9yZUlsbGVnYWxzOiB0cnVlIH0pLnZhbHVlIH19IC8+PC9wcmU+XG4gICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGNvZGVcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NsYXNzTmFtZX1cbiAgICAgICAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMCwwLDAsMC4wNSknLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzJweCA0cHgnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNHB4JyxcbiAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6ICdtb25vc3BhY2UnLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICB3b3JkQnJlYWs6ICdicmVhay1hbGwnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9jb2RlPlxuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIHA6ICh7IG5vZGUsIC4uLnByb3BzIH0pID0+IDxUeXBvZ3JhcGh5IHBhcmFncmFwaCB7Li4ucHJvcHN9IHN4PXt7IG1iOiAxLCBmb250U2l6ZTogJzAuOTVyZW0nLCB3b3JkQnJlYWs6ICdicmVhay13b3JkJyB9fSAvPixcbiAgICAgICAgICBzdHJvbmc6ICh7IG5vZGUsIC4uLnByb3BzIH0pID0+IDxUeXBvZ3JhcGh5IGNvbXBvbmVudD1cInN0cm9uZ1wiIGZvbnRXZWlnaHQ9XCJib2xkXCIgey4uLnByb3BzfSAvPixcbiAgICAgICAgICBlbTogKHsgbm9kZSwgLi4ucHJvcHMgfSkgPT4gPFR5cG9ncmFwaHkgY29tcG9uZW50PVwiZW1cIiBmb250U3R5bGU9XCJpdGFsaWNcIiB7Li4ucHJvcHN9IC8+LFxuICAgICAgICAgIHVsOiAoeyBub2RlLCAuLi5wcm9wcyB9KSA9PiA8VHlwb2dyYXBoeSBjb21wb25lbnQ9XCJ1bFwiIHN4PXt7IHBsOiAyLjUsIG1iOiAxLCBmb250U2l6ZTogJzAuOTVyZW0nLCB3b3JkQnJlYWs6ICdicmVhay13b3JkJyB9fSB7Li4ucHJvcHN9IC8+LFxuICAgICAgICAgIG9sOiAoeyBub2RlLCAuLi5wcm9wcyB9KSA9PiA8VHlwb2dyYXBoeSBjb21wb25lbnQ9XCJvbFwiIHN4PXt7IHBsOiAyLjUsIG1iOiAxLCBmb250U2l6ZTogJzAuOTVyZW0nLCB3b3JkQnJlYWs6ICdicmVhay13b3JkJyB9fSB7Li4ucHJvcHN9IC8+LFxuICAgICAgICAgIGxpOiAoeyBub2RlLCAuLi5wcm9wcyB9KSA9PiA8VHlwb2dyYXBoeSBjb21wb25lbnQ9XCJsaVwiIHN4PXt7IG1iOiAwLjI1LCBmb250U2l6ZTogJzAuOTVyZW0nLCB3b3JkQnJlYWs6ICdicmVhay13b3JkJyB9fSB7Li4ucHJvcHN9IC8+LFxuICAgICAgICB9fVxuICAgICAgPlxuICAgICAgICB7cmVzcG9uc2V9XG4gICAgICA8L1JlYWN0TWFya2Rvd24+XG4gICAgKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IHJlbmRlckhpc3RvcnlJdGVtID0gKGl0ZW0sIGluZGV4KSA9PiB7XG4gICAgaWYgKGl0ZW0udHlwZSAhPT0gJ3Jlc3BvbnNlJykgcmV0dXJuIG51bGw7XG4gICAgY29uc3QgSWNvbiA9IFNtYXJ0VG95SWNvbjtcbiAgICBjb25zdCB0aXRsZSA9ICdBSSBBc3Npc3RhbnQnO1xuICAgIGNvbnN0IGF2YXRhckJnQ29sb3IgPSB0aGVtZS5wYWxldHRlLnNlY29uZGFyeS5saWdodDtcblxuICAgIHJldHVybiAoXG4gICAgICA8TGlzdEl0ZW0ga2V5PXtgcmVzcG9uc2UtJHtpbmRleH1gfSBzeD17eyBhbGlnbkl0ZW1zOiAnZmxleC1zdGFydCcsIHB4OiAwLCBweTogMS41IH19PlxuICAgICAgICA8QXZhdGFyIHN4PXt7IGJnY29sb3I6IGF2YXRhckJnQ29sb3IsIG1yOiAyLCBtdDogMC41IH19PlxuICAgICAgICAgIDxJY29uIHN4PXt7IGNvbG9yOiB0aGVtZS5wYWxldHRlLmdldENvbnRyYXN0VGV4dChhdmF0YXJCZ0NvbG9yKSB9fSAvPlxuICAgICAgICA8L0F2YXRhcj5cbiAgICAgICAgPFBhcGVyIHZhcmlhbnQ9XCJvdXRsaW5lZFwiIHN4PXt7IHA6IDEuNSwgZmxleEdyb3c6IDEsIGJnY29sb3I6IHRoZW1lLnBhbGV0dGUuYmFja2dyb3VuZC5kZWZhdWx0LCBib3JkZXJDb2xvcjogdGhlbWUucGFsZXR0ZS5kaXZpZGVyLCBvdmVyZmxvd1g6ICdhdXRvJyB9fT5cbiAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgYWxpZ25JdGVtczogJ2NlbnRlcicsIG1iOiAwLjUgfX0+XG4gICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwic3VidGl0bGUyXCIgZm9udFdlaWdodD1cImJvbGRcIj57dGl0bGV9PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImNhcHRpb25cIiBjb2xvcj1cInRleHQuc2Vjb25kYXJ5XCI+e2l0ZW0udGltZXN0YW1wfTwvVHlwb2dyYXBoeT5cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgICB7Zm9ybWF0QW5kRGlzcGxheVJlc3BvbnNlKGl0ZW0udGV4dCl9XG4gICAgICAgIDwvUGFwZXI+XG4gICAgICA8L0xpc3RJdGVtPlxuICAgICk7XG4gIH07XG5cbiAgY29uc3QgcmVuZGVyUXVlc3Rpb25IaXN0b3J5SXRlbSA9IChpdGVtLCBpbmRleCkgPT4ge1xuICAgIGNvbnN0IEljb24gPSBpdGVtLnNvdXJjZSA9PT0gJ3N5c3RlbScgPyBIZWFyaW5nSWNvbiA6IFBlcnNvbkljb247XG4gICAgY29uc3QgdGl0bGUgPSBpdGVtLnNvdXJjZSA9PT0gJ3N5c3RlbScgPyAnSW50ZXJ2aWV3ZXInIDogJ0NhbmRpZGF0ZSc7XG4gICAgY29uc3QgYXZhdGFyQmdDb2xvciA9IGl0ZW0uc291cmNlID09PSAnc3lzdGVtJyA/IHRoZW1lLnBhbGV0dGUuaW5mby5saWdodCA6IHRoZW1lLnBhbGV0dGUuc3VjY2Vzcy5saWdodDtcblxuICAgIHJldHVybiAoXG4gICAgICA8TGlzdEl0ZW1cbiAgICAgICAga2V5PXtgcXVlc3Rpb24taGlzdC0ke2luZGV4fWB9XG4gICAgICAgIHNlY29uZGFyeUFjdGlvbj17XG4gICAgICAgICAgPENoZWNrYm94XG4gICAgICAgICAgICBlZGdlPVwiZW5kXCJcbiAgICAgICAgICAgIGNoZWNrZWQ9e3NlbGVjdGVkUXVlc3Rpb25zLmluY2x1ZGVzKGluZGV4KX1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoKSA9PiB7XG4gICAgICAgICAgICAgIHNldFNlbGVjdGVkUXVlc3Rpb25zKHByZXYgPT5cbiAgICAgICAgICAgICAgICBwcmV2LmluY2x1ZGVzKGluZGV4KSA/IHByZXYuZmlsdGVyKHggPT4geCAhPT0gaW5kZXgpIDogWy4uLnByZXYsIGluZGV4XVxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIGNvbG9yPVwic2Vjb25kYXJ5XCJcbiAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXG4gICAgICAgICAgLz5cbiAgICAgICAgfVxuICAgICAgICBkaXNhYmxlUGFkZGluZ1xuICAgICAgICBzeD17eyBweTogMC41LCBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInIH19XG4gICAgICA+XG4gICAgICAgIDxBdmF0YXIgc3g9e3sgYmdjb2xvcjogYXZhdGFyQmdDb2xvciwgbXI6IDEuNSwgd2lkdGg6IDMyLCBoZWlnaHQ6IDMyLCBmb250U2l6ZTogJzFyZW0nIH19PlxuICAgICAgICAgIDxJY29uIGZvbnRTaXplPVwic21hbGxcIiAvPlxuICAgICAgICA8L0F2YXRhcj5cbiAgICAgICAgPExpc3RJdGVtVGV4dFxuICAgICAgICAgIHByaW1hcnk9e1xuICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgbm9XcmFwIHN4PXt7IGZvbnRXZWlnaHQ6IHNlbGVjdGVkUXVlc3Rpb25zLmluY2x1ZGVzKGluZGV4KSA/ICdib2xkJyA6ICdub3JtYWwnLCBkaXNwbGF5OiAnLXdlYmtpdC1ib3gnLCBXZWJraXRMaW5lQ2xhbXA6IDIsIFdlYmtpdEJveE9yaWVudDogJ3ZlcnRpY2FsJywgb3ZlcmZsb3c6ICdoaWRkZW4nLCB0ZXh0T3ZlcmZsb3c6ICdlbGxpcHNpcycgfX0+XG4gICAgICAgICAgICAgIHtpdGVtLnRleHR9XG4gICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgfVxuICAgICAgICAgIHNlY29uZGFyeT17YCR7dGl0bGV9IC0gJHtpdGVtLnRpbWVzdGFtcH1gfVxuICAgICAgICAvPlxuICAgICAgPC9MaXN0SXRlbT5cbiAgICApO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNvcnRPcmRlclRvZ2dsZSA9ICgpID0+IHtcbiAgICBzZXRBaVJlc3BvbnNlU29ydE9yZGVyKHByZXYgPT4gcHJldiA9PT0gJ25ld2VzdEF0Qm90dG9tJyA/ICduZXdlc3RBdFRvcCcgOiAnbmV3ZXN0QXRCb3R0b20nKTtcbiAgfTtcblxuICBjb25zdCBnZXRBaVJlc3BvbnNlc1RvRGlzcGxheSA9ICgpID0+IHtcbiAgICBsZXQgcmVzcG9uc2VzID0gaGlzdG9yeS5maWx0ZXIoaXRlbSA9PiBpdGVtLnR5cGUgPT09ICdyZXNwb25zZScpLnNsaWNlKCk7XG4gICAgY29uc3QgY3VycmVudFN0cmVhbWluZ1RleHQgPSBhaVJlc3BvbnNlRnJvbVN0b3JlO1xuXG4gICAgaWYgKGlzUHJvY2Vzc2luZyAmJiBjdXJyZW50U3RyZWFtaW5nVGV4dCAmJiBjdXJyZW50U3RyZWFtaW5nVGV4dC50cmltKCkgIT09ICcnKSB7XG4gICAgICByZXNwb25zZXMucHVzaCh7IHRleHQ6IGN1cnJlbnRTdHJlYW1pbmdUZXh0LCB0aW1lc3RhbXA6ICdTdHJlYW1pbmcuLi4nLCB0eXBlOiAnY3VycmVudF9zdHJlYW1pbmcnIH0pO1xuICAgIH1cblxuICAgIGlmIChhaVJlc3BvbnNlU29ydE9yZGVyID09PSAnbmV3ZXN0QXRUb3AnKSB7XG4gICAgICByZXR1cm4gcmVzcG9uc2VzLnJldmVyc2UoKTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3BvbnNlcztcbiAgfTtcblxuICBjb25zdCB0b2dnbGVQaXBXaW5kb3cgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKGlzUGlwV2luZG93QWN0aXZlKSB7XG4gICAgICBpZiAoZG9jdW1lbnRQaXBXaW5kb3dSZWYuY3VycmVudCAmJiB0eXBlb2YgZG9jdW1lbnRQaXBXaW5kb3dSZWYuY3VycmVudC5jbG9zZSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGF3YWl0IGRvY3VtZW50UGlwV2luZG93UmVmLmN1cnJlbnQuY2xvc2UoKTtcbiAgICAgICAgfSBjYXRjaCAoZSkgeyBjb25zb2xlLmVycm9yKFwiRXJyb3IgY2xvc2luZyBkb2N1bWVudCBQaVAgd2luZG93OlwiLCBlKTsgfVxuICAgICAgfSBlbHNlIGlmIChwaXBXaW5kb3dSZWYuY3VycmVudCAmJiAhcGlwV2luZG93UmVmLmN1cnJlbnQuY2xvc2VkKSB7XG4gICAgICAgIHBpcFdpbmRvd1JlZi5jdXJyZW50LmNsb3NlKCk7XG4gICAgICB9XG4gICAgICByZXR1cm47IC8vIFN0YXRlIHVwZGF0ZSB3aWxsIGJlIGhhbmRsZWQgYnkgcGFnZWhpZGUvaW50ZXJ2YWwgbGlzdGVuZXJzXG4gICAgfVxuXG4gICAgY29uc3QgYWRkUmVzaXplTGlzdGVuZXIgPSAocGlwV2luZG93KSA9PiB7XG4gICAgICBjb25zdCBoYW5kbGVQaXBSZXNpemUgPSBkZWJvdW5jZSgoKSA9PiB7XG4gICAgICAgIGlmICghcGlwV2luZG93IHx8IChwaXBXaW5kb3cuY2xvc2VkKSkgcmV0dXJuO1xuICAgICAgICBjb25zdCB0YXJnZXQgPSBkb2N1bWVudFBpcElmcmFtZVJlZi5jdXJyZW50ID8gZG9jdW1lbnRQaXBJZnJhbWVSZWYuY3VycmVudC5jb250ZW50V2luZG93IDogcGlwV2luZG93O1xuICAgICAgICBpZiAodGFyZ2V0KSB7XG4gICAgICAgICAgdGFyZ2V0LnBvc3RNZXNzYWdlKHtcbiAgICAgICAgICAgIHR5cGU6ICdQSVBfUkVTSVpFJyxcbiAgICAgICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgICAgd2lkdGg6IHBpcFdpbmRvdy5pbm5lcldpZHRoLFxuICAgICAgICAgICAgICBoZWlnaHQ6IHBpcFdpbmRvdy5pbm5lckhlaWdodFxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sICcqJyk7XG4gICAgICAgIH1cbiAgICAgIH0sIDUwKTtcblxuICAgICAgcGlwV2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGhhbmRsZVBpcFJlc2l6ZSk7XG4gICAgICByZXR1cm4gKCkgPT4gcGlwV2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGhhbmRsZVBpcFJlc2l6ZSk7IC8vIFJldHVybiBhIGNsZWFudXAgZnVuY3Rpb25cbiAgICB9O1xuXG4gICAgaWYgKHdpbmRvdy5kb2N1bWVudFBpY3R1cmVJblBpY3R1cmUgJiYgdHlwZW9mIHdpbmRvdy5kb2N1bWVudFBpY3R1cmVJblBpY3R1cmUucmVxdWVzdFdpbmRvdyA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcGlwT3B0aW9ucyA9IHsgd2lkdGg6IDQwMCwgaGVpZ2h0OiAzMDAgfTtcbiAgICAgICAgY29uc3QgcmVxdWVzdGVkUGlwV2luZG93ID0gYXdhaXQgd2luZG93LmRvY3VtZW50UGljdHVyZUluUGljdHVyZS5yZXF1ZXN0V2luZG93KHBpcE9wdGlvbnMpO1xuICAgICAgICBkb2N1bWVudFBpcFdpbmRvd1JlZi5jdXJyZW50ID0gcmVxdWVzdGVkUGlwV2luZG93O1xuICAgICAgICBzZXRJc1BpcFdpbmRvd0FjdGl2ZSh0cnVlKTtcblxuICAgICAgICBjb25zdCBpZnJhbWUgPSBkb2N1bWVudFBpcFdpbmRvd1JlZi5jdXJyZW50LmRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2lmcmFtZScpO1xuICAgICAgICBpZnJhbWUuc3JjID0gJy9waXAtbG9nJztcbiAgICAgICAgaWZyYW1lLnN0eWxlLndpZHRoID0gJzEwMCUnO1xuICAgICAgICBpZnJhbWUuc3R5bGUuaGVpZ2h0ID0gJzEwMCUnO1xuICAgICAgICBpZnJhbWUuc3R5bGUuYm9yZGVyID0gJ25vbmUnO1xuICAgICAgICBkb2N1bWVudFBpcFdpbmRvd1JlZi5jdXJyZW50LmRvY3VtZW50LmJvZHkuc3R5bGUubWFyZ2luID0gJzAnO1xuICAgICAgICBkb2N1bWVudFBpcFdpbmRvd1JlZi5jdXJyZW50LmRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAnaGlkZGVuJztcbiAgICAgICAgZG9jdW1lbnRQaXBXaW5kb3dSZWYuY3VycmVudC5kb2N1bWVudC5ib2R5LmFwcGVuZChpZnJhbWUpO1xuICAgICAgICBkb2N1bWVudFBpcElmcmFtZVJlZi5jdXJyZW50ID0gaWZyYW1lO1xuXG4gICAgICAgIGNvbnN0IHJlbW92ZVJlc2l6ZUxpc3RlbmVyID0gYWRkUmVzaXplTGlzdGVuZXIoZG9jdW1lbnRQaXBXaW5kb3dSZWYuY3VycmVudCk7XG5cbiAgICAgICAgaWZyYW1lLm9ubG9hZCA9ICgpID0+IHtcbiAgICAgICAgICBpZiAoZG9jdW1lbnRQaXBJZnJhbWVSZWYuY3VycmVudCAmJiBkb2N1bWVudFBpcElmcmFtZVJlZi5jdXJyZW50LmNvbnRlbnRXaW5kb3cpIHtcbiAgICAgICAgICAgIGRvY3VtZW50UGlwSWZyYW1lUmVmLmN1cnJlbnQuY29udGVudFdpbmRvdy5wb3N0TWVzc2FnZSh7XG4gICAgICAgICAgICAgIHR5cGU6ICdBSV9MT0dfREFUQScsXG4gICAgICAgICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgICAgICBoaXN0b3JpY2FsUmVzcG9uc2VzOiBoaXN0b3J5LmZpbHRlcihpdGVtID0+IGl0ZW0udHlwZSA9PT0gJ3Jlc3BvbnNlJyksXG4gICAgICAgICAgICAgICAgY3VycmVudFN0cmVhbWluZ1RleHQ6IGlzUHJvY2Vzc2luZyA/IGFpUmVzcG9uc2VGcm9tU3RvcmUgOiAnJyxcbiAgICAgICAgICAgICAgICBpc1Byb2Nlc3Npbmc6IGlzUHJvY2Vzc2luZyxcbiAgICAgICAgICAgICAgICBzb3J0T3JkZXI6IGFpUmVzcG9uc2VTb3J0T3JkZXJcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSwgJyonKTtcbiAgICAgICAgICB9XG4gICAgICAgIH07XG5cbiAgICAgICAgZG9jdW1lbnRQaXBXaW5kb3dSZWYuY3VycmVudC5hZGRFdmVudExpc3RlbmVyKCdwYWdlaGlkZScsICgpID0+IHtcbiAgICAgICAgICByZW1vdmVSZXNpemVMaXN0ZW5lcigpO1xuICAgICAgICAgIHNldElzUGlwV2luZG93QWN0aXZlKGZhbHNlKTtcbiAgICAgICAgICBkb2N1bWVudFBpcFdpbmRvd1JlZi5jdXJyZW50ID0gbnVsbDtcbiAgICAgICAgICBkb2N1bWVudFBpcElmcmFtZVJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgc2hvd1NuYWNrYmFyKCdOYXRpdmUgUGlQIHdpbmRvdyBvcGVuZWQuJywgJ3N1Y2Nlc3MnKTtcbiAgICAgICAgcmV0dXJuO1xuXG4gICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRG9jdW1lbnQgUGljdHVyZS1pbi1QaWN0dXJlIEFQSSBlcnJvcjonLCBlcnIpO1xuICAgICAgICBzaG93U25hY2tiYXIoYE5hdGl2ZSBQaVAgbm90IGF2YWlsYWJsZSBvciBmYWlsZWQuIFRyeWluZyBwb3B1cC4gKCR7ZXJyLm1lc3NhZ2V9KWAsICd3YXJuaW5nJyk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcGlwV2luZG93UmVmLmN1cnJlbnQgPSB3aW5kb3cub3BlbignL3BpcC1sb2cnLCAnQUlSZXNwb25zZVBpUCcsICd3aWR0aD00MDAsaGVpZ2h0PTU1MCxyZXNpemFibGU9eWVzLHNjcm9sbGJhcnM9eWVzLHN0YXR1cz1ubyx0b29sYmFyPW5vLG1lbnViYXI9bm8sbG9jYXRpb249bm8sbm9vcGVuZXIsbm9yZWZlcnJlcixwb3B1cD15ZXMnKTtcblxuICAgIGlmIChwaXBXaW5kb3dSZWYuY3VycmVudCkge1xuICAgICAgc2V0SXNQaXBXaW5kb3dBY3RpdmUodHJ1ZSk7XG4gICAgICBjb25zdCByZW1vdmVSZXNpemVMaXN0ZW5lciA9IGFkZFJlc2l6ZUxpc3RlbmVyKHBpcFdpbmRvd1JlZi5jdXJyZW50KTtcblxuICAgICAgcGlwV2luZG93UmVmLmN1cnJlbnQub25sb2FkID0gKCkgPT4ge1xuICAgICAgICBpZiAocGlwV2luZG93UmVmLmN1cnJlbnQgJiYgIXBpcFdpbmRvd1JlZi5jdXJyZW50LmNsb3NlZCkge1xuICAgICAgICAgIHBpcFdpbmRvd1JlZi5jdXJyZW50LnBvc3RNZXNzYWdlKHtcbiAgICAgICAgICAgIHR5cGU6ICdBSV9MT0dfREFUQScsXG4gICAgICAgICAgICBwYXlsb2FkOiB7XG4gICAgICAgICAgICAgIGhpc3RvcmljYWxSZXNwb25zZXM6IGhpc3RvcnkuZmlsdGVyKGl0ZW0gPT4gaXRlbS50eXBlID09PSAncmVzcG9uc2UnKSxcbiAgICAgICAgICAgICAgY3VycmVudFN0cmVhbWluZ1RleHQ6IGlzUHJvY2Vzc2luZyA/IGFpUmVzcG9uc2VGcm9tU3RvcmUgOiAnJyxcbiAgICAgICAgICAgICAgaXNQcm9jZXNzaW5nOiBpc1Byb2Nlc3NpbmcsXG4gICAgICAgICAgICAgIHNvcnRPcmRlcjogYWlSZXNwb25zZVNvcnRPcmRlclxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sICcqJyk7XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgICBjb25zdCBwaXBDaGVja0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgICBpZiAocGlwV2luZG93UmVmLmN1cnJlbnQgJiYgcGlwV2luZG93UmVmLmN1cnJlbnQuY2xvc2VkKSB7XG4gICAgICAgICAgY2xlYXJJbnRlcnZhbChwaXBDaGVja0ludGVydmFsKTtcbiAgICAgICAgICByZW1vdmVSZXNpemVMaXN0ZW5lcigpO1xuICAgICAgICAgIHNldElzUGlwV2luZG93QWN0aXZlKGZhbHNlKTtcbiAgICAgICAgICBwaXBXaW5kb3dSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICAgIH1cbiAgICAgIH0sIDUwMCk7XG4gICAgICBpZiAocGlwV2luZG93UmVmLmN1cnJlbnQpIHBpcFdpbmRvd1JlZi5jdXJyZW50Ll9waXBJbnRlcnZhbElkID0gcGlwQ2hlY2tJbnRlcnZhbDtcbiAgICB9IGVsc2Uge1xuICAgICAgc2hvd1NuYWNrYmFyKCdGYWlsZWQgdG8gb3BlbiBQaVAgd2luZG93LiBQbGVhc2UgY2hlY2sgcG9wdXAgYmxvY2tlciBzZXR0aW5ncy4nLCAnZXJyb3InKTtcbiAgICAgIHNldElzUGlwV2luZG93QWN0aXZlKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaWYgKHBpcFdpbmRvd1JlZi5jdXJyZW50ICYmIHBpcFdpbmRvd1JlZi5jdXJyZW50Ll9waXBJbnRlcnZhbElkKSB7XG4gICAgICAgIGNsZWFySW50ZXJ2YWwocGlwV2luZG93UmVmLmN1cnJlbnQuX3BpcEludGVydmFsSWQpO1xuICAgICAgfVxuICAgICAgaWYgKGRvY3VtZW50UGlwV2luZG93UmVmLmN1cnJlbnQgJiYgdHlwZW9mIGRvY3VtZW50UGlwV2luZG93UmVmLmN1cnJlbnQuY2xvc2UgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgdHJ5IHsgZG9jdW1lbnRQaXBXaW5kb3dSZWYuY3VycmVudC5jbG9zZSgpOyB9IGNhdGNoIChlKSB7IC8qaWdub3JlKi8gfVxuICAgICAgfVxuICAgIH07XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxldCB0YXJnZXRXaW5kb3dGb3JNZXNzYWdlID0gbnVsbDtcblxuICAgIGlmIChkb2N1bWVudFBpcFdpbmRvd1JlZi5jdXJyZW50ICYmIGRvY3VtZW50UGlwSWZyYW1lUmVmLmN1cnJlbnQgJiYgZG9jdW1lbnRQaXBJZnJhbWVSZWYuY3VycmVudC5jb250ZW50V2luZG93KSB7XG4gICAgICB0YXJnZXRXaW5kb3dGb3JNZXNzYWdlID0gZG9jdW1lbnRQaXBJZnJhbWVSZWYuY3VycmVudC5jb250ZW50V2luZG93O1xuICAgIH0gZWxzZSBpZiAocGlwV2luZG93UmVmLmN1cnJlbnQgJiYgIXBpcFdpbmRvd1JlZi5jdXJyZW50LmNsb3NlZCkge1xuICAgICAgdGFyZ2V0V2luZG93Rm9yTWVzc2FnZSA9IHBpcFdpbmRvd1JlZi5jdXJyZW50O1xuICAgIH1cblxuICAgIGlmIChpc1BpcFdpbmRvd0FjdGl2ZSAmJiB0YXJnZXRXaW5kb3dGb3JNZXNzYWdlKSB7XG4gICAgICB0cnkge1xuICAgICAgICB0YXJnZXRXaW5kb3dGb3JNZXNzYWdlLnBvc3RNZXNzYWdlKHtcbiAgICAgICAgICB0eXBlOiAnQUlfTE9HX0RBVEEnLFxuICAgICAgICAgIHBheWxvYWQ6IHtcbiAgICAgICAgICAgIGhpc3RvcmljYWxSZXNwb25zZXM6IGhpc3RvcnkuZmlsdGVyKGl0ZW0gPT4gaXRlbS50eXBlID09PSAncmVzcG9uc2UnKSxcbiAgICAgICAgICAgIGN1cnJlbnRTdHJlYW1pbmdUZXh0OiBpc1Byb2Nlc3NpbmcgPyBhaVJlc3BvbnNlRnJvbVN0b3JlIDogJycsXG4gICAgICAgICAgICBpc1Byb2Nlc3Npbmc6IGlzUHJvY2Vzc2luZyxcbiAgICAgICAgICAgIHNvcnRPcmRlcjogYWlSZXNwb25zZVNvcnRPcmRlclxuICAgICAgICAgIH1cbiAgICAgICAgfSwgJyonKTtcbiAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgY29uc29sZS53YXJuKFwiQ291bGQgbm90IHBvc3QgbWVzc2FnZSB0byBQaVAgd2luZG93OlwiLCBlKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtoaXN0b3J5LCBhaVJlc3BvbnNlRnJvbVN0b3JlLCBpc1BpcFdpbmRvd0FjdGl2ZSwgYWlSZXNwb25zZVNvcnRPcmRlciwgaXNQcm9jZXNzaW5nXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPEhlYWQ+XG4gICAgICAgIDx0aXRsZT5JbnRlcnZpZXcgQ29waWxvdCAtIEFjdGl2ZSBTZXNzaW9uPC90aXRsZT5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJywgaGVpZ2h0OiAnMTAwdmgnIH19PlxuICAgICAgICA8QXBwQmFyIHBvc2l0aW9uPVwic3RhdGljXCIgY29sb3I9XCJkZWZhdWx0XCIgZWxldmF0aW9uPXsxfT5cbiAgICAgICAgICA8VG9vbGJhcj5cbiAgICAgICAgICAgIDxTbWFydFRveUljb24gc3g9e3sgbXI6IDIsIGNvbG9yOiAncHJpbWFyeS5tYWluJyB9fSAvPlxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCIgY29tcG9uZW50PVwiZGl2XCIgc3g9e3sgZmxleEdyb3c6IDEsIGNvbG9yOiAndGV4dC5wcmltYXJ5JyB9fT5cbiAgICAgICAgICAgICAgSW50ZXJ2aWV3IENvcGlsb3RcbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgIDxUb29sdGlwIHRpdGxlPVwiU2V0dGluZ3NcIj5cbiAgICAgICAgICAgICAgPEljb25CdXR0b24gY29sb3I9XCJwcmltYXJ5XCIgb25DbGljaz17KCkgPT4gc2V0U2V0dGluZ3NPcGVuKHRydWUpfSBhcmlhLWxhYmVsPVwic2V0dGluZ3NcIj5cbiAgICAgICAgICAgICAgICA8U2V0dGluZ3NJY29uIC8+XG4gICAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cbiAgICAgICAgICAgIDwvVG9vbHRpcD5cbiAgICAgICAgICA8L1Rvb2xiYXI+XG4gICAgICAgIDwvQXBwQmFyPlxuXG4gICAgICAgIDxDb250YWluZXIgbWF4V2lkdGg9XCJ4bFwiIHN4PXt7IGZsZXhHcm93OiAxLCBweTogMiwgZGlzcGxheTogJ2ZsZXgnLCBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyB9fT5cbiAgICAgICAgICA8R3JpZCBjb250YWluZXIgc3BhY2luZz17Mn0gc3g9e3sgZmxleEdyb3c6IDEgfX0+XG4gICAgICAgICAgICB7LyogTGVmdCBQYW5lbCAqL31cbiAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBtZD17M30gc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJywgZ2FwOiAyIH19PlxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlciB0aXRsZT1cIlN5c3RlbSBBdWRpbyAoSW50ZXJ2aWV3ZXIpXCIgYXZhdGFyPXs8SGVhcmluZ0ljb24gLz59IHN4PXt7IHBiOiAxIH19IC8+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgPEZvcm1Db250cm9sTGFiZWxcbiAgICAgICAgICAgICAgICAgICAgY29udHJvbD17PFN3aXRjaCBjaGVja2VkPXtzeXN0ZW1BdXRvTW9kZX0gb25DaGFuZ2U9e2UgPT4gc2V0U3lzdGVtQXV0b01vZGUoZS50YXJnZXQuY2hlY2tlZCl9IGNvbG9yPVwicHJpbWFyeVwiIC8+fVxuICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkF1dG8tU3VibWl0IFF1ZXN0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgc3g9e3sgbWI6IDEgfX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXG4gICAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxuICAgICAgICAgICAgICAgICAgICBtdWx0aWxpbmVcbiAgICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3RyYW5zY3JpcHRpb25Gcm9tU3RvcmV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlTWFudWFsSW5wdXRDaGFuZ2UoZS50YXJnZXQudmFsdWUsICdzeXN0ZW0nKX1cbiAgICAgICAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4gaGFuZGxlS2V5UHJlc3MoZSwgJ3N5c3RlbScpfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkludGVydmlld2VyJ3Mgc3BlZWNoLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgc3g9e3sgbWI6IDIgfX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAxLCBmbGV4V3JhcDogJ3dyYXAnIH19PlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17c3RhcnRTeXN0ZW1BdWRpb1JlY29nbml0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJjb250YWluZWRcIlxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPXtpc1N5c3RlbUF1ZGlvQWN0aXZlID8gJ2Vycm9yJyA6ICdwcmltYXJ5J31cbiAgICAgICAgICAgICAgICAgICAgICBzdGFydEljb249e2lzU3lzdGVtQXVkaW9BY3RpdmUgPyA8U3RvcFNjcmVlblNoYXJlSWNvbiAvPiA6IDxTY3JlZW5TaGFyZUljb24gLz59XG4gICAgICAgICAgICAgICAgICAgICAgc3g9e3sgZmxleEdyb3c6IDEgfX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtpc1N5c3RlbUF1ZGlvQWN0aXZlID8gJ1N0b3AgU3lzdGVtIEF1ZGlvJyA6ICdSZWNvcmQgU3lzdGVtIEF1ZGlvJ31cbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJjYXB0aW9uXCIgc3g9e3sgbXQ6IDEsIGRpc3BsYXk6ICdibG9jaycsIHdpZHRoOiAnMTAwJScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAge2lzU3lzdGVtQXVkaW9BY3RpdmUgPyAnUmVjb3JkaW5nIHN5c3RlbSBhdWRpby4uLicgOiAnU2VsZWN0IFwiQ2hyb21lIFRhYlwiIGFuZCBjaGVjayBcIlNoYXJlIGF1ZGlvXCIgd2hlbiBwcm9tcHRlZC4nfVxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIHRpdGxlPVwiQ2xlYXIgU3lzdGVtIFRyYW5zY3JpcHRpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVDbGVhclN5c3RlbVRyYW5zY3JpcHRpb259PjxEZWxldGVTd2VlcEljb24gLz48L0ljb25CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cbiAgICAgICAgICAgICAgICAgICAgeyFzeXN0ZW1BdXRvTW9kZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTWFudWFsU3VibWl0KCdzeXN0ZW0nKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj1cInByaW1hcnlcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhcnRJY29uPXs8U2VuZEljb24gLz59XG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNQcm9jZXNzaW5nIHx8ICF0cmFuc2NyaXB0aW9uRnJvbVN0b3JlLnRyaW0oKX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBTdWJtaXRcbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgPENhcmQgc3g9e3sgZmxleEdyb3c6IDEsIGRpc3BsYXk6ICdmbGV4JywgZmxleERpcmVjdGlvbjogJ2NvbHVtbicgfX0+XG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXJcbiAgICAgICAgICAgICAgICAgIHRpdGxlPVwiUXVlc3Rpb24gSGlzdG9yeVwiXG4gICAgICAgICAgICAgICAgICBhdmF0YXI9ezxQbGF5bGlzdEFkZENoZWNrSWNvbiAvPn1cbiAgICAgICAgICAgICAgICAgIGFjdGlvbj17XG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiY29udGFpbmVkXCJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNvbWJpbmVBbmRTdWJtaXR9XG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3NlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aCA9PT0gMCB8fCBpc1Byb2Nlc3Npbmd9XG4gICAgICAgICAgICAgICAgICAgICAgc3RhcnRJY29uPXtpc1Byb2Nlc3NpbmcgPyA8Q2lyY3VsYXJQcm9ncmVzcyBzaXplPXsxNn0gY29sb3I9XCJpbmhlcml0XCIgLz4gOiA8U2VuZEljb24gLz59XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICBBc2sgQ29tYmluZWRcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICBzeD17eyBwYjogMSwgYm9yZGVyQm90dG9tOiBgMXB4IHNvbGlkICR7dGhlbWUucGFsZXR0ZS5kaXZpZGVyfWAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBzeD17eyBmbGV4R3JvdzogMSwgb3ZlcmZsb3c6ICdoaWRkZW4nLCBwOiAwIH19PlxuICAgICAgICAgICAgICAgICAgPFNjcm9sbFRvQm90dG9tIGNsYXNzTmFtZT1cInNjcm9sbC10by1ib3R0b21cIiBmb2xsb3dCdXR0b25DbGFzc05hbWU9XCJoaWRkZW4tZm9sbG93LWJ1dHRvblwiPlxuICAgICAgICAgICAgICAgICAgICA8TGlzdCBkZW5zZSBzeD17eyBwdDogMCwgcHg6IDEgfX0+XG4gICAgICAgICAgICAgICAgICAgICAge2hpc3RvcnkuZmlsdGVyKGUgPT4gZS50eXBlID09PSAncXVlc3Rpb24nKS5zbGljZSgpLnJldmVyc2UoKS5tYXAocmVuZGVyUXVlc3Rpb25IaXN0b3J5SXRlbSl9XG4gICAgICAgICAgICAgICAgICAgIDwvTGlzdD5cbiAgICAgICAgICAgICAgICAgIDwvU2Nyb2xsVG9Cb3R0b20+XG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgPC9HcmlkPlxuXG4gICAgICAgICAgICB7LyogQ2VudGVyIFBhbmVsICovfVxuICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IG1kPXs2fSBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nIH19PlxuICAgICAgICAgICAgICA8Q2FyZCBzeD17eyBmbGV4R3JvdzogMSwgZGlzcGxheTogJ2ZsZXgnLCBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyB9fT5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlclxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJBSSBBc3Npc3RhbnQgTG9nXCJcbiAgICAgICAgICAgICAgICAgIGF2YXRhcj17PFNtYXJ0VG95SWNvbiAvPn1cbiAgICAgICAgICAgICAgICAgIGFjdGlvbj17XG4gICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgdGl0bGU9e2lzUGlwV2luZG93QWN0aXZlID8gXCJDbG9zZSBQaVAgTG9nXCIgOiBcIk9wZW4gUGlQIExvZ1wifT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJY29uQnV0dG9uIG9uQ2xpY2s9e3RvZ2dsZVBpcFdpbmRvd30gc2l6ZT1cInNtYWxsXCIgY29sb3I9e2lzUGlwV2luZG93QWN0aXZlID8gXCJzZWNvbmRhcnlcIiA6IFwiZGVmYXVsdFwifT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFBpY3R1cmVJblBpY3R1cmVBbHRJY29uIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxuICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwIHRpdGxlPXthaVJlc3BvbnNlU29ydE9yZGVyID09PSAnbmV3ZXN0QXRUb3AnID8gXCJTb3J0OiBOZXdlc3QgYXQgQm90dG9tXCIgOiBcIlNvcnQ6IE5ld2VzdCBvbiBUb3BcIn0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTb3J0T3JkZXJUb2dnbGV9IHNpemU9XCJzbWFsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7YWlSZXNwb25zZVNvcnRPcmRlciA9PT0gJ25ld2VzdEF0VG9wJyA/IDxBcnJvd0Rvd253YXJkSWNvbiAvPiA6IDxBcnJvd1Vwd2FyZEljb24gLz59XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJjYXB0aW9uXCIgc3g9e3sgbXI6IDEsIGZvbnRTdHlsZTogJ2l0YWxpYycgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7YWlSZXNwb25zZVNvcnRPcmRlciA9PT0gJ25ld2VzdEF0VG9wJyA/IFwiTmV3ZXN0IEZpcnN0XCIgOiBcIk9sZGVzdCBGaXJzdFwifVxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUNvbnRyb2xMYWJlbFxuICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbD17PFN3aXRjaCBjaGVja2VkPXthdXRvU2Nyb2xsfSBvbkNoYW5nZT17KGUpID0+IHNldEF1dG9TY3JvbGwoZS50YXJnZXQuY2hlY2tlZCl9IGNvbG9yPVwicHJpbWFyeVwiIC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJBdXRvIFNjcm9sbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzeD17eyBtbDogMSB9fVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgc3g9e3sgYm9yZGVyQm90dG9tOiBgMXB4IHNvbGlkICR7dGhlbWUucGFsZXR0ZS5kaXZpZGVyfWAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBzeD17eyBmbGV4R3JvdzogMSwgb3ZlcmZsb3c6ICdoaWRkZW4nLCBwOiAwIH19PlxuICAgICAgICAgICAgICAgICAgPFNjcm9sbFRvQm90dG9tXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNjcm9sbC10by1ib3R0b21cIlxuICAgICAgICAgICAgICAgICAgICBtb2RlPXthdXRvU2Nyb2xsID8gKGFpUmVzcG9uc2VTb3J0T3JkZXIgPT09ICduZXdlc3RBdFRvcCcgPyBcInRvcFwiIDogXCJib3R0b21cIikgOiB1bmRlZmluZWR9XG4gICAgICAgICAgICAgICAgICAgIGZvbGxvd0J1dHRvbkNsYXNzTmFtZT1cImhpZGRlbi1mb2xsb3ctYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPExpc3Qgc3g9e3sgcHg6IDIsIHB5OiAxIH19PlxuICAgICAgICAgICAgICAgICAgICAgIHtnZXRBaVJlc3BvbnNlc1RvRGlzcGxheSgpLm1hcChyZW5kZXJIaXN0b3J5SXRlbSl9XG4gICAgICAgICAgICAgICAgICAgICAge2lzUHJvY2Vzc2luZyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGlzdEl0ZW0gc3g9e3sganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLCBweTogMiB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENpcmN1bGFyUHJvZ3Jlc3Mgc2l6ZT17MjR9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJjYXB0aW9uXCIgc3g9e3sgbWw6IDEgfX0+QUkgaXMgdGhpbmtpbmcuLi48L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvTGlzdD5cbiAgICAgICAgICAgICAgICAgIDwvU2Nyb2xsVG9Cb3R0b20+XG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgPC9HcmlkPlxuXG4gICAgICAgICAgICB7LyogUmlnaHQgUGFuZWwgKi99XG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gbWQ9ezN9IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgZmxleERpcmVjdGlvbjogJ2NvbHVtbicgfX0+XG4gICAgICAgICAgICAgIDxDYXJkIHN4PXt7IGZsZXhHcm93OiAxLCBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nIH19PlxuICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIllvdXIgTWljIChDYW5kaWRhdGUpXCJcbiAgICAgICAgICAgICAgICAgIGF2YXRhcj17PFBlcnNvbkljb24gLz59XG4gICAgICAgICAgICAgICAgICBhY3Rpb249e1xuICAgICAgICAgICAgICAgICAgICBhY3RpdmVTcGVlY2hTZXJ2aWNlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8Q2hpcFxuICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9e2FjdGl2ZVNwZWVjaFNlcnZpY2UgPT09ICdkZWVwZ3JhbScgPyAnRGVlcGdyYW0nIDogJ0F6dXJlJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbWFsbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcj17YWN0aXZlU3BlZWNoU2VydmljZSA9PT0gJ2RlZXBncmFtJyA/ICdwcmltYXJ5JyA6ICdzZWNvbmRhcnknfVxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICBzeD17eyBwYjogMSB9fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50IHN4PXt7IGZsZXhHcm93OiAxLCBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nIH19PlxuICAgICAgICAgICAgICAgICAgPEZvcm1Db250cm9sTGFiZWxcbiAgICAgICAgICAgICAgICAgICAgY29udHJvbD17PFN3aXRjaCBjaGVja2VkPXtpc01hbnVhbE1vZGV9IG9uQ2hhbmdlPXtlID0+IHNldElzTWFudWFsTW9kZShlLnRhcmdldC5jaGVja2VkKX0gY29sb3I9XCJwcmltYXJ5XCIgLz59XG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiTWFudWFsIElucHV0IE1vZGVcIlxuICAgICAgICAgICAgICAgICAgICBzeD17eyBtYjogMSB9fVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxUZXh0RmllbGRcbiAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXG4gICAgICAgICAgICAgICAgICAgIG11bHRpbGluZVxuICAgICAgICAgICAgICAgICAgICByb3dzPXs4fVxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bWljVHJhbnNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVNYW51YWxJbnB1dENoYW5nZShlLnRhcmdldC52YWx1ZSwgJ21pY3JvcGhvbmUnKX1cbiAgICAgICAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4gaGFuZGxlS2V5UHJlc3MoZSwgJ21pY3JvcGhvbmUnKX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJZb3VyIHNwZWVjaCBvciBtYW51YWwgaW5wdXQuLi5cIlxuICAgICAgICAgICAgICAgICAgICBzeD17eyBtYjogMiwgZmxleEdyb3c6IDEgfX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAxLCBmbGV4V3JhcDogJ3dyYXAnLCBtdDogJ2F1dG8nIH19PlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17c3RhcnRNaWNyb3Bob25lUmVjb2duaXRpb259XG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImNvbnRhaW5lZFwiXG4gICAgICAgICAgICAgICAgICAgICAgY29sb3I9e2lzTWljcm9waG9uZUFjdGl2ZSA/ICdlcnJvcicgOiAncHJpbWFyeSd9XG4gICAgICAgICAgICAgICAgICAgICAgc3RhcnRJY29uPXtpc01pY3JvcGhvbmVBY3RpdmUgPyA8TWljT2ZmSWNvbiAvPiA6IDxNaWNJY29uIC8+fVxuICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IGZsZXhHcm93OiAxIH19XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7aXNNaWNyb3Bob25lQWN0aXZlID8gJ1N0b3AgTWljJyA6ICdTdGFydCBNaWMnfVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAgdGl0bGU9XCJDbGVhciBZb3VyIFRyYW5zY3JpcHRpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8SWNvbkJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVDbGVhck1pY1RyYW5zY3JpcHRpb259PjxEZWxldGVTd2VlcEljb24gLz48L0ljb25CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cbiAgICAgICAgICAgICAgICAgICAge2lzTWFudWFsTW9kZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTWFudWFsU3VibWl0KCdtaWNyb3Bob25lJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXJ0SWNvbj17PFNlbmRJY29uIC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzUHJvY2Vzc2luZyB8fCAhbWljVHJhbnNjcmlwdGlvbi50cmltKCl9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgU3VibWl0XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgPC9HcmlkPlxuICAgICAgICA8L0NvbnRhaW5lcj5cblxuICAgICAgICA8U2V0dGluZ3NEaWFsb2dcbiAgICAgICAgICBvcGVuPXtzZXR0aW5nc09wZW59XG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2V0dGluZ3NPcGVuKGZhbHNlKX1cbiAgICAgICAgICBvblNhdmU9e2hhbmRsZVNldHRpbmdzU2F2ZWR9XG4gICAgICAgIC8+XG4gICAgICAgIDxTbmFja2JhclxuICAgICAgICAgIG9wZW49e3NuYWNrYmFyT3Blbn1cbiAgICAgICAgICBhdXRvSGlkZUR1cmF0aW9uPXs0MDAwfVxuICAgICAgICAgIG9uQ2xvc2U9e2hhbmRsZVNuYWNrYmFyQ2xvc2V9XG4gICAgICAgICAgYW5jaG9yT3JpZ2luPXt7IHZlcnRpY2FsOiAnYm90dG9tJywgaG9yaXpvbnRhbDogJ2NlbnRlcicgfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxBbGVydCBvbkNsb3NlPXtoYW5kbGVTbmFja2JhckNsb3NlfSBzZXZlcml0eT17c25hY2tiYXJTZXZlcml0eX0gc3g9e3sgd2lkdGg6ICcxMDAlJywgYm94U2hhZG93OiB0aGVtZS5zaGFkb3dzWzZdIH19PlxuICAgICAgICAgICAge3NuYWNrYmFyTWVzc2FnZX1cbiAgICAgICAgICA8L0FsZXJ0PlxuICAgICAgICA8L1NuYWNrYmFyPlxuICAgICAgPC9Cb3g+XG4gICAgICA8c3R5bGUganN4IGdsb2JhbD57YFxuICAgICAgICAuc2Nyb2xsLXRvLWJvdHRvbSB7XG4gICAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87XG4gICAgICAgIH1cbiAgICAgICAgLmhpZGRlbi1mb2xsb3ctYnV0dG9uIHtcbiAgICAgICAgICBkaXNwbGF5OiBub25lO1xuICAgICAgICB9XG4gICAgICAgIC5zY3JvbGwtdG8tYm90dG9tOjotd2Via2l0LXNjcm9sbGJhciB7XG4gICAgICAgICAgd2lkdGg6IDhweDtcbiAgICAgICAgICBoZWlnaHQ6IDhweDtcbiAgICAgICAgfVxuICAgICAgICAuc2Nyb2xsLXRvLWJvdHRvbTo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xuICAgICAgICAgIGJhY2tncm91bmQ6ICR7dGhlbWUucGFsZXR0ZS5iYWNrZ3JvdW5kLnBhcGVyfTtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICAgICAgICB9XG4gICAgICAgIC5zY3JvbGwtdG8tYm90dG9tOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogJHt0aGVtZS5wYWxldHRlLmdyZXlbNDAwXX07XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgICAgICAgICBib3JkZXI6IDJweCBzb2xpZCAke3RoZW1lLnBhbGV0dGUuYmFja2dyb3VuZC5wYXBlcn07XG4gICAgICAgIH1cbiAgICAgICAgLnNjcm9sbC10by1ib3R0b206Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAke3RoZW1lLnBhbGV0dGUuZ3JleVs1MDBdfTtcbiAgICAgICAgfVxuICAgICAgICAuc2Nyb2xsLXRvLWJvdHRvbSB7XG4gICAgICAgICAgc2Nyb2xsYmFyLXdpZHRoOiB0aGluO1xuICAgICAgICAgIHNjcm9sbGJhci1jb2xvcjogJHt0aGVtZS5wYWxldHRlLmdyZXlbNDAwXX0gJHt0aGVtZS5wYWxldHRlLmJhY2tncm91bmQucGFwZXJ9O1xuICAgICAgICB9XG4gICAgICBgfTwvc3R5bGU+XG4gICAgPC8+XG4gICk7XG59Il0sIm5hbWVzIjpbInVzZUNhbGxiYWNrIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlU3RhdGUiLCJIZWFkIiwidXNlRGlzcGF0Y2giLCJ1c2VTZWxlY3RvciIsIkFsZXJ0IiwiQXBwQmFyIiwiQXZhdGFyIiwiQm94IiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNoZWNrYm94IiwiQ2hpcCIsIkNpcmN1bGFyUHJvZ3Jlc3MiLCJDb250YWluZXIiLCJGb3JtQ29udHJvbExhYmVsIiwiR3JpZCIsIkljb25CdXR0b24iLCJMaXN0IiwiTGlzdEl0ZW0iLCJMaXN0SXRlbVRleHQiLCJQYXBlciIsIlNuYWNrYmFyIiwiU3dpdGNoIiwiVGV4dEZpZWxkIiwiVG9vbGJhciIsIlRvb2x0aXAiLCJUeXBvZ3JhcGh5IiwidXNlVGhlbWUiLCJBcnJvd0Rvd253YXJkSWNvbiIsIkFycm93VXB3YXJkSWNvbiIsIkRlbGV0ZVN3ZWVwSWNvbiIsIkhlYXJpbmdJY29uIiwiTWljSWNvbiIsIk1pY09mZkljb24iLCJQZXJzb25JY29uIiwiUGljdHVyZUluUGljdHVyZUFsdEljb24iLCJQbGF5bGlzdEFkZENoZWNrSWNvbiIsIlNjcmVlblNoYXJlSWNvbiIsIlNlbmRJY29uIiwiU2V0dGluZ3NJY29uIiwiU21hcnRUb3lJY29uIiwiU3RvcFNjcmVlblNoYXJlSWNvbiIsIkdvb2dsZUdlbmVyYXRpdmVBSSIsImhsanMiLCJ0aHJvdHRsZSIsIk9wZW5BSSIsIlJlYWN0TWFya2Rvd24iLCJTY3JvbGxUb0JvdHRvbSIsIlNldHRpbmdzRGlhbG9nIiwic2V0QUlSZXNwb25zZSIsImFkZFRvSGlzdG9yeSIsImNsZWFyVHJhbnNjcmlwdGlvbiIsInNldFRyYW5zY3JpcHRpb24iLCJnZXRDb25maWciLCJjcmVhdGVTcGVlY2hTZXJ2aWNlIiwiZGVib3VuY2UiLCJmdW5jIiwidGltZW91dCIsInRpbWVyIiwiYXJncyIsImNsZWFyVGltZW91dCIsInNldFRpbWVvdXQiLCJhcHBseSIsIkludGVydmlld1BhZ2UiLCJkaXNwYXRjaCIsInRyYW5zY3JpcHRpb25Gcm9tU3RvcmUiLCJzdGF0ZSIsInRyYW5zY3JpcHRpb24iLCJhaVJlc3BvbnNlRnJvbVN0b3JlIiwiYWlSZXNwb25zZSIsImhpc3RvcnkiLCJ0aGVtZSIsImFwcENvbmZpZyIsInNldEFwcENvbmZpZyIsInN5c3RlbVJlY29nbml6ZXIiLCJzZXRTeXN0ZW1SZWNvZ25pemVyIiwibWljUmVjb2duaXplciIsInNldE1pY1JlY29nbml6ZXIiLCJzeXN0ZW1TcGVlY2hTZXJ2aWNlIiwic2V0U3lzdGVtU3BlZWNoU2VydmljZSIsIm1pY1NwZWVjaFNlcnZpY2UiLCJzZXRNaWNTcGVlY2hTZXJ2aWNlIiwiYWN0aXZlU3BlZWNoU2VydmljZSIsInNldEFjdGl2ZVNwZWVjaFNlcnZpY2UiLCJzeXN0ZW1BdXRvTW9kZSIsInNldFN5c3RlbUF1dG9Nb2RlIiwidW5kZWZpbmVkIiwib3BlbkFJIiwic2V0T3BlbkFJIiwic2V0dGluZ3NPcGVuIiwic2V0U2V0dGluZ3NPcGVuIiwiaXNNaWNyb3Bob25lQWN0aXZlIiwic2V0SXNNaWNyb3Bob25lQWN0aXZlIiwiaXNTeXN0ZW1BdWRpb0FjdGl2ZSIsInNldElzU3lzdGVtQXVkaW9BY3RpdmUiLCJzbmFja2Jhck9wZW4iLCJzZXRTbmFja2Jhck9wZW4iLCJzbmFja2Jhck1lc3NhZ2UiLCJzZXRTbmFja2Jhck1lc3NhZ2UiLCJzbmFja2JhclNldmVyaXR5Iiwic2V0U25hY2tiYXJTZXZlcml0eSIsInNlbGVjdGVkUXVlc3Rpb25zIiwic2V0U2VsZWN0ZWRRdWVzdGlvbnMiLCJpc01hbnVhbE1vZGUiLCJzZXRJc01hbnVhbE1vZGUiLCJtaWNUcmFuc2NyaXB0aW9uIiwic2V0TWljVHJhbnNjcmlwdGlvbiIsImlzUHJvY2Vzc2luZyIsInNldElzUHJvY2Vzc2luZyIsImlzQUlMb2FkaW5nIiwic2V0SXNBSUxvYWRpbmciLCJhdXRvU2Nyb2xsIiwic2V0QXV0b1Njcm9sbCIsImFpUmVzcG9uc2VTb3J0T3JkZXIiLCJzZXRBaVJlc3BvbnNlU29ydE9yZGVyIiwiaXNQaXBXaW5kb3dBY3RpdmUiLCJzZXRJc1BpcFdpbmRvd0FjdGl2ZSIsInBpcFdpbmRvd1JlZiIsImRvY3VtZW50UGlwV2luZG93UmVmIiwiZG9jdW1lbnRQaXBJZnJhbWVSZWYiLCJzeXN0ZW1JbnRlcmltVHJhbnNjcmlwdGlvbiIsIm1pY0ludGVyaW1UcmFuc2NyaXB0aW9uIiwic2lsZW5jZVRpbWVyIiwiZmluYWxUcmFuc2NyaXB0Iiwic3lzdGVtIiwibWljcm9waG9uZSIsImlzTWFudWFsTW9kZVJlZiIsInN5c3RlbUF1dG9Nb2RlUmVmIiwidGhyb3R0bGVkRGlzcGF0Y2hTZXRBSVJlc3BvbnNlUmVmIiwic2hvd1NuYWNrYmFyIiwibWVzc2FnZSIsInNldmVyaXR5IiwiaGFuZGxlU2V0dGluZ3NTYXZlZCIsIm5ld0NvbmZpZyIsImN1cnJlbnRDb25maWciLCJpbml0aWFsaXplQUkiLCJhaU1vZGVsIiwic3RhcnRzV2l0aCIsImdlbWluaUtleSIsImdlbkFJIiwib3BlbmFpS2V5Iiwib3BlbmFpQ2xpZW50IiwiYXBpS2V5IiwiZGFuZ2Vyb3VzbHlBbGxvd0Jyb3dzZXIiLCJlcnJvciIsImNvbnNvbGUiLCJjdXJyZW50IiwicGF5bG9hZCIsImxlYWRpbmciLCJ0cmFpbGluZyIsImNhbmNlbCIsImhhbmRsZVNuYWNrYmFyQ2xvc2UiLCJzdG9wUmVjb3JkaW5nIiwic291cmNlIiwic3BlZWNoU2VydmljZSIsInJlY29nbml6ZXIiLCJzdG9wIiwic3RvcENvbnRpbnVvdXNSZWNvZ25pdGlvbkFzeW5jIiwiYXVkaW9Db25maWciLCJwcml2U291cmNlIiwicHJpdlN0cmVhbSIsInN0cmVhbSIsIk1lZGlhU3RyZWFtIiwiZ2V0VHJhY2tzIiwiZm9yRWFjaCIsInRyYWNrIiwiY2xvc2UiLCJoYW5kbGVDbGVhclN5c3RlbVRyYW5zY3JpcHRpb24iLCJoYW5kbGVDbGVhck1pY1RyYW5zY3JpcHRpb24iLCJoYW5kbGVUcmFuc2NyaXB0aW9uRXZlbnQiLCJ0ZXh0IiwiY2xlYW5UZXh0IiwicmVwbGFjZSIsInRyaW0iLCJjdXJyZW50U2lsZW5jZVRpbWVyRHVyYXRpb24iLCJzaWxlbmNlVGltZXJEdXJhdGlvbiIsImFza09wZW5BSSIsImhhbmRsZU1hbnVhbElucHV0Q2hhbmdlIiwidmFsdWUiLCJoYW5kbGVNYW51YWxTdWJtaXQiLCJ0ZXh0VG9TdWJtaXQiLCJoYW5kbGVLZXlQcmVzcyIsImUiLCJrZXkiLCJzaGlmdEtleSIsInByZXZlbnREZWZhdWx0IiwiaGFuZGxlQ29tYmluZUFuZFN1Ym1pdCIsImxlbmd0aCIsInF1ZXN0aW9uSGlzdG9yeSIsImZpbHRlciIsInR5cGUiLCJzbGljZSIsInJldmVyc2UiLCJxdWVzdGlvblRleHRzIiwibWFwIiwic2VsZWN0ZWRJbmRleEluUmV2ZXJzZWRBcnJheSIsImNvbWJpbmVkVGV4dCIsImpvaW4iLCJjcmVhdGVMZWdhY3lBenVyZVJlY29nbml6ZXIiLCJtZWRpYVN0cmVhbSIsImF6dXJlVG9rZW4iLCJhenVyZVJlZ2lvbiIsIkVycm9yIiwiU3BlZWNoU0RLIiwiQXVkaW9Db25maWciLCJmcm9tU3RyZWFtSW5wdXQiLCJjb25maWdFcnJvciIsInNwZWVjaENvbmZpZyIsIlNwZWVjaENvbmZpZyIsImZyb21TdWJzY3JpcHRpb24iLCJzcGVlY2hSZWNvZ25pdGlvbkxhbmd1YWdlIiwiYXp1cmVMYW5ndWFnZSIsIlNwZWVjaFJlY29nbml6ZXIiLCJyZWNvZ25pemluZyIsInMiLCJyZXN1bHQiLCJyZWFzb24iLCJSZXN1bHRSZWFzb24iLCJSZWNvZ25pemluZ1NwZWVjaCIsImludGVyaW1UZXh0IiwicmVjb2duaXplZCIsIlJlY29nbml6ZWRTcGVlY2giLCJjYW5jZWxlZCIsImxvZyIsIkNhbmNlbGxhdGlvblJlYXNvbiIsImVycm9yQ29kZSIsImVycm9yRGV0YWlscyIsInNlc3Npb25TdG9wcGVkIiwic3RhcnRDb250aW51b3VzUmVjb2duaXRpb25Bc3luYyIsImNyZWF0ZVNwZWVjaFJlY29nbml6ZXIiLCJjYWxsYmFja3MiLCJvblN0YXJ0Iiwib25JbnRlcmltUmVzdWx0Iiwib25GaW5hbFJlc3VsdCIsIm9uRXJyb3IiLCJvblN0b3AiLCJzZWxlY3RlZFNlcnZpY2UiLCJjb25maWdUb1VzZSIsImRlZXBncmFtS2V5Iiwic3RhcnQiLCJzZXJ2aWNlTmFtZSIsImF6dXJlQ29uZmlnIiwiYXp1cmVTcGVlY2hTZXJ2aWNlIiwiYXp1cmVFcnJvciIsImxlZ2FjeVJlY29nbml6ZXIiLCJsZWdhY3lFcnJvciIsImluY2x1ZGVzIiwic3RhcnRTeXN0ZW1BdWRpb1JlY29nbml0aW9uIiwibmF2aWdhdG9yIiwibWVkaWFEZXZpY2VzIiwiZ2V0RGlzcGxheU1lZGlhIiwiYXVkaW8iLCJ2aWRlbyIsImRpc3BsYXlTdXJmYWNlIiwibG9naWNhbFN1cmZhY2UiLCJhdWRpb1RyYWNrcyIsImdldEF1ZGlvVHJhY2tzIiwic3BlZWNoU2VydmljZUluc3RhbmNlIiwib25lbmRlZCIsIm5hbWUiLCJzdGFydE1pY3JvcGhvbmVSZWNvZ25pdGlvbiIsImdldFVzZXJNZWRpYSIsImdldFJlYXNvbmluZ0VmZm9ydCIsInRleHRMZW5ndGgiLCJpc0NvbXBsZXhRdWVzdGlvbiIsImlzQ29tYmluZWQiLCJsZW5ndGhTZXR0aW5ncyIsImNvbmNpc2UiLCJ0ZW1wZXJhdHVyZSIsIm1heFRva2VucyIsIm1lZGl1bSIsImxlbmd0aHkiLCJyZXNwb25zZUxlbmd0aCIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJob3VyIiwibWludXRlIiwic3RyZWFtZWRSZXNwb25zZSIsInN0YXR1cyIsImNvbnZlcnNhdGlvbkhpc3RvcnlGb3JBUEkiLCJldmVudCIsInJvbGUiLCJjb250ZW50IiwibW9kZWxDb25maWciLCJtb2RlbCIsImdlbmVyYXRpb25Db25maWciLCJtYXhPdXRwdXRUb2tlbnMiLCJzeXN0ZW1JbnN0cnVjdGlvbiIsInBhcnRzIiwiZ3B0U3lzdGVtUHJvbXB0IiwidGhpbmtpbmdCdWRnZXQiLCJ0aGlua2luZ0NvbmZpZyIsImdldEdlbmVyYXRpdmVNb2RlbCIsImNoYXQiLCJzdGFydENoYXQiLCJtc2ciLCJzZW5kTWVzc2FnZVN0cmVhbSIsImNodW5rIiwiY2h1bmtUZXh0IiwibWVzc2FnZXMiLCJyZXF1ZXN0UGFyYW1zIiwibWF4X2NvbXBsZXRpb25fdG9rZW5zIiwibWF4X3Rva2VucyIsInJlYXNvbmluZ0VmZm9ydCIsInJlYXNvbmluZ19lZmZvcnQiLCJ2ZXJib3NpdHkiLCJyZXNwb25zZSIsImNvbXBsZXRpb25zIiwiY3JlYXRlIiwiY2hvaWNlcyIsImRlbHRhIiwiZmluYWxUaW1lc3RhbXAiLCJlcnJvck1lc3NhZ2UiLCJmb3JtYXRBbmREaXNwbGF5UmVzcG9uc2UiLCJjb21wb25lbnRzIiwiY29kZSIsIm5vZGUiLCJpbmxpbmUiLCJjbGFzc05hbWUiLCJjaGlsZHJlbiIsInByb3BzIiwibWF0Y2giLCJleGVjIiwic3giLCJteSIsInBvc2l0aW9uIiwiYm9yZGVyUmFkaXVzIiwicGFkZGluZyIsImZvbnRTaXplIiwib3ZlcmZsb3dYIiwid2hpdGVTcGFjZSIsIndvcmRCcmVhayIsInByZSIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwiaGlnaGxpZ2h0IiwiU3RyaW5nIiwibGFuZ3VhZ2UiLCJpZ25vcmVJbGxlZ2FscyIsInN0eWxlIiwiYmFja2dyb3VuZENvbG9yIiwiZm9udEZhbWlseSIsInAiLCJwYXJhZ3JhcGgiLCJtYiIsInN0cm9uZyIsImNvbXBvbmVudCIsImZvbnRXZWlnaHQiLCJlbSIsImZvbnRTdHlsZSIsInVsIiwicGwiLCJvbCIsImxpIiwicmVuZGVySGlzdG9yeUl0ZW0iLCJpdGVtIiwiaW5kZXgiLCJJY29uIiwidGl0bGUiLCJhdmF0YXJCZ0NvbG9yIiwicGFsZXR0ZSIsInNlY29uZGFyeSIsImxpZ2h0IiwiYWxpZ25JdGVtcyIsInB4IiwicHkiLCJiZ2NvbG9yIiwibXIiLCJtdCIsImNvbG9yIiwiZ2V0Q29udHJhc3RUZXh0IiwidmFyaWFudCIsImZsZXhHcm93IiwiYmFja2dyb3VuZCIsImRlZmF1bHQiLCJib3JkZXJDb2xvciIsImRpdmlkZXIiLCJkaXNwbGF5IiwianVzdGlmeUNvbnRlbnQiLCJyZW5kZXJRdWVzdGlvbkhpc3RvcnlJdGVtIiwiaW5mbyIsInN1Y2Nlc3MiLCJzZWNvbmRhcnlBY3Rpb24iLCJlZGdlIiwiY2hlY2tlZCIsIm9uQ2hhbmdlIiwicHJldiIsIngiLCJzaXplIiwiZGlzYWJsZVBhZGRpbmciLCJ3aWR0aCIsImhlaWdodCIsInByaW1hcnkiLCJub1dyYXAiLCJXZWJraXRMaW5lQ2xhbXAiLCJXZWJraXRCb3hPcmllbnQiLCJvdmVyZmxvdyIsInRleHRPdmVyZmxvdyIsImhhbmRsZVNvcnRPcmRlclRvZ2dsZSIsImdldEFpUmVzcG9uc2VzVG9EaXNwbGF5IiwicmVzcG9uc2VzIiwiY3VycmVudFN0cmVhbWluZ1RleHQiLCJwdXNoIiwidG9nZ2xlUGlwV2luZG93IiwiY2xvc2VkIiwiYWRkUmVzaXplTGlzdGVuZXIiLCJwaXBXaW5kb3ciLCJoYW5kbGVQaXBSZXNpemUiLCJ0YXJnZXQiLCJjb250ZW50V2luZG93IiwicG9zdE1lc3NhZ2UiLCJpbm5lcldpZHRoIiwiaW5uZXJIZWlnaHQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsIndpbmRvdyIsImRvY3VtZW50UGljdHVyZUluUGljdHVyZSIsInJlcXVlc3RXaW5kb3ciLCJwaXBPcHRpb25zIiwicmVxdWVzdGVkUGlwV2luZG93IiwiaWZyYW1lIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50Iiwic3JjIiwiYm9yZGVyIiwiYm9keSIsIm1hcmdpbiIsImFwcGVuZCIsInJlbW92ZVJlc2l6ZUxpc3RlbmVyIiwib25sb2FkIiwiaGlzdG9yaWNhbFJlc3BvbnNlcyIsInNvcnRPcmRlciIsImVyciIsIm9wZW4iLCJwaXBDaGVja0ludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwiX3BpcEludGVydmFsSWQiLCJ0YXJnZXRXaW5kb3dGb3JNZXNzYWdlIiwid2FybiIsInBhcGVyIiwiZ3JleSIsImZsZXhEaXJlY3Rpb24iLCJlbGV2YXRpb24iLCJvbkNsaWNrIiwiYXJpYS1sYWJlbCIsIm1heFdpZHRoIiwiY29udGFpbmVyIiwic3BhY2luZyIsInhzIiwibWQiLCJnYXAiLCJhdmF0YXIiLCJwYiIsImNvbnRyb2wiLCJsYWJlbCIsImZ1bGxXaWR0aCIsIm11bHRpbGluZSIsInJvd3MiLCJvbktleURvd24iLCJwbGFjZWhvbGRlciIsImZsZXhXcmFwIiwic3RhcnRJY29uIiwiZGlzYWJsZWQiLCJhY3Rpb24iLCJib3JkZXJCb3R0b20iLCJmb2xsb3dCdXR0b25DbGFzc05hbWUiLCJkZW5zZSIsInB0IiwibWwiLCJtb2RlIiwib25DbG9zZSIsIm9uU2F2ZSIsImF1dG9IaWRlRHVyYXRpb24iLCJhbmNob3JPcmlnaW4iLCJ2ZXJ0aWNhbCIsImhvcml6b250YWwiLCJib3hTaGFkb3ciLCJzaGFkb3dzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/interview.js\n"));

/***/ })

});