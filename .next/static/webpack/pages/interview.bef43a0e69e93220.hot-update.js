"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./utils/speechServices.js":
/*!*********************************!*\
  !*** ./utils/speechServices.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AzureSpeechService: () => (/* binding */ AzureSpeechService),\n/* harmony export */   DeepgramSpeechService: () => (/* binding */ DeepgramSpeechService),\n/* harmony export */   createSpeechService: () => (/* binding */ createSpeechService)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet createClient = null;\nlet SpeechSDK = null;\n// Initialize client-side dependencies\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\")).then((module)=>{\n        createClient = module.createClient;\n    });\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23)).then((module)=>{\n        SpeechSDK = module;\n    });\n}\n/**\n * Abstract base class for speech services\n */ class SpeechService {\n    async start(mediaStream, source) {\n        throw new Error('start method must be implemented');\n    }\n    async stop() {\n        throw new Error('stop method must be implemented');\n    }\n    cleanup() {\n        // Default cleanup implementation\n        if (this.recognizer) {\n            this.recognizer = null;\n        }\n        this.isActive = false;\n    }\n    constructor(config, callbacks){\n        this.config = config;\n        this.callbacks = callbacks;\n        this.isActive = false;\n        this.recognizer = null;\n    }\n}\n/**\n * Deepgram Speech Service Implementation\n */ class DeepgramSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            if (!this.config.deepgramKey) {\n                throw new Error('Deepgram API key is required');\n            }\n            if (!createClient) {\n                const { createClient: dgCreateClient } = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\"));\n                createClient = dgCreateClient;\n            }\n            this.source = source;\n            this.deepgram = createClient(this.config.deepgramKey);\n            // Create WebSocket connection for real-time transcription\n            this.connection = this.deepgram.listen.live({\n                model: this.config.deepgramModel || 'nova-2',\n                language: this.config.deepgramLanguage || 'en-US',\n                smart_format: true,\n                interim_results: true,\n                endpointing: 300,\n                utterance_end_ms: 1000,\n                vad_events: true\n            });\n            // Set up event handlers\n            this.connection.on('open', ()=>{\n                var _this_callbacks_onStart, _this_callbacks;\n                console.log(\"Deepgram connection opened for \".concat(source));\n                this.isActive = true;\n                // Clear any connection timeout\n                if (this.connectionTimeout) {\n                    clearTimeout(this.connectionTimeout);\n                    this.connectionTimeout = null;\n                }\n                (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            });\n            this.connection.on('Results', (data)=>{\n                var _data_channel_alternatives_, _data_channel_alternatives, _data_channel;\n                console.log(\"Deepgram results for \".concat(source, \":\"), data);\n                this.hasReceivedResults = true; // Mark that we've received results\n                const transcript = (_data_channel = data.channel) === null || _data_channel === void 0 ? void 0 : (_data_channel_alternatives = _data_channel.alternatives) === null || _data_channel_alternatives === void 0 ? void 0 : (_data_channel_alternatives_ = _data_channel_alternatives[0]) === null || _data_channel_alternatives_ === void 0 ? void 0 : _data_channel_alternatives_.transcript;\n                if (transcript && transcript.trim()) {\n                    if (data.is_final) {\n                        var _this_callbacks_onFinalResult, _this_callbacks;\n                        console.log(\"Final transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, transcript, source);\n                    } else {\n                        var _this_callbacks_onInterimResult, _this_callbacks1;\n                        console.log(\"Interim transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onInterimResult = (_this_callbacks1 = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks1, transcript, source);\n                    }\n                } else {\n                    var _data_channel1, _data_channel_alternatives1, _data_channel2, _data_channel_alternatives2, _data_channel3;\n                    // Log when we get results but no transcript\n                    console.log(\"Deepgram results received but no transcript for \".concat(source, \":\"), {\n                        hasChannel: !!data.channel,\n                        hasAlternatives: !!((_data_channel1 = data.channel) === null || _data_channel1 === void 0 ? void 0 : _data_channel1.alternatives),\n                        alternativesLength: (_data_channel2 = data.channel) === null || _data_channel2 === void 0 ? void 0 : (_data_channel_alternatives1 = _data_channel2.alternatives) === null || _data_channel_alternatives1 === void 0 ? void 0 : _data_channel_alternatives1.length,\n                        firstAlternative: (_data_channel3 = data.channel) === null || _data_channel3 === void 0 ? void 0 : (_data_channel_alternatives2 = _data_channel3.alternatives) === null || _data_channel_alternatives2 === void 0 ? void 0 : _data_channel_alternatives2[0],\n                        transcript: transcript\n                    });\n                }\n            });\n            this.connection.on('Metadata', (data)=>{\n                console.log(\"Deepgram metadata for \".concat(source, \":\"), data);\n            });\n            this.connection.on('error', (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"Deepgram error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                this.stop();\n            });\n            this.connection.on('close', (closeEvent)=>{\n                console.log(\"Deepgram connection closed for \".concat(source, \":\"), closeEvent);\n                this.isActive = false;\n                // If connection closes immediately after opening, it might be a configuration issue\n                if (closeEvent && closeEvent.code && closeEvent.code !== 1000) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection closed with error code \".concat(closeEvent.code, \": \").concat(closeEvent.reason));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error(\"Connection closed: \".concat(closeEvent.reason || 'Unknown error')), source);\n                } else {\n                    var _this_callbacks_onStop, _this_callbacks1;\n                    (_this_callbacks_onStop = (_this_callbacks1 = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks1, source);\n                }\n            });\n            this.connection.on('warning', (warning)=>{\n                console.warn(\"Deepgram warning for \".concat(source, \":\"), warning);\n            });\n            // Set up MediaRecorder to send audio data to Deepgram\n            // Try different mimeTypes for better compatibility\n            let mimeType = 'audio/webm;codecs=opus';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n                mimeType = 'audio/webm';\n                if (!MediaRecorder.isTypeSupported(mimeType)) {\n                    mimeType = 'audio/mp4';\n                    if (!MediaRecorder.isTypeSupported(mimeType)) {\n                        mimeType = ''; // Use default\n                    }\n                }\n            }\n            console.log(\"Using MediaRecorder mimeType: \".concat(mimeType || 'default', \" for \").concat(source));\n            this.mediaRecorder = new MediaRecorder(mediaStream, mimeType ? {\n                mimeType\n            } : {});\n            this.mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0 && this.connection && this.isActive) {\n                    console.log(\"Sending audio data to Deepgram: \".concat(event.data.size, \" bytes for \").concat(source));\n                    this.connection.send(event.data);\n                } else if (event.data.size === 0) {\n                    console.warn(\"Empty audio data received for \".concat(source));\n                }\n            };\n            this.mediaRecorder.onerror = (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"MediaRecorder error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            };\n            this.mediaRecorder.onstart = ()=>{\n                console.log(\"MediaRecorder started for \".concat(source));\n            };\n            this.mediaRecorder.onstop = ()=>{\n                console.log(\"MediaRecorder stopped for \".concat(source));\n                // Clear the data interval when recorder stops\n                if (this.dataInterval) {\n                    clearInterval(this.dataInterval);\n                    this.dataInterval = null;\n                }\n            // Don't automatically close connection when MediaRecorder stops\n            // The connection should stay open for the duration of the session\n            };\n            // Start recording and sending data\n            // Use continuous recording instead of timed chunks to prevent auto-stopping\n            this.mediaRecorder.start(); // Continuous recording\n            // Set up interval to request data periodically\n            this.dataInterval = setInterval(()=>{\n                if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n                    this.mediaRecorder.requestData();\n                }\n            }, 250); // Request data every 250ms\n            // Set up connection timeout to detect failed connections\n            this.connectionTimeout = setTimeout(()=>{\n                if (!this.isActive) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection timeout for \".concat(source));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error('Connection timeout - please check your API key and network connection'), source);\n                    this.stop();\n                }\n            }, 10000); // 10 second timeout\n            // Set up a timeout to check if we're receiving any transcription results\n            this.transcriptionTimeout = setTimeout(()=>{\n                if (this.isActive && !this.hasReceivedResults) {\n                    var // Don't stop the connection, just warn the user\n                    _this_callbacks_onError, _this_callbacks;\n                    console.warn(\"No transcription results received from Deepgram for \".concat(source, \" after 15 seconds. This might indicate an API key issue or audio format problem.\"));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error('No speech recognition results - please check your Deepgram API key and try speaking clearly'), source);\n                }\n            }, 15000); // 15 second timeout for transcription results\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks;\n            console.error(\"Failed to start Deepgram for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            // Clear data interval\n            if (this.dataInterval) {\n                clearInterval(this.dataInterval);\n                this.dataInterval = null;\n            }\n            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n                this.mediaRecorder.stop();\n            }\n            if (this.connection) {\n                this.connection.finish();\n                this.connection = null;\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Deepgram:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        if (this.connectionTimeout) {\n            clearTimeout(this.connectionTimeout);\n            this.connectionTimeout = null;\n        }\n        if (this.transcriptionTimeout) {\n            clearTimeout(this.transcriptionTimeout);\n            this.transcriptionTimeout = null;\n        }\n        if (this.dataInterval) {\n            clearInterval(this.dataInterval);\n            this.dataInterval = null;\n        }\n        this.mediaRecorder = null;\n        this.connection = null;\n        this.deepgram = null;\n        this.source = null;\n        this.hasReceivedResults = false;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.deepgram = null;\n        this.connection = null;\n        this.mediaRecorder = null;\n        this.source = null;\n        this.connectionTimeout = null;\n        this.transcriptionTimeout = null;\n        this.dataInterval = null;\n        this.hasReceivedResults = false;\n    }\n}\n/**\n * Azure Speech Service Implementation (Legacy)\n */ class AzureSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            var _this_callbacks_onStart, _this_callbacks;\n            if (!this.config.azureToken || !this.config.azureRegion) {\n                throw new Error('Azure Speech credentials are required');\n            }\n            if (!SpeechSDK) {\n                SpeechSDK = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23));\n            }\n            // Create audio config from media stream\n            this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n            // Create speech config\n            this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(this.config.azureToken, this.config.azureRegion);\n            this.speechConfig.speechRecognitionLanguage = this.config.azureLanguage || 'en-US';\n            // Create recognizer\n            this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);\n            // Set up event handlers\n            this.recognizer.recognizing = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                    var _this_callbacks_onInterimResult, _this_callbacks;\n                    (_this_callbacks_onInterimResult = (_this_callbacks = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.recognized = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                    var _this_callbacks_onFinalResult, _this_callbacks;\n                    (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.canceled = (s, e)=>{\n                console.log(\"Azure recognition canceled for \".concat(source, \": \").concat(e.reason));\n                if (e.reason === SpeechSDK.CancellationReason.Error) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    const error = new Error(\"Azure Speech error: \".concat(e.errorDetails));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                }\n                this.stop();\n            };\n            this.recognizer.sessionStopped = (s, e)=>{\n                var _this_callbacks_onStop, _this_callbacks;\n                console.log(\"Azure session stopped for \".concat(source));\n                (_this_callbacks_onStop = (_this_callbacks = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks, source);\n                this.stop();\n            };\n            // Start continuous recognition\n            await this.recognizer.startContinuousRecognitionAsync();\n            this.isActive = true;\n            (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks1;\n            console.error(\"Failed to start Azure Speech for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks1 = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks1, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            if (this.recognizer && typeof this.recognizer.stopContinuousRecognitionAsync === 'function') {\n                await this.recognizer.stopContinuousRecognitionAsync();\n            }\n            if (this.audioConfig && typeof this.audioConfig.close === 'function') {\n                this.audioConfig.close();\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Azure Speech:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n}\n/**\n * Factory function to create speech service instances\n */ function createSpeechService(config, callbacks) {\n    const serviceType = config.speechService || 'deepgram';\n    switch(serviceType){\n        case 'deepgram':\n            return new DeepgramSpeechService(config, callbacks);\n        case 'azure':\n            return new AzureSpeechService(config, callbacks);\n        default:\n            throw new Error(\"Unknown speech service: \".concat(serviceType));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./utils/speechServices.js\n"));

/***/ })

});