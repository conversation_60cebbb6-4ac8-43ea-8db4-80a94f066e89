"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./utils/speechServices.js":
/*!*********************************!*\
  !*** ./utils/speechServices.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AzureSpeechService: () => (/* binding */ AzureSpeechService),\n/* harmony export */   DeepgramSpeechService: () => (/* binding */ DeepgramSpeechService),\n/* harmony export */   createSpeechService: () => (/* binding */ createSpeechService)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet createClient = null;\nlet SpeechSDK = null;\n// Initialize client-side dependencies\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\")).then((module)=>{\n        createClient = module.createClient;\n    });\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23)).then((module)=>{\n        SpeechSDK = module;\n    });\n}\n/**\n * Abstract base class for speech services\n */ class SpeechService {\n    async start(mediaStream, source) {\n        throw new Error('start method must be implemented');\n    }\n    async stop() {\n        throw new Error('stop method must be implemented');\n    }\n    cleanup() {\n        // Default cleanup implementation\n        if (this.recognizer) {\n            this.recognizer = null;\n        }\n        this.isActive = false;\n    }\n    constructor(config, callbacks){\n        this.config = config;\n        this.callbacks = callbacks;\n        this.isActive = false;\n        this.recognizer = null;\n    }\n}\n/**\n * Deepgram Speech Service Implementation\n */ class DeepgramSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            if (!this.config.deepgramKey) {\n                throw new Error('Deepgram API key is required');\n            }\n            if (!createClient) {\n                const { createClient: dgCreateClient } = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\"));\n                createClient = dgCreateClient;\n            }\n            this.source = source;\n            this.deepgram = createClient(this.config.deepgramKey);\n            // Create WebSocket connection for real-time transcription\n            this.connection = this.deepgram.listen.live({\n                model: this.config.deepgramModel || 'nova-2',\n                language: this.config.deepgramLanguage || 'en-US',\n                smart_format: true,\n                interim_results: true,\n                endpointing: 300,\n                utterance_end_ms: 1000,\n                vad_events: true\n            });\n            // Set up event handlers\n            this.connection.on('open', ()=>{\n                var _this_callbacks_onStart, _this_callbacks;\n                console.log(\"Deepgram connection opened for \".concat(source));\n                this.isActive = true;\n                // Clear any connection timeout\n                if (this.connectionTimeout) {\n                    clearTimeout(this.connectionTimeout);\n                    this.connectionTimeout = null;\n                }\n                (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            });\n            this.connection.on('Results', (data)=>{\n                var _data_channel_alternatives_, _data_channel_alternatives, _data_channel;\n                console.log(\"Deepgram results for \".concat(source, \":\"), data);\n                this.hasReceivedResults = true; // Mark that we've received results\n                const transcript = (_data_channel = data.channel) === null || _data_channel === void 0 ? void 0 : (_data_channel_alternatives = _data_channel.alternatives) === null || _data_channel_alternatives === void 0 ? void 0 : (_data_channel_alternatives_ = _data_channel_alternatives[0]) === null || _data_channel_alternatives_ === void 0 ? void 0 : _data_channel_alternatives_.transcript;\n                if (transcript && transcript.trim()) {\n                    if (data.is_final) {\n                        var _this_callbacks_onFinalResult, _this_callbacks;\n                        console.log(\"Final transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, transcript, source);\n                    } else {\n                        var _this_callbacks_onInterimResult, _this_callbacks1;\n                        console.log(\"Interim transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onInterimResult = (_this_callbacks1 = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks1, transcript, source);\n                    }\n                } else {\n                    var _data_channel1, _data_channel_alternatives1, _data_channel2, _data_channel_alternatives2, _data_channel3;\n                    // Log when we get results but no transcript\n                    console.log(\"Deepgram results received but no transcript for \".concat(source, \":\"), {\n                        hasChannel: !!data.channel,\n                        hasAlternatives: !!((_data_channel1 = data.channel) === null || _data_channel1 === void 0 ? void 0 : _data_channel1.alternatives),\n                        alternativesLength: (_data_channel2 = data.channel) === null || _data_channel2 === void 0 ? void 0 : (_data_channel_alternatives1 = _data_channel2.alternatives) === null || _data_channel_alternatives1 === void 0 ? void 0 : _data_channel_alternatives1.length,\n                        firstAlternative: (_data_channel3 = data.channel) === null || _data_channel3 === void 0 ? void 0 : (_data_channel_alternatives2 = _data_channel3.alternatives) === null || _data_channel_alternatives2 === void 0 ? void 0 : _data_channel_alternatives2[0],\n                        transcript: transcript\n                    });\n                }\n            });\n            this.connection.on('Metadata', (data)=>{\n                console.log(\"Deepgram metadata for \".concat(source, \":\"), data);\n            });\n            this.connection.on('error', (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"Deepgram error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                this.stop();\n            });\n            this.connection.on('close', (closeEvent)=>{\n                console.log(\"Deepgram connection closed for \".concat(source, \":\"), closeEvent);\n                this.isActive = false;\n                // If connection closes immediately after opening, it might be a configuration issue\n                if (closeEvent && closeEvent.code && closeEvent.code !== 1000) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection closed with error code \".concat(closeEvent.code, \": \").concat(closeEvent.reason));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error(\"Connection closed: \".concat(closeEvent.reason || 'Unknown error')), source);\n                } else {\n                    var _this_callbacks_onStop, _this_callbacks1;\n                    (_this_callbacks_onStop = (_this_callbacks1 = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks1, source);\n                }\n            });\n            this.connection.on('warning', (warning)=>{\n                console.warn(\"Deepgram warning for \".concat(source, \":\"), warning);\n            });\n            // Set up MediaRecorder to send audio data to Deepgram\n            // Try different mimeTypes for better compatibility\n            let mimeType = 'audio/webm;codecs=opus';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n                mimeType = 'audio/webm';\n                if (!MediaRecorder.isTypeSupported(mimeType)) {\n                    mimeType = 'audio/mp4';\n                    if (!MediaRecorder.isTypeSupported(mimeType)) {\n                        mimeType = ''; // Use default\n                    }\n                }\n            }\n            console.log(\"Using MediaRecorder mimeType: \".concat(mimeType || 'default', \" for \").concat(source));\n            this.mediaRecorder = new MediaRecorder(mediaStream, mimeType ? {\n                mimeType\n            } : {});\n            this.mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0 && this.connection && this.isActive) {\n                    console.log(\"Sending audio data to Deepgram: \".concat(event.data.size, \" bytes for \").concat(source));\n                    this.connection.send(event.data);\n                } else if (event.data.size === 0) {\n                    console.warn(\"Empty audio data received for \".concat(source));\n                }\n            };\n            this.mediaRecorder.onerror = (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"MediaRecorder error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            };\n            this.mediaRecorder.onstart = ()=>{\n                console.log(\"MediaRecorder started for \".concat(source));\n            };\n            this.mediaRecorder.onstop = ()=>{\n                console.log(\"MediaRecorder stopped for \".concat(source));\n                // Clear the data interval when recorder stops\n                if (this.dataInterval) {\n                    clearInterval(this.dataInterval);\n                    this.dataInterval = null;\n                }\n            // Don't automatically close connection when MediaRecorder stops\n            // The connection should stay open for the duration of the session\n            };\n            // Start recording and sending data\n            // Use continuous recording instead of timed chunks to prevent auto-stopping\n            this.mediaRecorder.start(); // Continuous recording\n            // Set up interval to request data periodically\n            this.dataInterval = setInterval(()=>{\n                if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n                    this.mediaRecorder.requestData();\n                }\n            }, 250); // Request data every 250ms\n            // Set up connection timeout to detect failed connections\n            this.connectionTimeout = setTimeout(()=>{\n                if (!this.isActive) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection timeout for \".concat(source));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error('Connection timeout - please check your API key and network connection'), source);\n                    this.stop();\n                }\n            }, 10000); // 10 second timeout\n            // Set up a timeout to check if we're receiving any transcription results\n            this.transcriptionTimeout = setTimeout(()=>{\n                if (this.isActive && !this.hasReceivedResults) {\n                    var // Don't stop the connection, just warn the user\n                    _this_callbacks_onError, _this_callbacks;\n                    console.warn(\"No transcription results received from Deepgram for \".concat(source, \" after 15 seconds. This might indicate an API key issue or audio format problem.\"));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error('No speech recognition results - please check your Deepgram API key and try speaking clearly'), source);\n                }\n            }, 15000); // 15 second timeout for transcription results\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks;\n            console.error(\"Failed to start Deepgram for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            // Clear data interval\n            if (this.dataInterval) {\n                clearInterval(this.dataInterval);\n                this.dataInterval = null;\n            }\n            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n                this.mediaRecorder.stop();\n            }\n            if (this.connection) {\n                this.connection.finish();\n                this.connection = null;\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Deepgram:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        if (this.connectionTimeout) {\n            clearTimeout(this.connectionTimeout);\n            this.connectionTimeout = null;\n        }\n        if (this.dataInterval) {\n            clearInterval(this.dataInterval);\n            this.dataInterval = null;\n        }\n        this.mediaRecorder = null;\n        this.connection = null;\n        this.deepgram = null;\n        this.source = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.deepgram = null;\n        this.connection = null;\n        this.mediaRecorder = null;\n        this.source = null;\n        this.connectionTimeout = null;\n        this.transcriptionTimeout = null;\n        this.dataInterval = null;\n        this.hasReceivedResults = false;\n    }\n}\n/**\n * Azure Speech Service Implementation (Legacy)\n */ class AzureSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            var _this_callbacks_onStart, _this_callbacks;\n            if (!this.config.azureToken || !this.config.azureRegion) {\n                throw new Error('Azure Speech credentials are required');\n            }\n            if (!SpeechSDK) {\n                SpeechSDK = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23));\n            }\n            // Create audio config from media stream\n            this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n            // Create speech config\n            this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(this.config.azureToken, this.config.azureRegion);\n            this.speechConfig.speechRecognitionLanguage = this.config.azureLanguage || 'en-US';\n            // Create recognizer\n            this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);\n            // Set up event handlers\n            this.recognizer.recognizing = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                    var _this_callbacks_onInterimResult, _this_callbacks;\n                    (_this_callbacks_onInterimResult = (_this_callbacks = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.recognized = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                    var _this_callbacks_onFinalResult, _this_callbacks;\n                    (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.canceled = (s, e)=>{\n                console.log(\"Azure recognition canceled for \".concat(source, \": \").concat(e.reason));\n                if (e.reason === SpeechSDK.CancellationReason.Error) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    const error = new Error(\"Azure Speech error: \".concat(e.errorDetails));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                }\n                this.stop();\n            };\n            this.recognizer.sessionStopped = (s, e)=>{\n                var _this_callbacks_onStop, _this_callbacks;\n                console.log(\"Azure session stopped for \".concat(source));\n                (_this_callbacks_onStop = (_this_callbacks = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks, source);\n                this.stop();\n            };\n            // Start continuous recognition\n            await this.recognizer.startContinuousRecognitionAsync();\n            this.isActive = true;\n            (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks1;\n            console.error(\"Failed to start Azure Speech for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks1 = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks1, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            if (this.recognizer && typeof this.recognizer.stopContinuousRecognitionAsync === 'function') {\n                await this.recognizer.stopContinuousRecognitionAsync();\n            }\n            if (this.audioConfig && typeof this.audioConfig.close === 'function') {\n                this.audioConfig.close();\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Azure Speech:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n}\n/**\n * Factory function to create speech service instances\n */ function createSpeechService(config, callbacks) {\n    const serviceType = config.speechService || 'deepgram';\n    switch(serviceType){\n        case 'deepgram':\n            return new DeepgramSpeechService(config, callbacks);\n        case 'azure':\n            return new AzureSpeechService(config, callbacks);\n        default:\n            throw new Error(\"Unknown speech service: \".concat(serviceType));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./utils/speechServices.js\n"));

/***/ })

});