"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./utils/speechServices.js":
/*!*********************************!*\
  !*** ./utils/speechServices.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AzureSpeechService: () => (/* binding */ AzureSpeechService),\n/* harmony export */   DeepgramSpeechService: () => (/* binding */ DeepgramSpeechService),\n/* harmony export */   createSpeechService: () => (/* binding */ createSpeechService)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet createClient = null;\nlet SpeechSDK = null;\n// Initialize client-side dependencies\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\")).then((module)=>{\n        createClient = module.createClient;\n    });\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23)).then((module)=>{\n        SpeechSDK = module;\n    });\n}\n/**\n * Abstract base class for speech services\n */ class SpeechService {\n    async start(mediaStream, source) {\n        throw new Error('start method must be implemented');\n    }\n    async stop() {\n        throw new Error('stop method must be implemented');\n    }\n    cleanup() {\n        // Default cleanup implementation\n        if (this.recognizer) {\n            this.recognizer = null;\n        }\n        this.isActive = false;\n    }\n    constructor(config, callbacks){\n        this.config = config;\n        this.callbacks = callbacks;\n        this.isActive = false;\n        this.recognizer = null;\n    }\n}\n/**\n * Deepgram Speech Service Implementation\n */ class DeepgramSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            if (!this.config.deepgramKey) {\n                throw new Error('Deepgram API key is required. Get a free API key from deepgram.com and configure it in Settings (⚙️ icon).');\n            }\n            // Basic validation of API key format\n            if (typeof this.config.deepgramKey !== 'string' || this.config.deepgramKey.length < 10) {\n                throw new Error('Deepgram API key appears to be invalid. Please check your API key in Settings.');\n            }\n            if (!createClient) {\n                const { createClient: dgCreateClient } = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\"));\n                createClient = dgCreateClient;\n            }\n            this.source = source;\n            this.deepgram = createClient(this.config.deepgramKey);\n            console.log(\"Creating Deepgram connection for \".concat(source, \" with API key: \").concat(this.config.deepgramKey.substring(0, 8), \"...\"));\n            // Create WebSocket connection for real-time transcription\n            // Use minimal settings for maximum compatibility\n            try {\n                this.connection = this.deepgram.listen.live({\n                    model: 'nova-2',\n                    language: 'en-US',\n                    interim_results: true\n                });\n                console.log(\"Deepgram connection created for \".concat(source, \", setting up event listeners...\"));\n            } catch (connectionError) {\n                console.error(\"Failed to create Deepgram connection for \".concat(source, \":\"), connectionError);\n                throw new Error(\"Failed to create Deepgram connection: \".concat(connectionError.message));\n            }\n            // Set up event handlers\n            this.connection.on('open', ()=>{\n                var _this_callbacks_onStart, _this_callbacks;\n                console.log(\"Deepgram connection opened for \".concat(source));\n                this.isActive = true;\n                // Clear any connection timeout\n                if (this.connectionTimeout) {\n                    clearTimeout(this.connectionTimeout);\n                    this.connectionTimeout = null;\n                }\n                // Clear transcription timeout since connection is working\n                if (this.transcriptionTimeout) {\n                    clearTimeout(this.transcriptionTimeout);\n                    this.transcriptionTimeout = null;\n                }\n                (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            });\n            this.connection.on('Results', (data)=>{\n                var _data_channel_alternatives_, _data_channel_alternatives, _data_channel;\n                console.log(\"Deepgram results for \".concat(source, \":\"), data);\n                this.hasReceivedResults = true; // Mark that we've received results\n                const transcript = (_data_channel = data.channel) === null || _data_channel === void 0 ? void 0 : (_data_channel_alternatives = _data_channel.alternatives) === null || _data_channel_alternatives === void 0 ? void 0 : (_data_channel_alternatives_ = _data_channel_alternatives[0]) === null || _data_channel_alternatives_ === void 0 ? void 0 : _data_channel_alternatives_.transcript;\n                if (transcript && transcript.trim()) {\n                    if (data.is_final) {\n                        var _this_callbacks_onFinalResult, _this_callbacks;\n                        console.log(\"Final transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, transcript, source);\n                    } else {\n                        var _this_callbacks_onInterimResult, _this_callbacks1;\n                        console.log(\"Interim transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onInterimResult = (_this_callbacks1 = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks1, transcript, source);\n                    }\n                } else {\n                    var _data_channel1, _data_channel_alternatives1, _data_channel2, _data_channel_alternatives2, _data_channel3;\n                    // Log when we get results but no transcript\n                    console.log(\"Deepgram results received but no transcript for \".concat(source, \":\"), {\n                        hasChannel: !!data.channel,\n                        hasAlternatives: !!((_data_channel1 = data.channel) === null || _data_channel1 === void 0 ? void 0 : _data_channel1.alternatives),\n                        alternativesLength: (_data_channel2 = data.channel) === null || _data_channel2 === void 0 ? void 0 : (_data_channel_alternatives1 = _data_channel2.alternatives) === null || _data_channel_alternatives1 === void 0 ? void 0 : _data_channel_alternatives1.length,\n                        firstAlternative: (_data_channel3 = data.channel) === null || _data_channel3 === void 0 ? void 0 : (_data_channel_alternatives2 = _data_channel3.alternatives) === null || _data_channel_alternatives2 === void 0 ? void 0 : _data_channel_alternatives2[0],\n                        transcript: transcript\n                    });\n                }\n            });\n            this.connection.on('Metadata', (data)=>{\n                console.log(\"Deepgram metadata for \".concat(source, \":\"), data);\n            });\n            this.connection.on('error', (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"Deepgram error for \".concat(source, \":\"), error);\n                // Provide more specific error messages based on the error type\n                let errorMessage = 'Deepgram connection failed';\n                if (error.type === 'error' && error.target instanceof WebSocket) {\n                    errorMessage = 'Deepgram API key is invalid or missing. Please check your API key in Settings.';\n                }\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error(errorMessage), source);\n                this.stop();\n            });\n            this.connection.on('close', (closeEvent)=>{\n                console.log(\"Deepgram connection closed for \".concat(source, \":\"), closeEvent);\n                this.isActive = false;\n                // If connection closes immediately after opening, it might be a configuration issue\n                if (closeEvent && closeEvent.code && closeEvent.code !== 1000) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection closed with error code \".concat(closeEvent.code, \": \").concat(closeEvent.reason));\n                    let errorMessage = 'Deepgram connection failed';\n                    if (closeEvent.code === 1006) {\n                        errorMessage = 'Deepgram API key is invalid or missing. Please get a free API key from deepgram.com and configure it in Settings (⚙️ icon).';\n                    } else if (closeEvent.code === 1002) {\n                        errorMessage = 'Deepgram API key has insufficient permissions. Please check your API key settings.';\n                    } else if (closeEvent.code === 1011) {\n                        errorMessage = 'Deepgram service error. Please try again or contact support.';\n                    }\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error(errorMessage), source);\n                } else {\n                    var _this_callbacks_onStop, _this_callbacks1;\n                    (_this_callbacks_onStop = (_this_callbacks1 = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks1, source);\n                }\n            });\n            this.connection.on('warning', (warning)=>{\n                console.warn(\"Deepgram warning for \".concat(source, \":\"), warning);\n            });\n            // Set up MediaRecorder to send audio data to Deepgram\n            // Use the most compatible format for Deepgram\n            let mimeType = 'audio/webm';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n                mimeType = 'audio/mp4';\n                if (!MediaRecorder.isTypeSupported(mimeType)) {\n                    mimeType = ''; // Use default\n                }\n            }\n            console.log(\"Using MediaRecorder mimeType: \".concat(mimeType || 'default', \" for \").concat(source));\n            this.mediaRecorder = new MediaRecorder(mediaStream, mimeType ? {\n                mimeType\n            } : {});\n            this.mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0 && this.connection && this.isActive) {\n                    console.log(\"Sending audio data to Deepgram: \".concat(event.data.size, \" bytes for \").concat(source));\n                    this.connection.send(event.data);\n                } else if (event.data.size === 0) {\n                    console.warn(\"Empty audio data received for \".concat(source));\n                }\n            };\n            this.mediaRecorder.onerror = (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"MediaRecorder error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            };\n            this.mediaRecorder.onstart = ()=>{\n                console.log(\"MediaRecorder started for \".concat(source));\n            };\n            this.mediaRecorder.onstop = ()=>{\n                console.log(\"MediaRecorder stopped for \".concat(source));\n                // Clear the data interval when recorder stops\n                if (this.dataInterval) {\n                    clearInterval(this.dataInterval);\n                    this.dataInterval = null;\n                }\n            // Don't automatically close connection when MediaRecorder stops\n            // The connection should stay open for the duration of the session\n            };\n            // Start recording and sending data\n            // Use continuous recording instead of timed chunks to prevent auto-stopping\n            this.mediaRecorder.start(); // Continuous recording\n            // Set up interval to request data periodically\n            this.dataInterval = setInterval(()=>{\n                if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n                    this.mediaRecorder.requestData();\n                }\n            }, 250); // Request data every 250ms\n            // Set up connection timeout to detect failed connections\n            this.connectionTimeout = setTimeout(()=>{\n                if (!this.isActive) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection timeout for \".concat(source));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error('Connection timeout - please check your API key and network connection'), source);\n                    this.stop();\n                }\n            }, 10000); // 10 second timeout\n            // Set up a timeout to check if we're receiving any transcription results\n            this.transcriptionTimeout = setTimeout(()=>{\n                if (this.isActive && !this.hasReceivedResults) {\n                    var // Don't stop the connection, just warn the user\n                    _this_callbacks_onError, _this_callbacks;\n                    console.warn(\"No transcription results received from Deepgram for \".concat(source, \" after 15 seconds. This might indicate an API key issue or audio format problem.\"));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error('No speech recognition results - please check your Deepgram API key and try speaking clearly'), source);\n                }\n            }, 15000); // 15 second timeout for transcription results\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks;\n            console.error(\"Failed to start Deepgram for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            // Clear data interval\n            if (this.dataInterval) {\n                clearInterval(this.dataInterval);\n                this.dataInterval = null;\n            }\n            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n                this.mediaRecorder.stop();\n            }\n            if (this.connection) {\n                this.connection.finish();\n                this.connection = null;\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Deepgram:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        if (this.connectionTimeout) {\n            clearTimeout(this.connectionTimeout);\n            this.connectionTimeout = null;\n        }\n        if (this.transcriptionTimeout) {\n            clearTimeout(this.transcriptionTimeout);\n            this.transcriptionTimeout = null;\n        }\n        if (this.dataInterval) {\n            clearInterval(this.dataInterval);\n            this.dataInterval = null;\n        }\n        this.mediaRecorder = null;\n        this.connection = null;\n        this.deepgram = null;\n        this.source = null;\n        this.hasReceivedResults = false;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.deepgram = null;\n        this.connection = null;\n        this.mediaRecorder = null;\n        this.source = null;\n        this.connectionTimeout = null;\n        this.transcriptionTimeout = null;\n        this.dataInterval = null;\n        this.hasReceivedResults = false;\n    }\n}\n/**\n * Azure Speech Service Implementation (Legacy)\n */ class AzureSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            var _this_callbacks_onStart, _this_callbacks;\n            if (!this.config.azureToken || !this.config.azureRegion) {\n                throw new Error('Azure Speech credentials are required');\n            }\n            if (!SpeechSDK) {\n                SpeechSDK = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23));\n            }\n            // Create audio config from media stream\n            this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n            // Create speech config\n            this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(this.config.azureToken, this.config.azureRegion);\n            this.speechConfig.speechRecognitionLanguage = this.config.azureLanguage || 'en-US';\n            // Create recognizer\n            this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);\n            // Set up event handlers\n            this.recognizer.recognizing = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                    var _this_callbacks_onInterimResult, _this_callbacks;\n                    (_this_callbacks_onInterimResult = (_this_callbacks = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.recognized = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                    var _this_callbacks_onFinalResult, _this_callbacks;\n                    (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.canceled = (s, e)=>{\n                console.log(\"Azure recognition canceled for \".concat(source, \": \").concat(e.reason));\n                if (e.reason === SpeechSDK.CancellationReason.Error) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    const error = new Error(\"Azure Speech error: \".concat(e.errorDetails));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                }\n                this.stop();\n            };\n            this.recognizer.sessionStopped = (s, e)=>{\n                var _this_callbacks_onStop, _this_callbacks;\n                console.log(\"Azure session stopped for \".concat(source));\n                (_this_callbacks_onStop = (_this_callbacks = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks, source);\n                this.stop();\n            };\n            // Start continuous recognition\n            await this.recognizer.startContinuousRecognitionAsync();\n            this.isActive = true;\n            (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks1;\n            console.error(\"Failed to start Azure Speech for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks1 = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks1, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            if (this.recognizer && typeof this.recognizer.stopContinuousRecognitionAsync === 'function') {\n                await this.recognizer.stopContinuousRecognitionAsync();\n            }\n            if (this.audioConfig && typeof this.audioConfig.close === 'function') {\n                this.audioConfig.close();\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Azure Speech:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n}\n/**\n * Factory function to create speech service instances\n */ function createSpeechService(config, callbacks) {\n    const serviceType = config.speechService || 'deepgram';\n    switch(serviceType){\n        case 'deepgram':\n            return new DeepgramSpeechService(config, callbacks);\n        case 'azure':\n            return new AzureSpeechService(config, callbacks);\n        default:\n            throw new Error(\"Unknown speech service: \".concat(serviceType));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./utils/speechServices.js\n"));

/***/ })

});