"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./pages/interview.js":
/*!****************************!*\
  !*** ./pages/interview.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InterviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(pages-dir-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AppBar,Avatar,Box,Button,Card,CardContent,CardHeader,Checkbox,Chip,CircularProgress,Container,FormControlLabel,Grid,IconButton,List,ListItem,ListItemText,Paper,Snackbar,Switch,TextField,Toolbar,Tooltip,Typography,useTheme!=!@mui/material */ \"(pages-dir-browser)/__barrel_optimize__?names=Alert,AppBar,Avatar,Box,Button,Card,CardContent,CardHeader,Checkbox,Chip,CircularProgress,Container,FormControlLabel,Grid,IconButton,List,ListItem,ListItemText,Paper,Snackbar,Switch,TextField,Toolbar,Tooltip,Typography,useTheme!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/ArrowDownward */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ArrowDownward.js\");\n/* harmony import */ var _mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/icons-material/ArrowUpward */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ArrowUpward.js\");\n/* harmony import */ var _mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/icons-material/DeleteSweep */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/DeleteSweep.js\");\n/* harmony import */ var _mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/Hearing */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Hearing.js\");\n/* harmony import */ var _mui_icons_material_Mic__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/Mic */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Mic.js\");\n/* harmony import */ var _mui_icons_material_MicOff__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/icons-material/MicOff */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/MicOff.js\");\n/* harmony import */ var _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/Person */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _mui_icons_material_PictureInPictureAlt__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/PictureInPictureAlt */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/PictureInPictureAlt.js\");\n/* harmony import */ var _mui_icons_material_PlaylistAddCheck__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/icons-material/PlaylistAddCheck */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/PlaylistAddCheck.js\");\n/* harmony import */ var _mui_icons_material_ScreenShare__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/ScreenShare */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ScreenShare.js\");\n/* harmony import */ var _mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/icons-material/Send */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Send.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/icons-material/SmartToy */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/SmartToy.js\");\n/* harmony import */ var _mui_icons_material_StopScreenShare__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/StopScreenShare */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/StopScreenShare.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @google/generative-ai */ \"(pages-dir-browser)/./node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var highlight_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! highlight.js */ \"(pages-dir-browser)/./node_modules/highlight.js/es/index.js\");\n/* harmony import */ var highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! highlight.js/styles/atom-one-dark.css */ \"(pages-dir-browser)/./node_modules/highlight.js/styles/atom-one-dark.css\");\n/* harmony import */ var highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash.throttle */ \"(pages-dir-browser)/./node_modules/lodash.throttle/index.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! openai */ \"(pages-dir-browser)/./node_modules/openai/index.mjs\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-markdown */ \"(pages-dir-browser)/./node_modules/react-markdown/index.js\");\n/* harmony import */ var react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-scroll-to-bottom */ \"(pages-dir-browser)/./node_modules/react-scroll-to-bottom/lib/esm/index.js\");\n/* harmony import */ var _components_SettingsDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/SettingsDialog */ \"(pages-dir-browser)/./components/SettingsDialog.js\");\n/* harmony import */ var _redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../redux/aiResponseSlice */ \"(pages-dir-browser)/./redux/aiResponseSlice.js\");\n/* harmony import */ var _redux_historySlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../redux/historySlice */ \"(pages-dir-browser)/./redux/historySlice.js\");\n/* harmony import */ var _redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../redux/transcriptionSlice */ \"(pages-dir-browser)/./redux/transcriptionSlice.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../utils/config */ \"(pages-dir-browser)/./utils/config.js\");\n/* harmony import */ var _utils_speechServices__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../utils/speechServices */ \"(pages-dir-browser)/./utils/speechServices.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n// MUI Components\n\n// MUI Icons\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Third-party Libraries\n\n\n\n\n\n\n\n// Speech Services\n// Local Imports\n\n\n\n\n\n\nfunction debounce(func) {\n    let timeout = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;\n    var _this = this;\n    let timer;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        clearTimeout(timer);\n        timer = setTimeout(()=>{\n            func.apply(_this, args);\n        }, timeout);\n    };\n}\nfunction InterviewPage() {\n    _s();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useDispatch)();\n    const transcriptionFromStore = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[transcriptionFromStore]\": (state)=>state.transcription\n    }[\"InterviewPage.useSelector[transcriptionFromStore]\"]);\n    const aiResponseFromStore = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[aiResponseFromStore]\": (state)=>state.aiResponse\n    }[\"InterviewPage.useSelector[aiResponseFromStore]\"]);\n    const history = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[history]\": (state)=>state.history\n    }[\"InterviewPage.useSelector[history]\"]);\n    const theme = (0,_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    const [appConfig, setAppConfig] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)((0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)());\n    const [systemRecognizer, setSystemRecognizer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [micRecognizer, setMicRecognizer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [systemSpeechService, setSystemSpeechService] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [micSpeechService, setMicSpeechService] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [activeSpeechService, setActiveSpeechService] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null); // Track which service is active\n    const [systemAutoMode, setSystemAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(appConfig.systemAutoMode !== undefined ? appConfig.systemAutoMode : true);\n    const [openAI, setOpenAI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [settingsOpen, setSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isMicrophoneActive, setIsMicrophoneActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isSystemAudioActive, setIsSystemAudioActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('info');\n    const [selectedQuestions, setSelectedQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isManualMode, setIsManualMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(appConfig.isManualMode !== undefined ? appConfig.isManualMode : false);\n    const [micTranscription, setMicTranscription] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isAILoading, setIsAILoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [autoScroll, setAutoScroll] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [aiResponseSortOrder, setAiResponseSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('newestAtTop');\n    const [isPipWindowActive, setIsPipWindowActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const pipWindowRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const documentPipWindowRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const documentPipIframeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const systemInterimTranscription = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)('');\n    const micInterimTranscription = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)('');\n    const silenceTimer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const finalTranscript = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)({\n        system: '',\n        microphone: ''\n    });\n    const isManualModeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(isManualMode);\n    const systemAutoModeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(systemAutoMode);\n    const throttledDispatchSetAIResponseRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const showSnackbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"InterviewPage.useCallback[showSnackbar]\": function(message) {\n            let severity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n            setSnackbarMessage(message);\n            setSnackbarSeverity(severity);\n            setSnackbarOpen(true);\n        }\n    }[\"InterviewPage.useCallback[showSnackbar]\"], []);\n    const handleSettingsSaved = ()=>{\n        const newConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        setAppConfig(newConfig);\n        setIsAILoading(true);\n        setSystemAutoMode(newConfig.systemAutoMode !== undefined ? newConfig.systemAutoMode : true);\n        setIsManualMode(newConfig.isManualMode !== undefined ? newConfig.isManualMode : false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            const currentConfig = appConfig;\n            const initializeAI = {\n                \"InterviewPage.useEffect.initializeAI\": ()=>{\n                    try {\n                        if (currentConfig.aiModel.startsWith('gemini')) {\n                            if (!currentConfig.geminiKey) {\n                                showSnackbar('Gemini API key required. Please set it in Settings.', 'error');\n                                setOpenAI(null);\n                                return;\n                            }\n                            const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_4__.GoogleGenerativeAI(currentConfig.geminiKey);\n                            setOpenAI(genAI);\n                        } else {\n                            if (!currentConfig.openaiKey) {\n                                showSnackbar('OpenAI API key required. Please set it in Settings.', 'error');\n                                setOpenAI(null);\n                                return;\n                            }\n                            const openaiClient = new openai__WEBPACK_IMPORTED_MODULE_17__[\"default\"]({\n                                apiKey: currentConfig.openaiKey,\n                                dangerouslyAllowBrowser: true\n                            });\n                            setOpenAI(openaiClient);\n                        }\n                    } catch (error) {\n                        console.error('Error initializing AI client:', error);\n                        showSnackbar('Error initializing AI client: ' + error.message, 'error');\n                        setOpenAI(null);\n                    } finally{\n                        setIsAILoading(false);\n                    }\n                }\n            }[\"InterviewPage.useEffect.initializeAI\"];\n            if (isAILoading) initializeAI();\n        }\n    }[\"InterviewPage.useEffect\"], [\n        appConfig,\n        isAILoading,\n        showSnackbar\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            isManualModeRef.current = isManualMode;\n        }\n    }[\"InterviewPage.useEffect\"], [\n        isManualMode\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            systemAutoModeRef.current = systemAutoMode;\n        }\n    }[\"InterviewPage.useEffect\"], [\n        systemAutoMode\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            throttledDispatchSetAIResponseRef.current = lodash_throttle__WEBPACK_IMPORTED_MODULE_7___default()({\n                \"InterviewPage.useEffect\": (payload)=>{\n                    dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(payload));\n                }\n            }[\"InterviewPage.useEffect\"], 250, {\n                leading: true,\n                trailing: true\n            });\n            return ({\n                \"InterviewPage.useEffect\": ()=>{\n                    if (throttledDispatchSetAIResponseRef.current && typeof throttledDispatchSetAIResponseRef.current.cancel === 'function') {\n                        throttledDispatchSetAIResponseRef.current.cancel();\n                    }\n                }\n            })[\"InterviewPage.useEffect\"];\n        }\n    }[\"InterviewPage.useEffect\"], [\n        dispatch\n    ]);\n    const handleSnackbarClose = ()=>setSnackbarOpen(false);\n    const stopRecording = async (source)=>{\n        const speechService = source === 'system' ? systemSpeechService : micSpeechService;\n        const recognizer = source === 'system' ? systemRecognizer : micRecognizer;\n        try {\n            // Stop new speech service if available\n            if (speechService) {\n                await speechService.stop();\n            }\n            // Fallback to legacy Azure recognizer if still in use\n            if (recognizer && typeof recognizer.stopContinuousRecognitionAsync === 'function') {\n                await recognizer.stopContinuousRecognitionAsync();\n                if (recognizer.audioConfig && recognizer.audioConfig.privSource && recognizer.audioConfig.privSource.privStream) {\n                    const stream = recognizer.audioConfig.privSource.privStream;\n                    if (stream instanceof MediaStream) {\n                        stream.getTracks().forEach((track)=>{\n                            track.stop();\n                        });\n                    }\n                }\n                if (recognizer.audioConfig && typeof recognizer.audioConfig.close === 'function') {\n                    recognizer.audioConfig.close();\n                }\n            }\n        } catch (error) {\n            console.error(\"Error stopping \".concat(source, \" recognition:\"), error);\n            showSnackbar(\"Error stopping \".concat(source, \" audio: \").concat(error.message), 'error');\n        } finally{\n            if (source === 'system') {\n                setIsSystemAudioActive(false);\n                setSystemRecognizer(null);\n                setSystemSpeechService(null);\n            } else {\n                setIsMicrophoneActive(false);\n                setMicRecognizer(null);\n                setMicSpeechService(null);\n                setActiveSpeechService(null); // Clear active service when stopping microphone\n            }\n        }\n    };\n    const handleClearSystemTranscription = ()=>{\n        finalTranscript.current.system = '';\n        systemInterimTranscription.current = '';\n        dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.clearTranscription)());\n    };\n    const handleClearMicTranscription = ()=>{\n        finalTranscript.current.microphone = '';\n        micInterimTranscription.current = '';\n        setMicTranscription('');\n    };\n    const handleTranscriptionEvent = (text, source)=>{\n        const cleanText = text.replace(/\\s+/g, ' ').trim();\n        if (!cleanText) return;\n        finalTranscript.current[source] += cleanText + ' ';\n        if (source === 'system') {\n            dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + systemInterimTranscription.current));\n        } else {\n            setMicTranscription(finalTranscript.current.microphone + micInterimTranscription.current);\n        }\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        const currentSilenceTimerDuration = currentConfig.silenceTimerDuration;\n        if (source === 'system' && systemAutoModeRef.current || source === 'microphone' && !isManualModeRef.current) {\n            clearTimeout(silenceTimer.current);\n            silenceTimer.current = setTimeout(()=>{\n                askOpenAI(finalTranscript.current[source].trim(), source);\n            }, currentSilenceTimerDuration * 1000);\n        }\n    };\n    const handleManualInputChange = (value, source)=>{\n        if (source === 'system') {\n            dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(value));\n            finalTranscript.current.system = value;\n        } else {\n            setMicTranscription(value);\n            finalTranscript.current.microphone = value;\n        }\n    };\n    const handleManualSubmit = (source)=>{\n        const textToSubmit = source === 'system' ? transcriptionFromStore : micTranscription;\n        if (textToSubmit.trim()) {\n            askOpenAI(textToSubmit.trim(), source);\n        } else {\n            showSnackbar('Input is empty.', 'warning');\n        }\n    };\n    const handleKeyPress = (e, source)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleManualSubmit(source);\n        }\n    };\n    const handleCombineAndSubmit = ()=>{\n        if (selectedQuestions.length === 0) {\n            showSnackbar('No questions selected to combine.', 'warning');\n            return;\n        }\n        const questionHistory = history.filter((e)=>e.type === 'question').slice().reverse();\n        const questionTexts = selectedQuestions.map((selectedIndexInReversedArray)=>{\n            var _questionHistory_selectedIndexInReversedArray;\n            return (_questionHistory_selectedIndexInReversedArray = questionHistory[selectedIndexInReversedArray]) === null || _questionHistory_selectedIndexInReversedArray === void 0 ? void 0 : _questionHistory_selectedIndexInReversedArray.text;\n        }).filter((text)=>text);\n        if (questionTexts.length === 0) {\n            showSnackbar('Could not retrieve selected question texts.', 'warning');\n            return;\n        }\n        const combinedText = questionTexts.join('\\n\\n---\\n\\n');\n        askOpenAI(combinedText, 'combined');\n        setSelectedQuestions([]);\n    };\n    // Legacy Azure recognizer function for fallback\n    const createLegacyAzureRecognizer = async (mediaStream, source)=>{\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        if (!currentConfig.azureToken || !currentConfig.azureRegion) {\n            throw new Error('Azure Speech credentials missing');\n        }\n        let audioConfig;\n        try {\n            audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n        } catch (configError) {\n            console.error(\"Error creating AudioConfig for \".concat(source, \":\"), configError);\n            throw new Error(\"Error setting up audio for \".concat(source, \": \").concat(configError.message));\n        }\n        const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(currentConfig.azureToken, currentConfig.azureRegion);\n        speechConfig.speechRecognitionLanguage = currentConfig.azureLanguage;\n        const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);\n        recognizer.recognizing = (s, e)=>{\n            if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                const interimText = e.result.text;\n                if (source === 'system') {\n                    systemInterimTranscription.current = interimText;\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + interimText));\n                } else {\n                    micInterimTranscription.current = interimText;\n                    setMicTranscription(finalTranscript.current.microphone + interimText);\n                }\n            }\n        };\n        recognizer.recognized = (s, e)=>{\n            if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                if (source === 'system') systemInterimTranscription.current = '';\n                else micInterimTranscription.current = '';\n                handleTranscriptionEvent(e.result.text, source);\n            }\n        };\n        recognizer.canceled = (s, e)=>{\n            console.log(\"CANCELED: Reason=\".concat(e.reason, \" for \").concat(source));\n            if (e.reason === SpeechSDK.CancellationReason.Error) {\n                console.error(\"CANCELED: ErrorCode=\".concat(e.errorCode));\n                console.error(\"CANCELED: ErrorDetails=\".concat(e.errorDetails));\n                showSnackbar(\"Speech recognition error for \".concat(source, \": \").concat(e.errorDetails), 'error');\n            }\n            stopRecording(source);\n        };\n        recognizer.sessionStopped = (s, e)=>{\n            console.log(\"Session stopped event for \".concat(source, \".\"));\n            stopRecording(source);\n        };\n        try {\n            await recognizer.startContinuousRecognitionAsync();\n            showSnackbar(\"\".concat(source === 'system' ? 'System audio' : 'Microphone', \" recording started (Azure Speech).\"), 'success');\n            return recognizer;\n        } catch (error) {\n            console.error(\"Error starting \".concat(source, \" continuous recognition:\"), error);\n            if (audioConfig && typeof audioConfig.close === 'function') audioConfig.close();\n            throw error;\n        }\n    };\n    const createSpeechRecognizer = async (mediaStream, source)=>{\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        // Create speech service callbacks\n        const callbacks = {\n            onStart: (source)=>{\n                console.log(\"Speech recognition started for \".concat(source));\n                showSnackbar(\"\".concat(source === 'system' ? 'System audio' : 'Microphone', \" recording started.\"), 'success');\n            },\n            onInterimResult: (text, source)=>{\n                console.log(\"Interim result for \".concat(source, \":\"), text);\n                if (source === 'system') {\n                    systemInterimTranscription.current = text;\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + text));\n                } else {\n                    micInterimTranscription.current = text;\n                    setMicTranscription(finalTranscript.current.microphone + text);\n                }\n            },\n            onFinalResult: (text, source)=>{\n                console.log(\"Final result for \".concat(source, \":\"), text);\n                // Clear interim transcription\n                if (source === 'system') systemInterimTranscription.current = '';\n                else micInterimTranscription.current = '';\n                // Handle the final transcription\n                handleTranscriptionEvent(text, source);\n            },\n            onError: (error, source)=>{\n                console.error(\"Speech recognition error for \".concat(source, \":\"), error);\n                showSnackbar(\"Speech recognition error for \".concat(source, \": \").concat(error.message), 'error');\n                stopRecording(source);\n            },\n            onStop: (source)=>{\n                console.log(\"Speech recognition stopped for \".concat(source));\n            }\n        };\n        try {\n            // Check if we have the required credentials for the selected service\n            const selectedService = currentConfig.speechService || 'deepgram';\n            let configToUse = currentConfig;\n            if (selectedService === 'deepgram' && !currentConfig.deepgramKey) {\n                // If Deepgram is selected but no key is provided, check if Azure is available\n                if (currentConfig.azureToken && currentConfig.azureRegion) {\n                    console.log('Deepgram key missing, falling back to Azure Speech Services');\n                    configToUse = {\n                        ...currentConfig,\n                        speechService: 'azure'\n                    };\n                    showSnackbar('Using Azure Speech Services (Deepgram key not configured)', 'info');\n                } else {\n                    throw new Error('Deepgram API key is required. Get a free API key from deepgram.com and configure it in Settings (⚙️ icon).');\n                }\n            } else if (selectedService === 'azure' && (!currentConfig.azureToken || !currentConfig.azureRegion)) {\n                // If Azure is selected but credentials are missing, check if Deepgram is available\n                if (currentConfig.deepgramKey) {\n                    console.log('Azure credentials missing, falling back to Deepgram');\n                    configToUse = {\n                        ...currentConfig,\n                        speechService: 'deepgram'\n                    };\n                    showSnackbar('Using Deepgram (Azure credentials not configured)', 'info');\n                } else {\n                    throw new Error('Azure Speech credentials are required. Please configure them in Settings, or provide Deepgram API key for fallback.');\n                }\n            }\n            // Create the appropriate speech service\n            const speechService = (0,_utils_speechServices__WEBPACK_IMPORTED_MODULE_14__.createSpeechService)(configToUse, callbacks);\n            // Track which service is being used\n            setActiveSpeechService(configToUse.speechService);\n            // Start the speech service\n            await speechService.start(mediaStream, source);\n            // Show which service is active\n            const serviceName = configToUse.speechService === 'deepgram' ? 'Deepgram' : 'Azure Speech';\n            showSnackbar(\"Using \".concat(serviceName, \" for \").concat(source === 'system' ? 'system audio' : 'microphone'), 'info');\n            return speechService;\n        } catch (error) {\n            console.error(\"Error creating speech service for \".concat(source, \":\"), error);\n            // Try legacy Azure recognizer as final fallback\n            if (currentConfig.azureToken && currentConfig.azureRegion) {\n                try {\n                    console.log(\"Attempting legacy Azure fallback for \".concat(source));\n                    const legacyRecognizer = await createLegacyAzureRecognizer(mediaStream, source);\n                    showSnackbar(\"Using legacy Azure Speech Services for \".concat(source), 'warning');\n                    return legacyRecognizer;\n                } catch (legacyError) {\n                    console.error(\"Legacy Azure fallback also failed for \".concat(source, \":\"), legacyError);\n                }\n            }\n            // Provide helpful error messages\n            if (error.message.includes('API key') || error.message.includes('credentials')) {\n                showSnackbar(\"Speech service configuration required. Please configure API keys in Settings (⚙️ icon).\", 'error');\n            } else {\n                showSnackbar(\"Failed to start \".concat(source, \" recognition: \").concat(error.message), 'error');\n            }\n            mediaStream.getTracks().forEach((track)=>track.stop());\n            return null;\n        }\n    };\n    const startSystemAudioRecognition = async ()=>{\n        if (isSystemAudioActive) {\n            await stopRecording('system');\n            return;\n        }\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {\n            showSnackbar('Screen sharing is not supported by your browser.', 'error');\n            setIsSystemAudioActive(false);\n            return;\n        }\n        try {\n            const mediaStream = await navigator.mediaDevices.getDisplayMedia({\n                audio: true,\n                video: {\n                    displaySurface: 'browser',\n                    logicalSurface: true\n                }\n            });\n            const audioTracks = mediaStream.getAudioTracks();\n            if (audioTracks.length === 0) {\n                showSnackbar('No audio track detected. Please ensure you share a tab with audio.', 'warning');\n                mediaStream.getTracks().forEach((track)=>track.stop());\n                return;\n            }\n            if (systemRecognizer) {\n                await stopRecording('system');\n            }\n            const speechServiceInstance = await createSpeechRecognizer(mediaStream, 'system');\n            if (speechServiceInstance) {\n                // Check if it's a new speech service or legacy recognizer\n                if (speechServiceInstance.start && speechServiceInstance.stop) {\n                    // New speech service\n                    setSystemSpeechService(speechServiceInstance);\n                } else {\n                    // Legacy Azure recognizer\n                    setSystemRecognizer(speechServiceInstance);\n                }\n                setIsSystemAudioActive(true);\n                mediaStream.getTracks().forEach((track)=>{\n                    track.onended = ()=>{\n                        showSnackbar('Tab sharing ended.', 'info');\n                        stopRecording('system');\n                    };\n                });\n            } else {\n                mediaStream.getTracks().forEach((track)=>track.stop());\n            }\n        } catch (error) {\n            console.error('System audio capture error:', error);\n            if (error.name === \"NotAllowedError\") {\n                showSnackbar('Permission denied for screen recording. Please allow access.', 'error');\n            } else if (error.name === \"NotFoundError\") {\n                showSnackbar('No suitable tab/window found to share.', 'error');\n            } else if (error.name === \"NotSupportedError\") {\n                showSnackbar('System audio capture not supported by your browser.', 'error');\n            } else {\n                showSnackbar(\"Failed to start system audio capture: \".concat(error.message || 'Unknown error'), 'error');\n            }\n            setIsSystemAudioActive(false);\n        }\n    };\n    const startMicrophoneRecognition = async ()=>{\n        if (isMicrophoneActive) {\n            await stopRecording('microphone');\n            return;\n        }\n        try {\n            const mediaStream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            if (micSpeechService) await stopRecording('microphone');\n            const speechServiceInstance = await createSpeechRecognizer(mediaStream, 'microphone');\n            if (speechServiceInstance) {\n                // Check if it's a new speech service or legacy recognizer\n                if (speechServiceInstance.start && speechServiceInstance.stop) {\n                    // New speech service\n                    setMicSpeechService(speechServiceInstance);\n                } else {\n                    // Legacy Azure recognizer\n                    setMicRecognizer(speechServiceInstance);\n                }\n                setIsMicrophoneActive(true);\n            } else {\n                mediaStream.getTracks().forEach((track)=>track.stop());\n            }\n        } catch (error) {\n            console.error('Microphone capture error:', error);\n            if (error.name === \"NotAllowedError\" || error.name === \"NotFoundError\") {\n                showSnackbar('Permission denied for microphone. Please allow access.', 'error');\n            } else {\n                showSnackbar(\"Failed to access microphone: \".concat(error.message || 'Unknown error'), 'error');\n            }\n            setIsMicrophoneActive(false);\n        }\n    };\n    // Helper function to determine reasoning effort for future advanced models\n    const getReasoningEffort = (text, source)=>{\n        const textLength = text.length;\n        const isComplexQuestion = text.includes('?') && textLength > 100;\n        const isCombined = source === 'combined';\n        if (isCombined || isComplexQuestion || textLength > 500) return 'high';\n        if (textLength > 200) return 'medium';\n        return 'low';\n    };\n    const askOpenAI = async (text, source)=>{\n        if (!text.trim()) {\n            showSnackbar('No input text to process.', 'warning');\n            return;\n        }\n        if (!openAI || isAILoading) {\n            showSnackbar('AI client is not ready. Please wait or check settings.', 'warning');\n            return;\n        }\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        const lengthSettings = {\n            concise: {\n                temperature: 0.4,\n                maxTokens: 250\n            },\n            medium: {\n                temperature: 0.6,\n                maxTokens: 500\n            },\n            lengthy: {\n                temperature: 0.8,\n                maxTokens: 1000\n            }\n        };\n        const { temperature, maxTokens } = lengthSettings[currentConfig.responseLength || 'medium'];\n        setIsProcessing(true);\n        const timestamp = new Date().toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n        let streamedResponse = '';\n        dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n            type: 'question',\n            text,\n            timestamp,\n            source,\n            status: 'pending'\n        }));\n        dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(''));\n        try {\n            const conversationHistoryForAPI = history.filter((e)=>e.text && (e.type === 'question' || e.type === 'response') && e.status !== 'pending').slice(-6).map((event)=>({\n                    role: event.type === 'question' ? 'user' : 'assistant',\n                    content: event.text\n                }));\n            if (currentConfig.aiModel.startsWith('gemini')) {\n                // Enhanced Gemini API configuration for 2.5 models\n                const modelConfig = {\n                    model: currentConfig.aiModel,\n                    generationConfig: {\n                        temperature,\n                        maxOutputTokens: maxTokens\n                    },\n                    systemInstruction: {\n                        parts: [\n                            {\n                                text: currentConfig.gptSystemPrompt\n                            }\n                        ]\n                    }\n                };\n                // Add thinking configuration for Gemini 2.5 models\n                if (currentConfig.aiModel.includes('2.5') && currentConfig.thinkingBudget !== undefined) {\n                    if (currentConfig.thinkingBudget === 0) {\n                        // Disable thinking\n                        modelConfig.generationConfig.thinkingConfig = {\n                            thinkingBudget: 0\n                        };\n                    } else if (currentConfig.thinkingBudget > 0) {\n                        // Custom thinking budget\n                        modelConfig.generationConfig.thinkingConfig = {\n                            thinkingBudget: currentConfig.thinkingBudget\n                        };\n                    }\n                // If thinkingBudget is null, use default (thinking enabled)\n                }\n                const model = openAI.getGenerativeModel(modelConfig);\n                const chat = model.startChat({\n                    history: conversationHistoryForAPI.map((msg)=>({\n                            role: msg.role === 'user' ? 'user' : 'model',\n                            parts: [\n                                {\n                                    text: msg.content\n                                }\n                            ]\n                        }))\n                });\n                const result = await chat.sendMessageStream(text);\n                for await (const chunk of result.stream){\n                    if (chunk && typeof chunk.text === 'function') {\n                        const chunkText = chunk.text();\n                        streamedResponse += chunkText;\n                        if (throttledDispatchSetAIResponseRef.current) {\n                            throttledDispatchSetAIResponseRef.current(streamedResponse);\n                        }\n                    }\n                }\n            } else {\n                // Enhanced OpenAI API usage with future-ready parameters\n                const messages = [\n                    {\n                        role: 'system',\n                        content: currentConfig.gptSystemPrompt\n                    },\n                    ...conversationHistoryForAPI,\n                    {\n                        role: 'user',\n                        content: text\n                    }\n                ];\n                const requestParams = {\n                    model: currentConfig.aiModel,\n                    messages,\n                    stream: true\n                };\n                // Set temperature based on model capabilities\n                if (currentConfig.aiModel.startsWith('o1')) {\n                // o1 models don't support temperature parameter at all\n                // Don't set temperature for o1 models\n                } else if (currentConfig.aiModel.startsWith('gpt-5')) {\n                    // GPT-5 models may have temperature restrictions, use default value\n                    requestParams.temperature = 1;\n                } else {\n                    // Standard models support configurable temperature\n                    requestParams.temperature = temperature;\n                }\n                // Use the correct token parameter based on model\n                if (currentConfig.aiModel.startsWith('gpt-5') || currentConfig.aiModel.startsWith('o1')) {\n                    requestParams.max_completion_tokens = maxTokens;\n                } else {\n                    requestParams.max_tokens = maxTokens;\n                }\n                // Add model-specific parameters for different model types\n                if (currentConfig.aiModel.startsWith('gpt-5')) {\n                    // GPT-5 models support new parameters\n                    if (currentConfig.reasoningEffort) {\n                        requestParams.reasoning_effort = currentConfig.reasoningEffort;\n                    }\n                    if (currentConfig.verbosity !== undefined) {\n                        requestParams.verbosity = currentConfig.verbosity === 0 ? 'low' : currentConfig.verbosity === 1 ? 'medium' : 'high';\n                    }\n                } else if (currentConfig.aiModel.startsWith('o1')) {\n                    // o1 models use different parameters and don't support streaming\n                    // Remove streaming for o1 models\n                    requestParams.stream = false;\n                    // o1 models don't use system messages in the same way\n                    // Move system prompt to the first user message\n                    requestParams.messages = [\n                        {\n                            role: 'user',\n                            content: \"\".concat(currentConfig.gptSystemPrompt, \"\\n\\n\").concat(text)\n                        },\n                        ...conversationHistoryForAPI.slice(1) // Skip the system message\n                    ];\n                // o1 models require temperature = 1 (already set above)\n                // No additional temperature modification needed\n                }\n                if (currentConfig.aiModel.startsWith('o1')) {\n                    var _response_choices__message, _response_choices_;\n                    // o1 models don't support streaming, handle as single response\n                    const response = await openAI.chat.completions.create(requestParams);\n                    streamedResponse = ((_response_choices_ = response.choices[0]) === null || _response_choices_ === void 0 ? void 0 : (_response_choices__message = _response_choices_.message) === null || _response_choices__message === void 0 ? void 0 : _response_choices__message.content) || '';\n                    if (throttledDispatchSetAIResponseRef.current) {\n                        throttledDispatchSetAIResponseRef.current(streamedResponse);\n                    }\n                } else {\n                    // Standard streaming for GPT-5, GPT-4o, and other models\n                    const stream = await openAI.chat.completions.create(requestParams);\n                    for await (const chunk of stream){\n                        var _chunk_choices__delta, _chunk_choices_;\n                        const chunkText = ((_chunk_choices_ = chunk.choices[0]) === null || _chunk_choices_ === void 0 ? void 0 : (_chunk_choices__delta = _chunk_choices_.delta) === null || _chunk_choices__delta === void 0 ? void 0 : _chunk_choices__delta.content) || '';\n                        streamedResponse += chunkText;\n                        if (throttledDispatchSetAIResponseRef.current) {\n                            throttledDispatchSetAIResponseRef.current(streamedResponse);\n                        }\n                    }\n                }\n            }\n            if (throttledDispatchSetAIResponseRef.current && typeof throttledDispatchSetAIResponseRef.current.cancel === 'function') {\n                throttledDispatchSetAIResponseRef.current.cancel();\n            }\n            dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(streamedResponse));\n            const finalTimestamp = new Date().toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n            });\n            dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n                type: 'response',\n                text: streamedResponse,\n                timestamp: finalTimestamp,\n                status: 'completed'\n            }));\n        } catch (error) {\n            console.error(\"AI request error:\", error);\n            const errorMessage = \"AI request failed: \".concat(error.message || 'Unknown error');\n            showSnackbar(errorMessage, 'error');\n            dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(\"Error: \".concat(errorMessage)));\n            dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n                type: 'response',\n                text: \"Error: \".concat(errorMessage),\n                timestamp: new Date().toLocaleTimeString([], {\n                    hour: '2-digit',\n                    minute: '2-digit'\n                }),\n                status: 'error'\n            }));\n        } finally{\n            if (source === 'system' && systemAutoModeRef.current || source === 'microphone' && !isManualModeRef.current) {\n                finalTranscript.current[source] = '';\n                if (source === 'system') {\n                    systemInterimTranscription.current = '';\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(''));\n                } else {\n                    micInterimTranscription.current = '';\n                    setMicTranscription('');\n                }\n            }\n            setIsProcessing(false);\n        }\n    };\n    const formatAndDisplayResponse = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (response)=>{\n            if (!response) return null;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                components: {\n                    code (param) {\n                        let { node, inline, className, children, ...props } = param;\n                        const match = /language-(\\w+)/.exec(className || '');\n                        return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                            sx: {\n                                my: 1,\n                                position: 'relative',\n                                '& pre': {\n                                    borderRadius: '4px',\n                                    padding: '12px !important',\n                                    fontSize: '0.875rem',\n                                    overflowX: 'auto',\n                                    whiteSpace: 'pre-wrap',\n                                    wordBreak: 'break-all'\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: className,\n                                    ...props,\n                                    dangerouslySetInnerHTML: {\n                                        __html: highlight_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"].highlight(String(children).replace(/\\n$/, ''), {\n                                            language: match[1],\n                                            ignoreIllegals: true\n                                        }).value\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 804,\n                                    columnNumber: 22\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 804,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 792,\n                            columnNumber: 15\n                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: className,\n                            ...props,\n                            style: {\n                                backgroundColor: 'rgba(0,0,0,0.05)',\n                                padding: '2px 4px',\n                                borderRadius: '4px',\n                                fontFamily: 'monospace',\n                                fontSize: '0.875rem',\n                                wordBreak: 'break-all'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 807,\n                            columnNumber: 15\n                        }, void 0);\n                    },\n                    p: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                paragraph: true,\n                                ...props,\n                                sx: {\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 823,\n                                columnNumber: 38\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    strong: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"strong\",\n                                fontWeight: \"bold\",\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 824,\n                                columnNumber: 43\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    em: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"em\",\n                                fontStyle: \"italic\",\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 825,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    ul: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"ul\",\n                                sx: {\n                                    pl: 2.5,\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 826,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    ol: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"ol\",\n                                sx: {\n                                    pl: 2.5,\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 827,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    li: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"li\",\n                                sx: {\n                                    mb: 0.25,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 828,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"]\n                },\n                children: response\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 787,\n                columnNumber: 7\n            }, this);\n        }\n    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"], []);\n    const renderHistoryItem = (item, index)=>{\n        if (item.type !== 'response') return null;\n        const Icon = _mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n        const title = 'AI Assistant';\n        const avatarBgColor = theme.palette.secondary.light;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n            sx: {\n                alignItems: 'flex-start',\n                px: 0,\n                py: 1.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                    sx: {\n                        bgcolor: avatarBgColor,\n                        mr: 2,\n                        mt: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        sx: {\n                            color: theme.palette.getContrastText(avatarBgColor)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 845,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 844,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Paper, {\n                    variant: \"outlined\",\n                    sx: {\n                        p: 1.5,\n                        flexGrow: 1,\n                        bgcolor: theme.palette.background.default,\n                        borderColor: theme.palette.divider,\n                        overflowX: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                            sx: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                mb: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"subtitle2\",\n                                    fontWeight: \"bold\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 849,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"caption\",\n                                    color: \"text.secondary\",\n                                    children: item.timestamp\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 850,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 848,\n                            columnNumber: 11\n                        }, this),\n                        formatAndDisplayResponse(item.text)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 847,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, \"response-\".concat(index), true, {\n            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n            lineNumber: 843,\n            columnNumber: 7\n        }, this);\n    };\n    const renderQuestionHistoryItem = (item, index)=>{\n        const Icon = item.source === 'system' ? _mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__[\"default\"] : _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__[\"default\"];\n        const title = item.source === 'system' ? 'Interviewer' : 'Candidate';\n        const avatarBgColor = item.source === 'system' ? theme.palette.info.light : theme.palette.success.light;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n            secondaryAction: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Checkbox, {\n                edge: \"end\",\n                checked: selectedQuestions.includes(index),\n                onChange: ()=>{\n                    setSelectedQuestions((prev)=>prev.includes(index) ? prev.filter((x)=>x !== index) : [\n                            ...prev,\n                            index\n                        ]);\n                },\n                color: \"secondary\",\n                size: \"small\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 867,\n                columnNumber: 11\n            }, void 0),\n            disablePadding: true,\n            sx: {\n                py: 0.5,\n                display: 'flex',\n                alignItems: 'center'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                    sx: {\n                        bgcolor: avatarBgColor,\n                        mr: 1.5,\n                        width: 32,\n                        height: 32,\n                        fontSize: '1rem'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        fontSize: \"small\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 883,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 882,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItemText, {\n                    primary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                        variant: \"body2\",\n                        noWrap: true,\n                        sx: {\n                            fontWeight: selectedQuestions.includes(index) ? 'bold' : 'normal',\n                            display: '-webkit-box',\n                            WebkitLineClamp: 2,\n                            WebkitBoxOrient: 'vertical',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis'\n                        },\n                        children: item.text\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 887,\n                        columnNumber: 13\n                    }, void 0),\n                    secondary: \"\".concat(title, \" - \").concat(item.timestamp)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 885,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, \"question-hist-\".concat(index), true, {\n            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n            lineNumber: 864,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSortOrderToggle = ()=>{\n        setAiResponseSortOrder((prev)=>prev === 'newestAtBottom' ? 'newestAtTop' : 'newestAtBottom');\n    };\n    const getAiResponsesToDisplay = ()=>{\n        let responses = history.filter((item)=>item.type === 'response').slice();\n        const currentStreamingText = aiResponseFromStore;\n        if (isProcessing && currentStreamingText && currentStreamingText.trim() !== '') {\n            responses.push({\n                text: currentStreamingText,\n                timestamp: 'Streaming...',\n                type: 'current_streaming'\n            });\n        }\n        if (aiResponseSortOrder === 'newestAtTop') {\n            return responses.reverse();\n        }\n        return responses;\n    };\n    const togglePipWindow = async ()=>{\n        if (isPipWindowActive) {\n            if (documentPipWindowRef.current && typeof documentPipWindowRef.current.close === 'function') {\n                try {\n                    await documentPipWindowRef.current.close();\n                } catch (e) {\n                    console.error(\"Error closing document PiP window:\", e);\n                }\n            } else if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                pipWindowRef.current.close();\n            }\n            return; // State update will be handled by pagehide/interval listeners\n        }\n        const addResizeListener = (pipWindow)=>{\n            const handlePipResize = debounce(()=>{\n                if (!pipWindow || pipWindow.closed) return;\n                const target = documentPipIframeRef.current ? documentPipIframeRef.current.contentWindow : pipWindow;\n                if (target) {\n                    target.postMessage({\n                        type: 'PIP_RESIZE',\n                        payload: {\n                            width: pipWindow.innerWidth,\n                            height: pipWindow.innerHeight\n                        }\n                    }, '*');\n                }\n            }, 50);\n            pipWindow.addEventListener('resize', handlePipResize);\n            return ()=>pipWindow.removeEventListener('resize', handlePipResize); // Return a cleanup function\n        };\n        if (window.documentPictureInPicture && typeof window.documentPictureInPicture.requestWindow === 'function') {\n            try {\n                const pipOptions = {\n                    width: 400,\n                    height: 300\n                };\n                const requestedPipWindow = await window.documentPictureInPicture.requestWindow(pipOptions);\n                documentPipWindowRef.current = requestedPipWindow;\n                setIsPipWindowActive(true);\n                const iframe = documentPipWindowRef.current.document.createElement('iframe');\n                iframe.src = '/pip-log';\n                iframe.style.width = '100%';\n                iframe.style.height = '100%';\n                iframe.style.border = 'none';\n                documentPipWindowRef.current.document.body.style.margin = '0';\n                documentPipWindowRef.current.document.body.style.overflow = 'hidden';\n                documentPipWindowRef.current.document.body.append(iframe);\n                documentPipIframeRef.current = iframe;\n                const removeResizeListener = addResizeListener(documentPipWindowRef.current);\n                iframe.onload = ()=>{\n                    if (documentPipIframeRef.current && documentPipIframeRef.current.contentWindow) {\n                        documentPipIframeRef.current.contentWindow.postMessage({\n                            type: 'AI_LOG_DATA',\n                            payload: {\n                                historicalResponses: history.filter((item)=>item.type === 'response'),\n                                currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                                isProcessing: isProcessing,\n                                sortOrder: aiResponseSortOrder\n                            }\n                        }, '*');\n                    }\n                };\n                documentPipWindowRef.current.addEventListener('pagehide', ()=>{\n                    removeResizeListener();\n                    setIsPipWindowActive(false);\n                    documentPipWindowRef.current = null;\n                    documentPipIframeRef.current = null;\n                });\n                showSnackbar('Native PiP window opened.', 'success');\n                return;\n            } catch (err) {\n                console.error('Document Picture-in-Picture API error:', err);\n                showSnackbar(\"Native PiP not available or failed. Trying popup. (\".concat(err.message, \")\"), 'warning');\n            }\n        }\n        pipWindowRef.current = window.open('/pip-log', 'AIResponsePiP', 'width=400,height=550,resizable=yes,scrollbars=yes,status=no,toolbar=no,menubar=no,location=no,noopener,noreferrer,popup=yes');\n        if (pipWindowRef.current) {\n            setIsPipWindowActive(true);\n            const removeResizeListener = addResizeListener(pipWindowRef.current);\n            pipWindowRef.current.onload = ()=>{\n                if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                    pipWindowRef.current.postMessage({\n                        type: 'AI_LOG_DATA',\n                        payload: {\n                            historicalResponses: history.filter((item)=>item.type === 'response'),\n                            currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                            isProcessing: isProcessing,\n                            sortOrder: aiResponseSortOrder\n                        }\n                    }, '*');\n                }\n            };\n            const pipCheckInterval = setInterval(()=>{\n                if (pipWindowRef.current && pipWindowRef.current.closed) {\n                    clearInterval(pipCheckInterval);\n                    removeResizeListener();\n                    setIsPipWindowActive(false);\n                    pipWindowRef.current = null;\n                }\n            }, 500);\n            if (pipWindowRef.current) pipWindowRef.current._pipIntervalId = pipCheckInterval;\n        } else {\n            showSnackbar('Failed to open PiP window. Please check popup blocker settings.', 'error');\n            setIsPipWindowActive(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            return ({\n                \"InterviewPage.useEffect\": ()=>{\n                    if (pipWindowRef.current && pipWindowRef.current._pipIntervalId) {\n                        clearInterval(pipWindowRef.current._pipIntervalId);\n                    }\n                    if (documentPipWindowRef.current && typeof documentPipWindowRef.current.close === 'function') {\n                        try {\n                            documentPipWindowRef.current.close();\n                        } catch (e) {}\n                    }\n                }\n            })[\"InterviewPage.useEffect\"];\n        }\n    }[\"InterviewPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            let targetWindowForMessage = null;\n            if (documentPipWindowRef.current && documentPipIframeRef.current && documentPipIframeRef.current.contentWindow) {\n                targetWindowForMessage = documentPipIframeRef.current.contentWindow;\n            } else if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                targetWindowForMessage = pipWindowRef.current;\n            }\n            if (isPipWindowActive && targetWindowForMessage) {\n                try {\n                    targetWindowForMessage.postMessage({\n                        type: 'AI_LOG_DATA',\n                        payload: {\n                            historicalResponses: history.filter({\n                                \"InterviewPage.useEffect\": (item)=>item.type === 'response'\n                            }[\"InterviewPage.useEffect\"]),\n                            currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                            isProcessing: isProcessing,\n                            sortOrder: aiResponseSortOrder\n                        }\n                    }, '*');\n                } catch (e) {\n                    console.warn(\"Could not post message to PiP window:\", e);\n                }\n            }\n        }\n    }[\"InterviewPage.useEffect\"], [\n        history,\n        aiResponseFromStore,\n        isPipWindowActive,\n        aiResponseSortOrder,\n        isProcessing\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"88c8eba4614332d8\",\n                            [\n                                theme.palette.background.paper,\n                                theme.palette.grey[400],\n                                theme.palette.background.paper,\n                                theme.palette.grey[500],\n                                theme.palette.grey[400],\n                                theme.palette.background.paper\n                            ]\n                        ]\n                    ]),\n                    children: \"Interview Copilot - Active Session\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 1069,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 1068,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                sx: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    height: '100vh'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.AppBar, {\n                        position: \"static\",\n                        color: \"default\",\n                        elevation: 1,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Toolbar, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    sx: {\n                                        mr: 2,\n                                        color: 'primary.main'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1074,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"h6\",\n                                    component: \"div\",\n                                    sx: {\n                                        flexGrow: 1,\n                                        color: 'text.primary'\n                                    },\n                                    children: \"Interview Copilot\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1075,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                    title: \"Settings\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                        color: \"primary\",\n                                        onClick: ()=>setSettingsOpen(true),\n                                        \"aria-label\": \"settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1080,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1079,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1078,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1073,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1072,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Container, {\n                        maxWidth: \"xl\",\n                        sx: {\n                            flexGrow: 1,\n                            py: 2,\n                            display: 'flex',\n                            flexDirection: 'column'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 3,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column',\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                    title: \"System Audio (Interviewer)\",\n                                                    avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1091,\n                                                        columnNumber: 72\n                                                    }, void 0),\n                                                    sx: {\n                                                        pb: 1\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1091,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                            control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                                checked: systemAutoMode,\n                                                                onChange: (e)=>setSystemAutoMode(e.target.checked),\n                                                                color: \"primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1094,\n                                                                columnNumber: 30\n                                                            }, void 0),\n                                                            label: \"Auto-Submit Question\",\n                                                            sx: {\n                                                                mb: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1093,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.TextField, {\n                                                            fullWidth: true,\n                                                            multiline: true,\n                                                            rows: 3,\n                                                            variant: \"outlined\",\n                                                            value: transcriptionFromStore,\n                                                            onChange: (e)=>handleManualInputChange(e.target.value, 'system'),\n                                                            onKeyDown: (e)=>handleKeyPress(e, 'system'),\n                                                            placeholder: \"Interviewer's speech...\",\n                                                            sx: {\n                                                                mb: 2\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1098,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                            sx: {\n                                                                display: 'flex',\n                                                                gap: 1,\n                                                                flexWrap: 'wrap'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                    onClick: startSystemAudioRecognition,\n                                                                    variant: \"contained\",\n                                                                    color: isSystemAudioActive ? 'error' : 'primary',\n                                                                    startIcon: isSystemAudioActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_StopScreenShare__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1114,\n                                                                        columnNumber: 56\n                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ScreenShare__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1114,\n                                                                        columnNumber: 82\n                                                                    }, void 0),\n                                                                    sx: {\n                                                                        flexGrow: 1\n                                                                    },\n                                                                    children: isSystemAudioActive ? 'Stop System Audio' : 'Record System Audio'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1110,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                                    variant: \"caption\",\n                                                                    sx: {\n                                                                        mt: 1,\n                                                                        display: 'block',\n                                                                        width: '100%'\n                                                                    },\n                                                                    children: isSystemAudioActive ? 'Recording system audio...' : 'Select \"Chrome Tab\" and check \"Share audio\" when prompted.'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1119,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                                    title: \"Clear System Transcription\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                        onClick: handleClearSystemTranscription,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                            lineNumber: 1123,\n                                                                            columnNumber: 76\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1123,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1122,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                !systemAutoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                    onClick: ()=>handleManualSubmit('system'),\n                                                                    variant: \"outlined\",\n                                                                    color: \"primary\",\n                                                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1130,\n                                                                        columnNumber: 36\n                                                                    }, void 0),\n                                                                    disabled: isProcessing || !transcriptionFromStore.trim(),\n                                                                    children: \"Submit\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1126,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1109,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1092,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1090,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                            sx: {\n                                                flexGrow: 1,\n                                                display: 'flex',\n                                                flexDirection: 'column'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                    title: \"Question History\",\n                                                    avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_PlaylistAddCheck__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1142,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                        variant: \"contained\",\n                                                        size: \"small\",\n                                                        onClick: handleCombineAndSubmit,\n                                                        disabled: selectedQuestions.length === 0 || isProcessing,\n                                                        startIcon: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CircularProgress, {\n                                                            size: 16,\n                                                            color: \"inherit\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1149,\n                                                            columnNumber: 49\n                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1149,\n                                                            columnNumber: 98\n                                                        }, void 0),\n                                                        children: \"Ask Combined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1144,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    sx: {\n                                                        pb: 1,\n                                                        borderBottom: \"1px solid \".concat(theme.palette.divider)\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                    sx: {\n                                                        flexGrow: 1,\n                                                        overflow: 'hidden',\n                                                        p: 0\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"scroll-to-bottom\",\n                                                        followButtonClassName: \"hidden-follow-button\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.List, {\n                                                            dense: true,\n                                                            sx: {\n                                                                pt: 0,\n                                                                px: 1\n                                                            },\n                                                            children: history.filter((e)=>e.type === 'question').slice().reverse().map(renderQuestionHistoryItem)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1158,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1157,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1089,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        sx: {\n                                            flexGrow: 1,\n                                            display: 'flex',\n                                            flexDirection: 'column'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"AI Assistant Log\",\n                                                avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1171,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                            title: isPipWindowActive ? \"Close PiP Log\" : \"Open PiP Log\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                onClick: togglePipWindow,\n                                                                size: \"small\",\n                                                                color: isPipWindowActive ? \"secondary\" : \"default\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_PictureInPictureAlt__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1176,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1174,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                            title: aiResponseSortOrder === 'newestAtTop' ? \"Sort: Newest at Bottom\" : \"Sort: Newest on Top\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                onClick: handleSortOrderToggle,\n                                                                size: \"small\",\n                                                                children: aiResponseSortOrder === 'newestAtTop' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1181,\n                                                                    columnNumber: 68\n                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1181,\n                                                                    columnNumber: 92\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1180,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1179,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                            variant: \"caption\",\n                                                            sx: {\n                                                                mr: 1,\n                                                                fontStyle: 'italic'\n                                                            },\n                                                            children: aiResponseSortOrder === 'newestAtTop' ? \"Newest First\" : \"Oldest First\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1184,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                            control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                                checked: autoScroll,\n                                                                onChange: (e)=>setAutoScroll(e.target.checked),\n                                                                color: \"primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1188,\n                                                                columnNumber: 34\n                                                            }, void 0),\n                                                            label: \"Auto Scroll\",\n                                                            sx: {\n                                                                ml: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1187,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true),\n                                                sx: {\n                                                    borderBottom: \"1px solid \".concat(theme.palette.divider)\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                sx: {\n                                                    flexGrow: 1,\n                                                    overflow: 'hidden',\n                                                    p: 0\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"scroll-to-bottom\",\n                                                    mode: autoScroll ? aiResponseSortOrder === 'newestAtTop' ? \"top\" : \"bottom\" : undefined,\n                                                    followButtonClassName: \"hidden-follow-button\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.List, {\n                                                        sx: {\n                                                            px: 2,\n                                                            py: 1\n                                                        },\n                                                        children: [\n                                                            getAiResponsesToDisplay().map(renderHistoryItem),\n                                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n                                                                sx: {\n                                                                    justifyContent: 'center',\n                                                                    py: 2\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CircularProgress, {\n                                                                        size: 24\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1206,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                                        variant: \"caption\",\n                                                                        sx: {\n                                                                            ml: 1\n                                                                        },\n                                                                        children: \"AI is thinking...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1207,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1205,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1202,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1197,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1168,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 3,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        sx: {\n                                            flexGrow: 1,\n                                            display: 'flex',\n                                            flexDirection: 'column'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"Your Mic (Candidate)\",\n                                                avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1221,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                action: activeSpeechService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Chip, {\n                                                    label: activeSpeechService === 'deepgram' ? 'Deepgram' : 'Azure',\n                                                    size: \"small\",\n                                                    color: activeSpeechService === 'deepgram' ? 'primary' : 'secondary',\n                                                    variant: \"outlined\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1224,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                sx: {\n                                                    pb: 1\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                sx: {\n                                                    flexGrow: 1,\n                                                    display: 'flex',\n                                                    flexDirection: 'column'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                            checked: isManualMode,\n                                                            onChange: (e)=>setIsManualMode(e.target.checked),\n                                                            color: \"primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1236,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        label: \"Manual Input Mode\",\n                                                        sx: {\n                                                            mb: 1\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.TextField, {\n                                                        fullWidth: true,\n                                                        multiline: true,\n                                                        rows: 8,\n                                                        variant: \"outlined\",\n                                                        value: micTranscription,\n                                                        onChange: (e)=>handleManualInputChange(e.target.value, 'microphone'),\n                                                        onKeyDown: (e)=>handleKeyPress(e, 'microphone'),\n                                                        placeholder: \"Your speech or manual input...\",\n                                                        sx: {\n                                                            mb: 2,\n                                                            flexGrow: 1\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                        sx: {\n                                                            display: 'flex',\n                                                            gap: 1,\n                                                            flexWrap: 'wrap',\n                                                            mt: 'auto'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                onClick: startMicrophoneRecognition,\n                                                                variant: \"contained\",\n                                                                color: isMicrophoneActive ? 'error' : 'primary',\n                                                                startIcon: isMicrophoneActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_MicOff__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1256,\n                                                                    columnNumber: 55\n                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Mic__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1256,\n                                                                    columnNumber: 72\n                                                                }, void 0),\n                                                                sx: {\n                                                                    flexGrow: 1\n                                                                },\n                                                                children: isMicrophoneActive ? 'Stop Mic' : 'Start Mic'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1252,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                                title: \"Clear Your Transcription\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                    onClick: handleClearMicTranscription,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1262,\n                                                                        columnNumber: 73\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1262,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1261,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isManualMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                onClick: ()=>handleManualSubmit('microphone'),\n                                                                variant: \"outlined\",\n                                                                color: \"primary\",\n                                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1269,\n                                                                    columnNumber: 36\n                                                                }, void 0),\n                                                                disabled: isProcessing || !micTranscription.trim(),\n                                                                children: \"Submit\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1265,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1251,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1234,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1218,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1087,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1086,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SettingsDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        open: settingsOpen,\n                        onClose: ()=>setSettingsOpen(false),\n                        onSave: handleSettingsSaved\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Snackbar, {\n                        open: snackbarOpen,\n                        autoHideDuration: 4000,\n                        onClose: handleSnackbarClose,\n                        anchorOrigin: {\n                            vertical: 'bottom',\n                            horizontal: 'center'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Alert, {\n                            onClose: handleSnackbarClose,\n                            severity: snackbarSeverity,\n                            sx: {\n                                width: '100%',\n                                boxShadow: theme.shadows[6]\n                            },\n                            children: snackbarMessage\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1293,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 1071,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"88c8eba4614332d8\",\n                dynamic: [\n                    theme.palette.background.paper,\n                    theme.palette.grey[400],\n                    theme.palette.background.paper,\n                    theme.palette.grey[500],\n                    theme.palette.grey[400],\n                    theme.palette.background.paper\n                ],\n                children: \".scroll-to-bottom{height:100%;width:100%;overflow-y:auto}.hidden-follow-button{display:none}.scroll-to-bottom::-webkit-scrollbar{width:8px;height:8px}.scroll-to-bottom::-webkit-scrollbar-track{background:\".concat(theme.palette.background.paper, \";border-radius:10px}.scroll-to-bottom::-webkit-scrollbar-thumb{background-color:\").concat(theme.palette.grey[400], \";border-radius:10px;border:2px solid \").concat(theme.palette.background.paper, \"}.scroll-to-bottom::-webkit-scrollbar-thumb:hover{background-color:\").concat(theme.palette.grey[500], \"}.scroll-to-bottom{scrollbar-width:thin;scrollbar-color:\").concat(theme.palette.grey[400], \" \").concat(theme.palette.background.paper, \"}\")\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(InterviewPage, \"B0k6/B2kH+enNb98YMyuDAPpCss=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useDispatch,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        _barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_Chip_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.useTheme\n    ];\n});\n_c = InterviewPage;\nvar _c;\n$RefreshReg$(_c, \"InterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/interview.js\n"));

/***/ })

});