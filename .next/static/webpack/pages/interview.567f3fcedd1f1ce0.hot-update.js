"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./pages/interview.js":
/*!****************************!*\
  !*** ./pages/interview.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InterviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(pages-dir-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AppBar,Avatar,Box,Button,Card,CardContent,CardHeader,Checkbox,CircularProgress,Container,FormControlLabel,Grid,IconButton,List,ListItem,ListItemText,Paper,Snackbar,Switch,TextField,Toolbar,Tooltip,Typography,useTheme!=!@mui/material */ \"(pages-dir-browser)/__barrel_optimize__?names=Alert,AppBar,Avatar,Box,Button,Card,CardContent,CardHeader,Checkbox,CircularProgress,Container,FormControlLabel,Grid,IconButton,List,ListItem,ListItemText,Paper,Snackbar,Switch,TextField,Toolbar,Tooltip,Typography,useTheme!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/ArrowDownward */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ArrowDownward.js\");\n/* harmony import */ var _mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/icons-material/ArrowUpward */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ArrowUpward.js\");\n/* harmony import */ var _mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/icons-material/DeleteSweep */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/DeleteSweep.js\");\n/* harmony import */ var _mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/Hearing */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Hearing.js\");\n/* harmony import */ var _mui_icons_material_Mic__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/Mic */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Mic.js\");\n/* harmony import */ var _mui_icons_material_MicOff__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/icons-material/MicOff */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/MicOff.js\");\n/* harmony import */ var _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/Person */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _mui_icons_material_PictureInPictureAlt__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/PictureInPictureAlt */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/PictureInPictureAlt.js\");\n/* harmony import */ var _mui_icons_material_PlaylistAddCheck__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/icons-material/PlaylistAddCheck */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/PlaylistAddCheck.js\");\n/* harmony import */ var _mui_icons_material_ScreenShare__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/ScreenShare */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ScreenShare.js\");\n/* harmony import */ var _mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/icons-material/Send */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Send.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/icons-material/SmartToy */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/SmartToy.js\");\n/* harmony import */ var _mui_icons_material_StopScreenShare__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/StopScreenShare */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/StopScreenShare.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @google/generative-ai */ \"(pages-dir-browser)/./node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var highlight_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! highlight.js */ \"(pages-dir-browser)/./node_modules/highlight.js/es/index.js\");\n/* harmony import */ var highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! highlight.js/styles/atom-one-dark.css */ \"(pages-dir-browser)/./node_modules/highlight.js/styles/atom-one-dark.css\");\n/* harmony import */ var highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash.throttle */ \"(pages-dir-browser)/./node_modules/lodash.throttle/index.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! openai */ \"(pages-dir-browser)/./node_modules/openai/index.mjs\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-markdown */ \"(pages-dir-browser)/./node_modules/react-markdown/index.js\");\n/* harmony import */ var react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-scroll-to-bottom */ \"(pages-dir-browser)/./node_modules/react-scroll-to-bottom/lib/esm/index.js\");\n/* harmony import */ var _components_SettingsDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/SettingsDialog */ \"(pages-dir-browser)/./components/SettingsDialog.js\");\n/* harmony import */ var _redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../redux/aiResponseSlice */ \"(pages-dir-browser)/./redux/aiResponseSlice.js\");\n/* harmony import */ var _redux_historySlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../redux/historySlice */ \"(pages-dir-browser)/./redux/historySlice.js\");\n/* harmony import */ var _redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../redux/transcriptionSlice */ \"(pages-dir-browser)/./redux/transcriptionSlice.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../utils/config */ \"(pages-dir-browser)/./utils/config.js\");\n/* harmony import */ var _utils_speechServices__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../utils/speechServices */ \"(pages-dir-browser)/./utils/speechServices.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n// MUI Components\n\n// MUI Icons\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Third-party Libraries\n\n\n\n\n\n\n\n// Speech Services\n// Local Imports\n\n\n\n\n\n\nfunction debounce(func) {\n    let timeout = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;\n    var _this = this;\n    let timer;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        clearTimeout(timer);\n        timer = setTimeout(()=>{\n            func.apply(_this, args);\n        }, timeout);\n    };\n}\nfunction InterviewPage() {\n    _s();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useDispatch)();\n    const transcriptionFromStore = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[transcriptionFromStore]\": (state)=>state.transcription\n    }[\"InterviewPage.useSelector[transcriptionFromStore]\"]);\n    const aiResponseFromStore = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[aiResponseFromStore]\": (state)=>state.aiResponse\n    }[\"InterviewPage.useSelector[aiResponseFromStore]\"]);\n    const history = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[history]\": (state)=>state.history\n    }[\"InterviewPage.useSelector[history]\"]);\n    const theme = (0,_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    const [appConfig, setAppConfig] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)((0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)());\n    const [systemRecognizer, setSystemRecognizer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [micRecognizer, setMicRecognizer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [systemSpeechService, setSystemSpeechService] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [micSpeechService, setMicSpeechService] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [systemAutoMode, setSystemAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(appConfig.systemAutoMode !== undefined ? appConfig.systemAutoMode : true);\n    const [openAI, setOpenAI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [settingsOpen, setSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isMicrophoneActive, setIsMicrophoneActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isSystemAudioActive, setIsSystemAudioActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('info');\n    const [selectedQuestions, setSelectedQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isManualMode, setIsManualMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(appConfig.isManualMode !== undefined ? appConfig.isManualMode : false);\n    const [micTranscription, setMicTranscription] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isAILoading, setIsAILoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [autoScroll, setAutoScroll] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [aiResponseSortOrder, setAiResponseSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('newestAtTop');\n    const [isPipWindowActive, setIsPipWindowActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const pipWindowRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const documentPipWindowRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const documentPipIframeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const systemInterimTranscription = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)('');\n    const micInterimTranscription = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)('');\n    const silenceTimer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const finalTranscript = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)({\n        system: '',\n        microphone: ''\n    });\n    const isManualModeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(isManualMode);\n    const systemAutoModeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(systemAutoMode);\n    const throttledDispatchSetAIResponseRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const showSnackbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"InterviewPage.useCallback[showSnackbar]\": function(message) {\n            let severity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n            setSnackbarMessage(message);\n            setSnackbarSeverity(severity);\n            setSnackbarOpen(true);\n        }\n    }[\"InterviewPage.useCallback[showSnackbar]\"], []);\n    const handleSettingsSaved = ()=>{\n        const newConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        setAppConfig(newConfig);\n        setIsAILoading(true);\n        setSystemAutoMode(newConfig.systemAutoMode !== undefined ? newConfig.systemAutoMode : true);\n        setIsManualMode(newConfig.isManualMode !== undefined ? newConfig.isManualMode : false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            const currentConfig = appConfig;\n            const initializeAI = {\n                \"InterviewPage.useEffect.initializeAI\": ()=>{\n                    try {\n                        if (currentConfig.aiModel.startsWith('gemini')) {\n                            if (!currentConfig.geminiKey) {\n                                showSnackbar('Gemini API key required. Please set it in Settings.', 'error');\n                                setOpenAI(null);\n                                return;\n                            }\n                            const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_4__.GoogleGenerativeAI(currentConfig.geminiKey);\n                            setOpenAI(genAI);\n                        } else {\n                            if (!currentConfig.openaiKey) {\n                                showSnackbar('OpenAI API key required. Please set it in Settings.', 'error');\n                                setOpenAI(null);\n                                return;\n                            }\n                            const openaiClient = new openai__WEBPACK_IMPORTED_MODULE_17__[\"default\"]({\n                                apiKey: currentConfig.openaiKey,\n                                dangerouslyAllowBrowser: true\n                            });\n                            setOpenAI(openaiClient);\n                        }\n                    } catch (error) {\n                        console.error('Error initializing AI client:', error);\n                        showSnackbar('Error initializing AI client: ' + error.message, 'error');\n                        setOpenAI(null);\n                    } finally{\n                        setIsAILoading(false);\n                    }\n                }\n            }[\"InterviewPage.useEffect.initializeAI\"];\n            if (isAILoading) initializeAI();\n        }\n    }[\"InterviewPage.useEffect\"], [\n        appConfig,\n        isAILoading,\n        showSnackbar\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            isManualModeRef.current = isManualMode;\n        }\n    }[\"InterviewPage.useEffect\"], [\n        isManualMode\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            systemAutoModeRef.current = systemAutoMode;\n        }\n    }[\"InterviewPage.useEffect\"], [\n        systemAutoMode\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            throttledDispatchSetAIResponseRef.current = lodash_throttle__WEBPACK_IMPORTED_MODULE_7___default()({\n                \"InterviewPage.useEffect\": (payload)=>{\n                    dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(payload));\n                }\n            }[\"InterviewPage.useEffect\"], 250, {\n                leading: true,\n                trailing: true\n            });\n            return ({\n                \"InterviewPage.useEffect\": ()=>{\n                    if (throttledDispatchSetAIResponseRef.current && typeof throttledDispatchSetAIResponseRef.current.cancel === 'function') {\n                        throttledDispatchSetAIResponseRef.current.cancel();\n                    }\n                }\n            })[\"InterviewPage.useEffect\"];\n        }\n    }[\"InterviewPage.useEffect\"], [\n        dispatch\n    ]);\n    const handleSnackbarClose = ()=>setSnackbarOpen(false);\n    const stopRecording = async (source)=>{\n        const speechService = source === 'system' ? systemSpeechService : micSpeechService;\n        const recognizer = source === 'system' ? systemRecognizer : micRecognizer;\n        try {\n            // Stop new speech service if available\n            if (speechService) {\n                await speechService.stop();\n            }\n            // Fallback to legacy Azure recognizer if still in use\n            if (recognizer && typeof recognizer.stopContinuousRecognitionAsync === 'function') {\n                await recognizer.stopContinuousRecognitionAsync();\n                if (recognizer.audioConfig && recognizer.audioConfig.privSource && recognizer.audioConfig.privSource.privStream) {\n                    const stream = recognizer.audioConfig.privSource.privStream;\n                    if (stream instanceof MediaStream) {\n                        stream.getTracks().forEach((track)=>{\n                            track.stop();\n                        });\n                    }\n                }\n                if (recognizer.audioConfig && typeof recognizer.audioConfig.close === 'function') {\n                    recognizer.audioConfig.close();\n                }\n            }\n        } catch (error) {\n            console.error(\"Error stopping \".concat(source, \" recognition:\"), error);\n            showSnackbar(\"Error stopping \".concat(source, \" audio: \").concat(error.message), 'error');\n        } finally{\n            if (source === 'system') {\n                setIsSystemAudioActive(false);\n                setSystemRecognizer(null);\n                setSystemSpeechService(null);\n            } else {\n                setIsMicrophoneActive(false);\n                setMicRecognizer(null);\n                setMicSpeechService(null);\n            }\n        }\n    };\n    const handleClearSystemTranscription = ()=>{\n        finalTranscript.current.system = '';\n        systemInterimTranscription.current = '';\n        dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.clearTranscription)());\n    };\n    const handleClearMicTranscription = ()=>{\n        finalTranscript.current.microphone = '';\n        micInterimTranscription.current = '';\n        setMicTranscription('');\n    };\n    const handleTranscriptionEvent = (text, source)=>{\n        const cleanText = text.replace(/\\s+/g, ' ').trim();\n        if (!cleanText) return;\n        finalTranscript.current[source] += cleanText + ' ';\n        if (source === 'system') {\n            dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + systemInterimTranscription.current));\n        } else {\n            setMicTranscription(finalTranscript.current.microphone + micInterimTranscription.current);\n        }\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        const currentSilenceTimerDuration = currentConfig.silenceTimerDuration;\n        if (source === 'system' && systemAutoModeRef.current || source === 'microphone' && !isManualModeRef.current) {\n            clearTimeout(silenceTimer.current);\n            silenceTimer.current = setTimeout(()=>{\n                askOpenAI(finalTranscript.current[source].trim(), source);\n            }, currentSilenceTimerDuration * 1000);\n        }\n    };\n    const handleManualInputChange = (value, source)=>{\n        if (source === 'system') {\n            dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(value));\n            finalTranscript.current.system = value;\n        } else {\n            setMicTranscription(value);\n            finalTranscript.current.microphone = value;\n        }\n    };\n    const handleManualSubmit = (source)=>{\n        const textToSubmit = source === 'system' ? transcriptionFromStore : micTranscription;\n        if (textToSubmit.trim()) {\n            askOpenAI(textToSubmit.trim(), source);\n        } else {\n            showSnackbar('Input is empty.', 'warning');\n        }\n    };\n    const handleKeyPress = (e, source)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleManualSubmit(source);\n        }\n    };\n    const handleCombineAndSubmit = ()=>{\n        if (selectedQuestions.length === 0) {\n            showSnackbar('No questions selected to combine.', 'warning');\n            return;\n        }\n        const questionHistory = history.filter((e)=>e.type === 'question').slice().reverse();\n        const questionTexts = selectedQuestions.map((selectedIndexInReversedArray)=>{\n            var _questionHistory_selectedIndexInReversedArray;\n            return (_questionHistory_selectedIndexInReversedArray = questionHistory[selectedIndexInReversedArray]) === null || _questionHistory_selectedIndexInReversedArray === void 0 ? void 0 : _questionHistory_selectedIndexInReversedArray.text;\n        }).filter((text)=>text);\n        if (questionTexts.length === 0) {\n            showSnackbar('Could not retrieve selected question texts.', 'warning');\n            return;\n        }\n        const combinedText = questionTexts.join('\\n\\n---\\n\\n');\n        askOpenAI(combinedText, 'combined');\n        setSelectedQuestions([]);\n    };\n    // Legacy Azure recognizer function for fallback\n    const createLegacyAzureRecognizer = async (mediaStream, source)=>{\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        if (!currentConfig.azureToken || !currentConfig.azureRegion) {\n            throw new Error('Azure Speech credentials missing');\n        }\n        let audioConfig;\n        try {\n            audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n        } catch (configError) {\n            console.error(\"Error creating AudioConfig for \".concat(source, \":\"), configError);\n            throw new Error(\"Error setting up audio for \".concat(source, \": \").concat(configError.message));\n        }\n        const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(currentConfig.azureToken, currentConfig.azureRegion);\n        speechConfig.speechRecognitionLanguage = currentConfig.azureLanguage;\n        const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);\n        recognizer.recognizing = (s, e)=>{\n            if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                const interimText = e.result.text;\n                if (source === 'system') {\n                    systemInterimTranscription.current = interimText;\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + interimText));\n                } else {\n                    micInterimTranscription.current = interimText;\n                    setMicTranscription(finalTranscript.current.microphone + interimText);\n                }\n            }\n        };\n        recognizer.recognized = (s, e)=>{\n            if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                if (source === 'system') systemInterimTranscription.current = '';\n                else micInterimTranscription.current = '';\n                handleTranscriptionEvent(e.result.text, source);\n            }\n        };\n        recognizer.canceled = (s, e)=>{\n            console.log(\"CANCELED: Reason=\".concat(e.reason, \" for \").concat(source));\n            if (e.reason === SpeechSDK.CancellationReason.Error) {\n                console.error(\"CANCELED: ErrorCode=\".concat(e.errorCode));\n                console.error(\"CANCELED: ErrorDetails=\".concat(e.errorDetails));\n                showSnackbar(\"Speech recognition error for \".concat(source, \": \").concat(e.errorDetails), 'error');\n            }\n            stopRecording(source);\n        };\n        recognizer.sessionStopped = (s, e)=>{\n            console.log(\"Session stopped event for \".concat(source, \".\"));\n            stopRecording(source);\n        };\n        try {\n            await recognizer.startContinuousRecognitionAsync();\n            showSnackbar(\"\".concat(source === 'system' ? 'System audio' : 'Microphone', \" recording started (Azure Speech).\"), 'success');\n            return recognizer;\n        } catch (error) {\n            console.error(\"Error starting \".concat(source, \" continuous recognition:\"), error);\n            if (audioConfig && typeof audioConfig.close === 'function') audioConfig.close();\n            throw error;\n        }\n    };\n    const createSpeechRecognizer = async (mediaStream, source)=>{\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        // Create speech service callbacks\n        const callbacks = {\n            onStart: (source)=>{\n                console.log(\"Speech recognition started for \".concat(source));\n                showSnackbar(\"\".concat(source === 'system' ? 'System audio' : 'Microphone', \" recording started.\"), 'success');\n            },\n            onInterimResult: (text, source)=>{\n                if (source === 'system') {\n                    systemInterimTranscription.current = text;\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + text));\n                } else {\n                    micInterimTranscription.current = text;\n                    setMicTranscription(finalTranscript.current.microphone + text);\n                }\n            },\n            onFinalResult: (text, source)=>{\n                // Clear interim transcription\n                if (source === 'system') systemInterimTranscription.current = '';\n                else micInterimTranscription.current = '';\n                // Handle the final transcription\n                handleTranscriptionEvent(text, source);\n            },\n            onError: (error, source)=>{\n                console.error(\"Speech recognition error for \".concat(source, \":\"), error);\n                showSnackbar(\"Speech recognition error for \".concat(source, \": \").concat(error.message), 'error');\n                stopRecording(source);\n            },\n            onStop: (source)=>{\n                console.log(\"Speech recognition stopped for \".concat(source));\n            }\n        };\n        try {\n            // Check if we have the required credentials for the selected service\n            const selectedService = currentConfig.speechService || 'deepgram';\n            let configToUse = currentConfig;\n            if (selectedService === 'deepgram' && !currentConfig.deepgramKey) {\n                // If Deepgram is selected but no key is provided, check if Azure is available\n                if (currentConfig.azureToken && currentConfig.azureRegion) {\n                    console.log('Deepgram key missing, falling back to Azure Speech Services');\n                    configToUse = {\n                        ...currentConfig,\n                        speechService: 'azure'\n                    };\n                    showSnackbar('Using Azure Speech Services (Deepgram key not configured)', 'info');\n                } else {\n                    throw new Error('Deepgram API key is required. Please configure it in Settings, or provide Azure credentials for fallback.');\n                }\n            } else if (selectedService === 'azure' && (!currentConfig.azureToken || !currentConfig.azureRegion)) {\n                // If Azure is selected but credentials are missing, check if Deepgram is available\n                if (currentConfig.deepgramKey) {\n                    console.log('Azure credentials missing, falling back to Deepgram');\n                    configToUse = {\n                        ...currentConfig,\n                        speechService: 'deepgram'\n                    };\n                    showSnackbar('Using Deepgram (Azure credentials not configured)', 'info');\n                } else {\n                    throw new Error('Azure Speech credentials are required. Please configure them in Settings, or provide Deepgram API key for fallback.');\n                }\n            }\n            // Create the appropriate speech service\n            const speechService = (0,_utils_speechServices__WEBPACK_IMPORTED_MODULE_14__.createSpeechService)(configToUse, callbacks);\n            // Start the speech service\n            await speechService.start(mediaStream, source);\n            return speechService;\n        } catch (error) {\n            console.error(\"Error creating speech service for \".concat(source, \":\"), error);\n            // Try legacy Azure recognizer as final fallback\n            if (currentConfig.azureToken && currentConfig.azureRegion) {\n                try {\n                    console.log(\"Attempting legacy Azure fallback for \".concat(source));\n                    const legacyRecognizer = await createLegacyAzureRecognizer(mediaStream, source);\n                    showSnackbar(\"Using legacy Azure Speech Services for \".concat(source), 'warning');\n                    return legacyRecognizer;\n                } catch (legacyError) {\n                    console.error(\"Legacy Azure fallback also failed for \".concat(source, \":\"), legacyError);\n                }\n            }\n            // Provide helpful error messages\n            if (error.message.includes('API key') || error.message.includes('credentials')) {\n                showSnackbar(\"Speech service configuration required. Please configure API keys in Settings (⚙️ icon).\", 'error');\n            } else {\n                showSnackbar(\"Failed to start \".concat(source, \" recognition: \").concat(error.message), 'error');\n            }\n            mediaStream.getTracks().forEach((track)=>track.stop());\n            return null;\n        }\n    };\n    const startSystemAudioRecognition = async ()=>{\n        if (isSystemAudioActive) {\n            await stopRecording('system');\n            return;\n        }\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {\n            showSnackbar('Screen sharing is not supported by your browser.', 'error');\n            setIsSystemAudioActive(false);\n            return;\n        }\n        try {\n            const mediaStream = await navigator.mediaDevices.getDisplayMedia({\n                audio: true,\n                video: {\n                    displaySurface: 'browser',\n                    logicalSurface: true\n                }\n            });\n            const audioTracks = mediaStream.getAudioTracks();\n            if (audioTracks.length === 0) {\n                showSnackbar('No audio track detected. Please ensure you share a tab with audio.', 'warning');\n                mediaStream.getTracks().forEach((track)=>track.stop());\n                return;\n            }\n            if (systemRecognizer) {\n                await stopRecording('system');\n            }\n            const speechServiceInstance = await createSpeechRecognizer(mediaStream, 'system');\n            if (speechServiceInstance) {\n                setSystemSpeechService(speechServiceInstance);\n                setIsSystemAudioActive(true);\n                mediaStream.getTracks().forEach((track)=>{\n                    track.onended = ()=>{\n                        showSnackbar('Tab sharing ended.', 'info');\n                        stopRecording('system');\n                    };\n                });\n            } else {\n                mediaStream.getTracks().forEach((track)=>track.stop());\n            }\n        } catch (error) {\n            console.error('System audio capture error:', error);\n            if (error.name === \"NotAllowedError\") {\n                showSnackbar('Permission denied for screen recording. Please allow access.', 'error');\n            } else if (error.name === \"NotFoundError\") {\n                showSnackbar('No suitable tab/window found to share.', 'error');\n            } else if (error.name === \"NotSupportedError\") {\n                showSnackbar('System audio capture not supported by your browser.', 'error');\n            } else {\n                showSnackbar(\"Failed to start system audio capture: \".concat(error.message || 'Unknown error'), 'error');\n            }\n            setIsSystemAudioActive(false);\n        }\n    };\n    const startMicrophoneRecognition = async ()=>{\n        if (isMicrophoneActive) {\n            await stopRecording('microphone');\n            return;\n        }\n        try {\n            const mediaStream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            if (micSpeechService) await stopRecording('microphone');\n            const speechServiceInstance = await createSpeechRecognizer(mediaStream, 'microphone');\n            if (speechServiceInstance) {\n                // Check if it's a new speech service or legacy recognizer\n                if (speechServiceInstance.start && speechServiceInstance.stop) {\n                    // New speech service\n                    setMicSpeechService(speechServiceInstance);\n                } else {\n                    // Legacy Azure recognizer\n                    setMicRecognizer(speechServiceInstance);\n                }\n                setIsMicrophoneActive(true);\n            } else {\n                mediaStream.getTracks().forEach((track)=>track.stop());\n            }\n        } catch (error) {\n            console.error('Microphone capture error:', error);\n            if (error.name === \"NotAllowedError\" || error.name === \"NotFoundError\") {\n                showSnackbar('Permission denied for microphone. Please allow access.', 'error');\n            } else {\n                showSnackbar(\"Failed to access microphone: \".concat(error.message || 'Unknown error'), 'error');\n            }\n            setIsMicrophoneActive(false);\n        }\n    };\n    // Helper function to determine reasoning effort for future advanced models\n    const getReasoningEffort = (text, source)=>{\n        const textLength = text.length;\n        const isComplexQuestion = text.includes('?') && textLength > 100;\n        const isCombined = source === 'combined';\n        if (isCombined || isComplexQuestion || textLength > 500) return 'high';\n        if (textLength > 200) return 'medium';\n        return 'low';\n    };\n    const askOpenAI = async (text, source)=>{\n        if (!text.trim()) {\n            showSnackbar('No input text to process.', 'warning');\n            return;\n        }\n        if (!openAI || isAILoading) {\n            showSnackbar('AI client is not ready. Please wait or check settings.', 'warning');\n            return;\n        }\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        const lengthSettings = {\n            concise: {\n                temperature: 0.4,\n                maxTokens: 250\n            },\n            medium: {\n                temperature: 0.6,\n                maxTokens: 500\n            },\n            lengthy: {\n                temperature: 0.8,\n                maxTokens: 1000\n            }\n        };\n        const { temperature, maxTokens } = lengthSettings[currentConfig.responseLength || 'medium'];\n        setIsProcessing(true);\n        const timestamp = new Date().toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n        let streamedResponse = '';\n        dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n            type: 'question',\n            text,\n            timestamp,\n            source,\n            status: 'pending'\n        }));\n        dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(''));\n        try {\n            const conversationHistoryForAPI = history.filter((e)=>e.text && (e.type === 'question' || e.type === 'response') && e.status !== 'pending').slice(-6).map((event)=>({\n                    role: event.type === 'question' ? 'user' : 'assistant',\n                    content: event.text\n                }));\n            if (currentConfig.aiModel.startsWith('gemini')) {\n                // Enhanced Gemini API configuration for 2.5 models\n                const modelConfig = {\n                    model: currentConfig.aiModel,\n                    generationConfig: {\n                        temperature,\n                        maxOutputTokens: maxTokens\n                    },\n                    systemInstruction: {\n                        parts: [\n                            {\n                                text: currentConfig.gptSystemPrompt\n                            }\n                        ]\n                    }\n                };\n                // Add thinking configuration for Gemini 2.5 models\n                if (currentConfig.aiModel.includes('2.5') && currentConfig.thinkingBudget !== undefined) {\n                    if (currentConfig.thinkingBudget === 0) {\n                        // Disable thinking\n                        modelConfig.generationConfig.thinkingConfig = {\n                            thinkingBudget: 0\n                        };\n                    } else if (currentConfig.thinkingBudget > 0) {\n                        // Custom thinking budget\n                        modelConfig.generationConfig.thinkingConfig = {\n                            thinkingBudget: currentConfig.thinkingBudget\n                        };\n                    }\n                // If thinkingBudget is null, use default (thinking enabled)\n                }\n                const model = openAI.getGenerativeModel(modelConfig);\n                const chat = model.startChat({\n                    history: conversationHistoryForAPI.map((msg)=>({\n                            role: msg.role === 'user' ? 'user' : 'model',\n                            parts: [\n                                {\n                                    text: msg.content\n                                }\n                            ]\n                        }))\n                });\n                const result = await chat.sendMessageStream(text);\n                for await (const chunk of result.stream){\n                    if (chunk && typeof chunk.text === 'function') {\n                        const chunkText = chunk.text();\n                        streamedResponse += chunkText;\n                        if (throttledDispatchSetAIResponseRef.current) {\n                            throttledDispatchSetAIResponseRef.current(streamedResponse);\n                        }\n                    }\n                }\n            } else {\n                // Enhanced OpenAI API usage with future-ready parameters\n                const messages = [\n                    {\n                        role: 'system',\n                        content: currentConfig.gptSystemPrompt\n                    },\n                    ...conversationHistoryForAPI,\n                    {\n                        role: 'user',\n                        content: text\n                    }\n                ];\n                const requestParams = {\n                    model: currentConfig.aiModel,\n                    messages,\n                    stream: true\n                };\n                // Set temperature based on model capabilities\n                if (currentConfig.aiModel.startsWith('o1')) {\n                // o1 models don't support temperature parameter at all\n                // Don't set temperature for o1 models\n                } else if (currentConfig.aiModel.startsWith('gpt-5')) {\n                    // GPT-5 models may have temperature restrictions, use default value\n                    requestParams.temperature = 1;\n                } else {\n                    // Standard models support configurable temperature\n                    requestParams.temperature = temperature;\n                }\n                // Use the correct token parameter based on model\n                if (currentConfig.aiModel.startsWith('gpt-5') || currentConfig.aiModel.startsWith('o1')) {\n                    requestParams.max_completion_tokens = maxTokens;\n                } else {\n                    requestParams.max_tokens = maxTokens;\n                }\n                // Add model-specific parameters for different model types\n                if (currentConfig.aiModel.startsWith('gpt-5')) {\n                    // GPT-5 models support new parameters\n                    if (currentConfig.reasoningEffort) {\n                        requestParams.reasoning_effort = currentConfig.reasoningEffort;\n                    }\n                    if (currentConfig.verbosity !== undefined) {\n                        requestParams.verbosity = currentConfig.verbosity === 0 ? 'low' : currentConfig.verbosity === 1 ? 'medium' : 'high';\n                    }\n                } else if (currentConfig.aiModel.startsWith('o1')) {\n                    // o1 models use different parameters and don't support streaming\n                    // Remove streaming for o1 models\n                    requestParams.stream = false;\n                    // o1 models don't use system messages in the same way\n                    // Move system prompt to the first user message\n                    requestParams.messages = [\n                        {\n                            role: 'user',\n                            content: \"\".concat(currentConfig.gptSystemPrompt, \"\\n\\n\").concat(text)\n                        },\n                        ...conversationHistoryForAPI.slice(1) // Skip the system message\n                    ];\n                // o1 models require temperature = 1 (already set above)\n                // No additional temperature modification needed\n                }\n                if (currentConfig.aiModel.startsWith('o1')) {\n                    var _response_choices__message, _response_choices_;\n                    // o1 models don't support streaming, handle as single response\n                    const response = await openAI.chat.completions.create(requestParams);\n                    streamedResponse = ((_response_choices_ = response.choices[0]) === null || _response_choices_ === void 0 ? void 0 : (_response_choices__message = _response_choices_.message) === null || _response_choices__message === void 0 ? void 0 : _response_choices__message.content) || '';\n                    if (throttledDispatchSetAIResponseRef.current) {\n                        throttledDispatchSetAIResponseRef.current(streamedResponse);\n                    }\n                } else {\n                    // Standard streaming for GPT-5, GPT-4o, and other models\n                    const stream = await openAI.chat.completions.create(requestParams);\n                    for await (const chunk of stream){\n                        var _chunk_choices__delta, _chunk_choices_;\n                        const chunkText = ((_chunk_choices_ = chunk.choices[0]) === null || _chunk_choices_ === void 0 ? void 0 : (_chunk_choices__delta = _chunk_choices_.delta) === null || _chunk_choices__delta === void 0 ? void 0 : _chunk_choices__delta.content) || '';\n                        streamedResponse += chunkText;\n                        if (throttledDispatchSetAIResponseRef.current) {\n                            throttledDispatchSetAIResponseRef.current(streamedResponse);\n                        }\n                    }\n                }\n            }\n            if (throttledDispatchSetAIResponseRef.current && typeof throttledDispatchSetAIResponseRef.current.cancel === 'function') {\n                throttledDispatchSetAIResponseRef.current.cancel();\n            }\n            dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(streamedResponse));\n            const finalTimestamp = new Date().toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n            });\n            dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n                type: 'response',\n                text: streamedResponse,\n                timestamp: finalTimestamp,\n                status: 'completed'\n            }));\n        } catch (error) {\n            console.error(\"AI request error:\", error);\n            const errorMessage = \"AI request failed: \".concat(error.message || 'Unknown error');\n            showSnackbar(errorMessage, 'error');\n            dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(\"Error: \".concat(errorMessage)));\n            dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n                type: 'response',\n                text: \"Error: \".concat(errorMessage),\n                timestamp: new Date().toLocaleTimeString([], {\n                    hour: '2-digit',\n                    minute: '2-digit'\n                }),\n                status: 'error'\n            }));\n        } finally{\n            if (source === 'system' && systemAutoModeRef.current || source === 'microphone' && !isManualModeRef.current) {\n                finalTranscript.current[source] = '';\n                if (source === 'system') {\n                    systemInterimTranscription.current = '';\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(''));\n                } else {\n                    micInterimTranscription.current = '';\n                    setMicTranscription('');\n                }\n            }\n            setIsProcessing(false);\n        }\n    };\n    const formatAndDisplayResponse = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (response)=>{\n            if (!response) return null;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                components: {\n                    code (param) {\n                        let { node, inline, className, children, ...props } = param;\n                        const match = /language-(\\w+)/.exec(className || '');\n                        return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                            sx: {\n                                my: 1,\n                                position: 'relative',\n                                '& pre': {\n                                    borderRadius: '4px',\n                                    padding: '12px !important',\n                                    fontSize: '0.875rem',\n                                    overflowX: 'auto',\n                                    whiteSpace: 'pre-wrap',\n                                    wordBreak: 'break-all'\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: className,\n                                    ...props,\n                                    dangerouslySetInnerHTML: {\n                                        __html: highlight_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"].highlight(String(children).replace(/\\n$/, ''), {\n                                            language: match[1],\n                                            ignoreIllegals: true\n                                        }).value\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 785,\n                                    columnNumber: 22\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 785,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 773,\n                            columnNumber: 15\n                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: className,\n                            ...props,\n                            style: {\n                                backgroundColor: 'rgba(0,0,0,0.05)',\n                                padding: '2px 4px',\n                                borderRadius: '4px',\n                                fontFamily: 'monospace',\n                                fontSize: '0.875rem',\n                                wordBreak: 'break-all'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 788,\n                            columnNumber: 15\n                        }, void 0);\n                    },\n                    p: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                paragraph: true,\n                                ...props,\n                                sx: {\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 804,\n                                columnNumber: 38\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    strong: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"strong\",\n                                fontWeight: \"bold\",\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 805,\n                                columnNumber: 43\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    em: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"em\",\n                                fontStyle: \"italic\",\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 806,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    ul: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"ul\",\n                                sx: {\n                                    pl: 2.5,\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 807,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    ol: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"ol\",\n                                sx: {\n                                    pl: 2.5,\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 808,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    li: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"li\",\n                                sx: {\n                                    mb: 0.25,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 809,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"]\n                },\n                children: response\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 768,\n                columnNumber: 7\n            }, this);\n        }\n    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"], []);\n    const renderHistoryItem = (item, index)=>{\n        if (item.type !== 'response') return null;\n        const Icon = _mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n        const title = 'AI Assistant';\n        const avatarBgColor = theme.palette.secondary.light;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n            sx: {\n                alignItems: 'flex-start',\n                px: 0,\n                py: 1.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                    sx: {\n                        bgcolor: avatarBgColor,\n                        mr: 2,\n                        mt: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        sx: {\n                            color: theme.palette.getContrastText(avatarBgColor)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 826,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 825,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Paper, {\n                    variant: \"outlined\",\n                    sx: {\n                        p: 1.5,\n                        flexGrow: 1,\n                        bgcolor: theme.palette.background.default,\n                        borderColor: theme.palette.divider,\n                        overflowX: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                            sx: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                mb: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"subtitle2\",\n                                    fontWeight: \"bold\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 830,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"caption\",\n                                    color: \"text.secondary\",\n                                    children: item.timestamp\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 831,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 829,\n                            columnNumber: 11\n                        }, this),\n                        formatAndDisplayResponse(item.text)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 828,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, \"response-\".concat(index), true, {\n            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n            lineNumber: 824,\n            columnNumber: 7\n        }, this);\n    };\n    const renderQuestionHistoryItem = (item, index)=>{\n        const Icon = item.source === 'system' ? _mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__[\"default\"] : _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__[\"default\"];\n        const title = item.source === 'system' ? 'Interviewer' : 'Candidate';\n        const avatarBgColor = item.source === 'system' ? theme.palette.info.light : theme.palette.success.light;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n            secondaryAction: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Checkbox, {\n                edge: \"end\",\n                checked: selectedQuestions.includes(index),\n                onChange: ()=>{\n                    setSelectedQuestions((prev)=>prev.includes(index) ? prev.filter((x)=>x !== index) : [\n                            ...prev,\n                            index\n                        ]);\n                },\n                color: \"secondary\",\n                size: \"small\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 848,\n                columnNumber: 11\n            }, void 0),\n            disablePadding: true,\n            sx: {\n                py: 0.5,\n                display: 'flex',\n                alignItems: 'center'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                    sx: {\n                        bgcolor: avatarBgColor,\n                        mr: 1.5,\n                        width: 32,\n                        height: 32,\n                        fontSize: '1rem'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        fontSize: \"small\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 864,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 863,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItemText, {\n                    primary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                        variant: \"body2\",\n                        noWrap: true,\n                        sx: {\n                            fontWeight: selectedQuestions.includes(index) ? 'bold' : 'normal',\n                            display: '-webkit-box',\n                            WebkitLineClamp: 2,\n                            WebkitBoxOrient: 'vertical',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis'\n                        },\n                        children: item.text\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 868,\n                        columnNumber: 13\n                    }, void 0),\n                    secondary: \"\".concat(title, \" - \").concat(item.timestamp)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 866,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, \"question-hist-\".concat(index), true, {\n            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n            lineNumber: 845,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSortOrderToggle = ()=>{\n        setAiResponseSortOrder((prev)=>prev === 'newestAtBottom' ? 'newestAtTop' : 'newestAtBottom');\n    };\n    const getAiResponsesToDisplay = ()=>{\n        let responses = history.filter((item)=>item.type === 'response').slice();\n        const currentStreamingText = aiResponseFromStore;\n        if (isProcessing && currentStreamingText && currentStreamingText.trim() !== '') {\n            responses.push({\n                text: currentStreamingText,\n                timestamp: 'Streaming...',\n                type: 'current_streaming'\n            });\n        }\n        if (aiResponseSortOrder === 'newestAtTop') {\n            return responses.reverse();\n        }\n        return responses;\n    };\n    const togglePipWindow = async ()=>{\n        if (isPipWindowActive) {\n            if (documentPipWindowRef.current && typeof documentPipWindowRef.current.close === 'function') {\n                try {\n                    await documentPipWindowRef.current.close();\n                } catch (e) {\n                    console.error(\"Error closing document PiP window:\", e);\n                }\n            } else if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                pipWindowRef.current.close();\n            }\n            return; // State update will be handled by pagehide/interval listeners\n        }\n        const addResizeListener = (pipWindow)=>{\n            const handlePipResize = debounce(()=>{\n                if (!pipWindow || pipWindow.closed) return;\n                const target = documentPipIframeRef.current ? documentPipIframeRef.current.contentWindow : pipWindow;\n                if (target) {\n                    target.postMessage({\n                        type: 'PIP_RESIZE',\n                        payload: {\n                            width: pipWindow.innerWidth,\n                            height: pipWindow.innerHeight\n                        }\n                    }, '*');\n                }\n            }, 50);\n            pipWindow.addEventListener('resize', handlePipResize);\n            return ()=>pipWindow.removeEventListener('resize', handlePipResize); // Return a cleanup function\n        };\n        if (window.documentPictureInPicture && typeof window.documentPictureInPicture.requestWindow === 'function') {\n            try {\n                const pipOptions = {\n                    width: 400,\n                    height: 300\n                };\n                const requestedPipWindow = await window.documentPictureInPicture.requestWindow(pipOptions);\n                documentPipWindowRef.current = requestedPipWindow;\n                setIsPipWindowActive(true);\n                const iframe = documentPipWindowRef.current.document.createElement('iframe');\n                iframe.src = '/pip-log';\n                iframe.style.width = '100%';\n                iframe.style.height = '100%';\n                iframe.style.border = 'none';\n                documentPipWindowRef.current.document.body.style.margin = '0';\n                documentPipWindowRef.current.document.body.style.overflow = 'hidden';\n                documentPipWindowRef.current.document.body.append(iframe);\n                documentPipIframeRef.current = iframe;\n                const removeResizeListener = addResizeListener(documentPipWindowRef.current);\n                iframe.onload = ()=>{\n                    if (documentPipIframeRef.current && documentPipIframeRef.current.contentWindow) {\n                        documentPipIframeRef.current.contentWindow.postMessage({\n                            type: 'AI_LOG_DATA',\n                            payload: {\n                                historicalResponses: history.filter((item)=>item.type === 'response'),\n                                currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                                isProcessing: isProcessing,\n                                sortOrder: aiResponseSortOrder\n                            }\n                        }, '*');\n                    }\n                };\n                documentPipWindowRef.current.addEventListener('pagehide', ()=>{\n                    removeResizeListener();\n                    setIsPipWindowActive(false);\n                    documentPipWindowRef.current = null;\n                    documentPipIframeRef.current = null;\n                });\n                showSnackbar('Native PiP window opened.', 'success');\n                return;\n            } catch (err) {\n                console.error('Document Picture-in-Picture API error:', err);\n                showSnackbar(\"Native PiP not available or failed. Trying popup. (\".concat(err.message, \")\"), 'warning');\n            }\n        }\n        pipWindowRef.current = window.open('/pip-log', 'AIResponsePiP', 'width=400,height=550,resizable=yes,scrollbars=yes,status=no,toolbar=no,menubar=no,location=no,noopener,noreferrer,popup=yes');\n        if (pipWindowRef.current) {\n            setIsPipWindowActive(true);\n            const removeResizeListener = addResizeListener(pipWindowRef.current);\n            pipWindowRef.current.onload = ()=>{\n                if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                    pipWindowRef.current.postMessage({\n                        type: 'AI_LOG_DATA',\n                        payload: {\n                            historicalResponses: history.filter((item)=>item.type === 'response'),\n                            currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                            isProcessing: isProcessing,\n                            sortOrder: aiResponseSortOrder\n                        }\n                    }, '*');\n                }\n            };\n            const pipCheckInterval = setInterval(()=>{\n                if (pipWindowRef.current && pipWindowRef.current.closed) {\n                    clearInterval(pipCheckInterval);\n                    removeResizeListener();\n                    setIsPipWindowActive(false);\n                    pipWindowRef.current = null;\n                }\n            }, 500);\n            if (pipWindowRef.current) pipWindowRef.current._pipIntervalId = pipCheckInterval;\n        } else {\n            showSnackbar('Failed to open PiP window. Please check popup blocker settings.', 'error');\n            setIsPipWindowActive(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            return ({\n                \"InterviewPage.useEffect\": ()=>{\n                    if (pipWindowRef.current && pipWindowRef.current._pipIntervalId) {\n                        clearInterval(pipWindowRef.current._pipIntervalId);\n                    }\n                    if (documentPipWindowRef.current && typeof documentPipWindowRef.current.close === 'function') {\n                        try {\n                            documentPipWindowRef.current.close();\n                        } catch (e) {}\n                    }\n                }\n            })[\"InterviewPage.useEffect\"];\n        }\n    }[\"InterviewPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            let targetWindowForMessage = null;\n            if (documentPipWindowRef.current && documentPipIframeRef.current && documentPipIframeRef.current.contentWindow) {\n                targetWindowForMessage = documentPipIframeRef.current.contentWindow;\n            } else if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                targetWindowForMessage = pipWindowRef.current;\n            }\n            if (isPipWindowActive && targetWindowForMessage) {\n                try {\n                    targetWindowForMessage.postMessage({\n                        type: 'AI_LOG_DATA',\n                        payload: {\n                            historicalResponses: history.filter({\n                                \"InterviewPage.useEffect\": (item)=>item.type === 'response'\n                            }[\"InterviewPage.useEffect\"]),\n                            currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                            isProcessing: isProcessing,\n                            sortOrder: aiResponseSortOrder\n                        }\n                    }, '*');\n                } catch (e) {\n                    console.warn(\"Could not post message to PiP window:\", e);\n                }\n            }\n        }\n    }[\"InterviewPage.useEffect\"], [\n        history,\n        aiResponseFromStore,\n        isPipWindowActive,\n        aiResponseSortOrder,\n        isProcessing\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"88c8eba4614332d8\",\n                            [\n                                theme.palette.background.paper,\n                                theme.palette.grey[400],\n                                theme.palette.background.paper,\n                                theme.palette.grey[500],\n                                theme.palette.grey[400],\n                                theme.palette.background.paper\n                            ]\n                        ]\n                    ]),\n                    children: \"Interview Copilot - Active Session\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 1050,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 1049,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                sx: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    height: '100vh'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.AppBar, {\n                        position: \"static\",\n                        color: \"default\",\n                        elevation: 1,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Toolbar, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    sx: {\n                                        mr: 2,\n                                        color: 'primary.main'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1055,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"h6\",\n                                    component: \"div\",\n                                    sx: {\n                                        flexGrow: 1,\n                                        color: 'text.primary'\n                                    },\n                                    children: \"Interview Copilot\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1056,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                    title: \"Settings\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                        color: \"primary\",\n                                        onClick: ()=>setSettingsOpen(true),\n                                        \"aria-label\": \"settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1061,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1060,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1059,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1054,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1053,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Container, {\n                        maxWidth: \"xl\",\n                        sx: {\n                            flexGrow: 1,\n                            py: 2,\n                            display: 'flex',\n                            flexDirection: 'column'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 3,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column',\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                    title: \"System Audio (Interviewer)\",\n                                                    avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1072,\n                                                        columnNumber: 72\n                                                    }, void 0),\n                                                    sx: {\n                                                        pb: 1\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1072,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                            control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                                checked: systemAutoMode,\n                                                                onChange: (e)=>setSystemAutoMode(e.target.checked),\n                                                                color: \"primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1075,\n                                                                columnNumber: 30\n                                                            }, void 0),\n                                                            label: \"Auto-Submit Question\",\n                                                            sx: {\n                                                                mb: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1074,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.TextField, {\n                                                            fullWidth: true,\n                                                            multiline: true,\n                                                            rows: 3,\n                                                            variant: \"outlined\",\n                                                            value: transcriptionFromStore,\n                                                            onChange: (e)=>handleManualInputChange(e.target.value, 'system'),\n                                                            onKeyDown: (e)=>handleKeyPress(e, 'system'),\n                                                            placeholder: \"Interviewer's speech...\",\n                                                            sx: {\n                                                                mb: 2\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1079,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                            sx: {\n                                                                display: 'flex',\n                                                                gap: 1,\n                                                                flexWrap: 'wrap'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                    onClick: startSystemAudioRecognition,\n                                                                    variant: \"contained\",\n                                                                    color: isSystemAudioActive ? 'error' : 'primary',\n                                                                    startIcon: isSystemAudioActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_StopScreenShare__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1095,\n                                                                        columnNumber: 56\n                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ScreenShare__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1095,\n                                                                        columnNumber: 82\n                                                                    }, void 0),\n                                                                    sx: {\n                                                                        flexGrow: 1\n                                                                    },\n                                                                    children: isSystemAudioActive ? 'Stop System Audio' : 'Record System Audio'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1091,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                                    variant: \"caption\",\n                                                                    sx: {\n                                                                        mt: 1,\n                                                                        display: 'block',\n                                                                        width: '100%'\n                                                                    },\n                                                                    children: isSystemAudioActive ? 'Recording system audio...' : 'Select \"Chrome Tab\" and check \"Share audio\" when prompted.'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1100,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                                    title: \"Clear System Transcription\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                        onClick: handleClearSystemTranscription,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                            lineNumber: 1104,\n                                                                            columnNumber: 76\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1104,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1103,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                !systemAutoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                    onClick: ()=>handleManualSubmit('system'),\n                                                                    variant: \"outlined\",\n                                                                    color: \"primary\",\n                                                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1111,\n                                                                        columnNumber: 36\n                                                                    }, void 0),\n                                                                    disabled: isProcessing || !transcriptionFromStore.trim(),\n                                                                    children: \"Submit\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1107,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1090,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1073,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1071,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                            sx: {\n                                                flexGrow: 1,\n                                                display: 'flex',\n                                                flexDirection: 'column'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                    title: \"Question History\",\n                                                    avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_PlaylistAddCheck__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                        variant: \"contained\",\n                                                        size: \"small\",\n                                                        onClick: handleCombineAndSubmit,\n                                                        disabled: selectedQuestions.length === 0 || isProcessing,\n                                                        startIcon: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CircularProgress, {\n                                                            size: 16,\n                                                            color: \"inherit\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1130,\n                                                            columnNumber: 49\n                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1130,\n                                                            columnNumber: 98\n                                                        }, void 0),\n                                                        children: \"Ask Combined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1125,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    sx: {\n                                                        pb: 1,\n                                                        borderBottom: \"1px solid \".concat(theme.palette.divider)\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                    sx: {\n                                                        flexGrow: 1,\n                                                        overflow: 'hidden',\n                                                        p: 0\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"scroll-to-bottom\",\n                                                        followButtonClassName: \"hidden-follow-button\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.List, {\n                                                            dense: true,\n                                                            sx: {\n                                                                pt: 0,\n                                                                px: 1\n                                                            },\n                                                            children: history.filter((e)=>e.type === 'question').slice().reverse().map(renderQuestionHistoryItem)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1139,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1138,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1137,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1070,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        sx: {\n                                            flexGrow: 1,\n                                            display: 'flex',\n                                            flexDirection: 'column'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"AI Assistant Log\",\n                                                avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1152,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                            title: isPipWindowActive ? \"Close PiP Log\" : \"Open PiP Log\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                onClick: togglePipWindow,\n                                                                size: \"small\",\n                                                                color: isPipWindowActive ? \"secondary\" : \"default\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_PictureInPictureAlt__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1157,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1156,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1155,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                            title: aiResponseSortOrder === 'newestAtTop' ? \"Sort: Newest at Bottom\" : \"Sort: Newest on Top\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                onClick: handleSortOrderToggle,\n                                                                size: \"small\",\n                                                                children: aiResponseSortOrder === 'newestAtTop' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1162,\n                                                                    columnNumber: 68\n                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1162,\n                                                                    columnNumber: 92\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1161,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1160,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                            variant: \"caption\",\n                                                            sx: {\n                                                                mr: 1,\n                                                                fontStyle: 'italic'\n                                                            },\n                                                            children: aiResponseSortOrder === 'newestAtTop' ? \"Newest First\" : \"Oldest First\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1165,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                            control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                                checked: autoScroll,\n                                                                onChange: (e)=>setAutoScroll(e.target.checked),\n                                                                color: \"primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1169,\n                                                                columnNumber: 34\n                                                            }, void 0),\n                                                            label: \"Auto Scroll\",\n                                                            sx: {\n                                                                ml: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1168,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true),\n                                                sx: {\n                                                    borderBottom: \"1px solid \".concat(theme.palette.divider)\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                sx: {\n                                                    flexGrow: 1,\n                                                    overflow: 'hidden',\n                                                    p: 0\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"scroll-to-bottom\",\n                                                    mode: autoScroll ? aiResponseSortOrder === 'newestAtTop' ? \"top\" : \"bottom\" : undefined,\n                                                    followButtonClassName: \"hidden-follow-button\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.List, {\n                                                        sx: {\n                                                            px: 2,\n                                                            py: 1\n                                                        },\n                                                        children: [\n                                                            getAiResponsesToDisplay().map(renderHistoryItem),\n                                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n                                                                sx: {\n                                                                    justifyContent: 'center',\n                                                                    py: 2\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CircularProgress, {\n                                                                        size: 24\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1187,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                                        variant: \"caption\",\n                                                                        sx: {\n                                                                            ml: 1\n                                                                        },\n                                                                        children: \"AI is thinking...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1188,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1186,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1183,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1178,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1177,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1149,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 3,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        sx: {\n                                            flexGrow: 1,\n                                            display: 'flex',\n                                            flexDirection: 'column'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"Your Mic (Candidate)\",\n                                                avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1200,\n                                                    columnNumber: 66\n                                                }, void 0),\n                                                sx: {\n                                                    pb: 1\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                sx: {\n                                                    flexGrow: 1,\n                                                    display: 'flex',\n                                                    flexDirection: 'column'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                            checked: isManualMode,\n                                                            onChange: (e)=>setIsManualMode(e.target.checked),\n                                                            color: \"primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1203,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        label: \"Manual Input Mode\",\n                                                        sx: {\n                                                            mb: 1\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1202,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.TextField, {\n                                                        fullWidth: true,\n                                                        multiline: true,\n                                                        rows: 8,\n                                                        variant: \"outlined\",\n                                                        value: micTranscription,\n                                                        onChange: (e)=>handleManualInputChange(e.target.value, 'microphone'),\n                                                        onKeyDown: (e)=>handleKeyPress(e, 'microphone'),\n                                                        placeholder: \"Your speech or manual input...\",\n                                                        sx: {\n                                                            mb: 2,\n                                                            flexGrow: 1\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1207,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                        sx: {\n                                                            display: 'flex',\n                                                            gap: 1,\n                                                            flexWrap: 'wrap',\n                                                            mt: 'auto'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                onClick: startMicrophoneRecognition,\n                                                                variant: \"contained\",\n                                                                color: isMicrophoneActive ? 'error' : 'primary',\n                                                                startIcon: isMicrophoneActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_MicOff__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1223,\n                                                                    columnNumber: 55\n                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Mic__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1223,\n                                                                    columnNumber: 72\n                                                                }, void 0),\n                                                                sx: {\n                                                                    flexGrow: 1\n                                                                },\n                                                                children: isMicrophoneActive ? 'Stop Mic' : 'Start Mic'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1219,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                                title: \"Clear Your Transcription\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                    onClick: handleClearMicTranscription,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1229,\n                                                                        columnNumber: 73\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1229,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1228,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isManualMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                onClick: ()=>handleManualSubmit('microphone'),\n                                                                variant: \"outlined\",\n                                                                color: \"primary\",\n                                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1236,\n                                                                    columnNumber: 36\n                                                                }, void 0),\n                                                                disabled: isProcessing || !micTranscription.trim(),\n                                                                children: \"Submit\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1232,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1218,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1199,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1068,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1067,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SettingsDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        open: settingsOpen,\n                        onClose: ()=>setSettingsOpen(false),\n                        onSave: handleSettingsSaved\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1249,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Snackbar, {\n                        open: snackbarOpen,\n                        autoHideDuration: 4000,\n                        onClose: handleSnackbarClose,\n                        anchorOrigin: {\n                            vertical: 'bottom',\n                            horizontal: 'center'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Alert, {\n                            onClose: handleSnackbarClose,\n                            severity: snackbarSeverity,\n                            sx: {\n                                width: '100%',\n                                boxShadow: theme.shadows[6]\n                            },\n                            children: snackbarMessage\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1260,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 1052,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"88c8eba4614332d8\",\n                dynamic: [\n                    theme.palette.background.paper,\n                    theme.palette.grey[400],\n                    theme.palette.background.paper,\n                    theme.palette.grey[500],\n                    theme.palette.grey[400],\n                    theme.palette.background.paper\n                ],\n                children: \".scroll-to-bottom{height:100%;width:100%;overflow-y:auto}.hidden-follow-button{display:none}.scroll-to-bottom::-webkit-scrollbar{width:8px;height:8px}.scroll-to-bottom::-webkit-scrollbar-track{background:\".concat(theme.palette.background.paper, \";border-radius:10px}.scroll-to-bottom::-webkit-scrollbar-thumb{background-color:\").concat(theme.palette.grey[400], \";border-radius:10px;border:2px solid \").concat(theme.palette.background.paper, \"}.scroll-to-bottom::-webkit-scrollbar-thumb:hover{background-color:\").concat(theme.palette.grey[500], \"}.scroll-to-bottom{scrollbar-width:thin;scrollbar-color:\").concat(theme.palette.grey[400], \" \").concat(theme.palette.background.paper, \"}\")\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(InterviewPage, \"5kq7Xa8OgN+T13jCAWpZ96uerOw=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useDispatch,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        _barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.useTheme\n    ];\n});\n_c = InterviewPage;\nvar _c;\n$RefreshReg$(_c, \"InterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/interview.js\n"));

/***/ })

});