"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./utils/speechServices.js":
/*!*********************************!*\
  !*** ./utils/speechServices.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AzureSpeechService: () => (/* binding */ AzureSpeechService),\n/* harmony export */   DeepgramSpeechService: () => (/* binding */ DeepgramSpeechService),\n/* harmony export */   createSpeechService: () => (/* binding */ createSpeechService)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet createClient = null;\nlet SpeechSDK = null;\n// Initialize client-side dependencies\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\")).then((module)=>{\n        createClient = module.createClient;\n    });\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23)).then((module)=>{\n        SpeechSDK = module;\n    });\n}\n/**\n * Abstract base class for speech services\n */ class SpeechService {\n    async start(mediaStream, source) {\n        throw new Error('start method must be implemented');\n    }\n    async stop() {\n        throw new Error('stop method must be implemented');\n    }\n    cleanup() {\n        // Default cleanup implementation\n        if (this.recognizer) {\n            this.recognizer = null;\n        }\n        this.isActive = false;\n    }\n    constructor(config, callbacks){\n        this.config = config;\n        this.callbacks = callbacks;\n        this.isActive = false;\n        this.recognizer = null;\n    }\n}\n/**\n * Deepgram Speech Service Implementation\n */ class DeepgramSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            if (!this.config.deepgramKey) {\n                throw new Error('Deepgram API key is required. Get a free API key from deepgram.com and configure it in Settings (⚙️ icon).');\n            }\n            // Basic validation of API key format\n            if (typeof this.config.deepgramKey !== 'string' || this.config.deepgramKey.length < 10) {\n                throw new Error('Deepgram API key appears to be invalid. Please check your API key in Settings.');\n            }\n            if (!createClient) {\n                const { createClient: dgCreateClient } = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\"));\n                createClient = dgCreateClient;\n            }\n            this.source = source;\n            this.deepgram = createClient(this.config.deepgramKey);\n            console.log(\"Creating Deepgram connection for \".concat(source, \" with API key: \").concat(this.config.deepgramKey.substring(0, 8), \"...\"));\n            // Create WebSocket connection for real-time transcription\n            // Use minimal settings for maximum compatibility\n            try {\n                this.connection = this.deepgram.listen.live({\n                    model: 'nova-2',\n                    language: 'en-US',\n                    interim_results: true\n                });\n                console.log(\"Deepgram connection created for \".concat(source, \", setting up event listeners...\"));\n            } catch (connectionError) {\n                console.error(\"Failed to create Deepgram connection for \".concat(source, \":\"), connectionError);\n                throw new Error(\"Failed to create Deepgram connection: \".concat(connectionError.message));\n            }\n            // Set up event handlers\n            this.connection.on('open', ()=>{\n                var _this_callbacks_onStart, _this_callbacks;\n                console.log(\"Deepgram connection opened for \".concat(source));\n                this.isActive = true;\n                // Clear any connection timeout\n                if (this.connectionTimeout) {\n                    clearTimeout(this.connectionTimeout);\n                    this.connectionTimeout = null;\n                }\n                (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            });\n            this.connection.on('Results', (data)=>{\n                var _data_channel_alternatives_, _data_channel_alternatives, _data_channel;\n                console.log(\"Deepgram results for \".concat(source, \":\"), data);\n                this.hasReceivedResults = true; // Mark that we've received results\n                const transcript = (_data_channel = data.channel) === null || _data_channel === void 0 ? void 0 : (_data_channel_alternatives = _data_channel.alternatives) === null || _data_channel_alternatives === void 0 ? void 0 : (_data_channel_alternatives_ = _data_channel_alternatives[0]) === null || _data_channel_alternatives_ === void 0 ? void 0 : _data_channel_alternatives_.transcript;\n                if (transcript && transcript.trim()) {\n                    if (data.is_final) {\n                        var _this_callbacks_onFinalResult, _this_callbacks;\n                        console.log(\"Final transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, transcript, source);\n                    } else {\n                        var _this_callbacks_onInterimResult, _this_callbacks1;\n                        console.log(\"Interim transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onInterimResult = (_this_callbacks1 = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks1, transcript, source);\n                    }\n                } else {\n                    var _data_channel1, _data_channel_alternatives1, _data_channel2, _data_channel_alternatives2, _data_channel3;\n                    // Log when we get results but no transcript\n                    console.log(\"Deepgram results received but no transcript for \".concat(source, \":\"), {\n                        hasChannel: !!data.channel,\n                        hasAlternatives: !!((_data_channel1 = data.channel) === null || _data_channel1 === void 0 ? void 0 : _data_channel1.alternatives),\n                        alternativesLength: (_data_channel2 = data.channel) === null || _data_channel2 === void 0 ? void 0 : (_data_channel_alternatives1 = _data_channel2.alternatives) === null || _data_channel_alternatives1 === void 0 ? void 0 : _data_channel_alternatives1.length,\n                        firstAlternative: (_data_channel3 = data.channel) === null || _data_channel3 === void 0 ? void 0 : (_data_channel_alternatives2 = _data_channel3.alternatives) === null || _data_channel_alternatives2 === void 0 ? void 0 : _data_channel_alternatives2[0],\n                        transcript: transcript\n                    });\n                }\n            });\n            this.connection.on('Metadata', (data)=>{\n                console.log(\"Deepgram metadata for \".concat(source, \":\"), data);\n            });\n            this.connection.on('error', (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"Deepgram error for \".concat(source, \":\"), error);\n                // Provide more specific error messages based on the error type\n                let errorMessage = 'Deepgram connection failed';\n                if (error.type === 'error' && error.target instanceof WebSocket) {\n                    errorMessage = 'Deepgram API key is invalid or missing. Please check your API key in Settings.';\n                }\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error(errorMessage), source);\n                this.stop();\n            });\n            this.connection.on('close', (closeEvent)=>{\n                console.log(\"Deepgram connection closed for \".concat(source, \":\"), closeEvent);\n                this.isActive = false;\n                // If connection closes immediately after opening, it might be a configuration issue\n                if (closeEvent && closeEvent.code && closeEvent.code !== 1000) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection closed with error code \".concat(closeEvent.code, \": \").concat(closeEvent.reason));\n                    let errorMessage = 'Deepgram connection failed';\n                    if (closeEvent.code === 1006) {\n                        errorMessage = 'Deepgram API key is invalid or missing. Please get a free API key from deepgram.com and configure it in Settings (⚙️ icon).';\n                    } else if (closeEvent.code === 1002) {\n                        errorMessage = 'Deepgram API key has insufficient permissions. Please check your API key settings.';\n                    } else if (closeEvent.code === 1011) {\n                        errorMessage = 'Deepgram service error. Please try again or contact support.';\n                    }\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error(errorMessage), source);\n                } else {\n                    var _this_callbacks_onStop, _this_callbacks1;\n                    (_this_callbacks_onStop = (_this_callbacks1 = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks1, source);\n                }\n            });\n            this.connection.on('warning', (warning)=>{\n                console.warn(\"Deepgram warning for \".concat(source, \":\"), warning);\n            });\n            // Set up MediaRecorder to send audio data to Deepgram\n            // Use the most compatible format for Deepgram\n            let mimeType = 'audio/webm';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n                mimeType = 'audio/mp4';\n                if (!MediaRecorder.isTypeSupported(mimeType)) {\n                    mimeType = ''; // Use default\n                }\n            }\n            console.log(\"Using MediaRecorder mimeType: \".concat(mimeType || 'default', \" for \").concat(source));\n            this.mediaRecorder = new MediaRecorder(mediaStream, mimeType ? {\n                mimeType\n            } : {});\n            this.mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0 && this.connection && this.isActive) {\n                    console.log(\"Sending audio data to Deepgram: \".concat(event.data.size, \" bytes for \").concat(source));\n                    this.connection.send(event.data);\n                } else if (event.data.size === 0) {\n                    console.warn(\"Empty audio data received for \".concat(source));\n                }\n            };\n            this.mediaRecorder.onerror = (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"MediaRecorder error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            };\n            this.mediaRecorder.onstart = ()=>{\n                console.log(\"MediaRecorder started for \".concat(source));\n            };\n            this.mediaRecorder.onstop = ()=>{\n                console.log(\"MediaRecorder stopped for \".concat(source));\n                // Clear the data interval when recorder stops\n                if (this.dataInterval) {\n                    clearInterval(this.dataInterval);\n                    this.dataInterval = null;\n                }\n            // Don't automatically close connection when MediaRecorder stops\n            // The connection should stay open for the duration of the session\n            };\n            // Start recording and sending data\n            // Use continuous recording instead of timed chunks to prevent auto-stopping\n            this.mediaRecorder.start(); // Continuous recording\n            // Set up interval to request data periodically\n            this.dataInterval = setInterval(()=>{\n                if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {\n                    this.mediaRecorder.requestData();\n                }\n            }, 250); // Request data every 250ms\n            // Set up connection timeout to detect failed connections\n            this.connectionTimeout = setTimeout(()=>{\n                if (!this.isActive) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection timeout for \".concat(source));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error('Connection timeout - please check your API key and network connection'), source);\n                    this.stop();\n                }\n            }, 10000); // 10 second timeout\n            // Set up a timeout to check if we're receiving any transcription results\n            this.transcriptionTimeout = setTimeout(()=>{\n                if (this.isActive && !this.hasReceivedResults) {\n                    var // Don't stop the connection, just warn the user\n                    _this_callbacks_onError, _this_callbacks;\n                    console.warn(\"No transcription results received from Deepgram for \".concat(source, \" after 15 seconds. This might indicate an API key issue or audio format problem.\"));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error('No speech recognition results - please check your Deepgram API key and try speaking clearly'), source);\n                }\n            }, 15000); // 15 second timeout for transcription results\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks;\n            console.error(\"Failed to start Deepgram for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            // Clear data interval\n            if (this.dataInterval) {\n                clearInterval(this.dataInterval);\n                this.dataInterval = null;\n            }\n            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n                this.mediaRecorder.stop();\n            }\n            if (this.connection) {\n                this.connection.finish();\n                this.connection = null;\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Deepgram:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        if (this.connectionTimeout) {\n            clearTimeout(this.connectionTimeout);\n            this.connectionTimeout = null;\n        }\n        if (this.transcriptionTimeout) {\n            clearTimeout(this.transcriptionTimeout);\n            this.transcriptionTimeout = null;\n        }\n        if (this.dataInterval) {\n            clearInterval(this.dataInterval);\n            this.dataInterval = null;\n        }\n        this.mediaRecorder = null;\n        this.connection = null;\n        this.deepgram = null;\n        this.source = null;\n        this.hasReceivedResults = false;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.deepgram = null;\n        this.connection = null;\n        this.mediaRecorder = null;\n        this.source = null;\n        this.connectionTimeout = null;\n        this.transcriptionTimeout = null;\n        this.dataInterval = null;\n        this.hasReceivedResults = false;\n    }\n}\n/**\n * Azure Speech Service Implementation (Legacy)\n */ class AzureSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            var _this_callbacks_onStart, _this_callbacks;\n            if (!this.config.azureToken || !this.config.azureRegion) {\n                throw new Error('Azure Speech credentials are required');\n            }\n            if (!SpeechSDK) {\n                SpeechSDK = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23));\n            }\n            // Create audio config from media stream\n            this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n            // Create speech config\n            this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(this.config.azureToken, this.config.azureRegion);\n            this.speechConfig.speechRecognitionLanguage = this.config.azureLanguage || 'en-US';\n            // Create recognizer\n            this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);\n            // Set up event handlers\n            this.recognizer.recognizing = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                    var _this_callbacks_onInterimResult, _this_callbacks;\n                    (_this_callbacks_onInterimResult = (_this_callbacks = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.recognized = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                    var _this_callbacks_onFinalResult, _this_callbacks;\n                    (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.canceled = (s, e)=>{\n                console.log(\"Azure recognition canceled for \".concat(source, \": \").concat(e.reason));\n                if (e.reason === SpeechSDK.CancellationReason.Error) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    const error = new Error(\"Azure Speech error: \".concat(e.errorDetails));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                }\n                this.stop();\n            };\n            this.recognizer.sessionStopped = (s, e)=>{\n                var _this_callbacks_onStop, _this_callbacks;\n                console.log(\"Azure session stopped for \".concat(source));\n                (_this_callbacks_onStop = (_this_callbacks = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks, source);\n                this.stop();\n            };\n            // Start continuous recognition\n            await this.recognizer.startContinuousRecognitionAsync();\n            this.isActive = true;\n            (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks1;\n            console.error(\"Failed to start Azure Speech for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks1 = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks1, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            if (this.recognizer && typeof this.recognizer.stopContinuousRecognitionAsync === 'function') {\n                await this.recognizer.stopContinuousRecognitionAsync();\n            }\n            if (this.audioConfig && typeof this.audioConfig.close === 'function') {\n                this.audioConfig.close();\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Azure Speech:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n}\n/**\n * Factory function to create speech service instances\n */ function createSpeechService(config, callbacks) {\n    const serviceType = config.speechService || 'deepgram';\n    switch(serviceType){\n        case 'deepgram':\n            return new DeepgramSpeechService(config, callbacks);\n        case 'azure':\n            return new AzureSpeechService(config, callbacks);\n        default:\n            throw new Error(\"Unknown speech service: \".concat(serviceType));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./utils/speechServices.js\n"));

/***/ })

});