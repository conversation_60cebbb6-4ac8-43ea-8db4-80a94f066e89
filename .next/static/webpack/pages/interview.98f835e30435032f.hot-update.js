"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./utils/speechServices.js":
/*!*********************************!*\
  !*** ./utils/speechServices.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AzureSpeechService: () => (/* binding */ AzureSpeechService),\n/* harmony export */   DeepgramSpeechService: () => (/* binding */ DeepgramSpeechService),\n/* harmony export */   createSpeechService: () => (/* binding */ createSpeechService)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet createClient = null;\nlet SpeechSDK = null;\n// Initialize client-side dependencies\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\")).then((module)=>{\n        createClient = module.createClient;\n    });\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23)).then((module)=>{\n        SpeechSDK = module;\n    });\n}\n/**\n * Abstract base class for speech services\n */ class SpeechService {\n    async start(mediaStream, source) {\n        throw new Error('start method must be implemented');\n    }\n    async stop() {\n        throw new Error('stop method must be implemented');\n    }\n    cleanup() {\n        // Default cleanup implementation\n        if (this.recognizer) {\n            this.recognizer = null;\n        }\n        this.isActive = false;\n    }\n    constructor(config, callbacks){\n        this.config = config;\n        this.callbacks = callbacks;\n        this.isActive = false;\n        this.recognizer = null;\n    }\n}\n/**\n * Deepgram Speech Service Implementation\n */ class DeepgramSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            if (!this.config.deepgramKey) {\n                throw new Error('Deepgram API key is required');\n            }\n            if (!createClient) {\n                const { createClient: dgCreateClient } = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\"));\n                createClient = dgCreateClient;\n            }\n            this.source = source;\n            this.deepgram = createClient(this.config.deepgramKey);\n            // Create WebSocket connection for real-time transcription\n            this.connection = this.deepgram.listen.live({\n                model: this.config.deepgramModel || 'nova-2',\n                language: this.config.deepgramLanguage || 'en-US',\n                smart_format: true,\n                interim_results: true,\n                endpointing: 300,\n                utterance_end_ms: 1000,\n                vad_events: true\n            });\n            // Set up event handlers\n            this.connection.on('open', ()=>{\n                var _this_callbacks_onStart, _this_callbacks;\n                console.log(\"Deepgram connection opened for \".concat(source));\n                this.isActive = true;\n                // Clear any connection timeout\n                if (this.connectionTimeout) {\n                    clearTimeout(this.connectionTimeout);\n                    this.connectionTimeout = null;\n                }\n                (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            });\n            this.connection.on('Results', (data)=>{\n                var _data_channel_alternatives_, _data_channel_alternatives, _data_channel;\n                console.log(\"Deepgram results for \".concat(source, \":\"), data);\n                const transcript = (_data_channel = data.channel) === null || _data_channel === void 0 ? void 0 : (_data_channel_alternatives = _data_channel.alternatives) === null || _data_channel_alternatives === void 0 ? void 0 : (_data_channel_alternatives_ = _data_channel_alternatives[0]) === null || _data_channel_alternatives_ === void 0 ? void 0 : _data_channel_alternatives_.transcript;\n                if (transcript && transcript.trim()) {\n                    if (data.is_final) {\n                        var _this_callbacks_onFinalResult, _this_callbacks;\n                        console.log(\"Final transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, transcript, source);\n                    } else {\n                        var _this_callbacks_onInterimResult, _this_callbacks1;\n                        console.log(\"Interim transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onInterimResult = (_this_callbacks1 = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks1, transcript, source);\n                    }\n                }\n            });\n            this.connection.on('Metadata', (data)=>{\n                console.log(\"Deepgram metadata for \".concat(source, \":\"), data);\n            });\n            this.connection.on('error', (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"Deepgram error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                this.stop();\n            });\n            this.connection.on('close', (closeEvent)=>{\n                console.log(\"Deepgram connection closed for \".concat(source, \":\"), closeEvent);\n                this.isActive = false;\n                // If connection closes immediately after opening, it might be a configuration issue\n                if (closeEvent && closeEvent.code && closeEvent.code !== 1000) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection closed with error code \".concat(closeEvent.code, \": \").concat(closeEvent.reason));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error(\"Connection closed: \".concat(closeEvent.reason || 'Unknown error')), source);\n                } else {\n                    var _this_callbacks_onStop, _this_callbacks1;\n                    (_this_callbacks_onStop = (_this_callbacks1 = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks1, source);\n                }\n            });\n            this.connection.on('warning', (warning)=>{\n                console.warn(\"Deepgram warning for \".concat(source, \":\"), warning);\n            });\n            // Set up MediaRecorder to send audio data to Deepgram\n            // Try different mimeTypes for better compatibility\n            let mimeType = 'audio/webm;codecs=opus';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n                mimeType = 'audio/webm';\n                if (!MediaRecorder.isTypeSupported(mimeType)) {\n                    mimeType = 'audio/mp4';\n                    if (!MediaRecorder.isTypeSupported(mimeType)) {\n                        mimeType = ''; // Use default\n                    }\n                }\n            }\n            console.log(\"Using MediaRecorder mimeType: \".concat(mimeType || 'default', \" for \").concat(source));\n            this.mediaRecorder = new MediaRecorder(mediaStream, mimeType ? {\n                mimeType\n            } : {});\n            this.mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0 && this.connection && this.isActive) {\n                    console.log(\"Sending audio data to Deepgram: \".concat(event.data.size, \" bytes for \").concat(source));\n                    this.connection.send(event.data);\n                }\n            };\n            this.mediaRecorder.onerror = (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"MediaRecorder error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            };\n            this.mediaRecorder.onstart = ()=>{\n                console.log(\"MediaRecorder started for \".concat(source));\n            };\n            this.mediaRecorder.onstop = ()=>{\n                console.log(\"MediaRecorder stopped for \".concat(source));\n            };\n            // Start recording and sending data\n            this.mediaRecorder.start(250); // Send data every 250ms for better stability\n            // Set up connection timeout to detect failed connections\n            this.connectionTimeout = setTimeout(()=>{\n                if (!this.isActive) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection timeout for \".concat(source));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error('Connection timeout - please check your API key and network connection'), source);\n                    this.stop();\n                }\n            }, 10000); // 10 second timeout\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks;\n            console.error(\"Failed to start Deepgram for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n                this.mediaRecorder.stop();\n            }\n            if (this.connection) {\n                this.connection.finish();\n                this.connection = null;\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Deepgram:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        this.mediaRecorder = null;\n        this.connection = null;\n        this.deepgram = null;\n        this.source = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.deepgram = null;\n        this.connection = null;\n        this.mediaRecorder = null;\n        this.source = null;\n    }\n}\n/**\n * Azure Speech Service Implementation (Legacy)\n */ class AzureSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            var _this_callbacks_onStart, _this_callbacks;\n            if (!this.config.azureToken || !this.config.azureRegion) {\n                throw new Error('Azure Speech credentials are required');\n            }\n            if (!SpeechSDK) {\n                SpeechSDK = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23));\n            }\n            // Create audio config from media stream\n            this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n            // Create speech config\n            this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(this.config.azureToken, this.config.azureRegion);\n            this.speechConfig.speechRecognitionLanguage = this.config.azureLanguage || 'en-US';\n            // Create recognizer\n            this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);\n            // Set up event handlers\n            this.recognizer.recognizing = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                    var _this_callbacks_onInterimResult, _this_callbacks;\n                    (_this_callbacks_onInterimResult = (_this_callbacks = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.recognized = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                    var _this_callbacks_onFinalResult, _this_callbacks;\n                    (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.canceled = (s, e)=>{\n                console.log(\"Azure recognition canceled for \".concat(source, \": \").concat(e.reason));\n                if (e.reason === SpeechSDK.CancellationReason.Error) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    const error = new Error(\"Azure Speech error: \".concat(e.errorDetails));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                }\n                this.stop();\n            };\n            this.recognizer.sessionStopped = (s, e)=>{\n                var _this_callbacks_onStop, _this_callbacks;\n                console.log(\"Azure session stopped for \".concat(source));\n                (_this_callbacks_onStop = (_this_callbacks = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks, source);\n                this.stop();\n            };\n            // Start continuous recognition\n            await this.recognizer.startContinuousRecognitionAsync();\n            this.isActive = true;\n            (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks1;\n            console.error(\"Failed to start Azure Speech for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks1 = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks1, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            if (this.recognizer && typeof this.recognizer.stopContinuousRecognitionAsync === 'function') {\n                await this.recognizer.stopContinuousRecognitionAsync();\n            }\n            if (this.audioConfig && typeof this.audioConfig.close === 'function') {\n                this.audioConfig.close();\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Azure Speech:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n}\n/**\n * Factory function to create speech service instances\n */ function createSpeechService(config, callbacks) {\n    const serviceType = config.speechService || 'deepgram';\n    switch(serviceType){\n        case 'deepgram':\n            return new DeepgramSpeechService(config, callbacks);\n        case 'azure':\n            return new AzureSpeechService(config, callbacks);\n        default:\n            throw new Error(\"Unknown speech service: \".concat(serviceType));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./utils/speechServices.js\n"));

/***/ })

});