"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./utils/speechServices.js":
/*!*********************************!*\
  !*** ./utils/speechServices.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AzureSpeechService: () => (/* binding */ AzureSpeechService),\n/* harmony export */   DeepgramSpeechService: () => (/* binding */ DeepgramSpeechService),\n/* harmony export */   createSpeechService: () => (/* binding */ createSpeechService)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet createClient = null;\nlet SpeechSDK = null;\n// Initialize client-side dependencies\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\")).then((module)=>{\n        createClient = module.createClient;\n    });\n    __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23)).then((module)=>{\n        SpeechSDK = module;\n    });\n}\n/**\n * Abstract base class for speech services\n */ class SpeechService {\n    async start(mediaStream, source) {\n        throw new Error('start method must be implemented');\n    }\n    async stop() {\n        throw new Error('stop method must be implemented');\n    }\n    cleanup() {\n        // Default cleanup implementation\n        if (this.recognizer) {\n            this.recognizer = null;\n        }\n        this.isActive = false;\n    }\n    constructor(config, callbacks){\n        this.config = config;\n        this.callbacks = callbacks;\n        this.isActive = false;\n        this.recognizer = null;\n    }\n}\n/**\n * Deepgram Speech Service Implementation\n */ class DeepgramSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            if (!this.config.deepgramKey) {\n                throw new Error('Deepgram API key is required');\n            }\n            if (!createClient) {\n                const { createClient: dgCreateClient } = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_deepgram_sdk_dist_module_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @deepgram/sdk */ \"(pages-dir-browser)/./node_modules/@deepgram/sdk/dist/module/index.js\"));\n                createClient = dgCreateClient;\n            }\n            this.source = source;\n            this.deepgram = createClient(this.config.deepgramKey);\n            // Create WebSocket connection for real-time transcription\n            this.connection = this.deepgram.listen.live({\n                model: this.config.deepgramModel || 'nova-2',\n                language: this.config.deepgramLanguage || 'en-US',\n                smart_format: true,\n                interim_results: true,\n                endpointing: 300,\n                utterance_end_ms: 1000,\n                vad_events: true\n            });\n            // Set up event handlers\n            this.connection.on('open', ()=>{\n                var _this_callbacks_onStart, _this_callbacks;\n                console.log(\"Deepgram connection opened for \".concat(source));\n                this.isActive = true;\n                // Clear any connection timeout\n                if (this.connectionTimeout) {\n                    clearTimeout(this.connectionTimeout);\n                    this.connectionTimeout = null;\n                }\n                (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            });\n            this.connection.on('Results', (data)=>{\n                var _data_channel_alternatives_, _data_channel_alternatives, _data_channel;\n                console.log(\"Deepgram results for \".concat(source, \":\"), data);\n                const transcript = (_data_channel = data.channel) === null || _data_channel === void 0 ? void 0 : (_data_channel_alternatives = _data_channel.alternatives) === null || _data_channel_alternatives === void 0 ? void 0 : (_data_channel_alternatives_ = _data_channel_alternatives[0]) === null || _data_channel_alternatives_ === void 0 ? void 0 : _data_channel_alternatives_.transcript;\n                if (transcript && transcript.trim()) {\n                    if (data.is_final) {\n                        var _this_callbacks_onFinalResult, _this_callbacks;\n                        console.log(\"Final transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, transcript, source);\n                    } else {\n                        var _this_callbacks_onInterimResult, _this_callbacks1;\n                        console.log(\"Interim transcript for \".concat(source, ': \"').concat(transcript, '\"'));\n                        (_this_callbacks_onInterimResult = (_this_callbacks1 = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks1, transcript, source);\n                    }\n                }\n            });\n            this.connection.on('Metadata', (data)=>{\n                console.log(\"Deepgram metadata for \".concat(source, \":\"), data);\n            });\n            this.connection.on('error', (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"Deepgram error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                this.stop();\n            });\n            this.connection.on('close', (closeEvent)=>{\n                console.log(\"Deepgram connection closed for \".concat(source, \":\"), closeEvent);\n                this.isActive = false;\n                // If connection closes immediately after opening, it might be a configuration issue\n                if (closeEvent && closeEvent.code && closeEvent.code !== 1000) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    console.error(\"Deepgram connection closed with error code \".concat(closeEvent.code, \": \").concat(closeEvent.reason));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, new Error(\"Connection closed: \".concat(closeEvent.reason || 'Unknown error')), source);\n                } else {\n                    var _this_callbacks_onStop, _this_callbacks1;\n                    (_this_callbacks_onStop = (_this_callbacks1 = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks1, source);\n                }\n            });\n            this.connection.on('warning', (warning)=>{\n                console.warn(\"Deepgram warning for \".concat(source, \":\"), warning);\n            });\n            // Set up MediaRecorder to send audio data to Deepgram\n            // Try different mimeTypes for better compatibility\n            let mimeType = 'audio/webm;codecs=opus';\n            if (!MediaRecorder.isTypeSupported(mimeType)) {\n                mimeType = 'audio/webm';\n                if (!MediaRecorder.isTypeSupported(mimeType)) {\n                    mimeType = 'audio/mp4';\n                    if (!MediaRecorder.isTypeSupported(mimeType)) {\n                        mimeType = ''; // Use default\n                    }\n                }\n            }\n            console.log(\"Using MediaRecorder mimeType: \".concat(mimeType || 'default', \" for \").concat(source));\n            this.mediaRecorder = new MediaRecorder(mediaStream, mimeType ? {\n                mimeType\n            } : {});\n            this.mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0 && this.connection && this.isActive) {\n                    console.log(\"Sending audio data to Deepgram: \".concat(event.data.size, \" bytes for \").concat(source));\n                    this.connection.send(event.data);\n                }\n            };\n            this.mediaRecorder.onerror = (error)=>{\n                var _this_callbacks_onError, _this_callbacks;\n                console.error(\"MediaRecorder error for \".concat(source, \":\"), error);\n                (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            };\n            this.mediaRecorder.onstart = ()=>{\n                console.log(\"MediaRecorder started for \".concat(source));\n            };\n            this.mediaRecorder.onstop = ()=>{\n                console.log(\"MediaRecorder stopped for \".concat(source));\n            };\n            // Start recording and sending data\n            this.mediaRecorder.start(250); // Send data every 250ms for better stability\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks;\n            console.error(\"Failed to start Deepgram for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {\n                this.mediaRecorder.stop();\n            }\n            if (this.connection) {\n                this.connection.finish();\n                this.connection = null;\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Deepgram:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        this.mediaRecorder = null;\n        this.connection = null;\n        this.deepgram = null;\n        this.source = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.deepgram = null;\n        this.connection = null;\n        this.mediaRecorder = null;\n        this.source = null;\n    }\n}\n/**\n * Azure Speech Service Implementation (Legacy)\n */ class AzureSpeechService extends SpeechService {\n    async start(mediaStream, source) {\n        try {\n            var _this_callbacks_onStart, _this_callbacks;\n            if (!this.config.azureToken || !this.config.azureRegion) {\n                throw new Error('Azure Speech credentials are required');\n            }\n            if (!SpeechSDK) {\n                SpeechSDK = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_microsoft-cognitiveservices-speech-sdk_distrib_lib_microsoft_-996f6b\").then(__webpack_require__.t.bind(__webpack_require__, /*! microsoft-cognitiveservices-speech-sdk */ \"(pages-dir-browser)/./node_modules/microsoft-cognitiveservices-speech-sdk/distrib/lib/microsoft.cognitiveservices.speech.sdk.js\", 23));\n            }\n            // Create audio config from media stream\n            this.audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n            // Create speech config\n            this.speechConfig = SpeechSDK.SpeechConfig.fromSubscription(this.config.azureToken, this.config.azureRegion);\n            this.speechConfig.speechRecognitionLanguage = this.config.azureLanguage || 'en-US';\n            // Create recognizer\n            this.recognizer = new SpeechSDK.SpeechRecognizer(this.speechConfig, this.audioConfig);\n            // Set up event handlers\n            this.recognizer.recognizing = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                    var _this_callbacks_onInterimResult, _this_callbacks;\n                    (_this_callbacks_onInterimResult = (_this_callbacks = this.callbacks).onInterimResult) === null || _this_callbacks_onInterimResult === void 0 ? void 0 : _this_callbacks_onInterimResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.recognized = (s, e)=>{\n                if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                    var _this_callbacks_onFinalResult, _this_callbacks;\n                    (_this_callbacks_onFinalResult = (_this_callbacks = this.callbacks).onFinalResult) === null || _this_callbacks_onFinalResult === void 0 ? void 0 : _this_callbacks_onFinalResult.call(_this_callbacks, e.result.text, source);\n                }\n            };\n            this.recognizer.canceled = (s, e)=>{\n                console.log(\"Azure recognition canceled for \".concat(source, \": \").concat(e.reason));\n                if (e.reason === SpeechSDK.CancellationReason.Error) {\n                    var _this_callbacks_onError, _this_callbacks;\n                    const error = new Error(\"Azure Speech error: \".concat(e.errorDetails));\n                    (_this_callbacks_onError = (_this_callbacks = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks, error, source);\n                }\n                this.stop();\n            };\n            this.recognizer.sessionStopped = (s, e)=>{\n                var _this_callbacks_onStop, _this_callbacks;\n                console.log(\"Azure session stopped for \".concat(source));\n                (_this_callbacks_onStop = (_this_callbacks = this.callbacks).onStop) === null || _this_callbacks_onStop === void 0 ? void 0 : _this_callbacks_onStop.call(_this_callbacks, source);\n                this.stop();\n            };\n            // Start continuous recognition\n            await this.recognizer.startContinuousRecognitionAsync();\n            this.isActive = true;\n            (_this_callbacks_onStart = (_this_callbacks = this.callbacks).onStart) === null || _this_callbacks_onStart === void 0 ? void 0 : _this_callbacks_onStart.call(_this_callbacks, source);\n            return this;\n        } catch (error) {\n            var _this_callbacks_onError, _this_callbacks1;\n            console.error(\"Failed to start Azure Speech for \".concat(source, \":\"), error);\n            (_this_callbacks_onError = (_this_callbacks1 = this.callbacks).onError) === null || _this_callbacks_onError === void 0 ? void 0 : _this_callbacks_onError.call(_this_callbacks1, error, source);\n            throw error;\n        }\n    }\n    async stop() {\n        try {\n            this.isActive = false;\n            if (this.recognizer && typeof this.recognizer.stopContinuousRecognitionAsync === 'function') {\n                await this.recognizer.stopContinuousRecognitionAsync();\n            }\n            if (this.audioConfig && typeof this.audioConfig.close === 'function') {\n                this.audioConfig.close();\n            }\n            this.cleanup();\n        } catch (error) {\n            console.error('Error stopping Azure Speech:', error);\n            throw error;\n        }\n    }\n    cleanup() {\n        super.cleanup();\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n    constructor(config, callbacks){\n        super(config, callbacks);\n        this.audioConfig = null;\n        this.speechConfig = null;\n    }\n}\n/**\n * Factory function to create speech service instances\n */ function createSpeechService(config, callbacks) {\n    const serviceType = config.speechService || 'deepgram';\n    switch(serviceType){\n        case 'deepgram':\n            return new DeepgramSpeechService(config, callbacks);\n        case 'azure':\n            return new AzureSpeechService(config, callbacks);\n        default:\n            throw new Error(\"Unknown speech service: \".concat(serviceType));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./utils/speechServices.js\n"));

/***/ })

});