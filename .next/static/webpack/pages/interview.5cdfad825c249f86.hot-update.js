"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/interview",{

/***/ "(pages-dir-browser)/./pages/interview.js":
/*!****************************!*\
  !*** ./pages/interview.js ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InterviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(pages-dir-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-redux */ \"(pages-dir-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,AppBar,Avatar,Box,Button,Card,CardContent,CardHeader,Checkbox,CircularProgress,Container,FormControlLabel,Grid,IconButton,List,ListItem,ListItemText,Paper,Snackbar,Switch,TextField,Toolbar,Tooltip,Typography,useTheme!=!@mui/material */ \"(pages-dir-browser)/__barrel_optimize__?names=Alert,AppBar,Avatar,Box,Button,Card,CardContent,CardHeader,Checkbox,CircularProgress,Container,FormControlLabel,Grid,IconButton,List,ListItem,ListItemText,Paper,Snackbar,Switch,TextField,Toolbar,Tooltip,Typography,useTheme!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @mui/icons-material/ArrowDownward */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ArrowDownward.js\");\n/* harmony import */ var _mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @mui/icons-material/ArrowUpward */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ArrowUpward.js\");\n/* harmony import */ var _mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/icons-material/DeleteSweep */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/DeleteSweep.js\");\n/* harmony import */ var _mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @mui/icons-material/Hearing */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Hearing.js\");\n/* harmony import */ var _mui_icons_material_Mic__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @mui/icons-material/Mic */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Mic.js\");\n/* harmony import */ var _mui_icons_material_MicOff__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @mui/icons-material/MicOff */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/MicOff.js\");\n/* harmony import */ var _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @mui/icons-material/Person */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _mui_icons_material_PictureInPictureAlt__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @mui/icons-material/PictureInPictureAlt */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/PictureInPictureAlt.js\");\n/* harmony import */ var _mui_icons_material_PlaylistAddCheck__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/icons-material/PlaylistAddCheck */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/PlaylistAddCheck.js\");\n/* harmony import */ var _mui_icons_material_ScreenShare__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/icons-material/ScreenShare */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/ScreenShare.js\");\n/* harmony import */ var _mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @mui/icons-material/Send */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Send.js\");\n/* harmony import */ var _mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @mui/icons-material/Settings */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @mui/icons-material/SmartToy */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/SmartToy.js\");\n/* harmony import */ var _mui_icons_material_StopScreenShare__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @mui/icons-material/StopScreenShare */ \"(pages-dir-browser)/./node_modules/@mui/icons-material/esm/StopScreenShare.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @google/generative-ai */ \"(pages-dir-browser)/./node_modules/@google/generative-ai/dist/index.mjs\");\n/* harmony import */ var highlight_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! highlight.js */ \"(pages-dir-browser)/./node_modules/highlight.js/es/index.js\");\n/* harmony import */ var highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! highlight.js/styles/atom-one-dark.css */ \"(pages-dir-browser)/./node_modules/highlight.js/styles/atom-one-dark.css\");\n/* harmony import */ var highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(highlight_js_styles_atom_one_dark_css__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash.throttle */ \"(pages-dir-browser)/./node_modules/lodash.throttle/index.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! openai */ \"(pages-dir-browser)/./node_modules/openai/index.mjs\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-markdown */ \"(pages-dir-browser)/./node_modules/react-markdown/index.js\");\n/* harmony import */ var react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-scroll-to-bottom */ \"(pages-dir-browser)/./node_modules/react-scroll-to-bottom/lib/esm/index.js\");\n/* harmony import */ var _components_SettingsDialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/SettingsDialog */ \"(pages-dir-browser)/./components/SettingsDialog.js\");\n/* harmony import */ var _redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../redux/aiResponseSlice */ \"(pages-dir-browser)/./redux/aiResponseSlice.js\");\n/* harmony import */ var _redux_historySlice__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../redux/historySlice */ \"(pages-dir-browser)/./redux/historySlice.js\");\n/* harmony import */ var _redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../redux/transcriptionSlice */ \"(pages-dir-browser)/./redux/transcriptionSlice.js\");\n/* harmony import */ var _utils_config__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../utils/config */ \"(pages-dir-browser)/./utils/config.js\");\n/* harmony import */ var _utils_speechServices__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../utils/speechServices */ \"(pages-dir-browser)/./utils/speechServices.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n// MUI Components\n\n// MUI Icons\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Third-party Libraries\n\n\n\n\n\n\n\n// Speech Services\n// Local Imports\n\n\n\n\n\n\nfunction debounce(func) {\n    let timeout = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;\n    var _this = this;\n    let timer;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        clearTimeout(timer);\n        timer = setTimeout(()=>{\n            func.apply(_this, args);\n        }, timeout);\n    };\n}\nfunction InterviewPage() {\n    _s();\n    const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useDispatch)();\n    const transcriptionFromStore = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[transcriptionFromStore]\": (state)=>state.transcription\n    }[\"InterviewPage.useSelector[transcriptionFromStore]\"]);\n    const aiResponseFromStore = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[aiResponseFromStore]\": (state)=>state.aiResponse\n    }[\"InterviewPage.useSelector[aiResponseFromStore]\"]);\n    const history = (0,react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector)({\n        \"InterviewPage.useSelector[history]\": (state)=>state.history\n    }[\"InterviewPage.useSelector[history]\"]);\n    const theme = (0,_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.useTheme)();\n    const [appConfig, setAppConfig] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)((0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)());\n    const [systemRecognizer, setSystemRecognizer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [micRecognizer, setMicRecognizer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [systemSpeechService, setSystemSpeechService] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [micSpeechService, setMicSpeechService] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [systemAutoMode, setSystemAutoMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(appConfig.systemAutoMode !== undefined ? appConfig.systemAutoMode : true);\n    const [openAI, setOpenAI] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [settingsOpen, setSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isMicrophoneActive, setIsMicrophoneActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isSystemAudioActive, setIsSystemAudioActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarOpen, setSnackbarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [snackbarMessage, setSnackbarMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [snackbarSeverity, setSnackbarSeverity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('info');\n    const [selectedQuestions, setSelectedQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isManualMode, setIsManualMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(appConfig.isManualMode !== undefined ? appConfig.isManualMode : false);\n    const [micTranscription, setMicTranscription] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isAILoading, setIsAILoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [autoScroll, setAutoScroll] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [aiResponseSortOrder, setAiResponseSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('newestAtTop');\n    const [isPipWindowActive, setIsPipWindowActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const pipWindowRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const documentPipWindowRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const documentPipIframeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const systemInterimTranscription = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)('');\n    const micInterimTranscription = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)('');\n    const silenceTimer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const finalTranscript = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)({\n        system: '',\n        microphone: ''\n    });\n    const isManualModeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(isManualMode);\n    const systemAutoModeRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(systemAutoMode);\n    const throttledDispatchSetAIResponseRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const showSnackbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"InterviewPage.useCallback[showSnackbar]\": function(message) {\n            let severity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n            setSnackbarMessage(message);\n            setSnackbarSeverity(severity);\n            setSnackbarOpen(true);\n        }\n    }[\"InterviewPage.useCallback[showSnackbar]\"], []);\n    const handleSettingsSaved = ()=>{\n        const newConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        setAppConfig(newConfig);\n        setIsAILoading(true);\n        setSystemAutoMode(newConfig.systemAutoMode !== undefined ? newConfig.systemAutoMode : true);\n        setIsManualMode(newConfig.isManualMode !== undefined ? newConfig.isManualMode : false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            const currentConfig = appConfig;\n            const initializeAI = {\n                \"InterviewPage.useEffect.initializeAI\": ()=>{\n                    try {\n                        if (currentConfig.aiModel.startsWith('gemini')) {\n                            if (!currentConfig.geminiKey) {\n                                showSnackbar('Gemini API key required. Please set it in Settings.', 'error');\n                                setOpenAI(null);\n                                return;\n                            }\n                            const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_4__.GoogleGenerativeAI(currentConfig.geminiKey);\n                            setOpenAI(genAI);\n                        } else {\n                            if (!currentConfig.openaiKey) {\n                                showSnackbar('OpenAI API key required. Please set it in Settings.', 'error');\n                                setOpenAI(null);\n                                return;\n                            }\n                            const openaiClient = new openai__WEBPACK_IMPORTED_MODULE_17__[\"default\"]({\n                                apiKey: currentConfig.openaiKey,\n                                dangerouslyAllowBrowser: true\n                            });\n                            setOpenAI(openaiClient);\n                        }\n                    } catch (error) {\n                        console.error('Error initializing AI client:', error);\n                        showSnackbar('Error initializing AI client: ' + error.message, 'error');\n                        setOpenAI(null);\n                    } finally{\n                        setIsAILoading(false);\n                    }\n                }\n            }[\"InterviewPage.useEffect.initializeAI\"];\n            if (isAILoading) initializeAI();\n        }\n    }[\"InterviewPage.useEffect\"], [\n        appConfig,\n        isAILoading,\n        showSnackbar\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            isManualModeRef.current = isManualMode;\n        }\n    }[\"InterviewPage.useEffect\"], [\n        isManualMode\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            systemAutoModeRef.current = systemAutoMode;\n        }\n    }[\"InterviewPage.useEffect\"], [\n        systemAutoMode\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            throttledDispatchSetAIResponseRef.current = lodash_throttle__WEBPACK_IMPORTED_MODULE_7___default()({\n                \"InterviewPage.useEffect\": (payload)=>{\n                    dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(payload));\n                }\n            }[\"InterviewPage.useEffect\"], 250, {\n                leading: true,\n                trailing: true\n            });\n            return ({\n                \"InterviewPage.useEffect\": ()=>{\n                    if (throttledDispatchSetAIResponseRef.current && typeof throttledDispatchSetAIResponseRef.current.cancel === 'function') {\n                        throttledDispatchSetAIResponseRef.current.cancel();\n                    }\n                }\n            })[\"InterviewPage.useEffect\"];\n        }\n    }[\"InterviewPage.useEffect\"], [\n        dispatch\n    ]);\n    const handleSnackbarClose = ()=>setSnackbarOpen(false);\n    const stopRecording = async (source)=>{\n        const speechService = source === 'system' ? systemSpeechService : micSpeechService;\n        const recognizer = source === 'system' ? systemRecognizer : micRecognizer;\n        try {\n            // Stop new speech service if available\n            if (speechService) {\n                await speechService.stop();\n            }\n            // Fallback to legacy Azure recognizer if still in use\n            if (recognizer && typeof recognizer.stopContinuousRecognitionAsync === 'function') {\n                await recognizer.stopContinuousRecognitionAsync();\n                if (recognizer.audioConfig && recognizer.audioConfig.privSource && recognizer.audioConfig.privSource.privStream) {\n                    const stream = recognizer.audioConfig.privSource.privStream;\n                    if (stream instanceof MediaStream) {\n                        stream.getTracks().forEach((track)=>{\n                            track.stop();\n                        });\n                    }\n                }\n                if (recognizer.audioConfig && typeof recognizer.audioConfig.close === 'function') {\n                    recognizer.audioConfig.close();\n                }\n            }\n        } catch (error) {\n            console.error(\"Error stopping \".concat(source, \" recognition:\"), error);\n            showSnackbar(\"Error stopping \".concat(source, \" audio: \").concat(error.message), 'error');\n        } finally{\n            if (source === 'system') {\n                setIsSystemAudioActive(false);\n                setSystemRecognizer(null);\n                setSystemSpeechService(null);\n            } else {\n                setIsMicrophoneActive(false);\n                setMicRecognizer(null);\n                setMicSpeechService(null);\n            }\n        }\n    };\n    const handleClearSystemTranscription = ()=>{\n        finalTranscript.current.system = '';\n        systemInterimTranscription.current = '';\n        dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.clearTranscription)());\n    };\n    const handleClearMicTranscription = ()=>{\n        finalTranscript.current.microphone = '';\n        micInterimTranscription.current = '';\n        setMicTranscription('');\n    };\n    const handleTranscriptionEvent = (text, source)=>{\n        const cleanText = text.replace(/\\s+/g, ' ').trim();\n        if (!cleanText) return;\n        finalTranscript.current[source] += cleanText + ' ';\n        if (source === 'system') {\n            dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + systemInterimTranscription.current));\n        } else {\n            setMicTranscription(finalTranscript.current.microphone + micInterimTranscription.current);\n        }\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        const currentSilenceTimerDuration = currentConfig.silenceTimerDuration;\n        if (source === 'system' && systemAutoModeRef.current || source === 'microphone' && !isManualModeRef.current) {\n            clearTimeout(silenceTimer.current);\n            silenceTimer.current = setTimeout(()=>{\n                askOpenAI(finalTranscript.current[source].trim(), source);\n            }, currentSilenceTimerDuration * 1000);\n        }\n    };\n    const handleManualInputChange = (value, source)=>{\n        if (source === 'system') {\n            dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(value));\n            finalTranscript.current.system = value;\n        } else {\n            setMicTranscription(value);\n            finalTranscript.current.microphone = value;\n        }\n    };\n    const handleManualSubmit = (source)=>{\n        const textToSubmit = source === 'system' ? transcriptionFromStore : micTranscription;\n        if (textToSubmit.trim()) {\n            askOpenAI(textToSubmit.trim(), source);\n        } else {\n            showSnackbar('Input is empty.', 'warning');\n        }\n    };\n    const handleKeyPress = (e, source)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleManualSubmit(source);\n        }\n    };\n    const handleCombineAndSubmit = ()=>{\n        if (selectedQuestions.length === 0) {\n            showSnackbar('No questions selected to combine.', 'warning');\n            return;\n        }\n        const questionHistory = history.filter((e)=>e.type === 'question').slice().reverse();\n        const questionTexts = selectedQuestions.map((selectedIndexInReversedArray)=>{\n            var _questionHistory_selectedIndexInReversedArray;\n            return (_questionHistory_selectedIndexInReversedArray = questionHistory[selectedIndexInReversedArray]) === null || _questionHistory_selectedIndexInReversedArray === void 0 ? void 0 : _questionHistory_selectedIndexInReversedArray.text;\n        }).filter((text)=>text);\n        if (questionTexts.length === 0) {\n            showSnackbar('Could not retrieve selected question texts.', 'warning');\n            return;\n        }\n        const combinedText = questionTexts.join('\\n\\n---\\n\\n');\n        askOpenAI(combinedText, 'combined');\n        setSelectedQuestions([]);\n    };\n    // Legacy Azure recognizer function for fallback\n    const createLegacyAzureRecognizer = async (mediaStream, source)=>{\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        if (!currentConfig.azureToken || !currentConfig.azureRegion) {\n            throw new Error('Azure Speech credentials missing');\n        }\n        let audioConfig;\n        try {\n            audioConfig = SpeechSDK.AudioConfig.fromStreamInput(mediaStream);\n        } catch (configError) {\n            console.error(\"Error creating AudioConfig for \".concat(source, \":\"), configError);\n            throw new Error(\"Error setting up audio for \".concat(source, \": \").concat(configError.message));\n        }\n        const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(currentConfig.azureToken, currentConfig.azureRegion);\n        speechConfig.speechRecognitionLanguage = currentConfig.azureLanguage;\n        const recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);\n        recognizer.recognizing = (s, e)=>{\n            if (e.result.reason === SpeechSDK.ResultReason.RecognizingSpeech) {\n                const interimText = e.result.text;\n                if (source === 'system') {\n                    systemInterimTranscription.current = interimText;\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + interimText));\n                } else {\n                    micInterimTranscription.current = interimText;\n                    setMicTranscription(finalTranscript.current.microphone + interimText);\n                }\n            }\n        };\n        recognizer.recognized = (s, e)=>{\n            if (e.result.reason === SpeechSDK.ResultReason.RecognizedSpeech && e.result.text) {\n                if (source === 'system') systemInterimTranscription.current = '';\n                else micInterimTranscription.current = '';\n                handleTranscriptionEvent(e.result.text, source);\n            }\n        };\n        recognizer.canceled = (s, e)=>{\n            console.log(\"CANCELED: Reason=\".concat(e.reason, \" for \").concat(source));\n            if (e.reason === SpeechSDK.CancellationReason.Error) {\n                console.error(\"CANCELED: ErrorCode=\".concat(e.errorCode));\n                console.error(\"CANCELED: ErrorDetails=\".concat(e.errorDetails));\n                showSnackbar(\"Speech recognition error for \".concat(source, \": \").concat(e.errorDetails), 'error');\n            }\n            stopRecording(source);\n        };\n        recognizer.sessionStopped = (s, e)=>{\n            console.log(\"Session stopped event for \".concat(source, \".\"));\n            stopRecording(source);\n        };\n        try {\n            await recognizer.startContinuousRecognitionAsync();\n            showSnackbar(\"\".concat(source === 'system' ? 'System audio' : 'Microphone', \" recording started (Azure Speech).\"), 'success');\n            return recognizer;\n        } catch (error) {\n            console.error(\"Error starting \".concat(source, \" continuous recognition:\"), error);\n            if (audioConfig && typeof audioConfig.close === 'function') audioConfig.close();\n            throw error;\n        }\n    };\n    const createSpeechRecognizer = async (mediaStream, source)=>{\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        // Create speech service callbacks\n        const callbacks = {\n            onStart: (source)=>{\n                console.log(\"Speech recognition started for \".concat(source));\n                showSnackbar(\"\".concat(source === 'system' ? 'System audio' : 'Microphone', \" recording started.\"), 'success');\n            },\n            onInterimResult: (text, source)=>{\n                console.log(\"Interim result for \".concat(source, \":\"), text);\n                if (source === 'system') {\n                    systemInterimTranscription.current = text;\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(finalTranscript.current.system + text));\n                } else {\n                    micInterimTranscription.current = text;\n                    setMicTranscription(finalTranscript.current.microphone + text);\n                }\n            },\n            onFinalResult: (text, source)=>{\n                console.log(\"Final result for \".concat(source, \":\"), text);\n                // Clear interim transcription\n                if (source === 'system') systemInterimTranscription.current = '';\n                else micInterimTranscription.current = '';\n                // Handle the final transcription\n                handleTranscriptionEvent(text, source);\n            },\n            onError: (error, source)=>{\n                console.error(\"Speech recognition error for \".concat(source, \":\"), error);\n                showSnackbar(\"Speech recognition error for \".concat(source, \": \").concat(error.message), 'error');\n                stopRecording(source);\n            },\n            onStop: (source)=>{\n                console.log(\"Speech recognition stopped for \".concat(source));\n            }\n        };\n        try {\n            // Check if we have the required credentials for the selected service\n            const selectedService = currentConfig.speechService || 'deepgram';\n            let configToUse = currentConfig;\n            if (selectedService === 'deepgram' && !currentConfig.deepgramKey) {\n                // If Deepgram is selected but no key is provided, check if Azure is available\n                if (currentConfig.azureToken && currentConfig.azureRegion) {\n                    console.log('Deepgram key missing, falling back to Azure Speech Services');\n                    configToUse = {\n                        ...currentConfig,\n                        speechService: 'azure'\n                    };\n                    showSnackbar('Using Azure Speech Services (Deepgram key not configured)', 'info');\n                } else {\n                    throw new Error('Deepgram API key is required. Please configure it in Settings, or provide Azure credentials for fallback.');\n                }\n            } else if (selectedService === 'azure' && (!currentConfig.azureToken || !currentConfig.azureRegion)) {\n                // If Azure is selected but credentials are missing, check if Deepgram is available\n                if (currentConfig.deepgramKey) {\n                    console.log('Azure credentials missing, falling back to Deepgram');\n                    configToUse = {\n                        ...currentConfig,\n                        speechService: 'deepgram'\n                    };\n                    showSnackbar('Using Deepgram (Azure credentials not configured)', 'info');\n                } else {\n                    throw new Error('Azure Speech credentials are required. Please configure them in Settings, or provide Deepgram API key for fallback.');\n                }\n            }\n            // Create the appropriate speech service\n            const speechService = (0,_utils_speechServices__WEBPACK_IMPORTED_MODULE_14__.createSpeechService)(configToUse, callbacks);\n            // Start the speech service\n            await speechService.start(mediaStream, source);\n            return speechService;\n        } catch (error) {\n            console.error(\"Error creating speech service for \".concat(source, \":\"), error);\n            // Try legacy Azure recognizer as final fallback\n            if (currentConfig.azureToken && currentConfig.azureRegion) {\n                try {\n                    console.log(\"Attempting legacy Azure fallback for \".concat(source));\n                    const legacyRecognizer = await createLegacyAzureRecognizer(mediaStream, source);\n                    showSnackbar(\"Using legacy Azure Speech Services for \".concat(source), 'warning');\n                    return legacyRecognizer;\n                } catch (legacyError) {\n                    console.error(\"Legacy Azure fallback also failed for \".concat(source, \":\"), legacyError);\n                }\n            }\n            // Provide helpful error messages\n            if (error.message.includes('API key') || error.message.includes('credentials')) {\n                showSnackbar(\"Speech service configuration required. Please configure API keys in Settings (⚙️ icon).\", 'error');\n            } else {\n                showSnackbar(\"Failed to start \".concat(source, \" recognition: \").concat(error.message), 'error');\n            }\n            mediaStream.getTracks().forEach((track)=>track.stop());\n            return null;\n        }\n    };\n    const startSystemAudioRecognition = async ()=>{\n        if (isSystemAudioActive) {\n            await stopRecording('system');\n            return;\n        }\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {\n            showSnackbar('Screen sharing is not supported by your browser.', 'error');\n            setIsSystemAudioActive(false);\n            return;\n        }\n        try {\n            const mediaStream = await navigator.mediaDevices.getDisplayMedia({\n                audio: true,\n                video: {\n                    displaySurface: 'browser',\n                    logicalSurface: true\n                }\n            });\n            const audioTracks = mediaStream.getAudioTracks();\n            if (audioTracks.length === 0) {\n                showSnackbar('No audio track detected. Please ensure you share a tab with audio.', 'warning');\n                mediaStream.getTracks().forEach((track)=>track.stop());\n                return;\n            }\n            if (systemRecognizer) {\n                await stopRecording('system');\n            }\n            const speechServiceInstance = await createSpeechRecognizer(mediaStream, 'system');\n            if (speechServiceInstance) {\n                // Check if it's a new speech service or legacy recognizer\n                if (speechServiceInstance.start && speechServiceInstance.stop) {\n                    // New speech service\n                    setSystemSpeechService(speechServiceInstance);\n                } else {\n                    // Legacy Azure recognizer\n                    setSystemRecognizer(speechServiceInstance);\n                }\n                setIsSystemAudioActive(true);\n                mediaStream.getTracks().forEach((track)=>{\n                    track.onended = ()=>{\n                        showSnackbar('Tab sharing ended.', 'info');\n                        stopRecording('system');\n                    };\n                });\n            } else {\n                mediaStream.getTracks().forEach((track)=>track.stop());\n            }\n        } catch (error) {\n            console.error('System audio capture error:', error);\n            if (error.name === \"NotAllowedError\") {\n                showSnackbar('Permission denied for screen recording. Please allow access.', 'error');\n            } else if (error.name === \"NotFoundError\") {\n                showSnackbar('No suitable tab/window found to share.', 'error');\n            } else if (error.name === \"NotSupportedError\") {\n                showSnackbar('System audio capture not supported by your browser.', 'error');\n            } else {\n                showSnackbar(\"Failed to start system audio capture: \".concat(error.message || 'Unknown error'), 'error');\n            }\n            setIsSystemAudioActive(false);\n        }\n    };\n    const startMicrophoneRecognition = async ()=>{\n        if (isMicrophoneActive) {\n            await stopRecording('microphone');\n            return;\n        }\n        try {\n            const mediaStream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            if (micSpeechService) await stopRecording('microphone');\n            const speechServiceInstance = await createSpeechRecognizer(mediaStream, 'microphone');\n            if (speechServiceInstance) {\n                // Check if it's a new speech service or legacy recognizer\n                if (speechServiceInstance.start && speechServiceInstance.stop) {\n                    // New speech service\n                    setMicSpeechService(speechServiceInstance);\n                } else {\n                    // Legacy Azure recognizer\n                    setMicRecognizer(speechServiceInstance);\n                }\n                setIsMicrophoneActive(true);\n            } else {\n                mediaStream.getTracks().forEach((track)=>track.stop());\n            }\n        } catch (error) {\n            console.error('Microphone capture error:', error);\n            if (error.name === \"NotAllowedError\" || error.name === \"NotFoundError\") {\n                showSnackbar('Permission denied for microphone. Please allow access.', 'error');\n            } else {\n                showSnackbar(\"Failed to access microphone: \".concat(error.message || 'Unknown error'), 'error');\n            }\n            setIsMicrophoneActive(false);\n        }\n    };\n    // Helper function to determine reasoning effort for future advanced models\n    const getReasoningEffort = (text, source)=>{\n        const textLength = text.length;\n        const isComplexQuestion = text.includes('?') && textLength > 100;\n        const isCombined = source === 'combined';\n        if (isCombined || isComplexQuestion || textLength > 500) return 'high';\n        if (textLength > 200) return 'medium';\n        return 'low';\n    };\n    const askOpenAI = async (text, source)=>{\n        if (!text.trim()) {\n            showSnackbar('No input text to process.', 'warning');\n            return;\n        }\n        if (!openAI || isAILoading) {\n            showSnackbar('AI client is not ready. Please wait or check settings.', 'warning');\n            return;\n        }\n        const currentConfig = (0,_utils_config__WEBPACK_IMPORTED_MODULE_13__.getConfig)();\n        const lengthSettings = {\n            concise: {\n                temperature: 0.4,\n                maxTokens: 250\n            },\n            medium: {\n                temperature: 0.6,\n                maxTokens: 500\n            },\n            lengthy: {\n                temperature: 0.8,\n                maxTokens: 1000\n            }\n        };\n        const { temperature, maxTokens } = lengthSettings[currentConfig.responseLength || 'medium'];\n        setIsProcessing(true);\n        const timestamp = new Date().toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n        let streamedResponse = '';\n        dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n            type: 'question',\n            text,\n            timestamp,\n            source,\n            status: 'pending'\n        }));\n        dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(''));\n        try {\n            const conversationHistoryForAPI = history.filter((e)=>e.text && (e.type === 'question' || e.type === 'response') && e.status !== 'pending').slice(-6).map((event)=>({\n                    role: event.type === 'question' ? 'user' : 'assistant',\n                    content: event.text\n                }));\n            if (currentConfig.aiModel.startsWith('gemini')) {\n                // Enhanced Gemini API configuration for 2.5 models\n                const modelConfig = {\n                    model: currentConfig.aiModel,\n                    generationConfig: {\n                        temperature,\n                        maxOutputTokens: maxTokens\n                    },\n                    systemInstruction: {\n                        parts: [\n                            {\n                                text: currentConfig.gptSystemPrompt\n                            }\n                        ]\n                    }\n                };\n                // Add thinking configuration for Gemini 2.5 models\n                if (currentConfig.aiModel.includes('2.5') && currentConfig.thinkingBudget !== undefined) {\n                    if (currentConfig.thinkingBudget === 0) {\n                        // Disable thinking\n                        modelConfig.generationConfig.thinkingConfig = {\n                            thinkingBudget: 0\n                        };\n                    } else if (currentConfig.thinkingBudget > 0) {\n                        // Custom thinking budget\n                        modelConfig.generationConfig.thinkingConfig = {\n                            thinkingBudget: currentConfig.thinkingBudget\n                        };\n                    }\n                // If thinkingBudget is null, use default (thinking enabled)\n                }\n                const model = openAI.getGenerativeModel(modelConfig);\n                const chat = model.startChat({\n                    history: conversationHistoryForAPI.map((msg)=>({\n                            role: msg.role === 'user' ? 'user' : 'model',\n                            parts: [\n                                {\n                                    text: msg.content\n                                }\n                            ]\n                        }))\n                });\n                const result = await chat.sendMessageStream(text);\n                for await (const chunk of result.stream){\n                    if (chunk && typeof chunk.text === 'function') {\n                        const chunkText = chunk.text();\n                        streamedResponse += chunkText;\n                        if (throttledDispatchSetAIResponseRef.current) {\n                            throttledDispatchSetAIResponseRef.current(streamedResponse);\n                        }\n                    }\n                }\n            } else {\n                // Enhanced OpenAI API usage with future-ready parameters\n                const messages = [\n                    {\n                        role: 'system',\n                        content: currentConfig.gptSystemPrompt\n                    },\n                    ...conversationHistoryForAPI,\n                    {\n                        role: 'user',\n                        content: text\n                    }\n                ];\n                const requestParams = {\n                    model: currentConfig.aiModel,\n                    messages,\n                    stream: true\n                };\n                // Set temperature based on model capabilities\n                if (currentConfig.aiModel.startsWith('o1')) {\n                // o1 models don't support temperature parameter at all\n                // Don't set temperature for o1 models\n                } else if (currentConfig.aiModel.startsWith('gpt-5')) {\n                    // GPT-5 models may have temperature restrictions, use default value\n                    requestParams.temperature = 1;\n                } else {\n                    // Standard models support configurable temperature\n                    requestParams.temperature = temperature;\n                }\n                // Use the correct token parameter based on model\n                if (currentConfig.aiModel.startsWith('gpt-5') || currentConfig.aiModel.startsWith('o1')) {\n                    requestParams.max_completion_tokens = maxTokens;\n                } else {\n                    requestParams.max_tokens = maxTokens;\n                }\n                // Add model-specific parameters for different model types\n                if (currentConfig.aiModel.startsWith('gpt-5')) {\n                    // GPT-5 models support new parameters\n                    if (currentConfig.reasoningEffort) {\n                        requestParams.reasoning_effort = currentConfig.reasoningEffort;\n                    }\n                    if (currentConfig.verbosity !== undefined) {\n                        requestParams.verbosity = currentConfig.verbosity === 0 ? 'low' : currentConfig.verbosity === 1 ? 'medium' : 'high';\n                    }\n                } else if (currentConfig.aiModel.startsWith('o1')) {\n                    // o1 models use different parameters and don't support streaming\n                    // Remove streaming for o1 models\n                    requestParams.stream = false;\n                    // o1 models don't use system messages in the same way\n                    // Move system prompt to the first user message\n                    requestParams.messages = [\n                        {\n                            role: 'user',\n                            content: \"\".concat(currentConfig.gptSystemPrompt, \"\\n\\n\").concat(text)\n                        },\n                        ...conversationHistoryForAPI.slice(1) // Skip the system message\n                    ];\n                // o1 models require temperature = 1 (already set above)\n                // No additional temperature modification needed\n                }\n                if (currentConfig.aiModel.startsWith('o1')) {\n                    var _response_choices__message, _response_choices_;\n                    // o1 models don't support streaming, handle as single response\n                    const response = await openAI.chat.completions.create(requestParams);\n                    streamedResponse = ((_response_choices_ = response.choices[0]) === null || _response_choices_ === void 0 ? void 0 : (_response_choices__message = _response_choices_.message) === null || _response_choices__message === void 0 ? void 0 : _response_choices__message.content) || '';\n                    if (throttledDispatchSetAIResponseRef.current) {\n                        throttledDispatchSetAIResponseRef.current(streamedResponse);\n                    }\n                } else {\n                    // Standard streaming for GPT-5, GPT-4o, and other models\n                    const stream = await openAI.chat.completions.create(requestParams);\n                    for await (const chunk of stream){\n                        var _chunk_choices__delta, _chunk_choices_;\n                        const chunkText = ((_chunk_choices_ = chunk.choices[0]) === null || _chunk_choices_ === void 0 ? void 0 : (_chunk_choices__delta = _chunk_choices_.delta) === null || _chunk_choices__delta === void 0 ? void 0 : _chunk_choices__delta.content) || '';\n                        streamedResponse += chunkText;\n                        if (throttledDispatchSetAIResponseRef.current) {\n                            throttledDispatchSetAIResponseRef.current(streamedResponse);\n                        }\n                    }\n                }\n            }\n            if (throttledDispatchSetAIResponseRef.current && typeof throttledDispatchSetAIResponseRef.current.cancel === 'function') {\n                throttledDispatchSetAIResponseRef.current.cancel();\n            }\n            dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(streamedResponse));\n            const finalTimestamp = new Date().toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n            });\n            dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n                type: 'response',\n                text: streamedResponse,\n                timestamp: finalTimestamp,\n                status: 'completed'\n            }));\n        } catch (error) {\n            console.error(\"AI request error:\", error);\n            const errorMessage = \"AI request failed: \".concat(error.message || 'Unknown error');\n            showSnackbar(errorMessage, 'error');\n            dispatch((0,_redux_aiResponseSlice__WEBPACK_IMPORTED_MODULE_10__.setAIResponse)(\"Error: \".concat(errorMessage)));\n            dispatch((0,_redux_historySlice__WEBPACK_IMPORTED_MODULE_11__.addToHistory)({\n                type: 'response',\n                text: \"Error: \".concat(errorMessage),\n                timestamp: new Date().toLocaleTimeString([], {\n                    hour: '2-digit',\n                    minute: '2-digit'\n                }),\n                status: 'error'\n            }));\n        } finally{\n            if (source === 'system' && systemAutoModeRef.current || source === 'microphone' && !isManualModeRef.current) {\n                finalTranscript.current[source] = '';\n                if (source === 'system') {\n                    systemInterimTranscription.current = '';\n                    dispatch((0,_redux_transcriptionSlice__WEBPACK_IMPORTED_MODULE_12__.setTranscription)(''));\n                } else {\n                    micInterimTranscription.current = '';\n                    setMicTranscription('');\n                }\n            }\n            setIsProcessing(false);\n        }\n    };\n    const formatAndDisplayResponse = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (response)=>{\n            if (!response) return null;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                components: {\n                    code (param) {\n                        let { node, inline, className, children, ...props } = param;\n                        const match = /language-(\\w+)/.exec(className || '');\n                        return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                            sx: {\n                                my: 1,\n                                position: 'relative',\n                                '& pre': {\n                                    borderRadius: '4px',\n                                    padding: '12px !important',\n                                    fontSize: '0.875rem',\n                                    overflowX: 'auto',\n                                    whiteSpace: 'pre-wrap',\n                                    wordBreak: 'break-all'\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: className,\n                                    ...props,\n                                    dangerouslySetInnerHTML: {\n                                        __html: highlight_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"].highlight(String(children).replace(/\\n$/, ''), {\n                                            language: match[1],\n                                            ignoreIllegals: true\n                                        }).value\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 794,\n                                    columnNumber: 22\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 794,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 782,\n                            columnNumber: 15\n                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: className,\n                            ...props,\n                            style: {\n                                backgroundColor: 'rgba(0,0,0,0.05)',\n                                padding: '2px 4px',\n                                borderRadius: '4px',\n                                fontFamily: 'monospace',\n                                fontSize: '0.875rem',\n                                wordBreak: 'break-all'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 797,\n                            columnNumber: 15\n                        }, void 0);\n                    },\n                    p: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                paragraph: true,\n                                ...props,\n                                sx: {\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 813,\n                                columnNumber: 38\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    strong: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"strong\",\n                                fontWeight: \"bold\",\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 814,\n                                columnNumber: 43\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    em: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"em\",\n                                fontStyle: \"italic\",\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 815,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    ul: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"ul\",\n                                sx: {\n                                    pl: 2.5,\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 816,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    ol: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"ol\",\n                                sx: {\n                                    pl: 2.5,\n                                    mb: 1,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 817,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"],\n                    li: {\n                        \"InterviewPage.useCallback[formatAndDisplayResponse]\": (param)=>{\n                            let { node, ...props } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                component: \"li\",\n                                sx: {\n                                    mb: 0.25,\n                                    fontSize: '0.95rem',\n                                    wordBreak: 'break-word'\n                                },\n                                ...props\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                lineNumber: 818,\n                                columnNumber: 39\n                            }, void 0);\n                        }\n                    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"]\n                },\n                children: response\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 777,\n                columnNumber: 7\n            }, this);\n        }\n    }[\"InterviewPage.useCallback[formatAndDisplayResponse]\"], []);\n    const renderHistoryItem = (item, index)=>{\n        if (item.type !== 'response') return null;\n        const Icon = _mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n        const title = 'AI Assistant';\n        const avatarBgColor = theme.palette.secondary.light;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n            sx: {\n                alignItems: 'flex-start',\n                px: 0,\n                py: 1.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                    sx: {\n                        bgcolor: avatarBgColor,\n                        mr: 2,\n                        mt: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        sx: {\n                            color: theme.palette.getContrastText(avatarBgColor)\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 835,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 834,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Paper, {\n                    variant: \"outlined\",\n                    sx: {\n                        p: 1.5,\n                        flexGrow: 1,\n                        bgcolor: theme.palette.background.default,\n                        borderColor: theme.palette.divider,\n                        overflowX: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                            sx: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                mb: 0.5\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"subtitle2\",\n                                    fontWeight: \"bold\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 839,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"caption\",\n                                    color: \"text.secondary\",\n                                    children: item.timestamp\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 840,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 838,\n                            columnNumber: 11\n                        }, this),\n                        formatAndDisplayResponse(item.text)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 837,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, \"response-\".concat(index), true, {\n            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n            lineNumber: 833,\n            columnNumber: 7\n        }, this);\n    };\n    const renderQuestionHistoryItem = (item, index)=>{\n        const Icon = item.source === 'system' ? _mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__[\"default\"] : _mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__[\"default\"];\n        const title = item.source === 'system' ? 'Interviewer' : 'Candidate';\n        const avatarBgColor = item.source === 'system' ? theme.palette.info.light : theme.palette.success.light;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n            secondaryAction: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Checkbox, {\n                edge: \"end\",\n                checked: selectedQuestions.includes(index),\n                onChange: ()=>{\n                    setSelectedQuestions((prev)=>prev.includes(index) ? prev.filter((x)=>x !== index) : [\n                            ...prev,\n                            index\n                        ]);\n                },\n                color: \"secondary\",\n                size: \"small\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 857,\n                columnNumber: 11\n            }, void 0),\n            disablePadding: true,\n            sx: {\n                py: 0.5,\n                display: 'flex',\n                alignItems: 'center'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Avatar, {\n                    sx: {\n                        bgcolor: avatarBgColor,\n                        mr: 1.5,\n                        width: 32,\n                        height: 32,\n                        fontSize: '1rem'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        fontSize: \"small\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 873,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 872,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItemText, {\n                    primary: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                        variant: \"body2\",\n                        noWrap: true,\n                        sx: {\n                            fontWeight: selectedQuestions.includes(index) ? 'bold' : 'normal',\n                            display: '-webkit-box',\n                            WebkitLineClamp: 2,\n                            WebkitBoxOrient: 'vertical',\n                            overflow: 'hidden',\n                            textOverflow: 'ellipsis'\n                        },\n                        children: item.text\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 877,\n                        columnNumber: 13\n                    }, void 0),\n                    secondary: \"\".concat(title, \" - \").concat(item.timestamp)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 875,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, \"question-hist-\".concat(index), true, {\n            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n            lineNumber: 854,\n            columnNumber: 7\n        }, this);\n    };\n    const handleSortOrderToggle = ()=>{\n        setAiResponseSortOrder((prev)=>prev === 'newestAtBottom' ? 'newestAtTop' : 'newestAtBottom');\n    };\n    const getAiResponsesToDisplay = ()=>{\n        let responses = history.filter((item)=>item.type === 'response').slice();\n        const currentStreamingText = aiResponseFromStore;\n        if (isProcessing && currentStreamingText && currentStreamingText.trim() !== '') {\n            responses.push({\n                text: currentStreamingText,\n                timestamp: 'Streaming...',\n                type: 'current_streaming'\n            });\n        }\n        if (aiResponseSortOrder === 'newestAtTop') {\n            return responses.reverse();\n        }\n        return responses;\n    };\n    const togglePipWindow = async ()=>{\n        if (isPipWindowActive) {\n            if (documentPipWindowRef.current && typeof documentPipWindowRef.current.close === 'function') {\n                try {\n                    await documentPipWindowRef.current.close();\n                } catch (e) {\n                    console.error(\"Error closing document PiP window:\", e);\n                }\n            } else if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                pipWindowRef.current.close();\n            }\n            return; // State update will be handled by pagehide/interval listeners\n        }\n        const addResizeListener = (pipWindow)=>{\n            const handlePipResize = debounce(()=>{\n                if (!pipWindow || pipWindow.closed) return;\n                const target = documentPipIframeRef.current ? documentPipIframeRef.current.contentWindow : pipWindow;\n                if (target) {\n                    target.postMessage({\n                        type: 'PIP_RESIZE',\n                        payload: {\n                            width: pipWindow.innerWidth,\n                            height: pipWindow.innerHeight\n                        }\n                    }, '*');\n                }\n            }, 50);\n            pipWindow.addEventListener('resize', handlePipResize);\n            return ()=>pipWindow.removeEventListener('resize', handlePipResize); // Return a cleanup function\n        };\n        if (window.documentPictureInPicture && typeof window.documentPictureInPicture.requestWindow === 'function') {\n            try {\n                const pipOptions = {\n                    width: 400,\n                    height: 300\n                };\n                const requestedPipWindow = await window.documentPictureInPicture.requestWindow(pipOptions);\n                documentPipWindowRef.current = requestedPipWindow;\n                setIsPipWindowActive(true);\n                const iframe = documentPipWindowRef.current.document.createElement('iframe');\n                iframe.src = '/pip-log';\n                iframe.style.width = '100%';\n                iframe.style.height = '100%';\n                iframe.style.border = 'none';\n                documentPipWindowRef.current.document.body.style.margin = '0';\n                documentPipWindowRef.current.document.body.style.overflow = 'hidden';\n                documentPipWindowRef.current.document.body.append(iframe);\n                documentPipIframeRef.current = iframe;\n                const removeResizeListener = addResizeListener(documentPipWindowRef.current);\n                iframe.onload = ()=>{\n                    if (documentPipIframeRef.current && documentPipIframeRef.current.contentWindow) {\n                        documentPipIframeRef.current.contentWindow.postMessage({\n                            type: 'AI_LOG_DATA',\n                            payload: {\n                                historicalResponses: history.filter((item)=>item.type === 'response'),\n                                currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                                isProcessing: isProcessing,\n                                sortOrder: aiResponseSortOrder\n                            }\n                        }, '*');\n                    }\n                };\n                documentPipWindowRef.current.addEventListener('pagehide', ()=>{\n                    removeResizeListener();\n                    setIsPipWindowActive(false);\n                    documentPipWindowRef.current = null;\n                    documentPipIframeRef.current = null;\n                });\n                showSnackbar('Native PiP window opened.', 'success');\n                return;\n            } catch (err) {\n                console.error('Document Picture-in-Picture API error:', err);\n                showSnackbar(\"Native PiP not available or failed. Trying popup. (\".concat(err.message, \")\"), 'warning');\n            }\n        }\n        pipWindowRef.current = window.open('/pip-log', 'AIResponsePiP', 'width=400,height=550,resizable=yes,scrollbars=yes,status=no,toolbar=no,menubar=no,location=no,noopener,noreferrer,popup=yes');\n        if (pipWindowRef.current) {\n            setIsPipWindowActive(true);\n            const removeResizeListener = addResizeListener(pipWindowRef.current);\n            pipWindowRef.current.onload = ()=>{\n                if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                    pipWindowRef.current.postMessage({\n                        type: 'AI_LOG_DATA',\n                        payload: {\n                            historicalResponses: history.filter((item)=>item.type === 'response'),\n                            currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                            isProcessing: isProcessing,\n                            sortOrder: aiResponseSortOrder\n                        }\n                    }, '*');\n                }\n            };\n            const pipCheckInterval = setInterval(()=>{\n                if (pipWindowRef.current && pipWindowRef.current.closed) {\n                    clearInterval(pipCheckInterval);\n                    removeResizeListener();\n                    setIsPipWindowActive(false);\n                    pipWindowRef.current = null;\n                }\n            }, 500);\n            if (pipWindowRef.current) pipWindowRef.current._pipIntervalId = pipCheckInterval;\n        } else {\n            showSnackbar('Failed to open PiP window. Please check popup blocker settings.', 'error');\n            setIsPipWindowActive(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            return ({\n                \"InterviewPage.useEffect\": ()=>{\n                    if (pipWindowRef.current && pipWindowRef.current._pipIntervalId) {\n                        clearInterval(pipWindowRef.current._pipIntervalId);\n                    }\n                    if (documentPipWindowRef.current && typeof documentPipWindowRef.current.close === 'function') {\n                        try {\n                            documentPipWindowRef.current.close();\n                        } catch (e) {}\n                    }\n                }\n            })[\"InterviewPage.useEffect\"];\n        }\n    }[\"InterviewPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            let targetWindowForMessage = null;\n            if (documentPipWindowRef.current && documentPipIframeRef.current && documentPipIframeRef.current.contentWindow) {\n                targetWindowForMessage = documentPipIframeRef.current.contentWindow;\n            } else if (pipWindowRef.current && !pipWindowRef.current.closed) {\n                targetWindowForMessage = pipWindowRef.current;\n            }\n            if (isPipWindowActive && targetWindowForMessage) {\n                try {\n                    targetWindowForMessage.postMessage({\n                        type: 'AI_LOG_DATA',\n                        payload: {\n                            historicalResponses: history.filter({\n                                \"InterviewPage.useEffect\": (item)=>item.type === 'response'\n                            }[\"InterviewPage.useEffect\"]),\n                            currentStreamingText: isProcessing ? aiResponseFromStore : '',\n                            isProcessing: isProcessing,\n                            sortOrder: aiResponseSortOrder\n                        }\n                    }, '*');\n                } catch (e) {\n                    console.warn(\"Could not post message to PiP window:\", e);\n                }\n            }\n        }\n    }[\"InterviewPage.useEffect\"], [\n        history,\n        aiResponseFromStore,\n        isPipWindowActive,\n        aiResponseSortOrder,\n        isProcessing\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"88c8eba4614332d8\",\n                            [\n                                theme.palette.background.paper,\n                                theme.palette.grey[400],\n                                theme.palette.background.paper,\n                                theme.palette.grey[500],\n                                theme.palette.grey[400],\n                                theme.palette.background.paper\n                            ]\n                        ]\n                    ]),\n                    children: \"Interview Copilot - Active Session\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                    lineNumber: 1059,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 1058,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                sx: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    height: '100vh'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.AppBar, {\n                        position: \"static\",\n                        color: \"default\",\n                        elevation: 1,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Toolbar, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    sx: {\n                                        mr: 2,\n                                        color: 'primary.main'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1064,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                    variant: \"h6\",\n                                    component: \"div\",\n                                    sx: {\n                                        flexGrow: 1,\n                                        color: 'text.primary'\n                                    },\n                                    children: \"Interview Copilot\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1065,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                    title: \"Settings\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                        color: \"primary\",\n                                        onClick: ()=>setSettingsOpen(true),\n                                        \"aria-label\": \"settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Settings__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1070,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1069,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1068,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1063,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1062,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Container, {\n                        maxWidth: \"xl\",\n                        sx: {\n                            flexGrow: 1,\n                            py: 2,\n                            display: 'flex',\n                            flexDirection: 'column'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                            container: true,\n                            spacing: 2,\n                            sx: {\n                                flexGrow: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 3,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column',\n                                        gap: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                    title: \"System Audio (Interviewer)\",\n                                                    avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Hearing__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 72\n                                                    }, void 0),\n                                                    sx: {\n                                                        pb: 1\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1081,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                            control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                                checked: systemAutoMode,\n                                                                onChange: (e)=>setSystemAutoMode(e.target.checked),\n                                                                color: \"primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1084,\n                                                                columnNumber: 30\n                                                            }, void 0),\n                                                            label: \"Auto-Submit Question\",\n                                                            sx: {\n                                                                mb: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.TextField, {\n                                                            fullWidth: true,\n                                                            multiline: true,\n                                                            rows: 3,\n                                                            variant: \"outlined\",\n                                                            value: transcriptionFromStore,\n                                                            onChange: (e)=>handleManualInputChange(e.target.value, 'system'),\n                                                            onKeyDown: (e)=>handleKeyPress(e, 'system'),\n                                                            placeholder: \"Interviewer's speech...\",\n                                                            sx: {\n                                                                mb: 2\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                            sx: {\n                                                                display: 'flex',\n                                                                gap: 1,\n                                                                flexWrap: 'wrap'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                    onClick: startSystemAudioRecognition,\n                                                                    variant: \"contained\",\n                                                                    color: isSystemAudioActive ? 'error' : 'primary',\n                                                                    startIcon: isSystemAudioActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_StopScreenShare__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1104,\n                                                                        columnNumber: 56\n                                                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ScreenShare__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1104,\n                                                                        columnNumber: 82\n                                                                    }, void 0),\n                                                                    sx: {\n                                                                        flexGrow: 1\n                                                                    },\n                                                                    children: isSystemAudioActive ? 'Stop System Audio' : 'Record System Audio'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1100,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                                    variant: \"caption\",\n                                                                    sx: {\n                                                                        mt: 1,\n                                                                        display: 'block',\n                                                                        width: '100%'\n                                                                    },\n                                                                    children: isSystemAudioActive ? 'Recording system audio...' : 'Select \"Chrome Tab\" and check \"Share audio\" when prompted.'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1109,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                                    title: \"Clear System Transcription\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                        onClick: handleClearSystemTranscription,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                            lineNumber: 1113,\n                                                                            columnNumber: 76\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1113,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1112,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                !systemAutoMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                    onClick: ()=>handleManualSubmit('system'),\n                                                                    variant: \"outlined\",\n                                                                    color: \"primary\",\n                                                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1120,\n                                                                        columnNumber: 36\n                                                                    }, void 0),\n                                                                    disabled: isProcessing || !transcriptionFromStore.trim(),\n                                                                    children: \"Submit\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1116,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1099,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1082,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1080,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                            sx: {\n                                                flexGrow: 1,\n                                                display: 'flex',\n                                                flexDirection: 'column'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                    title: \"Question History\",\n                                                    avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_PlaylistAddCheck__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1132,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                        variant: \"contained\",\n                                                        size: \"small\",\n                                                        onClick: handleCombineAndSubmit,\n                                                        disabled: selectedQuestions.length === 0 || isProcessing,\n                                                        startIcon: isProcessing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CircularProgress, {\n                                                            size: 16,\n                                                            color: \"inherit\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1139,\n                                                            columnNumber: 49\n                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1139,\n                                                            columnNumber: 98\n                                                        }, void 0),\n                                                        children: \"Ask Combined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 21\n                                                    }, void 0),\n                                                    sx: {\n                                                        pb: 1,\n                                                        borderBottom: \"1px solid \".concat(theme.palette.divider)\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                    sx: {\n                                                        flexGrow: 1,\n                                                        overflow: 'hidden',\n                                                        p: 0\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"scroll-to-bottom\",\n                                                        followButtonClassName: \"hidden-follow-button\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.List, {\n                                                            dense: true,\n                                                            sx: {\n                                                                pt: 0,\n                                                                px: 1\n                                                            },\n                                                            children: history.filter((e)=>e.type === 'question').slice().reverse().map(renderQuestionHistoryItem)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1148,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1147,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1146,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                            lineNumber: 1129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1079,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        sx: {\n                                            flexGrow: 1,\n                                            display: 'flex',\n                                            flexDirection: 'column'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"AI Assistant Log\",\n                                                avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_SmartToy__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1161,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                            title: isPipWindowActive ? \"Close PiP Log\" : \"Open PiP Log\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                onClick: togglePipWindow,\n                                                                size: \"small\",\n                                                                color: isPipWindowActive ? \"secondary\" : \"default\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_PictureInPictureAlt__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1166,\n                                                                    columnNumber: 27\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1165,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1164,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                            title: aiResponseSortOrder === 'newestAtTop' ? \"Sort: Newest at Bottom\" : \"Sort: Newest on Top\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                onClick: handleSortOrderToggle,\n                                                                size: \"small\",\n                                                                children: aiResponseSortOrder === 'newestAtTop' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowDownward__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1171,\n                                                                    columnNumber: 68\n                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_ArrowUpward__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1171,\n                                                                    columnNumber: 92\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1170,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1169,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                            variant: \"caption\",\n                                                            sx: {\n                                                                mr: 1,\n                                                                fontStyle: 'italic'\n                                                            },\n                                                            children: aiResponseSortOrder === 'newestAtTop' ? \"Newest First\" : \"Oldest First\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1174,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                            control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                                checked: autoScroll,\n                                                                onChange: (e)=>setAutoScroll(e.target.checked),\n                                                                color: \"primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1178,\n                                                                columnNumber: 34\n                                                            }, void 0),\n                                                            label: \"Auto Scroll\",\n                                                            sx: {\n                                                                ml: 1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1177,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true),\n                                                sx: {\n                                                    borderBottom: \"1px solid \".concat(theme.palette.divider)\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                sx: {\n                                                    flexGrow: 1,\n                                                    overflow: 'hidden',\n                                                    p: 0\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_scroll_to_bottom__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"scroll-to-bottom\",\n                                                    mode: autoScroll ? aiResponseSortOrder === 'newestAtTop' ? \"top\" : \"bottom\" : undefined,\n                                                    followButtonClassName: \"hidden-follow-button\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.List, {\n                                                        sx: {\n                                                            px: 2,\n                                                            py: 1\n                                                        },\n                                                        children: [\n                                                            getAiResponsesToDisplay().map(renderHistoryItem),\n                                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.ListItem, {\n                                                                sx: {\n                                                                    justifyContent: 'center',\n                                                                    py: 2\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CircularProgress, {\n                                                                        size: 24\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1196,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Typography, {\n                                                                        variant: \"caption\",\n                                                                        sx: {\n                                                                            ml: 1\n                                                                        },\n                                                                        children: \"AI is thinking...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1197,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1195,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1192,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1186,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1158,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Grid, {\n                                    item: true,\n                                    xs: 12,\n                                    md: 3,\n                                    sx: {\n                                        display: 'flex',\n                                        flexDirection: 'column'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Card, {\n                                        sx: {\n                                            flexGrow: 1,\n                                            display: 'flex',\n                                            flexDirection: 'column'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardHeader, {\n                                                title: \"Your Mic (Candidate)\",\n                                                avatar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Person__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                    lineNumber: 1209,\n                                                    columnNumber: 66\n                                                }, void 0),\n                                                sx: {\n                                                    pb: 1\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1209,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.CardContent, {\n                                                sx: {\n                                                    flexGrow: 1,\n                                                    display: 'flex',\n                                                    flexDirection: 'column'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.FormControlLabel, {\n                                                        control: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Switch, {\n                                                            checked: isManualMode,\n                                                            onChange: (e)=>setIsManualMode(e.target.checked),\n                                                            color: \"primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                            lineNumber: 1212,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        label: \"Manual Input Mode\",\n                                                        sx: {\n                                                            mb: 1\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.TextField, {\n                                                        fullWidth: true,\n                                                        multiline: true,\n                                                        rows: 8,\n                                                        variant: \"outlined\",\n                                                        value: micTranscription,\n                                                        onChange: (e)=>handleManualInputChange(e.target.value, 'microphone'),\n                                                        onKeyDown: (e)=>handleKeyPress(e, 'microphone'),\n                                                        placeholder: \"Your speech or manual input...\",\n                                                        sx: {\n                                                            mb: 2,\n                                                            flexGrow: 1\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Box, {\n                                                        sx: {\n                                                            display: 'flex',\n                                                            gap: 1,\n                                                            flexWrap: 'wrap',\n                                                            mt: 'auto'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                onClick: startMicrophoneRecognition,\n                                                                variant: \"contained\",\n                                                                color: isMicrophoneActive ? 'error' : 'primary',\n                                                                startIcon: isMicrophoneActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_MicOff__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1232,\n                                                                    columnNumber: 55\n                                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Mic__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1232,\n                                                                    columnNumber: 72\n                                                                }, void 0),\n                                                                sx: {\n                                                                    flexGrow: 1\n                                                                },\n                                                                children: isMicrophoneActive ? 'Stop Mic' : 'Start Mic'\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1228,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Tooltip, {\n                                                                title: \"Clear Your Transcription\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.IconButton, {\n                                                                    onClick: handleClearMicTranscription,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_DeleteSweep__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                        lineNumber: 1238,\n                                                                        columnNumber: 73\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1238,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1237,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isManualMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                                                onClick: ()=>handleManualSubmit('microphone'),\n                                                                variant: \"outlined\",\n                                                                color: \"primary\",\n                                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_icons_material_Send__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                    lineNumber: 1245,\n                                                                    columnNumber: 36\n                                                                }, void 0),\n                                                                disabled: isProcessing || !micTranscription.trim(),\n                                                                children: \"Submit\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                                lineNumber: 1241,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                                lineNumber: 1210,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                        lineNumber: 1208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                                    lineNumber: 1207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1077,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1076,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SettingsDialog__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        open: settingsOpen,\n                        onClose: ()=>setSettingsOpen(false),\n                        onSave: handleSettingsSaved\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Snackbar, {\n                        open: snackbarOpen,\n                        autoHideDuration: 4000,\n                        onClose: handleSnackbarClose,\n                        anchorOrigin: {\n                            vertical: 'bottom',\n                            horizontal: 'center'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.Alert, {\n                            onClose: handleSnackbarClose,\n                            severity: snackbarSeverity,\n                            sx: {\n                                width: '100%',\n                                boxShadow: theme.shadows[6]\n                            },\n                            children: snackbarMessage\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                            lineNumber: 1269,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                        lineNumber: 1263,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/repos/mycopilot/pages/interview.js\",\n                lineNumber: 1061,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"88c8eba4614332d8\",\n                dynamic: [\n                    theme.palette.background.paper,\n                    theme.palette.grey[400],\n                    theme.palette.background.paper,\n                    theme.palette.grey[500],\n                    theme.palette.grey[400],\n                    theme.palette.background.paper\n                ],\n                children: \".scroll-to-bottom{height:100%;width:100%;overflow-y:auto}.hidden-follow-button{display:none}.scroll-to-bottom::-webkit-scrollbar{width:8px;height:8px}.scroll-to-bottom::-webkit-scrollbar-track{background:\".concat(theme.palette.background.paper, \";border-radius:10px}.scroll-to-bottom::-webkit-scrollbar-thumb{background-color:\").concat(theme.palette.grey[400], \";border-radius:10px;border:2px solid \").concat(theme.palette.background.paper, \"}.scroll-to-bottom::-webkit-scrollbar-thumb:hover{background-color:\").concat(theme.palette.grey[500], \"}.scroll-to-bottom{scrollbar-width:thin;scrollbar-color:\").concat(theme.palette.grey[400], \" \").concat(theme.palette.background.paper, \"}\")\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n_s(InterviewPage, \"5kq7Xa8OgN+T13jCAWpZ96uerOw=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useDispatch,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_15__.useSelector,\n        _barrel_optimize_names_Alert_AppBar_Avatar_Box_Button_Card_CardContent_CardHeader_Checkbox_CircularProgress_Container_FormControlLabel_Grid_IconButton_List_ListItem_ListItemText_Paper_Snackbar_Switch_TextField_Toolbar_Tooltip_Typography_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__.useTheme\n    ];\n});\n_c = InterviewPage;\nvar _c;\n$RefreshReg$(_c, \"InterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/interview.js\n"));

/***/ })

});