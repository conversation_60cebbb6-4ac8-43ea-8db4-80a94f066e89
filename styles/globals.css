/* final/styles/globals.css */
html,
body {
  padding: 0;
  margin: 0;
  font-family: "Roboto", "Helvetica", "Arial", sans-serif; /* Default font from theme */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%; /* Ensure html and body take full height */
}

#__next { /* Next.js wrapper div */
  height: 100%;
  display: flex;
  flex-direction: column;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/*
  Any styles previously in Home.module.css that were specific to the interview page
  should be reviewed. If they are general layout or component styles, they should ideally
  be handled by the Material UI theme or inline sx props in interview.js.
  If they are very specific and complex, they could remain in a module.css file imported
  into interview.js, but the goal is to rely more on the MUI theme for consistency.
*/

/* Example: If you had specific code block styling in Home.module.css */
/*
.code-block {
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
}
.inline-code {
  background-color: #e0e0e0;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Courier New', Courier, monospace;
}
*/
/* The above would now be handled by ReactMarkdown component's styling or hljs theme */

